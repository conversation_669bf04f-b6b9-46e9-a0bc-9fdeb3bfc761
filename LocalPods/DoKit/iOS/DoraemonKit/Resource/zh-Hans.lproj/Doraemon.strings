/*
 Doraemon.strings
 Pods
 
 Created by xgb on 2018/11/13.
 
 */

{
    //分组名称
    "业务工具"                                      = "业务工具";
    "平台工具"                                      = "平台工具";
    "常用工具"                                      = "常用工具";
    "性能检测"                                      = "性能检测";
    "视觉工具"                                      = "视觉工具";
    "Weex"                                         = "Weex";
    
    //每一个模块
    
    //数据Mock
    "Mock数据"                                = "Mock数据";
    "上传模版"                                 = "上传模版";
    "接口分组"                                 = "接口分组";
    "开关状态"                                 = "开关状态";
    "分组: %@\n"                             = "分组: %@\n";
    "修改人: %@\n"                           = "修改人: %@\n";
    "创建人: %@\n"                           = "创建人: %@\n";
    "数据预览"                               = "数据预览";
    "上传"                                  = "上传";
    "数据预览为空"                            = "数据预览为空";
    "上传成功"                               = "上传成功";
    "上传失败"                               = "上传失败";
    "本地是否存在mock数据: %@"                  = "本地是否存在mock数据: %@";
    "存在"                                  = "存在";
    "不存在"                                 = "不存在";
    "App当前处于健康体检状态，无法进行此操作"        = "App当前处于健康体检状态，无法进行此操作";
    
    //健康体检
    "健康体检"                                 = "健康体检";
    "点击开始检测"                              = "点击开始检测";
    "正在检测中..."                            = "正在检测中...";
    "向下滑动查看功能使用说明"                    = "向下滑动查看功能使用说明";
    "回到顶部"                                 = "回到顶部";
    "提交成功\n恭喜已完成检测！"                  = "提交成功\n恭喜已完成检测！";
    "第%d步"                                  = "第%d步";
    "是否确认"                                 = "是否确认";
    "结束前请完善下列信息"                        = "结束前请完善下列信息";
    "测试用例名称"                              = "测试用例名称";
    "测试人名称"                               = "测试人名称";
    "提交"                                    = "提交";
    "取消"                                     = "取消";
    "丢弃"                                     = "丢弃";
    "数据上传成功"                               = "数据上传成功";
    "数据上传失败"                               = "数据上传失败";
    
    //文件同步助手
    "文件同步"                                 = "文件同步";
    "需要到www.dokit.cn上注册pId才能使用该功能"    =  "需要到www.dokit.cn上注册pId才能使用该功能";
    
    //应用设置
    "应用设置"                               = "应用设置";
    
    //App信息
    "App信息"                                =   "App信息";
    "手机信息"                                =   "手机信息";
    "设备名称"                                = "设备名称";
    "手机型号"                                =   "手机型号";
    "系统版本"                                =   "系统版本";
    "手机屏幕"                               = "手机屏幕";
    "权限信息"                                =   "权限信息";
    "地理位置权限"                          =   "地理位置权限";
    "网络权限"                                =   "网络权限";
    "推送权限"                                =   "推送权限";
    "相机权限"                                =   "相机权限";
    "麦克风权限"                             =  "麦克风权限";
    "相册权限"                                =   "相册权限";
    "通讯录权限"                             =   "通讯录权限";
    "日历权限"                                =   "日历权限";
    "提醒事项权限"                            =   "提醒事项权限";
    "用户没有选择"                          =  "用户没有选择";
    "用户已经授权"                               = "用户已经授权";
    "用户没有授权"                               = "用户没有授权";
    "家长控制"                                     = "家长控制";
    
    //沙盒浏览器
    "沙盒浏览器"                             =  "沙盒浏览器";
    "本地预览"                                =   "本地预览";
    "分享"                                    = "分享";
    "请选择操作方式"                       =   "请选择操作方式";
    "返回上一级"                             =   "返回上一级";
    "根目录"                                   =   "根目录";
    "文件不存在"                             =   "文件不存在";
    "数据库预览"                                  = "数据库预览";
    "文件预览"                                =   "文件预览";
    
    //Mock GPS
    "Mock GPS"                                     =  "位置模拟";
    "打开Mock GPS"                                   = "打开位置模拟";
    "请输入经纬度"                               = "请输入经纬度";
    "经纬度不能为空"                       =  "经纬度不能为空";
    "格式不正确"                             =  "格式不正确";
    "(示例: 120.15 30.28)"                           = "(示例: 120.15 30.28)";
    "经度不合法"                             =   "经度不合法";
    "mock开关没有打开"                      =   "mock开关没有打开";
    "纬度不合法"                             =   "纬度不合法";
    
    //H5任意门
    "H5任意门"                                 =  "H5任意门";
    "清除搜索历史"                            = "清除搜索历史";
    "模拟器不支持扫码功能"                      = "模拟器不支持扫码功能";
    "扫描二维码"                             = "扫描二维码";
    "点击跳转"                                =   "点击跳转";
    "Doraemon内置浏览器"                          = "Doraemon内置浏览器";
    "链接不能为空"                        =   "链接不能为空";
    "设备无相机——设备无相机功能，无法进行扫描" = "设备无相机——设备无相机功能，无法进行扫描";
    "设备相机错误——无法启用相机，请检查"    = "设备相机错误——无法启用相机，请检查";
    "相机权限未开启，请到「设置-隐私-相机」中允许DoKit访问您的相机"    = "相机权限未开启，请到「设置-隐私-相机」中允许DoKit访问您的相机";
    
    //清除本地数据
    "清理缓存"                          = "清理缓存";
    "确定要删除本地数据"                      = "确定要删除本地数据";
    "正在清理中"                                  = "正在清理中";
    
    //NSLog
    "NSLog"                               = "NSLog";
    "开关"                                      = "开关";
    "查看记录"                                = "查看记录";
    "NSLog日志记录"                           = "NSLog日志记录";
    "导出"                                  = "导出";
    "清除"                                  = "清除";
    "%@\n触发时间: %@"                             = "%@\n触发时间: %@";
    
    //UserDefaults
    
    //CocoaLumberjack
    "Lumberjack"                                       = "Lumberjack";
    "CocoaLumberjack日志记录"                 =  "CocoaLumberjack日志记录";
    "日志记录"                           = "日志记录";
    "%@\n触发时间: %@\n文件名称: %@\n所在行: %zi\n线程id: %@ \n线程名称: %@" = "%@\n触发时间: %@\n文件名称: %@\n所在行: %zi\n线程id: %@ \n线程名称: %@";
    
    //DBView
    "开启服务"                                   = "开启服务";
    "关闭服务"                                   = "关闭服务";
    "温馨提示"                                   = "温馨提示";
    "你可以通过下面地址访问"                         = "你可以通过下面地址访问";
    "请保证当前手机和PC处在同一个局域网内"             = "请保证当前手机和PC处在同一个局域网内";
    "服务已关闭"                                   = "服务已关闭";
    
    //FPS
    "帧率"                                      =   "帧率";
    "帧率检测"                                     = "帧率检测";
    "帧率检测开关"                                 =  "帧率检测开关";
    
    
    //CPU
    "CPU"                                         =  "CPU";
    "CPU检测"                                   =   "CPU检测";
    "CPU检测开关"                                  = "CPU检测开关";
    
    //内存
    "内存"                                      =   "内存";
    "内存检测"                                     = "内存检测";
    "内存检测开关"                               = "内存检测开关";
    
    //网络
    "网络"                                      =   "网络";
    "网络监控"                                      =   "网络监控";
    "网络检测开关"                               = "网络检测开关";
    "显示网络检测详情"                         = "显示网络检测详情";
    "网络监控摘要"                               = "网络监控摘要";
    "暂未开启网络监控"                      =   "暂未开启网络监控";
    "总计已为您抓包"                       =   "总计已为您抓包";
    "秒"                                = "秒";
    "抓包数量"                                =   "抓包数量";
    "数据上传"                                =   "数据上传";
    "数据下载"                                =   "数据下载";
    "HTTP方法"                                  =   "HTTP方法";
    "数据类型"                                =   "数据类型";
    "网络监控列表"                          =   "网络监控列表";
    "耗时"                                      =   "耗时";
    "网络监控详情"                          =   "网络监控详情";
    "请求"                                      =   "请求";
    "请求概要"                                  = "请求概要";
    "链接"                                     = "链接";
    "请求头"                                   = "请求头";
    "请求体"                                   = "请求体";
    "响应"                                      =   "响应";
    "响应概要"                                  =   "响应概要";
    "响应头"                                  =   "响应头";
    "响应体"                                  =   "响应体";
    "数据大小 : %@"                           =   "数据大小 : %@";
    "你好"                                      =   "你好";
    
    //Crash
    "Crash"                                 =   "Crash";
    "Crash日志收集开关"                          = "Crash日志收集开关";
    "查看Crash日志"                                = "查看Crash日志";
    "一键清理Crash日志"                          = "一键清理Crash日志";
    "确认删除所有崩溃日志吗？"             = "确认删除所有崩溃日志吗？";
    "Crash日志列表"                                = "Crash日志列表";
    
    //子线程UI
    "子线程UI"                                 =   "子线程UI";
    "子线程UI渲染检测开关"               =   "子线程UI渲染检测开关";
    "查看检测记录"                          =   "查看检测记录";
    "检测列表"                                =   "检测列表";
    "检测详情"                                =   "检测详情";
    
     //卡顿
     "卡顿"                                      =   "卡顿";
     "卡顿检测"                                =   "卡顿检测";
     "卡顿检测开关"                          =   "卡顿检测开关";
     "查看卡顿记录"                          =   "查看卡顿记录";
     "一键清理卡顿记录"                          = "一键清理卡顿记录";
     "卡顿列表"                                =   "卡顿列表";
     "卡顿详情"                                =   "卡顿详情";
     "确认删除所有卡顿记录吗？"             = "确认删除所有卡顿记录吗？";
     
     //大图检测
     "大图检测"                                     = "大图检测";
     "大图检测开关"                                  = "大图检测开关";
     
     //弱网检测
     "模拟弱网"                               = "模拟弱网";
     "模拟弱网测试"                            = "模拟弱网测试";
     "弱网模式"                               = "弱网模式";
     "断网"                                  = "断网";
     "超时"                                  = "超时";
     "限速"                                  = "限速";
     "延时"                                  = "延时";
     "延时时间"                               = "延时时间";
     "请求限速"                               = "请求限速";
     "响应限速"                               = "响应限速";
     "上行流量"                                 = "上行流量";
     "下行流量"                                 = "下行流量";
     
     //开机启动
     "启动耗时"                               = "启动耗时";
     "本次启动时间为"                          = "本次启动时间为";
     
    //UI层级
    "UI层级"                                  = "UI层级";
    "UI层级检查开关"                              = "UI层级检查开关";

    //函数耗时
    "函数耗时"                                   = "函数耗时";
    "函数耗时描述" = "\n\n\n 该功能不提供UI操作界面，在你需要分析的代码之前插入 \n\n [DoraemonTimeProfiler startRecord]; \n\n结束的地方加上 \n\n[DoraemonTimeProfiler stopRecord];\n\n 然后手动操作App执行代码流程，即可在控制台看到完整的函数耗时分析。\n\nsdk过滤了代码调用层次>10层，耗时小于1ms的函数调用，当然你也可以通过api自行设置。 \n\n 分析完毕之后，记得删掉startRecord和stopRecord的函数调用。";
    
    //Load耗时
    "Load耗时"                                       = "Load耗时";
    "Load耗时检测开关"                           = "Load耗时检测开关";
    "Load耗时检测记录"                           = "Load耗时检测记录";
    "总共耗时"                                     = "总共耗时";
    
    //内存泄漏
    "内存泄漏"                                   = "内存泄漏";
    "内存泄漏检测开关"                            = "内存泄漏检测开关";
    "内存泄漏检测弹框提醒"                            = "内存泄漏检测弹框提醒";
    "内存泄漏检测结果"                             = "内存泄漏检测结果";
    "内存泄漏详情"                                =  "内存泄漏详情";
    
    //取色器
    "取色器"                             =   "取色器";
    
    //组件检查
    "组件检查"                                =   "组件检查";
    "控件名称"                                 =  "控件名称";
    "\n控件位置：左%0.1lf  上%0.1lf  宽%0.1lf  高%0.1lf" = "\n控件位置：左%0.1lf  上%0.1lf  宽%0.1lf  高%0.1lf";
    "\n背景颜色：%@"                              = "\n背景颜色：%@";
    "\n背景颜色：%@  字体颜色：%@  字体大小：%.f" = "\n背景颜色：%@  字体颜色：%@  字体大小：%.f";
    
    //对齐标尺
    "对齐标尺"                                =   "对齐标尺";
    "位置：左%@  右%@  上%@  下%@"                 =  "位置：左%@  右%@  上%@  下%@";
    
    //元素边框线
    "布局边框"                                  = "布局边框";
    "布局边框开关"                            = "布局边框开关";
    
    //UI结构
    "UI结构"                                 = "UI结构";
    
    
    //Weex Log
    "日志"                                       = "日志";
    "Weex日志记录"                               = "Weex日志记录";
    
    //Weex Cache
    "缓存"                                       = "缓存";
    "Weex缓存"                               = "Weex缓存";
    
    //Weex Info
    "信息"                                       = "信息";
    "weex信息查看"                                = "weex信息查看";
    "weexsdk版本号"                              = "weexsdk版本号";
    "wxJSLib版本号"                              = "wxJSLib版本号";
    "wxBundleType"                              = "wxBundleType";
    "wxBundleSize"                              = "wxBundleSize";
    "请求bundle时间"                              = "请求bundle时间";
    "处理bundle时间"                              = "处理bundle时间";
    "第一个view出现时间"                              = "第一个view出现时间";
    "可交互时间"                              = "可交互时间";
    
    //设置
    "设置"                                           = "设置";
    "工具管理"                                        = "工具管理";
    "编辑"                                           = "编辑";
    "完成"                                           = "完成";
    "是否保存已编辑的内容"                              = "是否保存已编辑的内容";
    "保存成功"                                        = "保存成功";
    "还原"                                           = "还原";
    "是否还原到初始状态"                                = "是否还原到初始状态";
    "每一个分组至少保留一项"                             = "每一个分组至少保留一项";
    
    //其他
    "提示"                                      =   "提示";
    "删除成功"                                     = "删除成功";
    "确定"                                      =   "确定";
    "关闭DoraemonKit"                                = "关闭DoraemonKit";
    "当前版本"                                 = "当前版本";
    "该功能需要重启App才能生效"        =   "该功能需要重启App才能生效";
    "请输入您要搜索的关键字"           =   "请输入您要搜索的关键字";
    "删除失败"                                     = "删除失败";
    "Doraemon关闭之后需要重启App才能重新打开"   =   "Doraemon关闭之后需要重启App才能重新打开";
    "复制"                                           = "复制";
    "删除"                                           = "删除";
    
}
