// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		00059A292AB1B35F0084A471 /* candy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 00310B722AB1AE7900795F4F /* candy.ttf */; };
		002B08DF2C1BD93C007AFDB3 /* WHPublishViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002B08DE2C1BD93C007AFDB3 /* WHPublishViewController.swift */; };
		002B08E22C1BE881007AFDB3 /* WHPublishView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002B08E12C1BE881007AFDB3 /* WHPublishView.swift */; };
		002B08E42C1BECF2007AFDB3 /* WHPublishImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002B08E32C1BECF2007AFDB3 /* WHPublishImageCell.swift */; };
		002B08E62C1C1886007AFDB3 /* WHPublishInputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002B08E52C1C1886007AFDB3 /* WHPublishInputCell.swift */; };
		002B08E82C1C2F7F007AFDB3 /* WHPublishControlCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002B08E72C1C2F7F007AFDB3 /* WHPublishControlCell.swift */; };
		00389EDB2AA5A59600E6089A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EDA2AA5A59600E6089A /* AppDelegate.swift */; };
		00389EDF2AA5A59600E6089A /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EDE2AA5A59600E6089A /* ViewController.swift */; };
		00389EE22AA5A59600E6089A /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 00389EE02AA5A59600E6089A /* Main.storyboard */; };
		00389EE42AA5A59800E6089A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 00389EE32AA5A59800E6089A /* Assets.xcassets */; };
		00389EF02AA5AAE800E6089A /* WHLaunchTaskManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EEF2AA5AAE800E6089A /* WHLaunchTaskManager.swift */; };
		00389EF22AA5AB2800E6089A /* WHLaunchTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EF12AA5AB2800E6089A /* WHLaunchTask.swift */; };
		00389EF52AA5ABF200E6089A /* WHLaunchAccountTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EF42AA5ABF200E6089A /* WHLaunchAccountTask.swift */; };
		00389EF82AA5B0DA00E6089A /* WHLaunchManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EF72AA5B0DA00E6089A /* WHLaunchManager.swift */; };
		00389EFA2AA5B27900E6089A /* WHLaunchAnalyticsTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EF92AA5B27900E6089A /* WHLaunchAnalyticsTask.swift */; };
		00389EFC2AA6F6DD00E6089A /* WHLaunchDoraemonTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EFB2AA6F6DD00E6089A /* WHLaunchDoraemonTask.swift */; };
		00389EFE2AA6FC2600E6089A /* WHLaunchWindowTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EFD2AA6FC2600E6089A /* WHLaunchWindowTask.swift */; };
		00389F002AA6FCB500E6089A /* WHLaunchRootVCTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00389EFF2AA6FCB500E6089A /* WHLaunchRootVCTask.swift */; };
		003F329C2B2311E900F8EA09 /* WHCreatPicCountCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 003F329B2B2311E900F8EA09 /* WHCreatPicCountCell.swift */; };
		0045E4352ACE8522008064AE /* WHRouterSetViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0045E4342ACE8522008064AE /* WHRouterSetViewController.swift */; };
		005CADFD2B26FF1A004315D2 /* WHCtreatPicButtonCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 005CADFC2B26FF1A004315D2 /* WHCtreatPicButtonCell.swift */; };
		005CADFF2B271F12004315D2 /* WHCreatPicView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 005CADFE2B271F12004315D2 /* WHCreatPicView.swift */; };
		0064181B2ABEBC6500933945 /* WHSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0064181A2ABEBC6500933945 /* WHSettingViewController.swift */; };
		0064CAB92AA9B3EA007EC639 /* WHLaunchEnviromentTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0064CAB82AA9B3EA007EC639 /* WHLaunchEnviromentTask.swift */; };
		0064CABF2AA9EFFD007EC639 /* WHMineViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0064CABE2AA9EFFD007EC639 /* WHMineViewController.swift */; };
		0069ADCC2AC172A800BB06DD /* WHMineSegemateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0069ADCB2AC172A800BB06DD /* WHMineSegemateView.swift */; };
		00767D0A2C20401300547237 /* WHPublishAlterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00767D092C20401300547237 /* WHPublishAlterView.swift */; };
		00767D0C2C204D9D00547237 /* WHMineManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00767D0B2C204D9D00547237 /* WHMineManager.swift */; };
		007D5E212C2946D100FE7197 /* WHMoreAlterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 007D5E202C2946D100FE7197 /* WHMoreAlterView.swift */; };
		007D5E232C29742800FE7197 /* WHMoreEditeAlterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 007D5E222C29742800FE7197 /* WHMoreEditeAlterView.swift */; };
		00821C2C2B29888C00C19764 /* WHPaindingPlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00821C2B2B29888C00C19764 /* WHPaindingPlayView.swift */; };
		00825F6D2AC2A76B00923D98 /* WHSettingTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00825F6C2AC2A76B00923D98 /* WHSettingTableCell.swift */; };
		008325292C2D37AE0092E6E2 /* WHDetailInfoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 008325282C2D37AE0092E6E2 /* WHDetailInfoManager.swift */; };
		0083252B2C2E897F0092E6E2 /* WHModelDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0083252A2C2E897F0092E6E2 /* WHModelDetailViewController.swift */; };
		0083252D2C2EA8760092E6E2 /* WHModelImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0083252C2C2EA8760092E6E2 /* WHModelImageCell.swift */; };
		0083252F2C2EB5550092E6E2 /* WHModelDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0083252E2C2EB5550092E6E2 /* WHModelDetailView.swift */; };
		00857A372AC1386B0090BC7E /* WHMineContentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00857A362AC1386B0090BC7E /* WHMineContentViewController.swift */; };
		00858AB12ABBDDA600A10201 /* WHMineHeadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00858AB02ABBDDA600A10201 /* WHMineHeadView.swift */; };
		008DB41E2C22D21700EB3EBF /* WHCreateInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 008DB41D2C22D21700EB3EBF /* WHCreateInfoView.swift */; };
		008DB4232C22D2DA00EB3EBF /* WHMyCreatInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 008DB4222C22D2DA00EB3EBF /* WHMyCreatInfoModel.swift */; };
		008DB4252C252C5000EB3EBF /* WHDetailMyBottoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 008DB4242C252C5000EB3EBF /* WHDetailMyBottoView.swift */; };
		00B3C8912B26B4CD00B037B5 /* WHCreatPicInputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00B3C8902B26B4CD00B037B5 /* WHCreatPicInputCell.swift */; };
		00B3C8932B26DE3C00B037B5 /* WHCreatPicCellManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00B3C8922B26DE3C00B037B5 /* WHCreatPicCellManager.swift */; };
		00F48F2C2AA74CF4003505BE /* WHDebugViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00F48F2B2AA74CF4003505BE /* WHDebugViewController.swift */; };
		031B29342E227E16000E1882 /* WHVideoFrameImageLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 031B29332E227E16000E1882 /* WHVideoFrameImageLoader.swift */; };
		032C3A852DD71D9F00B22F5C /* WHAIClearSegmentedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 032C3A842DD71D9F00B22F5C /* WHAIClearSegmentedView.swift */; };
		034695102D9257E50025B1DA /* PhotosUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0346950F2D9257E50025B1DA /* PhotosUI.framework */; };
		034EE7C02E0A9982009E0587 /* WHVideoHudDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7BF2E0A9982009E0587 /* WHVideoHudDetailViewController.swift */; };
		034EE7C72E0A9995009E0587 /* A.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 034EE7C12E0A9995009E0587 /* A.mp4 */; };
		034EE7C82E0A9995009E0587 /* A_H.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 034EE7C22E0A9995009E0587 /* A_H.mp4 */; };
		034EE7C92E0A9995009E0587 /* WHVideoHudDetailSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7C52E0A9995009E0587 /* WHVideoHudDetailSlider.swift */; };
		034EE7CA2E0A9995009E0587 /* WHVideoHudDetailFailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7C42E0A9995009E0587 /* WHVideoHudDetailFailView.swift */; };
		034EE7CC2E0A9F2A009E0587 /* WHVideoHudClipViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7CB2E0A9F2A009E0587 /* WHVideoHudClipViewController.swift */; };
		034EE7CE2E0AA402009E0587 /* WHMediaEditEntranceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7CD2E0AA402009E0587 /* WHMediaEditEntranceViewController.swift */; };
		034EE7D02E0AA812009E0587 /* WHVideoHudClipBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 034EE7CF2E0AA812009E0587 /* WHVideoHudClipBottomView.swift */; };
		035127922DD473C000B880F8 /* WHAIClearToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 035127912DD473C000B880F8 /* WHAIClearToolsView.swift */; };
		035CF4432DA4EE420017EAF7 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 035CF4402DA4EE420017EAF7 /* NotificationService.swift */; };
		0361786C2E13AD2B006A751A /* WHMediaManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0361786B2E13AD2B006A751A /* WHMediaManager.swift */; };
		036178702E13AEFA006A751A /* WHTimeLineConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0361786F2E13AEFA006A751A /* WHTimeLineConfig.swift */; };
		03626BB82D812647003A6712 /* WHMineButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03626BB72D812647003A6712 /* WHMineButtonView.swift */; };
		03626BC42D8195CD003A6712 /* reward_star.json in Resources */ = {isa = PBXBuildFile; fileRef = 03626BC22D8195CD003A6712 /* reward_star.json */; };
		03626BC52D8195CD003A6712 /* img_0.png in Resources */ = {isa = PBXBuildFile; fileRef = 03626BC02D8195CD003A6712 /* img_0.png */; };
		0381B11C2E0F9AC40034FBAD /* WHOneSentenceInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0381B11B2E0F9AC40034FBAD /* WHOneSentenceInputView.swift */; };
		03995B302DA4BF7E001C856D /* NotificationService.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 03995B292DA4BF7E001C856D /* NotificationService.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		039CCD8B2DE8358F0079A5F8 /* WHAILivePreViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 039CCD8A2DE8358F0079A5F8 /* WHAILivePreViewController.swift */; };
		039CCD8D2DE8537A0079A5F8 /* WHAILivePreCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 039CCD8C2DE8537A0079A5F8 /* WHAILivePreCollectionCell.swift */; };
		03BF23332E28C5410065FAE8 /* WHProgressLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03BF23322E28C5410065FAE8 /* WHProgressLoadingView.swift */; };
		03C4AEB82D8BF2ED00AD1E71 /* WHRouterDelegateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03C4AEB72D8BF2ED00AD1E71 /* WHRouterDelegateManager.swift */; };
		03C873E12E0E6DB1006253B3 /* WHOneSentenceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03C873E02E0E6DB1006253B3 /* WHOneSentenceViewController.swift */; };
		03C873E42E0E7329006253B3 /* WHRouterOneSentenceResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03C873E32E0E7329006253B3 /* WHRouterOneSentenceResponder.swift */; };
		03CCA70A2E78072000D5925F /* WHSOPDetailsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03CCA7072E78072000D5925F /* WHSOPDetailsViewController.swift */; };
		03CCA70D2E780A2D00D5925F /* WHSOPDetailsButtonCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03CCA70B2E780A2D00D5925F /* WHSOPDetailsButtonCell.swift */; };
		03CCA70F2E78162C00D5925F /* WHSOPDetailsImgInfoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03CCA70E2E78162C00D5925F /* WHSOPDetailsImgInfoView.swift */; };
		03CCA7132E783DDE00D5925F /* sop_details_again.json in Resources */ = {isa = PBXBuildFile; fileRef = 03CCA7122E783DDE00D5925F /* sop_details_again.json */; };
		03D7A5AB2E1CBAD100A78658 /* WHVideoHudClipTimeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03D7A5AA2E1CBAD100A78658 /* WHVideoHudClipTimeCell.swift */; };
		03D7A5B42E1CF10B00A78658 /* WHTimeLineClipView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03D7A5AE2E1CF10B00A78658 /* WHTimeLineClipView.swift */; };
		03D7A5B62E1CF10B00A78658 /* WHTimeLineProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03D7A5B02E1CF10B00A78658 /* WHTimeLineProgressView.swift */; };
		03D7A5B82E1CF10B00A78658 /* WHTimeLineTrackView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03D7A5B22E1CF10B00A78658 /* WHTimeLineTrackView.swift */; };
		03E8B9D22D7D8A8F0097557C /* WHRewardsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9D12D7D8A8F0097557C /* WHRewardsViewController.swift */; };
		03E8B9D42D7D90D00097557C /* WHRewardsInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9D32D7D90D00097557C /* WHRewardsInfoModel.swift */; };
		03E8B9D62D7D96AB0097557C /* WHRewardsMembershipCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9D52D7D96AB0097557C /* WHRewardsMembershipCell.swift */; };
		03E8B9D82D7D96E30097557C /* WHRewardsCheckInCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9D72D7D96E30097557C /* WHRewardsCheckInCell.swift */; };
		03E8B9DA2D7D97330097557C /* WHRewardsInviteBoostCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9D92D7D97330097557C /* WHRewardsInviteBoostCell.swift */; };
		03E8B9DC2D7D989E0097557C /* WHRewardsSectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03E8B9DB2D7D989E0097557C /* WHRewardsSectionHeaderView.swift */; };
		16176D332D881F14004D1C3A /* mtseclicense.pixel in Resources */ = {isa = PBXBuildFile; fileRef = 16176D322D881F14004D1C3A /* mtseclicense.pixel */; };
		167EACBA2C3690D700F175B1 /* WHDisclaimersCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 167EACB92C3690D700F175B1 /* WHDisclaimersCell.swift */; };
		167EACBC2C37E38F00F175B1 /* WHDoubleSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 167EACBB2C37E38F00F175B1 /* WHDoubleSliderView.swift */; };
		200BBCC42AFB7B3600FB89F2 /* WHLaunchAPNSTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 200BBCC32AFB7B3600FB89F2 /* WHLaunchAPNSTask.swift */; };
		200BBCC82AFE19C700FB89F2 /* WHCIADebugViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 200BBCC72AFE19C700FB89F2 /* WHCIADebugViewController.swift */; };
		200C7F332ADD28DF00E4DCCE /* WHSettingAboutUsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 200C7F322ADD28DF00E4DCCE /* WHSettingAboutUsViewController.swift */; };
		2027DB0A2BC3D9BE00625AD4 /* WHImageToImageInputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2027DB092BC3D9BE00625AD4 /* WHImageToImageInputCell.swift */; };
		2027DB0D2BC4F06300625AD4 /* WHRouterImgToImgResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2027DB0C2BC4F06300625AD4 /* WHRouterImgToImgResponder.swift */; };
		2027DB0F2BC5556800625AD4 /* WHImgToImgZoomCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2027DB0E2BC5556800625AD4 /* WHImgToImgZoomCell.swift */; };
		2044EB352B87342F00324E1D /* WHAIClearPreViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2044EB342B87342F00324E1D /* WHAIClearPreViewController.swift */; };
		2044EB382B87384800324E1D /* WHRouterAIClearResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2044EB372B87384800324E1D /* WHRouterAIClearResponder.swift */; };
		206AE7642AB5612600FA8483 /* WHLaunchCIATask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 206AE7632AB5612600FA8483 /* WHLaunchCIATask.swift */; };
		206AE7662AB5B3E700FA8483 /* WHLaunchSSOShareTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 206AE7652AB5B3E700FA8483 /* WHLaunchSSOShareTask.swift */; };
		206DFF332AFF3CAB001593DE /* WHAuthorityManageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 206DFF322AFF3CAB001593DE /* WHAuthorityManageViewController.swift */; };
		206DFF352AFF5FCA001593DE /* WHAuthorityManageTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 206DFF342AFF5FCA001593DE /* WHAuthorityManageTableViewCell.swift */; };
		2070BA222B283BFF00DE80E0 /* WHInpaindingRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2070BA212B283BFF00DE80E0 /* WHInpaindingRequest.swift */; };
		2070BA252B283D5A00DE80E0 /* WHInpaindingConfigModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2070BA242B283D5A00DE80E0 /* WHInpaindingConfigModel.swift */; };
		2070BA272B284DF000DE80E0 /* WHInpaindingDoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2070BA262B284DF000DE80E0 /* WHInpaindingDoModel.swift */; };
		2070BA312B2950D400DE80E0 /* WHRouterInpaintingResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2070BA302B2950D400DE80E0 /* WHRouterInpaintingResponder.swift */; };
		2083ED042B35A0B100FA42D1 /* WHMineVisitorModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2083ED032B35A0B100FA42D1 /* WHMineVisitorModel.swift */; };
		2093CC772BF25EA9003E7421 /* WHLaunchEditorTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2093CC762BF25EA9003E7421 /* WHLaunchEditorTask.swift */; };
		2096DC9C2AF243C600D5C80A /* tcmpp-ios-configurations-whee.json in Resources */ = {isa = PBXBuildFile; fileRef = 2096DC9B2AF243C600D5C80A /* tcmpp-ios-configurations-whee.json */; };
		20AF4EF32C0761570087FFD9 /* WHWebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20AF4EF22C0761570087FFD9 /* WHWebViewController.swift */; };
		20AF59FB2C086B480087FFD9 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 20AF59FA2C086B480087FFD9 /* PrivacyInfo.xcprivacy */; };
		20B197C02AE7E3DE003100DC /* WHQRCodeMiniController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20B197BF2AE7E3DE003100DC /* WHQRCodeMiniController.swift */; };
		20BBE0AB2B4D4B4D0053F2A9 /* WHAIVideoDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20BBE0AA2B4D4B4D0053F2A9 /* WHAIVideoDetailViewController.swift */; };
		20BBE0AD2B4D71830053F2A9 /* WHAIVideoDetailCreationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20BBE0AC2B4D71830053F2A9 /* WHAIVideoDetailCreationCell.swift */; };
		20BBE0AF2B4E8E860053F2A9 /* WHDetailViewCommonMoreView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20BBE0AE2B4E8E860053F2A9 /* WHDetailViewCommonMoreView.swift */; };
		20D1C16E2BB2C56C004B3DEE /* WHImageToImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20D1C16D2BB2C56C004B3DEE /* WHImageToImageViewController.swift */; };
		20D1C1712BB2E3DC004B3DEE /* WHImgToImgSelectImgCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20D1C1702BB2E3DC004B3DEE /* WHImgToImgSelectImgCell.swift */; };
		20D1C1762BB66E73004B3DEE /* WHImageToImageRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20D1C1752BB66E73004B3DEE /* WHImageToImageRequest.swift */; };
		20DA02CF2B49359A004C5898 /* WHHomeHotCreationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20DA02CE2B49359A004C5898 /* WHHomeHotCreationCell.swift */; };
		20DA02D12B493DFF004C5898 /* WHHomeCellManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20DA02D02B493DFF004C5898 /* WHHomeCellManager.swift */; };
		20DA02D32B493F23004C5898 /* WHHomeAiCreationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20DA02D22B493F23004C5898 /* WHHomeAiCreationCell.swift */; };
		20DEE5B42BA2FD7600B89A7B /* LaunchScreen_cn.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 20DEE5B22BA2FD7600B89A7B /* LaunchScreen_cn.storyboard */; };
		20DEE5B72BA2FD8200B89A7B /* LaunchScreen_en.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 20DEE5B52BA2FD8200B89A7B /* LaunchScreen_en.storyboard */; };
		20F607D72B3BFA3B00C97663 /* WHHomeConfigCacheModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20F607D62B3BFA3B00C97663 /* WHHomeConfigCacheModel.swift */; };
		20FD82B72B273264008E68AA /* WHInpaindingEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20FD82B62B273264008E68AA /* WHInpaindingEditView.swift */; };
		20FE35C52AD0251900789CF6 /* WHHomeConfigModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20FE35C42AD0251900789CF6 /* WHHomeConfigModel.swift */; };
		20FE35C82AD0252700789CF6 /* WHHomeRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20FE35C72AD0252700789CF6 /* WHHomeRequest.swift */; };
		230593B32E7910A400B424CE /* WHLaunchBusinessCommonTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 230593B22E7910A400B424CE /* WHLaunchBusinessCommonTask.swift */; };
		2334F4422E0A51C7000C4F50 /* WHRouterVideoHudResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2334F43F2E0A51C7000C4F50 /* WHRouterVideoHudResponder.swift */; };
		2366C66C2C8EA2F900FDBF67 /* WHRouterAIUpScalerResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2366C66B2C8EA2F900FDBF67 /* WHRouterAIUpScalerResponder.swift */; };
		2366C66E2C8EA33F00FDBF67 /* WHAIUpScalerPreViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2366C66D2C8EA33F00FDBF67 /* WHAIUpScalerPreViewController.swift */; };
		2366C6702C8EDC6000FDBF67 /* WHAIUpScalerRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2366C66F2C8EDC6000FDBF67 /* WHAIUpScalerRequest.swift */; };
		2366C6722C8EEE0500FDBF67 /* WHAIUpScalerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2366C6712C8EEE0500FDBF67 /* WHAIUpScalerViewController.swift */; };
		2366D1BA2CBA668A00FDBF67 /* WHShareInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2366D1B92CBA668A00FDBF67 /* WHShareInfoModel.swift */; };
		2386F4F52E13BFA60055CF97 /* WHVideoHudPreViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2386F4F42E13BFA60055CF97 /* WHVideoHudPreViewController.swift */; };
		2386F4F72E13D6C10055CF97 /* WHAIUpScalerUploadResource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2386F4F62E13D6C10055CF97 /* WHAIUpScalerUploadResource.swift */; };
		23A47DB32E0D574200D774DB /* WHVideoHudCreateViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23A47DB22E0D574200D774DB /* WHVideoHudCreateViewController.swift */; };
		23A47DB52E0D7B2B00D774DB /* WHVideoHudStyleSelectView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23A47DB42E0D7B2B00D774DB /* WHVideoHudStyleSelectView.swift */; };
		23A47DB82E0E887500D774DB /* WHVideoHudRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23A47DB72E0E887500D774DB /* WHVideoHudRequest.swift */; };
		23A47DBA2E0EAA0B00D774DB /* WHVideoHudVideoPlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23A47DB92E0EAA0B00D774DB /* WHVideoHudVideoPlayView.swift */; };
		23B3217C2DFAA10400BAF701 /* WHCommonToastAlertResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23B3217B2DFAA10400BAF701 /* WHCommonToastAlertResponder.swift */; };
		241005FA2E72D8AB009D7682 /* WHHomeFlowModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241005F92E72D8AB009D7682 /* WHHomeFlowModel.swift */; };
		241006012E73ECFE009D7682 /* WHCodableModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241006002E73ECFE009D7682 /* WHCodableModel.swift */; };
		241006032E758422009D7682 /* WHHomeFlowFilterButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241006022E758422009D7682 /* WHHomeFlowFilterButton.swift */; };
		241006052E77AD58009D7682 /* WHHomeFlowPopMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241006042E77AD58009D7682 /* WHHomeFlowPopMenu.swift */; };
		2415E2752E5706A800E60836 /* UIViewController+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2415E2742E5706A800E60836 /* UIViewController+Extensions.swift */; };
		2415E2782E5742B500E60836 /* WHSmartCutoutReport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2415E2772E5742B500E60836 /* WHSmartCutoutReport.swift */; };
		2415E27B2E58174500E60836 /* WHImageUploader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2415E27A2E58174500E60836 /* WHImageUploader.swift */; };
		2415E27D2E5C09C700E60836 /* CircularCursorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2415E27C2E5C09C700E60836 /* CircularCursorView.swift */; };
		241816882E4C7BC00075B516 /* WHSmartCutoutEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241816862E4C7BC00075B516 /* WHSmartCutoutEngine.swift */; };
		2418168B2E4C9A320075B516 /* WHSmartCutoutRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2418168A2E4C9A320075B516 /* WHSmartCutoutRequest.swift */; };
		2418168D2E4CB2030075B516 /* WHImageKitCanvasView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2418168C2E4CB2030075B516 /* WHImageKitCanvasView.swift */; };
		24295DB82E7AABE500365D09 /* WHHomeFullScreenResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24295DB72E7AABE500365D09 /* WHHomeFullScreenResponder.swift */; };
		244F91152E4DBA4900A6AA4B /* RxWHRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 244F91142E4DBA4900A6AA4B /* RxWHRequest.swift */; };
		244F91182E501FAB00A6AA4B /* UIColors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 244F91172E501FAB00A6AA4B /* UIColors.swift */; };
		244F911C2E50438E00A6AA4B /* WHSmartCutoutRoundButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 244F911B2E50438E00A6AA4B /* WHSmartCutoutRoundButton.swift */; };
		244F911E2E509AFB00A6AA4B /* SmartCutoutSmearHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 244F911D2E509AFB00A6AA4B /* SmartCutoutSmearHistory.swift */; };
		244F91202E52F68F00A6AA4B /* WHSmartCutoutEngine+MTIK.swift in Sources */ = {isa = PBXBuildFile; fileRef = 244F911F2E52F68F00A6AA4B /* WHSmartCutoutEngine+MTIK.swift */; };
		2470BACB2E702F1000531815 /* WHHomeFlowCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2470BACA2E702F1000531815 /* WHHomeFlowCollectionViewCell.swift */; };
		2470BAD12E70315600531815 /* WHHomeFlowViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2470BAD02E70315600531815 /* WHHomeFlowViewController.swift */; };
		2470BAD52E7033CB00531815 /* WHFlowContentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2470BAD42E7033CB00531815 /* WHFlowContentViewController.swift */; };
		248C9A892E5D7630000CCE49 /* WHSmartCutoutRequestObservable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 248C9A882E5D7630000CCE49 /* WHSmartCutoutRequestObservable.swift */; };
		248C9A8B2E5DAF86000CCE49 /* UIImage+Alpha.swift in Sources */ = {isa = PBXBuildFile; fileRef = 248C9A8A2E5DAF86000CCE49 /* UIImage+Alpha.swift */; };
		2498C01A2E79344C007FC3EF /* WHBlurImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2498C0192E79344C007FC3EF /* WHBlurImageView.swift */; };
		2498C01E2E7934F8007FC3EF /* WHHomeFullScreenFlowDataViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2498C01C2E7934F8007FC3EF /* WHHomeFullScreenFlowDataViewModel.swift */; };
		2498C01F2E7934F8007FC3EF /* WHHomeFullScreenFlowTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2498C01D2E7934F8007FC3EF /* WHHomeFullScreenFlowTableViewCell.swift */; };
		2498C0202E7934F8007FC3EF /* WHHomeFullScreenViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2498C01B2E7934F8007FC3EF /* WHHomeFullScreenViewController.swift */; };
		2498C0242E795CB6007FC3EF /* UIImageView+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2498C0232E795CB6007FC3EF /* UIImageView+Extension.swift */; };
		24D9D9C22E46FC950066BBC1 /* WHSmartCutoutRouterResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9C12E46FC950066BBC1 /* WHSmartCutoutRouterResponder.swift */; };
		24D9D9C62E472BF30066BBC1 /* WHSmartCutoutViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9C52E472BF30066BBC1 /* WHSmartCutoutViewController.swift */; };
		24D9D9C82E4734FD0066BBC1 /* WHSmartCutoutBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9C72E4734FD0066BBC1 /* WHSmartCutoutBottomView.swift */; };
		24D9D9CA2E4737DA0066BBC1 /* WHSliderDetailCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9C92E4737DA0066BBC1 /* WHSliderDetailCard.swift */; };
		24D9D9D02E499CA10066BBC1 /* WHSmartCutoutSlider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9CF2E499CA10066BBC1 /* WHSmartCutoutSlider.swift */; };
		24D9D9D62E49D4060066BBC1 /* WHSmartCutoutBottomToolBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9D52E49D4060066BBC1 /* WHSmartCutoutBottomToolBar.swift */; };
		24D9D9DA2E4B1F6F0066BBC1 /* WHSmartCutoutNavigationBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9D92E4B1F6F0066BBC1 /* WHSmartCutoutNavigationBar.swift */; };
		24D9D9E42E4B42F10066BBC1 /* WHSmartCutoutSaveResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9E32E4B42F10066BBC1 /* WHSmartCutoutSaveResultViewController.swift */; };
		24D9D9E62E4B44800066BBC1 /* WHSmartCutoutSavedToolBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24D9D9E52E4B44800066BBC1 /* WHSmartCutoutSavedToolBar.swift */; };
		24F8C4F42E71A18D001D7156 /* WHHomeVisualEffectsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24F8C4F32E71A18D001D7156 /* WHHomeVisualEffectsManager.swift */; };
		24F8C4F72E7272F0001D7156 /* UIApplication+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24F8C4F62E7272F0001D7156 /* UIApplication+Extension.swift */; };
		24F8C4FB2E72752F001D7156 /* UIDevice+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24F8C4FA2E72752F001D7156 /* UIDevice+Extension.swift */; };
		24F8C5012E72C9A8001D7156 /* WHAccountManager+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24F8C5002E72C9A8001D7156 /* WHAccountManager+Extension.swift */; };
		951125A52AB83EA6001B834A /* WHHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 951125A42AB83EA6001B834A /* WHHomeViewController.swift */; };
		951125A72AB83F36001B834A /* WHInspirationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 951125A62AB83F36001B834A /* WHInspirationViewController.swift */; };
		9520215D2CA152D70011B379 /* WHAIUSImageClarityView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9520215C2CA152D70011B379 /* WHAIUSImageClarityView.swift */; };
		952021612CA290220011B379 /* WHAIUSValueSettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 952021602CA290220011B379 /* WHAIUSValueSettingView.swift */; };
		953681FC2BC3D1D800E3943E /* WHControlCellSubviews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953681FB2BC3D1D800E3943E /* WHControlCellSubviews.swift */; };
		953681FE2BC412CD00E3943E /* WHReferTypeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953681FD2BC412CD00E3943E /* WHReferTypeViewController.swift */; };
		953682012BC9370600E3943E /* WHRouterHistoryResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682002BC9370600E3943E /* WHRouterHistoryResponder.swift */; };
		953682082BCF770D00E3943E /* WHToImageDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682072BCF770D00E3943E /* WHToImageDetailViewController.swift */; };
		9536820A2BCFF0EF00E3943E /* WHDetailBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682092BCFF0EF00E3943E /* WHDetailBottomView.swift */; };
		9536820E2BCFFA8A00E3943E /* WHToImageDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9536820D2BCFFA8A00E3943E /* WHToImageDetailView.swift */; };
		953682122BCFFDD700E3943E /* WHDetailUserInfoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682112BCFFDD700E3943E /* WHDetailUserInfoCell.swift */; };
		953682142BCFFDFD00E3943E /* WHDetailOtherCreateInfoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682132BCFFDFD00E3943E /* WHDetailOtherCreateInfoCell.swift */; };
		953682162BCFFE0A00E3943E /* WHDetailMyCreateInfoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682152BCFFE0A00E3943E /* WHDetailMyCreateInfoCell.swift */; };
		953682182BCFFEED00E3943E /* WHDetailImagesCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 953682172BCFFEED00E3943E /* WHDetailImagesCell.swift */; };
		954A4E1C2BA05D790037C440 /* WHCommonUseAlertModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E1B2BA05D790037C440 /* WHCommonUseAlertModel.swift */; };
		954A4E202BA1759D0037C440 /* WHCommonUseAlertManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E1F2BA1759D0037C440 /* WHCommonUseAlertManager.swift */; };
		954A4E222BA178980037C440 /* WHCommonUseAlertRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E212BA178970037C440 /* WHCommonUseAlertRequest.swift */; };
		954A4E262BA307550037C440 /* WHHomeToolCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E252BA307550037C440 /* WHHomeToolCell.swift */; };
		954A4E462BAC11680037C440 /* WHTextToImageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E362BAC11680037C440 /* WHTextToImageModel.swift */; };
		954A4E472BAC11680037C440 /* WHCreateImageControlCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E382BAC11680037C440 /* WHCreateImageControlCell.swift */; };
		954A4E482BAC11680037C440 /* WHTextToImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E392BAC11680037C440 /* WHTextToImageView.swift */; };
		954A4E492BAC11680037C440 /* WHCreateImageCountCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3A2BAC11680037C440 /* WHCreateImageCountCell.swift */; };
		954A4E4A2BAC11680037C440 /* WHTextToImageInputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3B2BAC11680037C440 /* WHTextToImageInputCell.swift */; };
		954A4E4B2BAC11680037C440 /* WHCreateImageHigherCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3C2BAC11680037C440 /* WHCreateImageHigherCell.swift */; };
		954A4E4C2BAC11680037C440 /* WHVisionModelCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3D2BAC11680037C440 /* WHVisionModelCell.swift */; };
		954A4E4D2BAC11680037C440 /* WHStyleStrengthView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3E2BAC11680037C440 /* WHStyleStrengthView.swift */; };
		954A4E4E2BAC11680037C440 /* WHCommonUploadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E3F2BAC11680037C440 /* WHCommonUploadView.swift */; };
		954A4E4F2BAC11680037C440 /* WHCreateImageStyleCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E402BAC11680037C440 /* WHCreateImageStyleCell.swift */; };
		954A4E502BAC11680037C440 /* WHCreateImageSizeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E412BAC11680037C440 /* WHCreateImageSizeCell.swift */; };
		954A4E512BAC11680037C440 /* WHTextToImageRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E432BAC11680037C440 /* WHTextToImageRequest.swift */; };
		954A4E522BAC11680037C440 /* WHTextToImageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954A4E452BAC11680037C440 /* WHTextToImageViewController.swift */; };
		954FCD3C2AF25FBB00F08C50 /* WHUserAgreeAbroadView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954FCD3B2AF25FBB00F08C50 /* WHUserAgreeAbroadView.swift */; };
		955064B62AD41C36005AA274 /* WHLaunchConfigTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 955064B52AD41C36005AA274 /* WHLaunchConfigTask.swift */; };
		955064B92AD52A55005AA274 /* WHAppConfigManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 955064B82AD52A55005AA274 /* WHAppConfigManager.swift */; };
		955064C02ADCE6B0005AA274 /* WHMineRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 955064BF2ADCE6B0005AA274 /* WHMineRequest.swift */; };
		955064C22ADE7F9A005AA274 /* Loading_Logo.json in Resources */ = {isa = PBXBuildFile; fileRef = 955064C12ADE7F9A005AA274 /* Loading_Logo.json */; };
		955064C52ADFBDF7005AA274 /* WHCommonUserInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 955064C42ADFBDF7005AA274 /* WHCommonUserInfo.swift */; };
		955A585E2B0C4CAE0068054F /* radius.json in Resources */ = {isa = PBXBuildFile; fileRef = 955A585D2B0C4CAE0068054F /* radius.json */; };
		95756B472B6CDFA400127A82 /* WHComonInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95756B462B6CDFA400127A82 /* WHComonInputView.swift */; };
		957577732B871D4100127A82 /* WHAIClearViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957577722B871D4100127A82 /* WHAIClearViewController.swift */; };
		957577772B888F9000127A82 /* WHAIClearRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957577762B888F9000127A82 /* WHAIClearRequest.swift */; };
		957577792B8DB57B00127A82 /* WHAIClearModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957577782B8DB57B00127A82 /* WHAIClearModel.swift */; };
		9575777C2B9089B800127A82 /* aiClear.webp in Resources */ = {isa = PBXBuildFile; fileRef = 9575777B2B9089B800127A82 /* aiClear.webp */; };
		95887B6F2B148A4600B0ABE7 /* theme_wheeApp.json in Resources */ = {isa = PBXBuildFile; fileRef = 95887B6E2B148A4600B0ABE7 /* theme_wheeApp.json */; };
		959009C02B46CD8D00F1DC7A /* WHAICreatVideoZoomCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009BF2B46CD8D00F1DC7A /* WHAICreatVideoZoomCell.swift */; };
		959009C22B47D6A200F1DC7A /* WHMineWorksViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009C12B47D6A200F1DC7A /* WHMineWorksViewController.swift */; };
		959009CF2B4E44DB00F1DC7A /* WHVideoDetailPlayManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009CE2B4E44DB00F1DC7A /* WHVideoDetailPlayManager.swift */; };
		959009D12B4E46B400F1DC7A /* Fadable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009D02B4E46B400F1DC7A /* Fadable.swift */; };
		959009D32B4E473C00F1DC7A /* WHVideoPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009D22B4E473C00F1DC7A /* WHVideoPlayerView.swift */; };
		959009D52B4E6C1E00F1DC7A /* WHAIVideoDetailVideoCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959009D42B4E6C1E00F1DC7A /* WHAIVideoDetailVideoCell.swift */; };
		959009D72B4E732600F1DC7A /* video_loading.json in Resources */ = {isa = PBXBuildFile; fileRef = 959009D62B4E732600F1DC7A /* video_loading.json */; };
		959740762B396B470027D29B /* WHAICreateVideoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740752B396B470027D29B /* WHAICreateVideoViewController.swift */; };
		9597407A2B397A6D0027D29B /* WHAICreatVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740792B397A6D0027D29B /* WHAICreatVideoView.swift */; };
		9597407C2B398D6E0027D29B /* WHAICreateVideoInputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9597407B2B398D6E0027D29B /* WHAICreateVideoInputCell.swift */; };
		9597407E2B398DB90027D29B /* WHAICreateVideoSizeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9597407D2B398DB90027D29B /* WHAICreateVideoSizeCell.swift */; };
		959740802B398E150027D29B /* WHAICreateVideoCameCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9597407F2B398E150027D29B /* WHAICreateVideoCameCell.swift */; };
		959740822B398E470027D29B /* WHAICreateVideoUploadCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740812B398E470027D29B /* WHAICreateVideoUploadCell.swift */; };
		959740862B3BC9870027D29B /* WHAICreateVideoTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740852B3BC9870027D29B /* WHAICreateVideoTabView.swift */; };
		9597408A2B3C10FD0027D29B /* WHAICreateVideoTipsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740892B3C10FD0027D29B /* WHAICreateVideoTipsView.swift */; };
		9597408D2B3D19EB0027D29B /* WHAICreateVideoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9597408C2B3D19EB0027D29B /* WHAICreateVideoModel.swift */; };
		959740AC2B453E4B0027D29B /* WHAICreateVideoRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959740AB2B453E4B0027D29B /* WHAICreateVideoRequest.swift */; };
		95A389592B62333100332D7B /* WHAICreateVideoFormulaCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A389582B62333100332D7B /* WHAICreateVideoFormulaCell.swift */; };
		95AFED502BAD96C900297588 /* WHStyleFlowViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95AFED4F2BAD96C900297588 /* WHStyleFlowViewController.swift */; };
		95AFED522BB1582500297588 /* WHStyleTabViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95AFED512BB1582500297588 /* WHStyleTabViewController.swift */; };
		95BC83B12B5E5E0100436AF0 /* input_lenovo.json in Resources */ = {isa = PBXBuildFile; fileRef = 95BC83B02B5E5E0100436AF0 /* input_lenovo.json */; };
		95BCB1CB2AA9A74A00A76B9C /* WHLaunchVipSdkTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95BCB1CA2AA9A74A00A76B9C /* WHLaunchVipSdkTask.swift */; };
		95BCB1D12AA9C46900A76B9C /* WHTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95BCB1D02AA9C46900A76B9C /* WHTabBarController.swift */; };
		95BF6B022AB5907700B13E2B /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 95BF6B042AB5907700B13E2B /* Localizable.strings */; };
		95BF6B072AB5917B00B13E2B /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 95BF6B092AB5917B00B13E2B /* InfoPlist.strings */; };
		95BF6B172AB8324500B13E2B /* WHLanguageSetViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95BF6B162AB8324500B13E2B /* WHLanguageSetViewController.swift */; };
		95C290322B55246E00A66E16 /* WHAICreateVideoCameTextCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C290312B55246E00A66E16 /* WHAICreateVideoCameTextCell.swift */; };
		95C9E0342AF11BC100D8E24A /* feed_status.json in Resources */ = {isa = PBXBuildFile; fileRef = 95C9E0332AF11BC100D8E24A /* feed_status.json */; };
		95CACD292B21C04200354A81 /* WHInpaindingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CACD282B21C04200354A81 /* WHInpaindingManager.swift */; };
		95CACD2B2B21C49A00354A81 /* WHInpaindingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95CACD2A2B21C49A00354A81 /* WHInpaindingViewController.swift */; };
		95D3DBAD2AFB387000DD3FEC /* WHAgreementAlertOptionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D3DBAC2AFB387000DD3FEC /* WHAgreementAlertOptionCell.swift */; };
		95D3DBB02AFDD9E800DD3FEC /* WHUpgradeAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D3DBAF2AFDD9E800DD3FEC /* WHUpgradeAlertView.swift */; };
		95D3DBB42AFF282600DD3FEC /* WHUpgradeInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D3DBB32AFF282600DD3FEC /* WHUpgradeInfo.swift */; };
		95D902752C92C519004DD5B2 /* WHAIExpandModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D902742C92C519004DD5B2 /* WHAIExpandModel.swift */; };
		95D919302C92F260004DD5B2 /* WHRouterAIExpandResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D9192F2C92F260004DD5B2 /* WHRouterAIExpandResponder.swift */; };
		95D919322C9A8203004DD5B2 /* WHAIExpandRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D919312C9A8203004DD5B2 /* WHAIExpandRequest.swift */; };
		95D9193A2C9D4C93004DD5B2 /* WHAIUpScalerModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D919392C9D4C93004DD5B2 /* WHAIUpScalerModel.swift */; };
		95D9F7172C91A15A004DD5B2 /* WHAIExpandViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D9F7162C91A15A004DD5B2 /* WHAIExpandViewController.swift */; };
		95D9F7192C91A25C004DD5B2 /* WHAICommonViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95D9F7182C91A25C004DD5B2 /* WHAICommonViewController.swift */; };
		95DA81B12B26A9F000E8D8C0 /* WHInpaindingBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DA81B02B26A9F000E8D8C0 /* WHInpaindingBarView.swift */; };
		95DC0B742ABB0CDA0026E3D3 /* WHFlowManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B6F2ABB0CDA0026E3D3 /* WHFlowManager.swift */; };
		95DC0B762ABB0CDA0026E3D3 /* WHFlowAbstract.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B712ABB0CDA0026E3D3 /* WHFlowAbstract.swift */; };
		95DC0B782ABB0CEB0026E3D3 /* WHFlowRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B772ABB0CEB0026E3D3 /* WHFlowRequest.swift */; };
		95DC0B7D2ABB0CF50026E3D3 /* WHFlowBaseCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B792ABB0CF50026E3D3 /* WHFlowBaseCardView.swift */; };
		95DC0B7E2ABB0CF50026E3D3 /* WHFlowBaseCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B7A2ABB0CF50026E3D3 /* WHFlowBaseCollectionViewCell.swift */; };
		95DC0B7F2ABB0CF50026E3D3 /* WHFlowBaseDisplayEntity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B7B2ABB0CF50026E3D3 /* WHFlowBaseDisplayEntity.swift */; };
		95DC0B802ABB0CF50026E3D3 /* WHFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B7C2ABB0CF50026E3D3 /* WHFlowLayout.swift */; };
		95DC0B822ABB0D040026E3D3 /* WHFlowViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC0B812ABB0D040026E3D3 /* WHFlowViewController.swift */; };
		95DC20672AC3DA830026E3D3 /* WHSearchViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC20662AC3DA830026E3D3 /* WHSearchViewController.swift */; };
		95DC20692AC3FD8B0026E3D3 /* WHCustomSearchBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC20682AC3FD8B0026E3D3 /* WHCustomSearchBarView.swift */; };
		95DC20762AC41C060026E3D3 /* MTSuggestCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC20752AC41C060026E3D3 /* MTSuggestCollectionView.swift */; };
		95DC207A2AC430500026E3D3 /* WHSearchSuggestionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC20792AC430500026E3D3 /* WHSearchSuggestionCell.swift */; };
		95DC207E2AC51C050026E3D3 /* WHSearchDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC207D2AC51C050026E3D3 /* WHSearchDetailViewController.swift */; };
		95DC20A02ACE5CEC0026E3D3 /* WHSearchHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC209F2ACE5CEC0026E3D3 /* WHSearchHistoryCell.swift */; };
		95DC20A22ACE625D0026E3D3 /* WHSearchHistroyTitleCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC20A12ACE625D0026E3D3 /* WHSearchHistroyTitleCell.swift */; };
		95DC2B0B2AD105E40026E3D3 /* WHUserAgreementAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC2B0A2AD105E40026E3D3 /* WHUserAgreementAlertView.swift */; };
		95DC2B0F2AD14B6A0026E3D3 /* WHLaunchAlertDispathTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DC2B0E2AD14B6A0026E3D3 /* WHLaunchAlertDispathTask.swift */; };
		95ED04B42CA2C8F90056C11A /* WHAIUSDemoTypeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED04B32CA2C8F90056C11A /* WHAIUSDemoTypeView.swift */; };
		95ED04B62CA2CA790056C11A /* WHAIUSAdaptStyleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED04B52CA2CA790056C11A /* WHAIUSAdaptStyleView.swift */; };
		95ED10122CA3E8D30056C11A /* WHAIUpScalerDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED10112CA3E8D30056C11A /* WHAIUpScalerDetailViewController.swift */; };
		95ED10142CA3FF190056C11A /* WHAIUSDetailPicTypeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED10132CA3FF190056C11A /* WHAIUSDetailPicTypeView.swift */; };
		95ED10162CA425570056C11A /* WHAIUSCanvasView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED10152CA425570056C11A /* WHAIUSCanvasView.swift */; };
		95ED10182CA505240056C11A /* WHAIUSIKManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95ED10172CA505240056C11A /* WHAIUSIKManager.swift */; };
		9F3E6C352D7AE03600ACFC0C /* WHAILiveDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F3E6C342D7AE01F00ACFC0C /* WHAILiveDetailViewController.swift */; };
		9F3E6C372D7E8BD700ACFC0C /* WHDownLoadManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F3E6C362D7E8BBA00ACFC0C /* WHDownLoadManager.swift */; };
		9F3E6C3D2D800EC200ACFC0C /* WHInviteCodeAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F3E6C3C2D800EC200ACFC0C /* WHInviteCodeAlert.swift */; };
		9F3E6C3F2D80314100ACFC0C /* WHInvitedFriendAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F3E6C3E2D80312F00ACFC0C /* WHInvitedFriendAlert.swift */; };
		9F56039C2D8991FB00D56C8C /* WHAILiveTypeCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F56039A2D8991FB00D56C8C /* WHAILiveTypeCollectionViewCell.swift */; };
		9F56039E2D89921700D56C8C /* WHAILiveLoadTypeAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F56039D2D8991FF00D56C8C /* WHAILiveLoadTypeAlert.swift */; };
		9FB593052D782BB900CFBA6E /* WHAILiveViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FB593022D782BB900CFBA6E /* WHAILiveViewController.swift */; };
		9FB593062D782BB900CFBA6E /* WHAILiveRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FB593002D782BB900CFBA6E /* WHAILiveRequest.swift */; };
		9FB593072D782BB900CFBA6E /* WHAILiveModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FB592FE2D782BB900CFBA6E /* WHAILiveModel.swift */; };
		9FB593082D782BB900CFBA6E /* WHRouterAILiveResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FB592FC2D782BB900CFBA6E /* WHRouterAILiveResponder.swift */; };
		9FDDE7A22DB6276F00279D3A /* WHPopupAlertModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FDDE7A12DB6276C00279D3A /* WHPopupAlertModel.swift */; };
		9FE748482DA4D06600764130 /* WHRouterAILiveDetailResponder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FE748472DA4D02500764130 /* WHRouterAILiveDetailResponder.swift */; };
		9FFCD7862CBD38EC00074C84 /* WHToImageDtailFailedTips.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FFCD7852CBD38EC00074C84 /* WHToImageDtailFailedTips.swift */; };
		ACD7036B4AF7B9CE94C82333 /* Pods_NotificationService.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 96AC3BBD82A11BCC687B8990 /* Pods_NotificationService.framework */; };
		ECBB5463606334573658614D /* Pods_MeituWhee.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9927A0DD64277EAFDD23AB11 /* Pods_MeituWhee.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		03995B2E2DA4BF7E001C856D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00389ECF2AA5A59600E6089A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 03995B282DA4BF7E001C856D;
			remoteInfo = NotificationService;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		03995B312DA4BF7E001C856D /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				03995B302DA4BF7E001C856D /* NotificationService.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		002B08DE2C1BD93C007AFDB3 /* WHPublishViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishViewController.swift; sourceTree = "<group>"; };
		002B08E12C1BE881007AFDB3 /* WHPublishView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishView.swift; sourceTree = "<group>"; };
		002B08E32C1BECF2007AFDB3 /* WHPublishImageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishImageCell.swift; sourceTree = "<group>"; };
		002B08E52C1C1886007AFDB3 /* WHPublishInputCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishInputCell.swift; sourceTree = "<group>"; };
		002B08E72C1C2F7F007AFDB3 /* WHPublishControlCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishControlCell.swift; sourceTree = "<group>"; };
		00310B722AB1AE7900795F4F /* candy.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = candy.ttf; sourceTree = "<group>"; };
		00389ED72AA5A59600E6089A /* MeituWhee.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MeituWhee.app; sourceTree = BUILT_PRODUCTS_DIR; };
		00389EDA2AA5A59600E6089A /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		00389EDE2AA5A59600E6089A /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		00389EE12AA5A59600E6089A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		00389EE32AA5A59800E6089A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		00389EE82AA5A59800E6089A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00389EEF2AA5AAE800E6089A /* WHLaunchTaskManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchTaskManager.swift; sourceTree = "<group>"; };
		00389EF12AA5AB2800E6089A /* WHLaunchTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchTask.swift; sourceTree = "<group>"; };
		00389EF42AA5ABF200E6089A /* WHLaunchAccountTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchAccountTask.swift; sourceTree = "<group>"; };
		00389EF62AA5AC4B00E6089A /* MeituWhee.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MeituWhee.entitlements; sourceTree = "<group>"; };
		00389EF72AA5B0DA00E6089A /* WHLaunchManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchManager.swift; sourceTree = "<group>"; };
		00389EF92AA5B27900E6089A /* WHLaunchAnalyticsTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchAnalyticsTask.swift; sourceTree = "<group>"; };
		00389EFB2AA6F6DD00E6089A /* WHLaunchDoraemonTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchDoraemonTask.swift; sourceTree = "<group>"; };
		00389EFD2AA6FC2600E6089A /* WHLaunchWindowTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchWindowTask.swift; sourceTree = "<group>"; };
		00389EFF2AA6FCB500E6089A /* WHLaunchRootVCTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchRootVCTask.swift; sourceTree = "<group>"; };
		003F329B2B2311E900F8EA09 /* WHCreatPicCountCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCreatPicCountCell.swift; sourceTree = "<group>"; };
		0045E4342ACE8522008064AE /* WHRouterSetViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterSetViewController.swift; sourceTree = "<group>"; };
		005CADFC2B26FF1A004315D2 /* WHCtreatPicButtonCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCtreatPicButtonCell.swift; sourceTree = "<group>"; };
		005CADFE2B271F12004315D2 /* WHCreatPicView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCreatPicView.swift; sourceTree = "<group>"; };
		0064181A2ABEBC6500933945 /* WHSettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSettingViewController.swift; sourceTree = "<group>"; };
		0064CAB82AA9B3EA007EC639 /* WHLaunchEnviromentTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchEnviromentTask.swift; sourceTree = "<group>"; };
		0064CABE2AA9EFFD007EC639 /* WHMineViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineViewController.swift; sourceTree = "<group>"; };
		0069ADCB2AC172A800BB06DD /* WHMineSegemateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineSegemateView.swift; sourceTree = "<group>"; };
		00767D092C20401300547237 /* WHPublishAlterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPublishAlterView.swift; sourceTree = "<group>"; };
		00767D0B2C204D9D00547237 /* WHMineManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineManager.swift; sourceTree = "<group>"; };
		007D5E202C2946D100FE7197 /* WHMoreAlterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMoreAlterView.swift; sourceTree = "<group>"; };
		007D5E222C29742800FE7197 /* WHMoreEditeAlterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMoreEditeAlterView.swift; sourceTree = "<group>"; };
		00821C2B2B29888C00C19764 /* WHPaindingPlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPaindingPlayView.swift; sourceTree = "<group>"; };
		00825F6C2AC2A76B00923D98 /* WHSettingTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSettingTableCell.swift; sourceTree = "<group>"; };
		008325282C2D37AE0092E6E2 /* WHDetailInfoManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailInfoManager.swift; sourceTree = "<group>"; };
		0083252A2C2E897F0092E6E2 /* WHModelDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHModelDetailViewController.swift; sourceTree = "<group>"; };
		0083252C2C2EA8760092E6E2 /* WHModelImageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHModelImageCell.swift; sourceTree = "<group>"; };
		0083252E2C2EB5550092E6E2 /* WHModelDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHModelDetailView.swift; sourceTree = "<group>"; };
		00857A362AC1386B0090BC7E /* WHMineContentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineContentViewController.swift; sourceTree = "<group>"; };
		00858AB02ABBDDA600A10201 /* WHMineHeadView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineHeadView.swift; sourceTree = "<group>"; };
		008DB41D2C22D21700EB3EBF /* WHCreateInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCreateInfoView.swift; sourceTree = "<group>"; };
		008DB4222C22D2DA00EB3EBF /* WHMyCreatInfoModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHMyCreatInfoModel.swift; sourceTree = "<group>"; };
		008DB4242C252C5000EB3EBF /* WHDetailMyBottoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailMyBottoView.swift; sourceTree = "<group>"; };
		00B3C8902B26B4CD00B037B5 /* WHCreatPicInputCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCreatPicInputCell.swift; sourceTree = "<group>"; };
		00B3C8922B26DE3C00B037B5 /* WHCreatPicCellManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCreatPicCellManager.swift; sourceTree = "<group>"; };
		00F48F2B2AA74CF4003505BE /* WHDebugViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDebugViewController.swift; sourceTree = "<group>"; };
		031B29332E227E16000E1882 /* WHVideoFrameImageLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoFrameImageLoader.swift; sourceTree = "<group>"; };
		032C3A842DD71D9F00B22F5C /* WHAIClearSegmentedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearSegmentedView.swift; sourceTree = "<group>"; };
		03437D462D6F2F3900661BFB /* WHEE-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "WHEE-Bridging-Header.h"; sourceTree = "<group>"; };
		0346950F2D9257E50025B1DA /* PhotosUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PhotosUI.framework; path = System/Library/Frameworks/PhotosUI.framework; sourceTree = SDKROOT; };
		034EE7BF2E0A9982009E0587 /* WHVideoHudDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudDetailViewController.swift; sourceTree = "<group>"; };
		034EE7C12E0A9995009E0587 /* A.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = A.mp4; sourceTree = "<group>"; };
		034EE7C22E0A9995009E0587 /* A_H.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = A_H.mp4; sourceTree = "<group>"; };
		034EE7C42E0A9995009E0587 /* WHVideoHudDetailFailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudDetailFailView.swift; sourceTree = "<group>"; };
		034EE7C52E0A9995009E0587 /* WHVideoHudDetailSlider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudDetailSlider.swift; sourceTree = "<group>"; };
		034EE7CB2E0A9F2A009E0587 /* WHVideoHudClipViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudClipViewController.swift; sourceTree = "<group>"; };
		034EE7CD2E0AA402009E0587 /* WHMediaEditEntranceViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMediaEditEntranceViewController.swift; sourceTree = "<group>"; };
		034EE7CF2E0AA812009E0587 /* WHVideoHudClipBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudClipBottomView.swift; sourceTree = "<group>"; };
		035127912DD473C000B880F8 /* WHAIClearToolsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearToolsView.swift; sourceTree = "<group>"; };
		035CF43E2DA4EE420017EAF7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		035CF43F2DA4EE420017EAF7 /* NotificationService.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationService.entitlements; sourceTree = "<group>"; };
		035CF4402DA4EE420017EAF7 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		0361786B2E13AD2B006A751A /* WHMediaManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMediaManager.swift; sourceTree = "<group>"; };
		0361786F2E13AEFA006A751A /* WHTimeLineConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTimeLineConfig.swift; sourceTree = "<group>"; };
		03626BB72D812647003A6712 /* WHMineButtonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineButtonView.swift; sourceTree = "<group>"; };
		03626BC02D8195CD003A6712 /* img_0.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = img_0.png; sourceTree = "<group>"; };
		03626BC22D8195CD003A6712 /* reward_star.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = reward_star.json; sourceTree = "<group>"; };
		0381B11B2E0F9AC40034FBAD /* WHOneSentenceInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHOneSentenceInputView.swift; sourceTree = "<group>"; };
		03995B292DA4BF7E001C856D /* NotificationService.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationService.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		039CCD8A2DE8358F0079A5F8 /* WHAILivePreViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILivePreViewController.swift; sourceTree = "<group>"; };
		039CCD8C2DE8537A0079A5F8 /* WHAILivePreCollectionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILivePreCollectionCell.swift; sourceTree = "<group>"; };
		03BF23322E28C5410065FAE8 /* WHProgressLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHProgressLoadingView.swift; sourceTree = "<group>"; };
		03C4AEB72D8BF2ED00AD1E71 /* WHRouterDelegateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterDelegateManager.swift; sourceTree = "<group>"; };
		03C873E02E0E6DB1006253B3 /* WHOneSentenceViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHOneSentenceViewController.swift; sourceTree = "<group>"; };
		03C873E32E0E7329006253B3 /* WHRouterOneSentenceResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterOneSentenceResponder.swift; sourceTree = "<group>"; };
		03CCA7072E78072000D5925F /* WHSOPDetailsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSOPDetailsViewController.swift; sourceTree = "<group>"; };
		03CCA70B2E780A2D00D5925F /* WHSOPDetailsButtonCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSOPDetailsButtonCell.swift; sourceTree = "<group>"; };
		03CCA70E2E78162C00D5925F /* WHSOPDetailsImgInfoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSOPDetailsImgInfoView.swift; sourceTree = "<group>"; };
		03CCA7122E783DDE00D5925F /* sop_details_again.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = sop_details_again.json; sourceTree = "<group>"; };
		03D7A5AA2E1CBAD100A78658 /* WHVideoHudClipTimeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudClipTimeCell.swift; sourceTree = "<group>"; };
		03D7A5AE2E1CF10B00A78658 /* WHTimeLineClipView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTimeLineClipView.swift; sourceTree = "<group>"; };
		03D7A5B02E1CF10B00A78658 /* WHTimeLineProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTimeLineProgressView.swift; sourceTree = "<group>"; };
		03D7A5B22E1CF10B00A78658 /* WHTimeLineTrackView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTimeLineTrackView.swift; sourceTree = "<group>"; };
		03E8B9D12D7D8A8F0097557C /* WHRewardsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsViewController.swift; sourceTree = "<group>"; };
		03E8B9D32D7D90D00097557C /* WHRewardsInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsInfoModel.swift; sourceTree = "<group>"; };
		03E8B9D52D7D96AB0097557C /* WHRewardsMembershipCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsMembershipCell.swift; sourceTree = "<group>"; };
		03E8B9D72D7D96E30097557C /* WHRewardsCheckInCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsCheckInCell.swift; sourceTree = "<group>"; };
		03E8B9D92D7D97330097557C /* WHRewardsInviteBoostCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsInviteBoostCell.swift; sourceTree = "<group>"; };
		03E8B9DB2D7D989E0097557C /* WHRewardsSectionHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRewardsSectionHeaderView.swift; sourceTree = "<group>"; };
		16176D322D881F14004D1C3A /* mtseclicense.pixel */ = {isa = PBXFileReference; lastKnownFileType = text; path = mtseclicense.pixel; sourceTree = "<group>"; };
		167EACB92C3690D700F175B1 /* WHDisclaimersCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDisclaimersCell.swift; sourceTree = "<group>"; };
		167EACBB2C37E38F00F175B1 /* WHDoubleSliderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHDoubleSliderView.swift; sourceTree = "<group>"; };
		200BBCC32AFB7B3600FB89F2 /* WHLaunchAPNSTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchAPNSTask.swift; sourceTree = "<group>"; };
		200BBCC72AFE19C700FB89F2 /* WHCIADebugViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCIADebugViewController.swift; sourceTree = "<group>"; };
		200C7F322ADD28DF00E4DCCE /* WHSettingAboutUsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHSettingAboutUsViewController.swift; sourceTree = "<group>"; };
		2027DB092BC3D9BE00625AD4 /* WHImageToImageInputCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImageToImageInputCell.swift; sourceTree = "<group>"; };
		2027DB0C2BC4F06300625AD4 /* WHRouterImgToImgResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterImgToImgResponder.swift; sourceTree = "<group>"; };
		2027DB0E2BC5556800625AD4 /* WHImgToImgZoomCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImgToImgZoomCell.swift; sourceTree = "<group>"; };
		2044EB342B87342F00324E1D /* WHAIClearPreViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearPreViewController.swift; sourceTree = "<group>"; };
		2044EB372B87384800324E1D /* WHRouterAIClearResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterAIClearResponder.swift; sourceTree = "<group>"; };
		206AE7632AB5612600FA8483 /* WHLaunchCIATask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchCIATask.swift; sourceTree = "<group>"; };
		206AE7652AB5B3E700FA8483 /* WHLaunchSSOShareTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchSSOShareTask.swift; sourceTree = "<group>"; };
		206DFF322AFF3CAB001593DE /* WHAuthorityManageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAuthorityManageViewController.swift; sourceTree = "<group>"; };
		206DFF342AFF5FCA001593DE /* WHAuthorityManageTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAuthorityManageTableViewCell.swift; sourceTree = "<group>"; };
		2070BA212B283BFF00DE80E0 /* WHInpaindingRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingRequest.swift; sourceTree = "<group>"; };
		2070BA242B283D5A00DE80E0 /* WHInpaindingConfigModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingConfigModel.swift; sourceTree = "<group>"; };
		2070BA262B284DF000DE80E0 /* WHInpaindingDoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingDoModel.swift; sourceTree = "<group>"; };
		2070BA302B2950D400DE80E0 /* WHRouterInpaintingResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterInpaintingResponder.swift; sourceTree = "<group>"; };
		2083ED032B35A0B100FA42D1 /* WHMineVisitorModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineVisitorModel.swift; sourceTree = "<group>"; };
		2093CC762BF25EA9003E7421 /* WHLaunchEditorTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchEditorTask.swift; sourceTree = "<group>"; };
		2096DC9B2AF243C600D5C80A /* tcmpp-ios-configurations-whee.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = "tcmpp-ios-configurations-whee.json"; sourceTree = "<group>"; };
		20AF4EF22C0761570087FFD9 /* WHWebViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHWebViewController.swift; sourceTree = "<group>"; };
		20AF59FA2C086B480087FFD9 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		20B197BF2AE7E3DE003100DC /* WHQRCodeMiniController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHQRCodeMiniController.swift; sourceTree = "<group>"; };
		20BBE0AA2B4D4B4D0053F2A9 /* WHAIVideoDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIVideoDetailViewController.swift; sourceTree = "<group>"; };
		20BBE0AC2B4D71830053F2A9 /* WHAIVideoDetailCreationCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIVideoDetailCreationCell.swift; sourceTree = "<group>"; };
		20BBE0AE2B4E8E860053F2A9 /* WHDetailViewCommonMoreView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailViewCommonMoreView.swift; sourceTree = "<group>"; };
		20D1C16D2BB2C56C004B3DEE /* WHImageToImageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImageToImageViewController.swift; sourceTree = "<group>"; };
		20D1C1702BB2E3DC004B3DEE /* WHImgToImgSelectImgCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImgToImgSelectImgCell.swift; sourceTree = "<group>"; };
		20D1C1752BB66E73004B3DEE /* WHImageToImageRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImageToImageRequest.swift; sourceTree = "<group>"; };
		20DA02CE2B49359A004C5898 /* WHHomeHotCreationCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeHotCreationCell.swift; sourceTree = "<group>"; };
		20DA02D02B493DFF004C5898 /* WHHomeCellManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeCellManager.swift; sourceTree = "<group>"; };
		20DA02D22B493F23004C5898 /* WHHomeAiCreationCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeAiCreationCell.swift; sourceTree = "<group>"; };
		20DEE5B32BA2FD7600B89A7B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen_cn.storyboard; sourceTree = "<group>"; };
		20DEE5B62BA2FD8200B89A7B /* en */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = en; path = en.lproj/LaunchScreen_en.storyboard; sourceTree = "<group>"; };
		20F607D62B3BFA3B00C97663 /* WHHomeConfigCacheModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeConfigCacheModel.swift; sourceTree = "<group>"; };
		20FD82B62B273264008E68AA /* WHInpaindingEditView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHInpaindingEditView.swift; sourceTree = "<group>"; };
		20FE35C42AD0251900789CF6 /* WHHomeConfigModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHHomeConfigModel.swift; sourceTree = "<group>"; };
		20FE35C72AD0252700789CF6 /* WHHomeRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHHomeRequest.swift; sourceTree = "<group>"; };
		230593B22E7910A400B424CE /* WHLaunchBusinessCommonTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchBusinessCommonTask.swift; sourceTree = "<group>"; };
		2334F43F2E0A51C7000C4F50 /* WHRouterVideoHudResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterVideoHudResponder.swift; sourceTree = "<group>"; };
		2366C66B2C8EA2F900FDBF67 /* WHRouterAIUpScalerResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterAIUpScalerResponder.swift; sourceTree = "<group>"; };
		2366C66D2C8EA33F00FDBF67 /* WHAIUpScalerPreViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerPreViewController.swift; sourceTree = "<group>"; };
		2366C66F2C8EDC6000FDBF67 /* WHAIUpScalerRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerRequest.swift; sourceTree = "<group>"; };
		2366C6712C8EEE0500FDBF67 /* WHAIUpScalerViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerViewController.swift; sourceTree = "<group>"; };
		2366D1B92CBA668A00FDBF67 /* WHShareInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHShareInfoModel.swift; sourceTree = "<group>"; };
		2386F4F42E13BFA60055CF97 /* WHVideoHudPreViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudPreViewController.swift; sourceTree = "<group>"; };
		2386F4F62E13D6C10055CF97 /* WHAIUpScalerUploadResource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerUploadResource.swift; sourceTree = "<group>"; };
		23A47DB22E0D574200D774DB /* WHVideoHudCreateViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudCreateViewController.swift; sourceTree = "<group>"; };
		23A47DB42E0D7B2B00D774DB /* WHVideoHudStyleSelectView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudStyleSelectView.swift; sourceTree = "<group>"; };
		23A47DB72E0E887500D774DB /* WHVideoHudRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudRequest.swift; sourceTree = "<group>"; };
		23A47DB92E0EAA0B00D774DB /* WHVideoHudVideoPlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoHudVideoPlayView.swift; sourceTree = "<group>"; };
		23B3217B2DFAA10400BAF701 /* WHCommonToastAlertResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonToastAlertResponder.swift; sourceTree = "<group>"; };
		241005F92E72D8AB009D7682 /* WHHomeFlowModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFlowModel.swift; sourceTree = "<group>"; };
		241006002E73ECFE009D7682 /* WHCodableModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCodableModel.swift; sourceTree = "<group>"; };
		241006022E758422009D7682 /* WHHomeFlowFilterButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFlowFilterButton.swift; sourceTree = "<group>"; };
		241006042E77AD58009D7682 /* WHHomeFlowPopMenu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFlowPopMenu.swift; sourceTree = "<group>"; };
		2415E2742E5706A800E60836 /* UIViewController+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIViewController+Extensions.swift"; sourceTree = "<group>"; };
		2415E2772E5742B500E60836 /* WHSmartCutoutReport.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutReport.swift; sourceTree = "<group>"; };
		2415E27A2E58174500E60836 /* WHImageUploader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImageUploader.swift; sourceTree = "<group>"; };
		2415E27C2E5C09C700E60836 /* CircularCursorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CircularCursorView.swift; sourceTree = "<group>"; };
		241816862E4C7BC00075B516 /* WHSmartCutoutEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutEngine.swift; sourceTree = "<group>"; };
		2418168A2E4C9A320075B516 /* WHSmartCutoutRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutRequest.swift; sourceTree = "<group>"; };
		2418168C2E4CB2030075B516 /* WHImageKitCanvasView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHImageKitCanvasView.swift; sourceTree = "<group>"; };
		24295DB72E7AABE500365D09 /* WHHomeFullScreenResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFullScreenResponder.swift; sourceTree = "<group>"; };
		244F91142E4DBA4900A6AA4B /* RxWHRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RxWHRequest.swift; sourceTree = "<group>"; };
		244F91172E501FAB00A6AA4B /* UIColors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIColors.swift; sourceTree = "<group>"; };
		244F911B2E50438E00A6AA4B /* WHSmartCutoutRoundButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutRoundButton.swift; sourceTree = "<group>"; };
		244F911D2E509AFB00A6AA4B /* SmartCutoutSmearHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartCutoutSmearHistory.swift; sourceTree = "<group>"; };
		244F911F2E52F68F00A6AA4B /* WHSmartCutoutEngine+MTIK.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "WHSmartCutoutEngine+MTIK.swift"; sourceTree = "<group>"; };
		2470BACA2E702F1000531815 /* WHHomeFlowCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFlowCollectionViewCell.swift; sourceTree = "<group>"; };
		2470BAD02E70315600531815 /* WHHomeFlowViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFlowViewController.swift; sourceTree = "<group>"; };
		2470BAD42E7033CB00531815 /* WHFlowContentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHFlowContentViewController.swift; sourceTree = "<group>"; };
		248C9A882E5D7630000CCE49 /* WHSmartCutoutRequestObservable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutRequestObservable.swift; sourceTree = "<group>"; };
		248C9A8A2E5DAF86000CCE49 /* UIImage+Alpha.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImage+Alpha.swift"; sourceTree = "<group>"; };
		2498C0192E79344C007FC3EF /* WHBlurImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHBlurImageView.swift; sourceTree = "<group>"; };
		2498C01B2E7934F8007FC3EF /* WHHomeFullScreenViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFullScreenViewController.swift; sourceTree = "<group>"; };
		2498C01C2E7934F8007FC3EF /* WHHomeFullScreenFlowDataViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFullScreenFlowDataViewModel.swift; sourceTree = "<group>"; };
		2498C01D2E7934F8007FC3EF /* WHHomeFullScreenFlowTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeFullScreenFlowTableViewCell.swift; sourceTree = "<group>"; };
		2498C0232E795CB6007FC3EF /* UIImageView+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImageView+Extension.swift"; sourceTree = "<group>"; };
		24D9D9C12E46FC950066BBC1 /* WHSmartCutoutRouterResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutRouterResponder.swift; sourceTree = "<group>"; };
		24D9D9C32E471B890066BBC1 /* WHCommonRoouterResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonRoouterResponder.swift; sourceTree = "<group>"; };
		24D9D9C52E472BF30066BBC1 /* WHSmartCutoutViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutViewController.swift; sourceTree = "<group>"; };
		24D9D9C72E4734FD0066BBC1 /* WHSmartCutoutBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutBottomView.swift; sourceTree = "<group>"; };
		24D9D9C92E4737DA0066BBC1 /* WHSliderDetailCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSliderDetailCard.swift; sourceTree = "<group>"; };
		24D9D9CF2E499CA10066BBC1 /* WHSmartCutoutSlider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutSlider.swift; sourceTree = "<group>"; };
		24D9D9D52E49D4060066BBC1 /* WHSmartCutoutBottomToolBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutBottomToolBar.swift; sourceTree = "<group>"; };
		24D9D9D92E4B1F6F0066BBC1 /* WHSmartCutoutNavigationBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutNavigationBar.swift; sourceTree = "<group>"; };
		24D9D9E32E4B42F10066BBC1 /* WHSmartCutoutSaveResultViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutSaveResultViewController.swift; sourceTree = "<group>"; };
		24D9D9E52E4B44800066BBC1 /* WHSmartCutoutSavedToolBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSmartCutoutSavedToolBar.swift; sourceTree = "<group>"; };
		24F8C4F32E71A18D001D7156 /* WHHomeVisualEffectsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeVisualEffectsManager.swift; sourceTree = "<group>"; };
		24F8C4F62E7272F0001D7156 /* UIApplication+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIApplication+Extension.swift"; sourceTree = "<group>"; };
		24F8C4FA2E72752F001D7156 /* UIDevice+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIDevice+Extension.swift"; sourceTree = "<group>"; };
		24F8C5002E72C9A8001D7156 /* WHAccountManager+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "WHAccountManager+Extension.swift"; sourceTree = "<group>"; };
		65E6E1952EA4193F93A93A62 /* Pods-MeituWhee.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeituWhee.debug.xcconfig"; path = "Target Support Files/Pods-MeituWhee/Pods-MeituWhee.debug.xcconfig"; sourceTree = "<group>"; };
		6BD40224DA8A682B059D32B9 /* Pods-MeituWhee.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeituWhee.release.xcconfig"; path = "Target Support Files/Pods-MeituWhee/Pods-MeituWhee.release.xcconfig"; sourceTree = "<group>"; };
		951125A42AB83EA6001B834A /* WHHomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeViewController.swift; sourceTree = "<group>"; };
		951125A62AB83F36001B834A /* WHInspirationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInspirationViewController.swift; sourceTree = "<group>"; };
		9520215C2CA152D70011B379 /* WHAIUSImageClarityView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSImageClarityView.swift; sourceTree = "<group>"; };
		952021602CA290220011B379 /* WHAIUSValueSettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSValueSettingView.swift; sourceTree = "<group>"; };
		953681FB2BC3D1D800E3943E /* WHControlCellSubviews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHControlCellSubviews.swift; sourceTree = "<group>"; };
		953681FD2BC412CD00E3943E /* WHReferTypeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHReferTypeViewController.swift; sourceTree = "<group>"; };
		953682002BC9370600E3943E /* WHRouterHistoryResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterHistoryResponder.swift; sourceTree = "<group>"; };
		953682072BCF770D00E3943E /* WHToImageDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHToImageDetailViewController.swift; sourceTree = "<group>"; };
		953682092BCFF0EF00E3943E /* WHDetailBottomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailBottomView.swift; sourceTree = "<group>"; };
		9536820D2BCFFA8A00E3943E /* WHToImageDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHToImageDetailView.swift; sourceTree = "<group>"; };
		953682112BCFFDD700E3943E /* WHDetailUserInfoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailUserInfoCell.swift; sourceTree = "<group>"; };
		953682132BCFFDFD00E3943E /* WHDetailOtherCreateInfoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailOtherCreateInfoCell.swift; sourceTree = "<group>"; };
		953682152BCFFE0A00E3943E /* WHDetailMyCreateInfoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailMyCreateInfoCell.swift; sourceTree = "<group>"; };
		953682172BCFFEED00E3943E /* WHDetailImagesCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDetailImagesCell.swift; sourceTree = "<group>"; };
		954A4E1B2BA05D790037C440 /* WHCommonUseAlertModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonUseAlertModel.swift; sourceTree = "<group>"; };
		954A4E1F2BA1759D0037C440 /* WHCommonUseAlertManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonUseAlertManager.swift; sourceTree = "<group>"; };
		954A4E212BA178970037C440 /* WHCommonUseAlertRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonUseAlertRequest.swift; sourceTree = "<group>"; };
		954A4E252BA307550037C440 /* WHHomeToolCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHHomeToolCell.swift; sourceTree = "<group>"; };
		954A4E282BA7EEC10037C440 /* WHTextToImageRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTextToImageRequest.swift; sourceTree = "<group>"; };
		954A4E362BAC11680037C440 /* WHTextToImageModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHTextToImageModel.swift; sourceTree = "<group>"; };
		954A4E382BAC11680037C440 /* WHCreateImageControlCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCreateImageControlCell.swift; sourceTree = "<group>"; };
		954A4E392BAC11680037C440 /* WHTextToImageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHTextToImageView.swift; sourceTree = "<group>"; };
		954A4E3A2BAC11680037C440 /* WHCreateImageCountCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCreateImageCountCell.swift; sourceTree = "<group>"; };
		954A4E3B2BAC11680037C440 /* WHTextToImageInputCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHTextToImageInputCell.swift; sourceTree = "<group>"; };
		954A4E3C2BAC11680037C440 /* WHCreateImageHigherCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCreateImageHigherCell.swift; sourceTree = "<group>"; };
		954A4E3D2BAC11680037C440 /* WHVisionModelCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHVisionModelCell.swift; sourceTree = "<group>"; };
		954A4E3E2BAC11680037C440 /* WHStyleStrengthView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHStyleStrengthView.swift; sourceTree = "<group>"; };
		954A4E3F2BAC11680037C440 /* WHCommonUploadView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCommonUploadView.swift; sourceTree = "<group>"; };
		954A4E402BAC11680037C440 /* WHCreateImageStyleCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCreateImageStyleCell.swift; sourceTree = "<group>"; };
		954A4E412BAC11680037C440 /* WHCreateImageSizeCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHCreateImageSizeCell.swift; sourceTree = "<group>"; };
		954A4E432BAC11680037C440 /* WHTextToImageRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHTextToImageRequest.swift; sourceTree = "<group>"; };
		954A4E452BAC11680037C440 /* WHTextToImageViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHTextToImageViewController.swift; sourceTree = "<group>"; };
		954FCD3B2AF25FBB00F08C50 /* WHUserAgreeAbroadView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHUserAgreeAbroadView.swift; sourceTree = "<group>"; };
		955064B52AD41C36005AA274 /* WHLaunchConfigTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchConfigTask.swift; sourceTree = "<group>"; };
		955064B82AD52A55005AA274 /* WHAppConfigManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAppConfigManager.swift; sourceTree = "<group>"; };
		955064BF2ADCE6B0005AA274 /* WHMineRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineRequest.swift; sourceTree = "<group>"; };
		955064C12ADE7F9A005AA274 /* Loading_Logo.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = Loading_Logo.json; sourceTree = "<group>"; };
		955064C42ADFBDF7005AA274 /* WHCommonUserInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCommonUserInfo.swift; sourceTree = "<group>"; };
		955A585D2B0C4CAE0068054F /* radius.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = radius.json; sourceTree = "<group>"; };
		95756B462B6CDFA400127A82 /* WHComonInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHComonInputView.swift; sourceTree = "<group>"; };
		957577722B871D4100127A82 /* WHAIClearViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearViewController.swift; sourceTree = "<group>"; };
		957577762B888F9000127A82 /* WHAIClearRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearRequest.swift; sourceTree = "<group>"; };
		957577782B8DB57B00127A82 /* WHAIClearModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIClearModel.swift; sourceTree = "<group>"; };
		9575777B2B9089B800127A82 /* aiClear.webp */ = {isa = PBXFileReference; lastKnownFileType = file; path = aiClear.webp; sourceTree = "<group>"; };
		95887B6E2B148A4600B0ABE7 /* theme_wheeApp.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = theme_wheeApp.json; sourceTree = "<group>"; };
		959009BF2B46CD8D00F1DC7A /* WHAICreatVideoZoomCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreatVideoZoomCell.swift; sourceTree = "<group>"; };
		959009C12B47D6A200F1DC7A /* WHMineWorksViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHMineWorksViewController.swift; sourceTree = "<group>"; };
		959009CE2B4E44DB00F1DC7A /* WHVideoDetailPlayManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoDetailPlayManager.swift; sourceTree = "<group>"; };
		959009D02B4E46B400F1DC7A /* Fadable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Fadable.swift; sourceTree = "<group>"; };
		959009D22B4E473C00F1DC7A /* WHVideoPlayerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHVideoPlayerView.swift; sourceTree = "<group>"; };
		959009D42B4E6C1E00F1DC7A /* WHAIVideoDetailVideoCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIVideoDetailVideoCell.swift; sourceTree = "<group>"; };
		959009D62B4E732600F1DC7A /* video_loading.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = video_loading.json; sourceTree = "<group>"; };
		959740752B396B470027D29B /* WHAICreateVideoViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoViewController.swift; sourceTree = "<group>"; };
		959740792B397A6D0027D29B /* WHAICreatVideoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreatVideoView.swift; sourceTree = "<group>"; };
		9597407B2B398D6E0027D29B /* WHAICreateVideoInputCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoInputCell.swift; sourceTree = "<group>"; };
		9597407D2B398DB90027D29B /* WHAICreateVideoSizeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoSizeCell.swift; sourceTree = "<group>"; };
		9597407F2B398E150027D29B /* WHAICreateVideoCameCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoCameCell.swift; sourceTree = "<group>"; };
		959740812B398E470027D29B /* WHAICreateVideoUploadCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoUploadCell.swift; sourceTree = "<group>"; };
		959740852B3BC9870027D29B /* WHAICreateVideoTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoTabView.swift; sourceTree = "<group>"; };
		959740892B3C10FD0027D29B /* WHAICreateVideoTipsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoTipsView.swift; sourceTree = "<group>"; };
		9597408C2B3D19EB0027D29B /* WHAICreateVideoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoModel.swift; sourceTree = "<group>"; };
		959740AB2B453E4B0027D29B /* WHAICreateVideoRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoRequest.swift; sourceTree = "<group>"; };
		95A389582B62333100332D7B /* WHAICreateVideoFormulaCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoFormulaCell.swift; sourceTree = "<group>"; };
		95AFED4F2BAD96C900297588 /* WHStyleFlowViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHStyleFlowViewController.swift; sourceTree = "<group>"; };
		95AFED512BB1582500297588 /* WHStyleTabViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHStyleTabViewController.swift; sourceTree = "<group>"; };
		95BC83B02B5E5E0100436AF0 /* input_lenovo.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = input_lenovo.json; sourceTree = "<group>"; };
		95BCB1CA2AA9A74A00A76B9C /* WHLaunchVipSdkTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchVipSdkTask.swift; sourceTree = "<group>"; };
		95BCB1D02AA9C46900A76B9C /* WHTabBarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHTabBarController.swift; sourceTree = "<group>"; };
		95BF6B032AB5907700B13E2B /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		95BF6B082AB5917B00B13E2B /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		95BF6B0A2AB591A200B13E2B /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		95BF6B0B2AB591AB00B13E2B /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		95BF6B162AB8324500B13E2B /* WHLanguageSetViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHLanguageSetViewController.swift; sourceTree = "<group>"; };
		95C290312B55246E00A66E16 /* WHAICreateVideoCameTextCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICreateVideoCameTextCell.swift; sourceTree = "<group>"; };
		95C9E0332AF11BC100D8E24A /* feed_status.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = feed_status.json; sourceTree = "<group>"; };
		95CACD282B21C04200354A81 /* WHInpaindingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingManager.swift; sourceTree = "<group>"; };
		95CACD2A2B21C49A00354A81 /* WHInpaindingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingViewController.swift; sourceTree = "<group>"; };
		95D3DBAC2AFB387000DD3FEC /* WHAgreementAlertOptionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAgreementAlertOptionCell.swift; sourceTree = "<group>"; };
		95D3DBAF2AFDD9E800DD3FEC /* WHUpgradeAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHUpgradeAlertView.swift; sourceTree = "<group>"; };
		95D3DBB32AFF282600DD3FEC /* WHUpgradeInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHUpgradeInfo.swift; sourceTree = "<group>"; };
		95D902742C92C519004DD5B2 /* WHAIExpandModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIExpandModel.swift; sourceTree = "<group>"; };
		95D9192F2C92F260004DD5B2 /* WHRouterAIExpandResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterAIExpandResponder.swift; sourceTree = "<group>"; };
		95D919312C9A8203004DD5B2 /* WHAIExpandRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIExpandRequest.swift; sourceTree = "<group>"; };
		95D919392C9D4C93004DD5B2 /* WHAIUpScalerModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerModel.swift; sourceTree = "<group>"; };
		95D9F7162C91A15A004DD5B2 /* WHAIExpandViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIExpandViewController.swift; sourceTree = "<group>"; };
		95D9F7182C91A25C004DD5B2 /* WHAICommonViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAICommonViewController.swift; sourceTree = "<group>"; };
		95DA81B02B26A9F000E8D8C0 /* WHInpaindingBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInpaindingBarView.swift; sourceTree = "<group>"; };
		95DC0B6F2ABB0CDA0026E3D3 /* WHFlowManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowManager.swift; sourceTree = "<group>"; };
		95DC0B712ABB0CDA0026E3D3 /* WHFlowAbstract.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowAbstract.swift; sourceTree = "<group>"; };
		95DC0B772ABB0CEB0026E3D3 /* WHFlowRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowRequest.swift; sourceTree = "<group>"; };
		95DC0B792ABB0CF50026E3D3 /* WHFlowBaseCardView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowBaseCardView.swift; sourceTree = "<group>"; };
		95DC0B7A2ABB0CF50026E3D3 /* WHFlowBaseCollectionViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowBaseCollectionViewCell.swift; sourceTree = "<group>"; };
		95DC0B7B2ABB0CF50026E3D3 /* WHFlowBaseDisplayEntity.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowBaseDisplayEntity.swift; sourceTree = "<group>"; };
		95DC0B7C2ABB0CF50026E3D3 /* WHFlowLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowLayout.swift; sourceTree = "<group>"; };
		95DC0B812ABB0D040026E3D3 /* WHFlowViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHFlowViewController.swift; sourceTree = "<group>"; };
		95DC20662AC3DA830026E3D3 /* WHSearchViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSearchViewController.swift; sourceTree = "<group>"; };
		95DC20682AC3FD8B0026E3D3 /* WHCustomSearchBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHCustomSearchBarView.swift; sourceTree = "<group>"; };
		95DC20752AC41C060026E3D3 /* MTSuggestCollectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MTSuggestCollectionView.swift; sourceTree = "<group>"; };
		95DC20792AC430500026E3D3 /* WHSearchSuggestionCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WHSearchSuggestionCell.swift; sourceTree = "<group>"; };
		95DC207D2AC51C050026E3D3 /* WHSearchDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSearchDetailViewController.swift; sourceTree = "<group>"; };
		95DC209F2ACE5CEC0026E3D3 /* WHSearchHistoryCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSearchHistoryCell.swift; sourceTree = "<group>"; };
		95DC20A12ACE625D0026E3D3 /* WHSearchHistroyTitleCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHSearchHistroyTitleCell.swift; sourceTree = "<group>"; };
		95DC2B0A2AD105E40026E3D3 /* WHUserAgreementAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHUserAgreementAlertView.swift; sourceTree = "<group>"; };
		95DC2B0E2AD14B6A0026E3D3 /* WHLaunchAlertDispathTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHLaunchAlertDispathTask.swift; sourceTree = "<group>"; };
		95ED04B32CA2C8F90056C11A /* WHAIUSDemoTypeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSDemoTypeView.swift; sourceTree = "<group>"; };
		95ED04B52CA2CA790056C11A /* WHAIUSAdaptStyleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSAdaptStyleView.swift; sourceTree = "<group>"; };
		95ED10112CA3E8D30056C11A /* WHAIUpScalerDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUpScalerDetailViewController.swift; sourceTree = "<group>"; };
		95ED10132CA3FF190056C11A /* WHAIUSDetailPicTypeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSDetailPicTypeView.swift; sourceTree = "<group>"; };
		95ED10152CA425570056C11A /* WHAIUSCanvasView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSCanvasView.swift; sourceTree = "<group>"; };
		95ED10172CA505240056C11A /* WHAIUSIKManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAIUSIKManager.swift; sourceTree = "<group>"; };
		96AC3BBD82A11BCC687B8990 /* Pods_NotificationService.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NotificationService.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9927A0DD64277EAFDD23AB11 /* Pods_MeituWhee.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MeituWhee.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9F3E6C342D7AE01F00ACFC0C /* WHAILiveDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveDetailViewController.swift; sourceTree = "<group>"; };
		9F3E6C362D7E8BBA00ACFC0C /* WHDownLoadManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHDownLoadManager.swift; sourceTree = "<group>"; };
		9F3E6C3C2D800EC200ACFC0C /* WHInviteCodeAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInviteCodeAlert.swift; sourceTree = "<group>"; };
		9F3E6C3E2D80312F00ACFC0C /* WHInvitedFriendAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHInvitedFriendAlert.swift; sourceTree = "<group>"; };
		9F56039A2D8991FB00D56C8C /* WHAILiveTypeCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveTypeCollectionViewCell.swift; sourceTree = "<group>"; };
		9F56039D2D8991FF00D56C8C /* WHAILiveLoadTypeAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveLoadTypeAlert.swift; sourceTree = "<group>"; };
		9FB592FC2D782BB900CFBA6E /* WHRouterAILiveResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterAILiveResponder.swift; sourceTree = "<group>"; };
		9FB592FE2D782BB900CFBA6E /* WHAILiveModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveModel.swift; sourceTree = "<group>"; };
		9FB593002D782BB900CFBA6E /* WHAILiveRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveRequest.swift; sourceTree = "<group>"; };
		9FB593022D782BB900CFBA6E /* WHAILiveViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHAILiveViewController.swift; sourceTree = "<group>"; };
		9FDDE7A12DB6276C00279D3A /* WHPopupAlertModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHPopupAlertModel.swift; sourceTree = "<group>"; };
		9FE748472DA4D02500764130 /* WHRouterAILiveDetailResponder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHRouterAILiveDetailResponder.swift; sourceTree = "<group>"; };
		9FFCD7852CBD38EC00074C84 /* WHToImageDtailFailedTips.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WHToImageDtailFailedTips.swift; sourceTree = "<group>"; };
		B61DB9DEF4B1B8B1F4F3D446 /* Pods-NotificationService.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationService.debug.xcconfig"; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.debug.xcconfig"; sourceTree = "<group>"; };
		DCF9F3218D8FDADE1312047B /* Pods-NotificationService.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationService.release.xcconfig"; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00389ED42AA5A59600E6089A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				ECBB5463606334573658614D /* Pods_MeituWhee.framework in Frameworks */,
				034695102D9257E50025B1DA /* PhotosUI.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03995B262DA4BF7E001C856D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				ACD7036B4AF7B9CE94C82333 /* Pods_NotificationService.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		002B08DC2C1BD7F2007AFDB3 /* publish */ = {
			isa = PBXGroup;
			children = (
				002B08E02C1BE865007AFDB3 /* View */,
				002B08DD2C1BD80A007AFDB3 /* ViewController */,
			);
			path = publish;
			sourceTree = "<group>";
		};
		002B08DD2C1BD80A007AFDB3 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				002B08DE2C1BD93C007AFDB3 /* WHPublishViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		002B08E02C1BE865007AFDB3 /* View */ = {
			isa = PBXGroup;
			children = (
				002B08E12C1BE881007AFDB3 /* WHPublishView.swift */,
				00767D092C20401300547237 /* WHPublishAlterView.swift */,
				002B08E32C1BECF2007AFDB3 /* WHPublishImageCell.swift */,
				002B08E52C1C1886007AFDB3 /* WHPublishInputCell.swift */,
				002B08E72C1C2F7F007AFDB3 /* WHPublishControlCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		00389ECE2AA5A59600E6089A = {
			isa = PBXGroup;
			children = (
				00389ED92AA5A59600E6089A /* MeituWhee */,
				035CF4412DA4EE420017EAF7 /* NotificationService */,
				00389ED82AA5A59600E6089A /* Products */,
				8EC90FC9A9D2FD8314014518 /* Pods */,
				8C725E5EC89D296CD43B5231 /* Frameworks */,
				954A4E2D2BAC0C4A0037C440 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		00389ED82AA5A59600E6089A /* Products */ = {
			isa = PBXGroup;
			children = (
				00389ED72AA5A59600E6089A /* MeituWhee.app */,
				03995B292DA4BF7E001C856D /* NotificationService.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00389ED92AA5A59600E6089A /* MeituWhee */ = {
			isa = PBXGroup;
			children = (
				16176D322D881F14004D1C3A /* mtseclicense.pixel */,
				03437D462D6F2F3900661BFB /* WHEE-Bridging-Header.h */,
				20AF59FA2C086B480087FFD9 /* PrivacyInfo.xcprivacy */,
				00389EF62AA5AC4B00E6089A /* MeituWhee.entitlements */,
				00389EEE2AA5AAD000E6089A /* AppLaunch */,
				9511259D2AB83CD9001B834A /* Module */,
				00389EDA2AA5A59600E6089A /* AppDelegate.swift */,
				00389EDE2AA5A59600E6089A /* ViewController.swift */,
				00F48F2B2AA74CF4003505BE /* WHDebugViewController.swift */,
				20B197BF2AE7E3DE003100DC /* WHQRCodeMiniController.swift */,
				00389EE02AA5A59600E6089A /* Main.storyboard */,
				00389EE32AA5A59800E6089A /* Assets.xcassets */,
				95BF6B0C2AB5947300B13E2B /* Resources */,
				00389EE82AA5A59800E6089A /* Info.plist */,
			);
			path = MeituWhee;
			sourceTree = "<group>";
		};
		00389EEE2AA5AAD000E6089A /* AppLaunch */ = {
			isa = PBXGroup;
			children = (
				00389EEF2AA5AAE800E6089A /* WHLaunchTaskManager.swift */,
				00389EF72AA5B0DA00E6089A /* WHLaunchManager.swift */,
				00389EF12AA5AB2800E6089A /* WHLaunchTask.swift */,
				00389EF32AA5ABC100E6089A /* Tasks */,
			);
			path = AppLaunch;
			sourceTree = "<group>";
		};
		00389EF32AA5ABC100E6089A /* Tasks */ = {
			isa = PBXGroup;
			children = (
				00389EF42AA5ABF200E6089A /* WHLaunchAccountTask.swift */,
				00389EF92AA5B27900E6089A /* WHLaunchAnalyticsTask.swift */,
				206AE7632AB5612600FA8483 /* WHLaunchCIATask.swift */,
				00389EFB2AA6F6DD00E6089A /* WHLaunchDoraemonTask.swift */,
				0064CAB82AA9B3EA007EC639 /* WHLaunchEnviromentTask.swift */,
				00389EFD2AA6FC2600E6089A /* WHLaunchWindowTask.swift */,
				00389EFF2AA6FCB500E6089A /* WHLaunchRootVCTask.swift */,
				95BCB1CA2AA9A74A00A76B9C /* WHLaunchVipSdkTask.swift */,
				206AE7652AB5B3E700FA8483 /* WHLaunchSSOShareTask.swift */,
				955064B52AD41C36005AA274 /* WHLaunchConfigTask.swift */,
				95DC2B0E2AD14B6A0026E3D3 /* WHLaunchAlertDispathTask.swift */,
				200BBCC32AFB7B3600FB89F2 /* WHLaunchAPNSTask.swift */,
				2093CC762BF25EA9003E7421 /* WHLaunchEditorTask.swift */,
				230593B22E7910A400B424CE /* WHLaunchBusinessCommonTask.swift */,
			);
			path = Tasks;
			sourceTree = "<group>";
		};
		003F32962B22E58100F8EA09 /* view */ = {
			isa = PBXGroup;
			children = (
				20FD82B62B273264008E68AA /* WHInpaindingEditView.swift */,
				00821C2B2B29888C00C19764 /* WHPaindingPlayView.swift */,
				003F329B2B2311E900F8EA09 /* WHCreatPicCountCell.swift */,
				005CADFC2B26FF1A004315D2 /* WHCtreatPicButtonCell.swift */,
				00B3C8902B26B4CD00B037B5 /* WHCreatPicInputCell.swift */,
				00B3C8922B26DE3C00B037B5 /* WHCreatPicCellManager.swift */,
				005CADFE2B271F12004315D2 /* WHCreatPicView.swift */,
				95DA81B02B26A9F000E8D8C0 /* WHInpaindingBarView.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		00858AAF2ABBDD8A00A10201 /* View */ = {
			isa = PBXGroup;
			children = (
				9F3E6C3E2D80312F00ACFC0C /* WHInvitedFriendAlert.swift */,
				9F3E6C3C2D800EC200ACFC0C /* WHInviteCodeAlert.swift */,
				00858AB02ABBDDA600A10201 /* WHMineHeadView.swift */,
				0069ADCB2AC172A800BB06DD /* WHMineSegemateView.swift */,
				00825F6C2AC2A76B00923D98 /* WHSettingTableCell.swift */,
				206DFF342AFF5FCA001593DE /* WHAuthorityManageTableViewCell.swift */,
				03E8B9D52D7D96AB0097557C /* WHRewardsMembershipCell.swift */,
				03E8B9D72D7D96E30097557C /* WHRewardsCheckInCell.swift */,
				03E8B9D92D7D97330097557C /* WHRewardsInviteBoostCell.swift */,
				03E8B9DB2D7D989E0097557C /* WHRewardsSectionHeaderView.swift */,
				03626BB72D812647003A6712 /* WHMineButtonView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		008DB4212C22D2C700EB3EBF /* Model */ = {
			isa = PBXGroup;
			children = (
				008DB4222C22D2DA00EB3EBF /* WHMyCreatInfoModel.swift */,
				008325282C2D37AE0092E6E2 /* WHDetailInfoManager.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		034EE7BE2E0A993D009E0587 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				2386F4F42E13BFA60055CF97 /* WHVideoHudPreViewController.swift */,
				034EE7BF2E0A9982009E0587 /* WHVideoHudDetailViewController.swift */,
				034EE7CD2E0AA402009E0587 /* WHMediaEditEntranceViewController.swift */,
				034EE7CB2E0A9F2A009E0587 /* WHVideoHudClipViewController.swift */,
				23A47DB22E0D574200D774DB /* WHVideoHudCreateViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		034EE7C32E0A9995009E0587 /* Resource */ = {
			isa = PBXGroup;
			children = (
				034EE7C12E0A9995009E0587 /* A.mp4 */,
				034EE7C22E0A9995009E0587 /* A_H.mp4 */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		034EE7C62E0A9995009E0587 /* View */ = {
			isa = PBXGroup;
			children = (
				034EE7C42E0A9995009E0587 /* WHVideoHudDetailFailView.swift */,
				034EE7C52E0A9995009E0587 /* WHVideoHudDetailSlider.swift */,
				034EE7CF2E0AA812009E0587 /* WHVideoHudClipBottomView.swift */,
				03D7A5AA2E1CBAD100A78658 /* WHVideoHudClipTimeCell.swift */,
				23A47DB42E0D7B2B00D774DB /* WHVideoHudStyleSelectView.swift */,
				23A47DB92E0EAA0B00D774DB /* WHVideoHudVideoPlayView.swift */,
				03BF23322E28C5410065FAE8 /* WHProgressLoadingView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		035127902DD473A700B880F8 /* View */ = {
			isa = PBXGroup;
			children = (
				035127912DD473C000B880F8 /* WHAIClearToolsView.swift */,
				032C3A842DD71D9F00B22F5C /* WHAIClearSegmentedView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		035CF4412DA4EE420017EAF7 /* NotificationService */ = {
			isa = PBXGroup;
			children = (
				035CF43E2DA4EE420017EAF7 /* Info.plist */,
				035CF43F2DA4EE420017EAF7 /* NotificationService.entitlements */,
				035CF4402DA4EE420017EAF7 /* NotificationService.swift */,
			);
			path = NotificationService;
			sourceTree = "<group>";
		};
		0361786D2E13AE65006A751A /* TimeLine */ = {
			isa = PBXGroup;
			children = (
				03D7A5B32E1CF10B00A78658 /* View */,
				0361786E2E13AE78006A751A /* Manager */,
			);
			path = TimeLine;
			sourceTree = "<group>";
		};
		0361786E2E13AE78006A751A /* Manager */ = {
			isa = PBXGroup;
			children = (
				0361786F2E13AEFA006A751A /* WHTimeLineConfig.swift */,
				031B29332E227E16000E1882 /* WHVideoFrameImageLoader.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		03626BC12D8195CD003A6712 /* images */ = {
			isa = PBXGroup;
			children = (
				03626BC02D8195CD003A6712 /* img_0.png */,
			);
			path = images;
			sourceTree = "<group>";
		};
		03626BC32D8195CD003A6712 /* RewardAnimate */ = {
			isa = PBXGroup;
			children = (
				03626BC12D8195CD003A6712 /* images */,
				03626BC22D8195CD003A6712 /* reward_star.json */,
			);
			path = RewardAnimate;
			sourceTree = "<group>";
		};
		0381B11A2E0F9AAF0034FBAD /* View */ = {
			isa = PBXGroup;
			children = (
				0381B11B2E0F9AC40034FBAD /* WHOneSentenceInputView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		03C873DE2E0E6D62006253B3 /* OneSentence */ = {
			isa = PBXGroup;
			children = (
				0381B11A2E0F9AAF0034FBAD /* View */,
				03C873E22E0E72EC006253B3 /* Manager */,
				03C873DF2E0E6D8A006253B3 /* ViewController */,
			);
			path = OneSentence;
			sourceTree = "<group>";
		};
		03C873DF2E0E6D8A006253B3 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				03C873E02E0E6DB1006253B3 /* WHOneSentenceViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		03C873E22E0E72EC006253B3 /* Manager */ = {
			isa = PBXGroup;
			children = (
				03C873E32E0E7329006253B3 /* WHRouterOneSentenceResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		03CCA7082E78072000D5925F /* ViewController */ = {
			isa = PBXGroup;
			children = (
				03CCA7072E78072000D5925F /* WHSOPDetailsViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		03CCA7092E78072000D5925F /* SOP */ = {
			isa = PBXGroup;
			children = (
				03CCA70C2E780A2D00D5925F /* View */,
				03CCA7082E78072000D5925F /* ViewController */,
			);
			path = SOP;
			sourceTree = "<group>";
		};
		03CCA70C2E780A2D00D5925F /* View */ = {
			isa = PBXGroup;
			children = (
				03CCA70B2E780A2D00D5925F /* WHSOPDetailsButtonCell.swift */,
				03CCA70E2E78162C00D5925F /* WHSOPDetailsImgInfoView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		03D7A5B32E1CF10B00A78658 /* View */ = {
			isa = PBXGroup;
			children = (
				03D7A5AE2E1CF10B00A78658 /* WHTimeLineClipView.swift */,
				03D7A5B02E1CF10B00A78658 /* WHTimeLineProgressView.swift */,
				03D7A5B22E1CF10B00A78658 /* WHTimeLineTrackView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		2027DB0B2BC4EFEE00625AD4 /* Manager */ = {
			isa = PBXGroup;
			children = (
				2027DB0C2BC4F06300625AD4 /* WHRouterImgToImgResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		2044EB362B87374700324E1D /* Manager */ = {
			isa = PBXGroup;
			children = (
				2044EB372B87384800324E1D /* WHRouterAIClearResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		2070BA202B283BD100DE80E0 /* Request */ = {
			isa = PBXGroup;
			children = (
				2070BA212B283BFF00DE80E0 /* WHInpaindingRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		2070BA232B283D3600DE80E0 /* Model */ = {
			isa = PBXGroup;
			children = (
				2070BA242B283D5A00DE80E0 /* WHInpaindingConfigModel.swift */,
				2070BA262B284DF000DE80E0 /* WHInpaindingDoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		20D1C16B2BB2C516004B3DEE /* ImageToImage */ = {
			isa = PBXGroup;
			children = (
				2027DB0B2BC4EFEE00625AD4 /* Manager */,
				20D1C1742BB66E5A004B3DEE /* Request */,
				20D1C16F2BB2D2FE004B3DEE /* View */,
				20D1C16C2BB2C538004B3DEE /* ViewController */,
			);
			path = ImageToImage;
			sourceTree = "<group>";
		};
		20D1C16C2BB2C538004B3DEE /* ViewController */ = {
			isa = PBXGroup;
			children = (
				20D1C16D2BB2C56C004B3DEE /* WHImageToImageViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		20D1C16F2BB2D2FE004B3DEE /* View */ = {
			isa = PBXGroup;
			children = (
				20D1C1702BB2E3DC004B3DEE /* WHImgToImgSelectImgCell.swift */,
				2027DB092BC3D9BE00625AD4 /* WHImageToImageInputCell.swift */,
				2027DB0E2BC5556800625AD4 /* WHImgToImgZoomCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		20D1C1742BB66E5A004B3DEE /* Request */ = {
			isa = PBXGroup;
			children = (
				20D1C1752BB66E73004B3DEE /* WHImageToImageRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		20DA02CD2B492ED7004C5898 /* View */ = {
			isa = PBXGroup;
			children = (
				20DA02CE2B49359A004C5898 /* WHHomeHotCreationCell.swift */,
				20DA02D22B493F23004C5898 /* WHHomeAiCreationCell.swift */,
				20DA02D02B493DFF004C5898 /* WHHomeCellManager.swift */,
				954A4E252BA307550037C440 /* WHHomeToolCell.swift */,
				2470BACA2E702F1000531815 /* WHHomeFlowCollectionViewCell.swift */,
				241006022E758422009D7682 /* WHHomeFlowFilterButton.swift */,
				241006042E77AD58009D7682 /* WHHomeFlowPopMenu.swift */,
				2498C0192E79344C007FC3EF /* WHBlurImageView.swift */,
				2498C01D2E7934F8007FC3EF /* WHHomeFullScreenFlowTableViewCell.swift */,
				2498C0232E795CB6007FC3EF /* UIImageView+Extension.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		20FE35C32AD0251900789CF6 /* Model */ = {
			isa = PBXGroup;
			children = (
				20FE35C42AD0251900789CF6 /* WHHomeConfigModel.swift */,
				20F607D62B3BFA3B00C97663 /* WHHomeConfigCacheModel.swift */,
				241006002E73ECFE009D7682 /* WHCodableModel.swift */,
				241005F92E72D8AB009D7682 /* WHHomeFlowModel.swift */,
				2498C01C2E7934F8007FC3EF /* WHHomeFullScreenFlowDataViewModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		20FE35C62AD0252700789CF6 /* Request */ = {
			isa = PBXGroup;
			children = (
				20FE35C72AD0252700789CF6 /* WHHomeRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		2334F4402E0A51C7000C4F50 /* Manager */ = {
			isa = PBXGroup;
			children = (
				2334F43F2E0A51C7000C4F50 /* WHRouterVideoHudResponder.swift */,
				0361786B2E13AD2B006A751A /* WHMediaManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		2334F4412E0A51C7000C4F50 /* VideoHud */ = {
			isa = PBXGroup;
			children = (
				0361786D2E13AE65006A751A /* TimeLine */,
				23A47DB62E0E883800D774DB /* Request */,
				034EE7C32E0A9995009E0587 /* Resource */,
				034EE7C62E0A9995009E0587 /* View */,
				034EE7BE2E0A993D009E0587 /* ViewController */,
				2334F4402E0A51C7000C4F50 /* Manager */,
			);
			path = VideoHud;
			sourceTree = "<group>";
		};
		2366C6662C8EA18700FDBF67 /* AIUpScaler */ = {
			isa = PBXGroup;
			children = (
				2366C6682C8EA28B00FDBF67 /* Mananger */,
				95D919382C9D4C85004DD5B2 /* Model */,
				95D919332C9D45FB004DD5B2 /* View */,
				2366C6692C8EA28B00FDBF67 /* Request */,
				2366C6672C8EA28B00FDBF67 /* ViewController */,
			);
			path = AIUpScaler;
			sourceTree = "<group>";
		};
		2366C6672C8EA28B00FDBF67 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				2366C66D2C8EA33F00FDBF67 /* WHAIUpScalerPreViewController.swift */,
				2366C6712C8EEE0500FDBF67 /* WHAIUpScalerViewController.swift */,
				95ED10112CA3E8D30056C11A /* WHAIUpScalerDetailViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		2366C6682C8EA28B00FDBF67 /* Mananger */ = {
			isa = PBXGroup;
			children = (
				2366C66B2C8EA2F900FDBF67 /* WHRouterAIUpScalerResponder.swift */,
				95ED10172CA505240056C11A /* WHAIUSIKManager.swift */,
				2386F4F62E13D6C10055CF97 /* WHAIUpScalerUploadResource.swift */,
			);
			path = Mananger;
			sourceTree = "<group>";
		};
		2366C6692C8EA28B00FDBF67 /* Request */ = {
			isa = PBXGroup;
			children = (
				2366C66F2C8EDC6000FDBF67 /* WHAIUpScalerRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		23A47DB62E0E883800D774DB /* Request */ = {
			isa = PBXGroup;
			children = (
				23A47DB72E0E887500D774DB /* WHVideoHudRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		2415E2762E5742A000E60836 /* report */ = {
			isa = PBXGroup;
			children = (
				2415E2772E5742B500E60836 /* WHSmartCutoutReport.swift */,
			);
			path = report;
			sourceTree = "<group>";
		};
		2415E2792E58172000E60836 /* Utils */ = {
			isa = PBXGroup;
			children = (
				2415E27A2E58174500E60836 /* WHImageUploader.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		241816872E4C7BC00075B516 /* Engine */ = {
			isa = PBXGroup;
			children = (
				241816862E4C7BC00075B516 /* WHSmartCutoutEngine.swift */,
				244F911F2E52F68F00A6AA4B /* WHSmartCutoutEngine+MTIK.swift */,
			);
			path = Engine;
			sourceTree = "<group>";
		};
		241816892E4C9A0D0075B516 /* Request */ = {
			isa = PBXGroup;
			children = (
				2418168A2E4C9A320075B516 /* WHSmartCutoutRequest.swift */,
				248C9A882E5D7630000CCE49 /* WHSmartCutoutRequestObservable.swift */,
				244F91142E4DBA4900A6AA4B /* RxWHRequest.swift */,
				244F911D2E509AFB00A6AA4B /* SmartCutoutSmearHistory.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		24295DB62E7AABCF00365D09 /* Router */ = {
			isa = PBXGroup;
			children = (
				24295DB72E7AABE500365D09 /* WHHomeFullScreenResponder.swift */,
			);
			path = Router;
			sourceTree = "<group>";
		};
		244F91162E501F9400A6AA4B /* UIKit+Extensions */ = {
			isa = PBXGroup;
			children = (
				244F91172E501FAB00A6AA4B /* UIColors.swift */,
				248C9A8A2E5DAF86000CCE49 /* UIImage+Alpha.swift */,
			);
			path = "UIKit+Extensions";
			sourceTree = "<group>";
		};
		24D9D9C02E46FC630066BBC1 /* SmartCutout */ = {
			isa = PBXGroup;
			children = (
				2415E2792E58172000E60836 /* Utils */,
				2415E2762E5742A000E60836 /* report */,
				244F91162E501F9400A6AA4B /* UIKit+Extensions */,
				241816892E4C9A0D0075B516 /* Request */,
				241816872E4C7BC00075B516 /* Engine */,
				24D9D9DB2E4B2E090066BBC1 /* ViewController */,
				24D9D9CE2E4758FE0066BBC1 /* Router */,
				24D9D9CD2E4758F10066BBC1 /* View */,
			);
			path = SmartCutout;
			sourceTree = "<group>";
		};
		24D9D9CD2E4758F10066BBC1 /* View */ = {
			isa = PBXGroup;
			children = (
				244F911B2E50438E00A6AA4B /* WHSmartCutoutRoundButton.swift */,
				24D9D9E52E4B44800066BBC1 /* WHSmartCutoutSavedToolBar.swift */,
				24D9D9D52E49D4060066BBC1 /* WHSmartCutoutBottomToolBar.swift */,
				24D9D9CF2E499CA10066BBC1 /* WHSmartCutoutSlider.swift */,
				24D9D9C92E4737DA0066BBC1 /* WHSliderDetailCard.swift */,
				24D9D9C72E4734FD0066BBC1 /* WHSmartCutoutBottomView.swift */,
				24D9D9D92E4B1F6F0066BBC1 /* WHSmartCutoutNavigationBar.swift */,
				2418168C2E4CB2030075B516 /* WHImageKitCanvasView.swift */,
				2415E2742E5706A800E60836 /* UIViewController+Extensions.swift */,
				2415E27C2E5C09C700E60836 /* CircularCursorView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		24D9D9CE2E4758FE0066BBC1 /* Router */ = {
			isa = PBXGroup;
			children = (
				24D9D9C12E46FC950066BBC1 /* WHSmartCutoutRouterResponder.swift */,
				24D9D9C32E471B890066BBC1 /* WHCommonRoouterResponder.swift */,
			);
			path = Router;
			sourceTree = "<group>";
		};
		24D9D9DB2E4B2E090066BBC1 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				24D9D9E32E4B42F10066BBC1 /* WHSmartCutoutSaveResultViewController.swift */,
				24D9D9C52E472BF30066BBC1 /* WHSmartCutoutViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		24F8C4F52E7272CC001D7156 /* App */ = {
			isa = PBXGroup;
			children = (
				24F8C4F62E7272F0001D7156 /* UIApplication+Extension.swift */,
				24F8C4FA2E72752F001D7156 /* UIDevice+Extension.swift */,
				24F8C5002E72C9A8001D7156 /* WHAccountManager+Extension.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		8C725E5EC89D296CD43B5231 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0346950F2D9257E50025B1DA /* PhotosUI.framework */,
				9927A0DD64277EAFDD23AB11 /* Pods_MeituWhee.framework */,
				96AC3BBD82A11BCC687B8990 /* Pods_NotificationService.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8EC90FC9A9D2FD8314014518 /* Pods */ = {
			isa = PBXGroup;
			children = (
				65E6E1952EA4193F93A93A62 /* Pods-MeituWhee.debug.xcconfig */,
				6BD40224DA8A682B059D32B9 /* Pods-MeituWhee.release.xcconfig */,
				B61DB9DEF4B1B8B1F4F3D446 /* Pods-NotificationService.debug.xcconfig */,
				DCF9F3218D8FDADE1312047B /* Pods-NotificationService.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		9511259D2AB83CD9001B834A /* Module */ = {
			isa = PBXGroup;
			children = (
				03C4AEB72D8BF2ED00AD1E71 /* WHRouterDelegateManager.swift */,
				95BCB1CE2AA9C3DD00A76B9C /* Base */,
				951125A02AB83D4D001B834A /* Home */,
				9511259F2AB83D48001B834A /* Inspiration */,
				9511259E2AB83D38001B834A /* Mine */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		9511259E2AB83D38001B834A /* Mine */ = {
			isa = PBXGroup;
			children = (
				953681FF2BC9366C00E3943E /* Manager */,
				955064C32ADFBBD6005AA274 /* Model */,
				955064BE2ADCE68E005AA274 /* Request */,
				00858AAF2ABBDD8A00A10201 /* View */,
				951125A32AB83DB3001B834A /* VIewController */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		9511259F2AB83D48001B834A /* Inspiration */ = {
			isa = PBXGroup;
			children = (
				95DC20632AC3DA360026E3D3 /* Search */,
				951125A22AB83DAE001B834A /* VIewController */,
			);
			path = Inspiration;
			sourceTree = "<group>";
		};
		951125A02AB83D4D001B834A /* Home */ = {
			isa = PBXGroup;
			children = (
				24295DB62E7AABCF00365D09 /* Router */,
				24F8C4F52E7272CC001D7156 /* App */,
				20DA02CD2B492ED7004C5898 /* View */,
				20FE35C62AD0252700789CF6 /* Request */,
				20FE35C32AD0251900789CF6 /* Model */,
				951125A12AB83D9A001B834A /* ViewController */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		951125A12AB83D9A001B834A /* ViewController */ = {
			isa = PBXGroup;
			children = (
				24F8C4F32E71A18D001D7156 /* WHHomeVisualEffectsManager.swift */,
				951125A42AB83EA6001B834A /* WHHomeViewController.swift */,
				2470BAD02E70315600531815 /* WHHomeFlowViewController.swift */,
				2470BAD42E7033CB00531815 /* WHFlowContentViewController.swift */,
				2498C01B2E7934F8007FC3EF /* WHHomeFullScreenViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		951125A22AB83DAE001B834A /* VIewController */ = {
			isa = PBXGroup;
			children = (
				951125A62AB83F36001B834A /* WHInspirationViewController.swift */,
			);
			path = VIewController;
			sourceTree = "<group>";
		};
		951125A32AB83DB3001B834A /* VIewController */ = {
			isa = PBXGroup;
			children = (
				200C7F322ADD28DF00E4DCCE /* WHSettingAboutUsViewController.swift */,
				0064CABE2AA9EFFD007EC639 /* WHMineViewController.swift */,
				0064181A2ABEBC6500933945 /* WHSettingViewController.swift */,
				00857A362AC1386B0090BC7E /* WHMineContentViewController.swift */,
				206DFF322AFF3CAB001593DE /* WHAuthorityManageViewController.swift */,
				959009C12B47D6A200F1DC7A /* WHMineWorksViewController.swift */,
				03E8B9D12D7D8A8F0097557C /* WHRewardsViewController.swift */,
			);
			path = VIewController;
			sourceTree = "<group>";
		};
		953681FF2BC9366C00E3943E /* Manager */ = {
			isa = PBXGroup;
			children = (
				953682002BC9370600E3943E /* WHRouterHistoryResponder.swift */,
				00767D0B2C204D9D00547237 /* WHMineManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		953682022BCF741500E3943E /* ToImageDetail */ = {
			isa = PBXGroup;
			children = (
				008DB4212C22D2C700EB3EBF /* Model */,
				953682062BCF769D00E3943E /* Request */,
				953682042BCF768800E3943E /* View */,
				953682032BCF767500E3943E /* ViewController */,
			);
			path = ToImageDetail;
			sourceTree = "<group>";
		};
		953682032BCF767500E3943E /* ViewController */ = {
			isa = PBXGroup;
			children = (
				953682072BCF770D00E3943E /* WHToImageDetailViewController.swift */,
				0083252A2C2E897F0092E6E2 /* WHModelDetailViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		953682042BCF768800E3943E /* View */ = {
			isa = PBXGroup;
			children = (
				007D5E202C2946D100FE7197 /* WHMoreAlterView.swift */,
				007D5E222C29742800FE7197 /* WHMoreEditeAlterView.swift */,
				9536820D2BCFFA8A00E3943E /* WHToImageDetailView.swift */,
				953682092BCFF0EF00E3943E /* WHDetailBottomView.swift */,
				008DB4242C252C5000EB3EBF /* WHDetailMyBottoView.swift */,
				953682172BCFFEED00E3943E /* WHDetailImagesCell.swift */,
				0083252C2C2EA8760092E6E2 /* WHModelImageCell.swift */,
				953682112BCFFDD700E3943E /* WHDetailUserInfoCell.swift */,
				953682132BCFFDFD00E3943E /* WHDetailOtherCreateInfoCell.swift */,
				953682152BCFFE0A00E3943E /* WHDetailMyCreateInfoCell.swift */,
				008DB41D2C22D21700EB3EBF /* WHCreateInfoView.swift */,
				0083252E2C2EB5550092E6E2 /* WHModelDetailView.swift */,
				9FFCD7852CBD38EC00074C84 /* WHToImageDtailFailedTips.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		953682062BCF769D00E3943E /* Request */ = {
			isa = PBXGroup;
			children = (
			);
			path = Request;
			sourceTree = "<group>";
		};
		954A4E192BA05C3A0037C440 /* BaseAlert */ = {
			isa = PBXGroup;
			children = (
				23B3217B2DFAA10400BAF701 /* WHCommonToastAlertResponder.swift */,
				954A4E1A2BA05D4A0037C440 /* CommonUseAlert */,
				95DC2B052AD00EA40026E3D3 /* userAgreement */,
				95D3DBAE2AFDD99800DD3FEC /* UpdateAlert */,
			);
			path = BaseAlert;
			sourceTree = "<group>";
		};
		954A4E1A2BA05D4A0037C440 /* CommonUseAlert */ = {
			isa = PBXGroup;
			children = (
				954A4E1B2BA05D790037C440 /* WHCommonUseAlertModel.swift */,
				954A4E212BA178970037C440 /* WHCommonUseAlertRequest.swift */,
				954A4E1F2BA1759D0037C440 /* WHCommonUseAlertManager.swift */,
			);
			path = CommonUseAlert;
			sourceTree = "<group>";
		};
		954A4E2D2BAC0C4A0037C440 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				954A4E282BA7EEC10037C440 /* WHTextToImageRequest.swift */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		954A4E342BAC11680037C440 /* TextToImage */ = {
			isa = PBXGroup;
			children = (
				954A4E352BAC11680037C440 /* Model */,
				954A4E372BAC11680037C440 /* View */,
				954A4E422BAC11680037C440 /* Request */,
				954A4E442BAC11680037C440 /* ViewController */,
			);
			path = TextToImage;
			sourceTree = "<group>";
		};
		954A4E352BAC11680037C440 /* Model */ = {
			isa = PBXGroup;
			children = (
				954A4E362BAC11680037C440 /* WHTextToImageModel.swift */,
				2366D1B92CBA668A00FDBF67 /* WHShareInfoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		954A4E372BAC11680037C440 /* View */ = {
			isa = PBXGroup;
			children = (
				954A4E392BAC11680037C440 /* WHTextToImageView.swift */,
				167EACBB2C37E38F00F175B1 /* WHDoubleSliderView.swift */,
				954A4E3A2BAC11680037C440 /* WHCreateImageCountCell.swift */,
				954A4E3B2BAC11680037C440 /* WHTextToImageInputCell.swift */,
				954A4E3C2BAC11680037C440 /* WHCreateImageHigherCell.swift */,
				954A4E3D2BAC11680037C440 /* WHVisionModelCell.swift */,
				167EACB92C3690D700F175B1 /* WHDisclaimersCell.swift */,
				954A4E3E2BAC11680037C440 /* WHStyleStrengthView.swift */,
				954A4E3F2BAC11680037C440 /* WHCommonUploadView.swift */,
				954A4E402BAC11680037C440 /* WHCreateImageStyleCell.swift */,
				954A4E412BAC11680037C440 /* WHCreateImageSizeCell.swift */,
				954A4E382BAC11680037C440 /* WHCreateImageControlCell.swift */,
				953681FB2BC3D1D800E3943E /* WHControlCellSubviews.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		954A4E422BAC11680037C440 /* Request */ = {
			isa = PBXGroup;
			children = (
				954A4E432BAC11680037C440 /* WHTextToImageRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		954A4E442BAC11680037C440 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				954A4E452BAC11680037C440 /* WHTextToImageViewController.swift */,
				95AFED512BB1582500297588 /* WHStyleTabViewController.swift */,
				95AFED4F2BAD96C900297588 /* WHStyleFlowViewController.swift */,
				953681FD2BC412CD00E3943E /* WHReferTypeViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		955064B72AD52A0C005AA274 /* ConfigManager */ = {
			isa = PBXGroup;
			children = (
				955064B82AD52A55005AA274 /* WHAppConfigManager.swift */,
			);
			path = ConfigManager;
			sourceTree = "<group>";
		};
		955064BE2ADCE68E005AA274 /* Request */ = {
			isa = PBXGroup;
			children = (
				955064BF2ADCE6B0005AA274 /* WHMineRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		955064C32ADFBBD6005AA274 /* Model */ = {
			isa = PBXGroup;
			children = (
				955064C42ADFBDF7005AA274 /* WHCommonUserInfo.swift */,
				2083ED032B35A0B100FA42D1 /* WHMineVisitorModel.swift */,
				03E8B9D32D7D90D00097557C /* WHRewardsInfoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		9575776D2B871A8100127A82 /* AIClear */ = {
			isa = PBXGroup;
			children = (
				035127902DD473A700B880F8 /* View */,
				2044EB362B87374700324E1D /* Manager */,
				957577712B871AA500127A82 /* Request */,
				957577702B871A9F00127A82 /* Model */,
				9575776E2B871A9700127A82 /* ViewController */,
			);
			path = AIClear;
			sourceTree = "<group>";
		};
		9575776E2B871A9700127A82 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				957577722B871D4100127A82 /* WHAIClearViewController.swift */,
				2044EB342B87342F00324E1D /* WHAIClearPreViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		957577702B871A9F00127A82 /* Model */ = {
			isa = PBXGroup;
			children = (
				957577782B8DB57B00127A82 /* WHAIClearModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		957577712B871AA500127A82 /* Request */ = {
			isa = PBXGroup;
			children = (
				957577762B888F9000127A82 /* WHAIClearRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		959009CD2B4E446000F1DC7A /* Manager */ = {
			isa = PBXGroup;
			children = (
				959009D02B4E46B400F1DC7A /* Fadable.swift */,
				959009CE2B4E44DB00F1DC7A /* WHVideoDetailPlayManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		959740742B396AFB0027D29B /* AIVideo */ = {
			isa = PBXGroup;
			children = (
				959009CD2B4E446000F1DC7A /* Manager */,
				959740AA2B453E190027D29B /* Request */,
				9597408B2B3D19D10027D29B /* Model */,
				959740782B39791A0027D29B /* View */,
				959740772B3979080027D29B /* ViewController */,
			);
			path = AIVideo;
			sourceTree = "<group>";
		};
		959740772B3979080027D29B /* ViewController */ = {
			isa = PBXGroup;
			children = (
				959740752B396B470027D29B /* WHAICreateVideoViewController.swift */,
				20BBE0AA2B4D4B4D0053F2A9 /* WHAIVideoDetailViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		959740782B39791A0027D29B /* View */ = {
			isa = PBXGroup;
			children = (
				959740792B397A6D0027D29B /* WHAICreatVideoView.swift */,
				9597407B2B398D6E0027D29B /* WHAICreateVideoInputCell.swift */,
				9597407D2B398DB90027D29B /* WHAICreateVideoSizeCell.swift */,
				9597407F2B398E150027D29B /* WHAICreateVideoCameCell.swift */,
				95C290312B55246E00A66E16 /* WHAICreateVideoCameTextCell.swift */,
				959740812B398E470027D29B /* WHAICreateVideoUploadCell.swift */,
				959740852B3BC9870027D29B /* WHAICreateVideoTabView.swift */,
				959740892B3C10FD0027D29B /* WHAICreateVideoTipsView.swift */,
				959009BF2B46CD8D00F1DC7A /* WHAICreatVideoZoomCell.swift */,
				20BBE0AC2B4D71830053F2A9 /* WHAIVideoDetailCreationCell.swift */,
				959009D22B4E473C00F1DC7A /* WHVideoPlayerView.swift */,
				959009D42B4E6C1E00F1DC7A /* WHAIVideoDetailVideoCell.swift */,
				20BBE0AE2B4E8E860053F2A9 /* WHDetailViewCommonMoreView.swift */,
				95A389582B62333100332D7B /* WHAICreateVideoFormulaCell.swift */,
				95756B462B6CDFA400127A82 /* WHComonInputView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		9597408B2B3D19D10027D29B /* Model */ = {
			isa = PBXGroup;
			children = (
				9597408C2B3D19EB0027D29B /* WHAICreateVideoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		959740AA2B453E190027D29B /* Request */ = {
			isa = PBXGroup;
			children = (
				959740AB2B453E4B0027D29B /* WHAICreateVideoRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		95BC793B2B5A19B500436AF0 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				95CACD2A2B21C49A00354A81 /* WHInpaindingViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		95BC793C2B5A19D100436AF0 /* Manager */ = {
			isa = PBXGroup;
			children = (
				95CACD282B21C04200354A81 /* WHInpaindingManager.swift */,
				2070BA302B2950D400DE80E0 /* WHRouterInpaintingResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		95BCB1CE2AA9C3DD00A76B9C /* Base */ = {
			isa = PBXGroup;
			children = (
				03CCA7092E78072000D5925F /* SOP */,
				24D9D9C02E46FC630066BBC1 /* SmartCutout */,
				2334F4412E0A51C7000C4F50 /* VideoHud */,
				03C873DE2E0E6D62006253B3 /* OneSentence */,
				002B08DC2C1BD7F2007AFDB3 /* publish */,
				9FB593042D782BB900CFBA6E /* AILive */,
				953682022BCF741500E3943E /* ToImageDetail */,
				95D9F7112C91A0B7004DD5B2 /* AIExpand */,
				2366C6662C8EA18700FDBF67 /* AIUpScaler */,
				954A4E342BAC11680037C440 /* TextToImage */,
				20D1C16B2BB2C516004B3DEE /* ImageToImage */,
				954A4E192BA05C3A0037C440 /* BaseAlert */,
				9575776D2B871A8100127A82 /* AIClear */,
				959740742B396AFB0027D29B /* AIVideo */,
				95CACD262B21BF9D00354A81 /* Inpainding */,
				955064B72AD52A0C005AA274 /* ConfigManager */,
				95DC0B662ABB0C6F0026E3D3 /* Flow */,
				95BCB1CF2AA9C41900A76B9C /* ViewController */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		95BCB1CF2AA9C41900A76B9C /* ViewController */ = {
			isa = PBXGroup;
			children = (
				20AF4EF22C0761570087FFD9 /* WHWebViewController.swift */,
				95BF6B162AB8324500B13E2B /* WHLanguageSetViewController.swift */,
				0045E4342ACE8522008064AE /* WHRouterSetViewController.swift */,
				200BBCC72AFE19C700FB89F2 /* WHCIADebugViewController.swift */,
				95BCB1D02AA9C46900A76B9C /* WHTabBarController.swift */,
				95D9F7182C91A25C004DD5B2 /* WHAICommonViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		95BF6B0C2AB5947300B13E2B /* Resources */ = {
			isa = PBXGroup;
			children = (
				03CCA7122E783DDE00D5925F /* sop_details_again.json */,
				03626BC32D8195CD003A6712 /* RewardAnimate */,
				20DEE5B52BA2FD8200B89A7B /* LaunchScreen_en.storyboard */,
				20DEE5B22BA2FD7600B89A7B /* LaunchScreen_cn.storyboard */,
				9575777B2B9089B800127A82 /* aiClear.webp */,
				95BC83B02B5E5E0100436AF0 /* input_lenovo.json */,
				959009D62B4E732600F1DC7A /* video_loading.json */,
				95887B6E2B148A4600B0ABE7 /* theme_wheeApp.json */,
				955A585D2B0C4CAE0068054F /* radius.json */,
				2096DC9B2AF243C600D5C80A /* tcmpp-ios-configurations-whee.json */,
				95C9E0332AF11BC100D8E24A /* feed_status.json */,
				955064C12ADE7F9A005AA274 /* Loading_Logo.json */,
				00310B722AB1AE7900795F4F /* candy.ttf */,
				95BF6B0D2AB594FF00B13E2B /* Localizable */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		95BF6B0D2AB594FF00B13E2B /* Localizable */ = {
			isa = PBXGroup;
			children = (
				95BF6B092AB5917B00B13E2B /* InfoPlist.strings */,
				95BF6B042AB5907700B13E2B /* Localizable.strings */,
			);
			path = Localizable;
			sourceTree = "<group>";
		};
		95CACD262B21BF9D00354A81 /* Inpainding */ = {
			isa = PBXGroup;
			children = (
				95BC793C2B5A19D100436AF0 /* Manager */,
				2070BA232B283D3600DE80E0 /* Model */,
				2070BA202B283BD100DE80E0 /* Request */,
				003F32962B22E58100F8EA09 /* view */,
				95BC793B2B5A19B500436AF0 /* ViewController */,
			);
			path = Inpainding;
			sourceTree = "<group>";
		};
		95D3DBAE2AFDD99800DD3FEC /* UpdateAlert */ = {
			isa = PBXGroup;
			children = (
				95D3DBAF2AFDD9E800DD3FEC /* WHUpgradeAlertView.swift */,
				95D3DBB32AFF282600DD3FEC /* WHUpgradeInfo.swift */,
			);
			path = UpdateAlert;
			sourceTree = "<group>";
		};
		95D9192A2C92E5FD004DD5B2 /* Manager */ = {
			isa = PBXGroup;
			children = (
				95D9192F2C92F260004DD5B2 /* WHRouterAIExpandResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		95D919332C9D45FB004DD5B2 /* View */ = {
			isa = PBXGroup;
			children = (
				9520215C2CA152D70011B379 /* WHAIUSImageClarityView.swift */,
				952021602CA290220011B379 /* WHAIUSValueSettingView.swift */,
				95ED04B32CA2C8F90056C11A /* WHAIUSDemoTypeView.swift */,
				95ED04B52CA2CA790056C11A /* WHAIUSAdaptStyleView.swift */,
				95ED10132CA3FF190056C11A /* WHAIUSDetailPicTypeView.swift */,
				95ED10152CA425570056C11A /* WHAIUSCanvasView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		95D919382C9D4C85004DD5B2 /* Model */ = {
			isa = PBXGroup;
			children = (
				9FDDE7A12DB6276C00279D3A /* WHPopupAlertModel.swift */,
				95D919392C9D4C93004DD5B2 /* WHAIUpScalerModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		95D9F7112C91A0B7004DD5B2 /* AIExpand */ = {
			isa = PBXGroup;
			children = (
				95D9192A2C92E5FD004DD5B2 /* Manager */,
				95D9F7152C91A109004DD5B2 /* Request */,
				95D9F7142C91A100004DD5B2 /* Model */,
				95D9F7122C91A0C5004DD5B2 /* ViewController */,
			);
			path = AIExpand;
			sourceTree = "<group>";
		};
		95D9F7122C91A0C5004DD5B2 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				95D9F7162C91A15A004DD5B2 /* WHAIExpandViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		95D9F7142C91A100004DD5B2 /* Model */ = {
			isa = PBXGroup;
			children = (
				95D902742C92C519004DD5B2 /* WHAIExpandModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		95D9F7152C91A109004DD5B2 /* Request */ = {
			isa = PBXGroup;
			children = (
				95D919312C9A8203004DD5B2 /* WHAIExpandRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		95DC0B662ABB0C6F0026E3D3 /* Flow */ = {
			isa = PBXGroup;
			children = (
				95DC0B6C2ABB0C9F0026E3D3 /* Reuqest */,
				95DC0B6B2ABB0C9B0026E3D3 /* Model */,
				95DC0B6A2ABB0C980026E3D3 /* Vew */,
				95DC0B692ABB0C940026E3D3 /* ViewController */,
			);
			path = Flow;
			sourceTree = "<group>";
		};
		95DC0B692ABB0C940026E3D3 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				95DC0B812ABB0D040026E3D3 /* WHFlowViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		95DC0B6A2ABB0C980026E3D3 /* Vew */ = {
			isa = PBXGroup;
			children = (
				95DC0B792ABB0CF50026E3D3 /* WHFlowBaseCardView.swift */,
				95DC0B7A2ABB0CF50026E3D3 /* WHFlowBaseCollectionViewCell.swift */,
				95DC0B7B2ABB0CF50026E3D3 /* WHFlowBaseDisplayEntity.swift */,
				95DC0B7C2ABB0CF50026E3D3 /* WHFlowLayout.swift */,
			);
			path = Vew;
			sourceTree = "<group>";
		};
		95DC0B6B2ABB0C9B0026E3D3 /* Model */ = {
			isa = PBXGroup;
			children = (
				95DC0B712ABB0CDA0026E3D3 /* WHFlowAbstract.swift */,
				95DC0B6F2ABB0CDA0026E3D3 /* WHFlowManager.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		95DC0B6C2ABB0C9F0026E3D3 /* Reuqest */ = {
			isa = PBXGroup;
			children = (
				95DC0B772ABB0CEB0026E3D3 /* WHFlowRequest.swift */,
			);
			path = Reuqest;
			sourceTree = "<group>";
		};
		95DC20632AC3DA360026E3D3 /* Search */ = {
			isa = PBXGroup;
			children = (
				95DC209C2AC58AEC0026E3D3 /* Manager */,
				95DC20652AC3DA620026E3D3 /* View */,
				95DC20642AC3DA450026E3D3 /* ViewController */,
			);
			path = Search;
			sourceTree = "<group>";
		};
		95DC20642AC3DA450026E3D3 /* ViewController */ = {
			isa = PBXGroup;
			children = (
				95DC20662AC3DA830026E3D3 /* WHSearchViewController.swift */,
				95DC207D2AC51C050026E3D3 /* WHSearchDetailViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		95DC20652AC3DA620026E3D3 /* View */ = {
			isa = PBXGroup;
			children = (
				95DC20792AC430500026E3D3 /* WHSearchSuggestionCell.swift */,
				95DC20752AC41C060026E3D3 /* MTSuggestCollectionView.swift */,
				95DC20682AC3FD8B0026E3D3 /* WHCustomSearchBarView.swift */,
				95DC209F2ACE5CEC0026E3D3 /* WHSearchHistoryCell.swift */,
				95DC20A12ACE625D0026E3D3 /* WHSearchHistroyTitleCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		95DC209C2AC58AEC0026E3D3 /* Manager */ = {
			isa = PBXGroup;
			children = (
			);
			path = Manager;
			sourceTree = "<group>";
		};
		95DC2B052AD00EA40026E3D3 /* userAgreement */ = {
			isa = PBXGroup;
			children = (
				95DC2B0A2AD105E40026E3D3 /* WHUserAgreementAlertView.swift */,
				954FCD3B2AF25FBB00F08C50 /* WHUserAgreeAbroadView.swift */,
				95D3DBAC2AFB387000DD3FEC /* WHAgreementAlertOptionCell.swift */,
			);
			path = userAgreement;
			sourceTree = "<group>";
		};
		9F56039B2D8991FB00D56C8C /* View */ = {
			isa = PBXGroup;
			children = (
				9F56039D2D8991FF00D56C8C /* WHAILiveLoadTypeAlert.swift */,
				9F56039A2D8991FB00D56C8C /* WHAILiveTypeCollectionViewCell.swift */,
				039CCD8C2DE8537A0079A5F8 /* WHAILivePreCollectionCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		9FB592FD2D782BB900CFBA6E /* Manager */ = {
			isa = PBXGroup;
			children = (
				9FE748472DA4D02500764130 /* WHRouterAILiveDetailResponder.swift */,
				9FB592FC2D782BB900CFBA6E /* WHRouterAILiveResponder.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		9FB592FF2D782BB900CFBA6E /* Model */ = {
			isa = PBXGroup;
			children = (
				9FB592FE2D782BB900CFBA6E /* WHAILiveModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		9FB593012D782BB900CFBA6E /* Request */ = {
			isa = PBXGroup;
			children = (
				9F3E6C362D7E8BBA00ACFC0C /* WHDownLoadManager.swift */,
				9FB593002D782BB900CFBA6E /* WHAILiveRequest.swift */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		9FB593032D782BB900CFBA6E /* ViewController */ = {
			isa = PBXGroup;
			children = (
				9F3E6C342D7AE01F00ACFC0C /* WHAILiveDetailViewController.swift */,
				9FB593022D782BB900CFBA6E /* WHAILiveViewController.swift */,
				039CCD8A2DE8358F0079A5F8 /* WHAILivePreViewController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		9FB593042D782BB900CFBA6E /* AILive */ = {
			isa = PBXGroup;
			children = (
				9F56039B2D8991FB00D56C8C /* View */,
				9FB592FD2D782BB900CFBA6E /* Manager */,
				9FB592FF2D782BB900CFBA6E /* Model */,
				9FB593012D782BB900CFBA6E /* Request */,
				9FB593032D782BB900CFBA6E /* ViewController */,
			);
			path = AILive;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00389ED62AA5A59600E6089A /* MeituWhee */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00389EEB2AA5A59800E6089A /* Build configuration list for PBXNativeTarget "MeituWhee" */;
			buildPhases = (
				5BAD0D0A23F23F2B8CC7CED8 /* [CP] Check Pods Manifest.lock */,
				00389ED32AA5A59600E6089A /* Sources */,
				00389ED42AA5A59600E6089A /* Frameworks */,
				00389ED52AA5A59600E6089A /* Resources */,
				B2E3EFC2AF02618C62A92E9D /* [CP] Embed Pods Frameworks */,
				553B6913B97FB9F1E0B9A3D0 /* [CP] Copy Pods Resources */,
				03995B312DA4BF7E001C856D /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				03995B2F2DA4BF7E001C856D /* PBXTargetDependency */,
			);
			name = MeituWhee;
			productName = MeituWhee;
			productReference = 00389ED72AA5A59600E6089A /* MeituWhee.app */;
			productType = "com.apple.product-type.application";
		};
		03995B282DA4BF7E001C856D /* NotificationService */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 03995B352DA4BF7E001C856D /* Build configuration list for PBXNativeTarget "NotificationService" */;
			buildPhases = (
				6FC5442E4DDE389C50A0891B /* [CP] Check Pods Manifest.lock */,
				03995B252DA4BF7E001C856D /* Sources */,
				03995B262DA4BF7E001C856D /* Frameworks */,
				03995B272DA4BF7E001C856D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationService;
			productName = NotificationService;
			productReference = 03995B292DA4BF7E001C856D /* NotificationService.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		00389ECF2AA5A59600E6089A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					00389ED62AA5A59600E6089A = {
						CreatedOnToolsVersion = 14.3.1;
					};
					03995B282DA4BF7E001C856D = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 00389ED22AA5A59600E6089A /* Build configuration list for PBXProject "MeituWhee" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 00389ECE2AA5A59600E6089A;
			productRefGroup = 00389ED82AA5A59600E6089A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				00389ED62AA5A59600E6089A /* MeituWhee */,
				03995B282DA4BF7E001C856D /* NotificationService */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00389ED52AA5A59600E6089A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				95BF6B022AB5907700B13E2B /* Localizable.strings in Resources */,
				95BF6B072AB5917B00B13E2B /* InfoPlist.strings in Resources */,
				95C9E0342AF11BC100D8E24A /* feed_status.json in Resources */,
				034EE7C72E0A9995009E0587 /* A.mp4 in Resources */,
				034EE7C82E0A9995009E0587 /* A_H.mp4 in Resources */,
				00059A292AB1B35F0084A471 /* candy.ttf in Resources */,
				03626BC42D8195CD003A6712 /* reward_star.json in Resources */,
				03626BC52D8195CD003A6712 /* img_0.png in Resources */,
				20DEE5B72BA2FD8200B89A7B /* LaunchScreen_en.storyboard in Resources */,
				95BC83B12B5E5E0100436AF0 /* input_lenovo.json in Resources */,
				03CCA7132E783DDE00D5925F /* sop_details_again.json in Resources */,
				20AF59FB2C086B480087FFD9 /* PrivacyInfo.xcprivacy in Resources */,
				16176D332D881F14004D1C3A /* mtseclicense.pixel in Resources */,
				95887B6F2B148A4600B0ABE7 /* theme_wheeApp.json in Resources */,
				2096DC9C2AF243C600D5C80A /* tcmpp-ios-configurations-whee.json in Resources */,
				00389EE42AA5A59800E6089A /* Assets.xcassets in Resources */,
				9575777C2B9089B800127A82 /* aiClear.webp in Resources */,
				00389EE22AA5A59600E6089A /* Main.storyboard in Resources */,
				20DEE5B42BA2FD7600B89A7B /* LaunchScreen_cn.storyboard in Resources */,
				955064C22ADE7F9A005AA274 /* Loading_Logo.json in Resources */,
				959009D72B4E732600F1DC7A /* video_loading.json in Resources */,
				955A585E2B0C4CAE0068054F /* radius.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03995B272DA4BF7E001C856D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		553B6913B97FB9F1E0B9A3D0 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5BAD0D0A23F23F2B8CC7CED8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MeituWhee-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		6FC5442E4DDE389C50A0891B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationService-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B2E3EFC2AF02618C62A92E9D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MeituWhee/Pods-MeituWhee-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00389ED32AA5A59600E6089A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				959740AC2B453E4B0027D29B /* WHAICreateVideoRequest.swift in Sources */,
				95756B472B6CDFA400127A82 /* WHComonInputView.swift in Sources */,
				95DC20A22ACE625D0026E3D3 /* WHSearchHistroyTitleCell.swift in Sources */,
				23A47DBA2E0EAA0B00D774DB /* WHVideoHudVideoPlayView.swift in Sources */,
				2415E2752E5706A800E60836 /* UIViewController+Extensions.swift in Sources */,
				00389EF02AA5AAE800E6089A /* WHLaunchTaskManager.swift in Sources */,
				24D9D9E42E4B42F10066BBC1 /* WHSmartCutoutSaveResultViewController.swift in Sources */,
				20DA02CF2B49359A004C5898 /* WHHomeHotCreationCell.swift in Sources */,
				955064C02ADCE6B0005AA274 /* WHMineRequest.swift in Sources */,
				24D9D9C62E472BF30066BBC1 /* WHSmartCutoutViewController.swift in Sources */,
				953681FE2BC412CD00E3943E /* WHReferTypeViewController.swift in Sources */,
				95DC207A2AC430500026E3D3 /* WHSearchSuggestionCell.swift in Sources */,
				9F3E6C372D7E8BD700ACFC0C /* WHDownLoadManager.swift in Sources */,
				20BBE0AB2B4D4B4D0053F2A9 /* WHAIVideoDetailViewController.swift in Sources */,
				955064B62AD41C36005AA274 /* WHLaunchConfigTask.swift in Sources */,
				03E8B9DA2D7D97330097557C /* WHRewardsInviteBoostCell.swift in Sources */,
				954A4E262BA307550037C440 /* WHHomeToolCell.swift in Sources */,
				954A4E1C2BA05D790037C440 /* WHCommonUseAlertModel.swift in Sources */,
				95D3DBAD2AFB387000DD3FEC /* WHAgreementAlertOptionCell.swift in Sources */,
				244F91152E4DBA4900A6AA4B /* RxWHRequest.swift in Sources */,
				005CADFF2B271F12004315D2 /* WHCreatPicView.swift in Sources */,
				95DC0B802ABB0CF50026E3D3 /* WHFlowLayout.swift in Sources */,
				0045E4352ACE8522008064AE /* WHRouterSetViewController.swift in Sources */,
				9F56039E2D89921700D56C8C /* WHAILiveLoadTypeAlert.swift in Sources */,
				24D9D9D62E49D4060066BBC1 /* WHSmartCutoutBottomToolBar.swift in Sources */,
				00B3C8932B26DE3C00B037B5 /* WHCreatPicCellManager.swift in Sources */,
				95AFED522BB1582500297588 /* WHStyleTabViewController.swift in Sources */,
				2027DB0A2BC3D9BE00625AD4 /* WHImageToImageInputCell.swift in Sources */,
				95ED04B62CA2CA790056C11A /* WHAIUSAdaptStyleView.swift in Sources */,
				95D9F7192C91A25C004DD5B2 /* WHAICommonViewController.swift in Sources */,
				2093CC772BF25EA9003E7421 /* WHLaunchEditorTask.swift in Sources */,
				9536820E2BCFFA8A00E3943E /* WHToImageDetailView.swift in Sources */,
				957577732B871D4100127A82 /* WHAIClearViewController.swift in Sources */,
				20FE35C52AD0251900789CF6 /* WHHomeConfigModel.swift in Sources */,
				2498C01A2E79344C007FC3EF /* WHBlurImageView.swift in Sources */,
				20BBE0AF2B4E8E860053F2A9 /* WHDetailViewCommonMoreView.swift in Sources */,
				007D5E232C29742800FE7197 /* WHMoreEditeAlterView.swift in Sources */,
				2334F4422E0A51C7000C4F50 /* WHRouterVideoHudResponder.swift in Sources */,
				206DFF352AFF5FCA001593DE /* WHAuthorityManageTableViewCell.swift in Sources */,
				95D9F7172C91A15A004DD5B2 /* WHAIExpandViewController.swift in Sources */,
				2070BA222B283BFF00DE80E0 /* WHInpaindingRequest.swift in Sources */,
				959009C22B47D6A200F1DC7A /* WHMineWorksViewController.swift in Sources */,
				95DC20672AC3DA830026E3D3 /* WHSearchViewController.swift in Sources */,
				24F8C4FB2E72752F001D7156 /* UIDevice+Extension.swift in Sources */,
				00389EDF2AA5A59600E6089A /* ViewController.swift in Sources */,
				9597407C2B398D6E0027D29B /* WHAICreateVideoInputCell.swift in Sources */,
				002B08DF2C1BD93C007AFDB3 /* WHPublishViewController.swift in Sources */,
				953682012BC9370600E3943E /* WHRouterHistoryResponder.swift in Sources */,
				2044EB352B87342F00324E1D /* WHAIClearPreViewController.swift in Sources */,
				008DB41E2C22D21700EB3EBF /* WHCreateInfoView.swift in Sources */,
				00858AB12ABBDDA600A10201 /* WHMineHeadView.swift in Sources */,
				9FDDE7A22DB6276F00279D3A /* WHPopupAlertModel.swift in Sources */,
				95C290322B55246E00A66E16 /* WHAICreateVideoCameTextCell.swift in Sources */,
				2366C6722C8EEE0500FDBF67 /* WHAIUpScalerViewController.swift in Sources */,
				95D9193A2C9D4C93004DD5B2 /* WHAIUpScalerModel.swift in Sources */,
				95BCB1CB2AA9A74A00A76B9C /* WHLaunchVipSdkTask.swift in Sources */,
				20DA02D32B493F23004C5898 /* WHHomeAiCreationCell.swift in Sources */,
				032C3A852DD71D9F00B22F5C /* WHAIClearSegmentedView.swift in Sources */,
				00389EFC2AA6F6DD00E6089A /* WHLaunchDoraemonTask.swift in Sources */,
				034EE7C02E0A9982009E0587 /* WHVideoHudDetailViewController.swift in Sources */,
				241005FA2E72D8AB009D7682 /* WHHomeFlowModel.swift in Sources */,
				034EE7C92E0A9995009E0587 /* WHVideoHudDetailSlider.swift in Sources */,
				244F91182E501FAB00A6AA4B /* UIColors.swift in Sources */,
				034EE7CA2E0A9995009E0587 /* WHVideoHudDetailFailView.swift in Sources */,
				954A4E482BAC11680037C440 /* WHTextToImageView.swift in Sources */,
				00389EF52AA5ABF200E6089A /* WHLaunchAccountTask.swift in Sources */,
				241006052E77AD58009D7682 /* WHHomeFlowPopMenu.swift in Sources */,
				034EE7D02E0AA812009E0587 /* WHVideoHudClipBottomView.swift in Sources */,
				24D9D9CA2E4737DA0066BBC1 /* WHSliderDetailCard.swift in Sources */,
				244F91202E52F68F00A6AA4B /* WHSmartCutoutEngine+MTIK.swift in Sources */,
				95ED04B42CA2C8F90056C11A /* WHAIUSDemoTypeView.swift in Sources */,
				03BF23332E28C5410065FAE8 /* WHProgressLoadingView.swift in Sources */,
				034EE7CC2E0A9F2A009E0587 /* WHVideoHudClipViewController.swift in Sources */,
				039CCD8B2DE8358F0079A5F8 /* WHAILivePreViewController.swift in Sources */,
				2386F4F72E13D6C10055CF97 /* WHAIUpScalerUploadResource.swift in Sources */,
				0064181B2ABEBC6500933945 /* WHSettingViewController.swift in Sources */,
				23A47DB52E0D7B2B00D774DB /* WHVideoHudStyleSelectView.swift in Sources */,
				95DC0B822ABB0D040026E3D3 /* WHFlowViewController.swift in Sources */,
				954A4E522BAC11680037C440 /* WHTextToImageViewController.swift in Sources */,
				954A4E472BAC11680037C440 /* WHCreateImageControlCell.swift in Sources */,
				23A47DB82E0E887500D774DB /* WHVideoHudRequest.swift in Sources */,
				03C873E42E0E7329006253B3 /* WHRouterOneSentenceResponder.swift in Sources */,
				2418168D2E4CB2030075B516 /* WHImageKitCanvasView.swift in Sources */,
				953682162BCFFE0A00E3943E /* WHDetailMyCreateInfoCell.swift in Sources */,
				954A4E4A2BAC11680037C440 /* WHTextToImageInputCell.swift in Sources */,
				95DC2B0F2AD14B6A0026E3D3 /* WHLaunchAlertDispathTask.swift in Sources */,
				03D7A5B42E1CF10B00A78658 /* WHTimeLineClipView.swift in Sources */,
				03D7A5B62E1CF10B00A78658 /* WHTimeLineProgressView.swift in Sources */,
				03D7A5B82E1CF10B00A78658 /* WHTimeLineTrackView.swift in Sources */,
				00B3C8912B26B4CD00B037B5 /* WHCreatPicInputCell.swift in Sources */,
				248C9A892E5D7630000CCE49 /* WHSmartCutoutRequestObservable.swift in Sources */,
				03E8B9D22D7D8A8F0097557C /* WHRewardsViewController.swift in Sources */,
				2470BAD12E70315600531815 /* WHHomeFlowViewController.swift in Sources */,
				03E8B9D82D7D96E30097557C /* WHRewardsCheckInCell.swift in Sources */,
				2083ED042B35A0B100FA42D1 /* WHMineVisitorModel.swift in Sources */,
				00767D0A2C20401300547237 /* WHPublishAlterView.swift in Sources */,
				9F3E6C352D7AE03600ACFC0C /* WHAILiveDetailViewController.swift in Sources */,
				24D9D9C82E4734FD0066BBC1 /* WHSmartCutoutBottomView.swift in Sources */,
				95D3DBB02AFDD9E800DD3FEC /* WHUpgradeAlertView.swift in Sources */,
				03CCA70F2E78162C00D5925F /* WHSOPDetailsImgInfoView.swift in Sources */,
				2366D1BA2CBA668A00FDBF67 /* WHShareInfoModel.swift in Sources */,
				20D1C1712BB2E3DC004B3DEE /* WHImgToImgSelectImgCell.swift in Sources */,
				9597407E2B398DB90027D29B /* WHAICreateVideoSizeCell.swift in Sources */,
				03E8B9D62D7D96AB0097557C /* WHRewardsMembershipCell.swift in Sources */,
				9F3E6C3D2D800EC200ACFC0C /* WHInviteCodeAlert.swift in Sources */,
				954A4E492BAC11680037C440 /* WHCreateImageCountCell.swift in Sources */,
				034EE7CE2E0AA402009E0587 /* WHMediaEditEntranceViewController.swift in Sources */,
				954A4E4D2BAC11680037C440 /* WHStyleStrengthView.swift in Sources */,
				95DC0B742ABB0CDA0026E3D3 /* WHFlowManager.swift in Sources */,
				00389F002AA6FCB500E6089A /* WHLaunchRootVCTask.swift in Sources */,
				959009D12B4E46B400F1DC7A /* Fadable.swift in Sources */,
				95DC2B0B2AD105E40026E3D3 /* WHUserAgreementAlertView.swift in Sources */,
				167EACBC2C37E38F00F175B1 /* WHDoubleSliderView.swift in Sources */,
				959740822B398E470027D29B /* WHAICreateVideoUploadCell.swift in Sources */,
				039CCD8D2DE8537A0079A5F8 /* WHAILivePreCollectionCell.swift in Sources */,
				959740862B3BC9870027D29B /* WHAICreateVideoTabView.swift in Sources */,
				959009D52B4E6C1E00F1DC7A /* WHAIVideoDetailVideoCell.swift in Sources */,
				2470BAD52E7033CB00531815 /* WHFlowContentViewController.swift in Sources */,
				957577792B8DB57B00127A82 /* WHAIClearModel.swift in Sources */,
				031B29342E227E16000E1882 /* WHVideoFrameImageLoader.swift in Sources */,
				95CACD2B2B21C49A00354A81 /* WHInpaindingViewController.swift in Sources */,
				95ED10182CA505240056C11A /* WHAIUSIKManager.swift in Sources */,
				24295DB82E7AABE500365D09 /* WHHomeFullScreenResponder.swift in Sources */,
				0064CAB92AA9B3EA007EC639 /* WHLaunchEnviromentTask.swift in Sources */,
				0064CABF2AA9EFFD007EC639 /* WHMineViewController.swift in Sources */,
				248C9A8B2E5DAF86000CCE49 /* UIImage+Alpha.swift in Sources */,
				95BF6B172AB8324500B13E2B /* WHLanguageSetViewController.swift in Sources */,
				00389EFA2AA5B27900E6089A /* WHLaunchAnalyticsTask.swift in Sources */,
				951125A72AB83F36001B834A /* WHInspirationViewController.swift in Sources */,
				24F8C4F42E71A18D001D7156 /* WHHomeVisualEffectsManager.swift in Sources */,
				241006012E73ECFE009D7682 /* WHCodableModel.swift in Sources */,
				95D3DBB42AFF282600DD3FEC /* WHUpgradeInfo.swift in Sources */,
				002B08E82C1C2F7F007AFDB3 /* WHPublishControlCell.swift in Sources */,
				20D1C1762BB66E73004B3DEE /* WHImageToImageRequest.swift in Sources */,
				0361786C2E13AD2B006A751A /* WHMediaManager.swift in Sources */,
				003F329C2B2311E900F8EA09 /* WHCreatPicCountCell.swift in Sources */,
				002B08E42C1BECF2007AFDB3 /* WHPublishImageCell.swift in Sources */,
				03E8B9DC2D7D989E0097557C /* WHRewardsSectionHeaderView.swift in Sources */,
				9597407A2B397A6D0027D29B /* WHAICreatVideoView.swift in Sources */,
				959740802B398E150027D29B /* WHAICreateVideoCameCell.swift in Sources */,
				24D9D9C22E46FC950066BBC1 /* WHSmartCutoutRouterResponder.swift in Sources */,
				2415E27B2E58174500E60836 /* WHImageUploader.swift in Sources */,
				2070BA312B2950D400DE80E0 /* WHRouterInpaintingResponder.swift in Sources */,
				954A4E502BAC11680037C440 /* WHCreateImageSizeCell.swift in Sources */,
				036178702E13AEFA006A751A /* WHTimeLineConfig.swift in Sources */,
				9F3E6C3F2D80314100ACFC0C /* WHInvitedFriendAlert.swift in Sources */,
				9520215D2CA152D70011B379 /* WHAIUSImageClarityView.swift in Sources */,
				953682082BCF770D00E3943E /* WHToImageDetailViewController.swift in Sources */,
				00767D0C2C204D9D00547237 /* WHMineManager.swift in Sources */,
				2044EB382B87384800324E1D /* WHRouterAIClearResponder.swift in Sources */,
				03C873E12E0E6DB1006253B3 /* WHOneSentenceViewController.swift in Sources */,
				9FB593052D782BB900CFBA6E /* WHAILiveViewController.swift in Sources */,
				9FB593062D782BB900CFBA6E /* WHAILiveRequest.swift in Sources */,
				9FB593072D782BB900CFBA6E /* WHAILiveModel.swift in Sources */,
				9FB593082D782BB900CFBA6E /* WHRouterAILiveResponder.swift in Sources */,
				9597408A2B3C10FD0027D29B /* WHAICreateVideoTipsView.swift in Sources */,
				954A4E462BAC11680037C440 /* WHTextToImageModel.swift in Sources */,
				953682182BCFFEED00E3943E /* WHDetailImagesCell.swift in Sources */,
				2470BACB2E702F1000531815 /* WHHomeFlowCollectionViewCell.swift in Sources */,
				954A4E4B2BAC11680037C440 /* WHCreateImageHigherCell.swift in Sources */,
				20D1C16E2BB2C56C004B3DEE /* WHImageToImageViewController.swift in Sources */,
				002B08E62C1C1886007AFDB3 /* WHPublishInputCell.swift in Sources */,
				954A4E512BAC11680037C440 /* WHTextToImageRequest.swift in Sources */,
				2415E2782E5742B500E60836 /* WHSmartCutoutReport.swift in Sources */,
				007D5E212C2946D100FE7197 /* WHMoreAlterView.swift in Sources */,
				955064C52ADFBDF7005AA274 /* WHCommonUserInfo.swift in Sources */,
				954A4E222BA178980037C440 /* WHCommonUseAlertRequest.swift in Sources */,
				2366C66E2C8EA33F00FDBF67 /* WHAIUpScalerPreViewController.swift in Sources */,
				244F911E2E509AFB00A6AA4B /* SmartCutoutSmearHistory.swift in Sources */,
				20BBE0AD2B4D71830053F2A9 /* WHAIVideoDetailCreationCell.swift in Sources */,
				2366C66C2C8EA2F900FDBF67 /* WHRouterAIUpScalerResponder.swift in Sources */,
				20FD82B72B273264008E68AA /* WHInpaindingEditView.swift in Sources */,
				005CADFD2B26FF1A004315D2 /* WHCtreatPicButtonCell.swift in Sources */,
				230593B32E7910A400B424CE /* WHLaunchBusinessCommonTask.swift in Sources */,
				24D9D9E62E4B44800066BBC1 /* WHSmartCutoutSavedToolBar.swift in Sources */,
				035127922DD473C000B880F8 /* WHAIClearToolsView.swift in Sources */,
				954A4E4C2BAC11680037C440 /* WHVisionModelCell.swift in Sources */,
				20B197C02AE7E3DE003100DC /* WHQRCodeMiniController.swift in Sources */,
				00389EDB2AA5A59600E6089A /* AppDelegate.swift in Sources */,
				95AFED502BAD96C900297588 /* WHStyleFlowViewController.swift in Sources */,
				9597408D2B3D19EB0027D29B /* WHAICreateVideoModel.swift in Sources */,
				206AE7642AB5612600FA8483 /* WHLaunchCIATask.swift in Sources */,
				959009C02B46CD8D00F1DC7A /* WHAICreatVideoZoomCell.swift in Sources */,
				95DC20692AC3FD8B0026E3D3 /* WHCustomSearchBarView.swift in Sources */,
				2415E27D2E5C09C700E60836 /* CircularCursorView.swift in Sources */,
				95ED10122CA3E8D30056C11A /* WHAIUpScalerDetailViewController.swift in Sources */,
				24F8C5012E72C9A8001D7156 /* WHAccountManager+Extension.swift in Sources */,
				953682142BCFFDFD00E3943E /* WHDetailOtherCreateInfoCell.swift in Sources */,
				03D7A5AB2E1CBAD100A78658 /* WHVideoHudClipTimeCell.swift in Sources */,
				952021612CA290220011B379 /* WHAIUSValueSettingView.swift in Sources */,
				200BBCC82AFE19C700FB89F2 /* WHCIADebugViewController.swift in Sources */,
				0381B11C2E0F9AC40034FBAD /* WHOneSentenceInputView.swift in Sources */,
				95DC0B7E2ABB0CF50026E3D3 /* WHFlowBaseCollectionViewCell.swift in Sources */,
				206DFF332AFF3CAB001593DE /* WHAuthorityManageViewController.swift in Sources */,
				20F607D72B3BFA3B00C97663 /* WHHomeConfigCacheModel.swift in Sources */,
				20FE35C82AD0252700789CF6 /* WHHomeRequest.swift in Sources */,
				241006032E758422009D7682 /* WHHomeFlowFilterButton.swift in Sources */,
				00389EF82AA5B0DA00E6089A /* WHLaunchManager.swift in Sources */,
				2070BA272B284DF000DE80E0 /* WHInpaindingDoModel.swift in Sources */,
				24F8C4F72E7272F0001D7156 /* UIApplication+Extension.swift in Sources */,
				9F56039C2D8991FB00D56C8C /* WHAILiveTypeCollectionViewCell.swift in Sources */,
				959009D32B4E473C00F1DC7A /* WHVideoPlayerView.swift in Sources */,
				00389EFE2AA6FC2600E6089A /* WHLaunchWindowTask.swift in Sources */,
				24D9D9D02E499CA10066BBC1 /* WHSmartCutoutSlider.swift in Sources */,
				03626BB82D812647003A6712 /* WHMineButtonView.swift in Sources */,
				008325292C2D37AE0092E6E2 /* WHDetailInfoManager.swift in Sources */,
				0083252F2C2EB5550092E6E2 /* WHModelDetailView.swift in Sources */,
				23B3217C2DFAA10400BAF701 /* WHCommonToastAlertResponder.swift in Sources */,
				954A4E4F2BAC11680037C440 /* WHCreateImageStyleCell.swift in Sources */,
				008DB4232C22D2DA00EB3EBF /* WHMyCreatInfoModel.swift in Sources */,
				167EACBA2C3690D700F175B1 /* WHDisclaimersCell.swift in Sources */,
				95DC0B7D2ABB0CF50026E3D3 /* WHFlowBaseCardView.swift in Sources */,
				959740762B396B470027D29B /* WHAICreateVideoViewController.swift in Sources */,
				95DC207E2AC51C050026E3D3 /* WHSearchDetailViewController.swift in Sources */,
				953682122BCFFDD700E3943E /* WHDetailUserInfoCell.swift in Sources */,
				2498C01E2E7934F8007FC3EF /* WHHomeFullScreenFlowDataViewModel.swift in Sources */,
				2498C01F2E7934F8007FC3EF /* WHHomeFullScreenFlowTableViewCell.swift in Sources */,
				2498C0202E7934F8007FC3EF /* WHHomeFullScreenViewController.swift in Sources */,
				954A4E202BA1759D0037C440 /* WHCommonUseAlertManager.swift in Sources */,
				00821C2C2B29888C00C19764 /* WHPaindingPlayView.swift in Sources */,
				23A47DB32E0D574200D774DB /* WHVideoHudCreateViewController.swift in Sources */,
				03CCA70A2E78072000D5925F /* WHSOPDetailsViewController.swift in Sources */,
				95DC0B762ABB0CDA0026E3D3 /* WHFlowAbstract.swift in Sources */,
				00857A372AC1386B0090BC7E /* WHMineContentViewController.swift in Sources */,
				200BBCC42AFB7B3600FB89F2 /* WHLaunchAPNSTask.swift in Sources */,
				0069ADCC2AC172A800BB06DD /* WHMineSegemateView.swift in Sources */,
				00825F6D2AC2A76B00923D98 /* WHSettingTableCell.swift in Sources */,
				03CCA70D2E780A2D00D5925F /* WHSOPDetailsButtonCell.swift in Sources */,
				0083252B2C2E897F0092E6E2 /* WHModelDetailViewController.swift in Sources */,
				959009CF2B4E44DB00F1DC7A /* WHVideoDetailPlayManager.swift in Sources */,
				95DC20A02ACE5CEC0026E3D3 /* WHSearchHistoryCell.swift in Sources */,
				2366C6702C8EDC6000FDBF67 /* WHAIUpScalerRequest.swift in Sources */,
				2386F4F52E13BFA60055CF97 /* WHVideoHudPreViewController.swift in Sources */,
				2418168B2E4C9A320075B516 /* WHSmartCutoutRequest.swift in Sources */,
				20AF4EF32C0761570087FFD9 /* WHWebViewController.swift in Sources */,
				95A389592B62333100332D7B /* WHAICreateVideoFormulaCell.swift in Sources */,
				95DA81B12B26A9F000E8D8C0 /* WHInpaindingBarView.swift in Sources */,
				95DC0B7F2ABB0CF50026E3D3 /* WHFlowBaseDisplayEntity.swift in Sources */,
				95D919322C9A8203004DD5B2 /* WHAIExpandRequest.swift in Sources */,
				955064B92AD52A55005AA274 /* WHAppConfigManager.swift in Sources */,
				200C7F332ADD28DF00E4DCCE /* WHSettingAboutUsViewController.swift in Sources */,
				95CACD292B21C04200354A81 /* WHInpaindingManager.swift in Sources */,
				954FCD3C2AF25FBB00F08C50 /* WHUserAgreeAbroadView.swift in Sources */,
				2027DB0D2BC4F06300625AD4 /* WHRouterImgToImgResponder.swift in Sources */,
				0083252D2C2EA8760092E6E2 /* WHModelImageCell.swift in Sources */,
				03E8B9D42D7D90D00097557C /* WHRewardsInfoModel.swift in Sources */,
				953681FC2BC3D1D800E3943E /* WHControlCellSubviews.swift in Sources */,
				20DA02D12B493DFF004C5898 /* WHHomeCellManager.swift in Sources */,
				24D9D9DA2E4B1F6F0066BBC1 /* WHSmartCutoutNavigationBar.swift in Sources */,
				00389EF22AA5AB2800E6089A /* WHLaunchTask.swift in Sources */,
				03C4AEB82D8BF2ED00AD1E71 /* WHRouterDelegateManager.swift in Sources */,
				2027DB0F2BC5556800625AD4 /* WHImgToImgZoomCell.swift in Sources */,
				957577772B888F9000127A82 /* WHAIClearRequest.swift in Sources */,
				9FE748482DA4D06600764130 /* WHRouterAILiveDetailResponder.swift in Sources */,
				008DB4252C252C5000EB3EBF /* WHDetailMyBottoView.swift in Sources */,
				95ED10142CA3FF190056C11A /* WHAIUSDetailPicTypeView.swift in Sources */,
				95DC20762AC41C060026E3D3 /* MTSuggestCollectionView.swift in Sources */,
				954A4E4E2BAC11680037C440 /* WHCommonUploadView.swift in Sources */,
				95BCB1D12AA9C46900A76B9C /* WHTabBarController.swift in Sources */,
				002B08E22C1BE881007AFDB3 /* WHPublishView.swift in Sources */,
				9536820A2BCFF0EF00E3943E /* WHDetailBottomView.swift in Sources */,
				95D919302C92F260004DD5B2 /* WHRouterAIExpandResponder.swift in Sources */,
				2070BA252B283D5A00DE80E0 /* WHInpaindingConfigModel.swift in Sources */,
				9FFCD7862CBD38EC00074C84 /* WHToImageDtailFailedTips.swift in Sources */,
				95DC0B782ABB0CEB0026E3D3 /* WHFlowRequest.swift in Sources */,
				951125A52AB83EA6001B834A /* WHHomeViewController.swift in Sources */,
				00F48F2C2AA74CF4003505BE /* WHDebugViewController.swift in Sources */,
				95ED10162CA425570056C11A /* WHAIUSCanvasView.swift in Sources */,
				206AE7662AB5B3E700FA8483 /* WHLaunchSSOShareTask.swift in Sources */,
				244F911C2E50438E00A6AA4B /* WHSmartCutoutRoundButton.swift in Sources */,
				95D902752C92C519004DD5B2 /* WHAIExpandModel.swift in Sources */,
				2498C0242E795CB6007FC3EF /* UIImageView+Extension.swift in Sources */,
				241816882E4C7BC00075B516 /* WHSmartCutoutEngine.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		03995B252DA4BF7E001C856D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				035CF4432DA4EE420017EAF7 /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		03995B2F2DA4BF7E001C856D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 03995B282DA4BF7E001C856D /* NotificationService */;
			targetProxy = 03995B2E2DA4BF7E001C856D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		00389EE02AA5A59600E6089A /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				00389EE12AA5A59600E6089A /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		20DEE5B22BA2FD7600B89A7B /* LaunchScreen_cn.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				20DEE5B32BA2FD7600B89A7B /* Base */,
			);
			name = LaunchScreen_cn.storyboard;
			sourceTree = "<group>";
		};
		20DEE5B52BA2FD8200B89A7B /* LaunchScreen_en.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				20DEE5B62BA2FD8200B89A7B /* en */,
			);
			name = LaunchScreen_en.storyboard;
			sourceTree = "<group>";
		};
		95BF6B042AB5907700B13E2B /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				95BF6B032AB5907700B13E2B /* zh-Hans */,
				95BF6B0B2AB591AB00B13E2B /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		95BF6B092AB5917B00B13E2B /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				95BF6B082AB5917B00B13E2B /* zh-Hans */,
				95BF6B0A2AB591A200B13E2B /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00389EE92AA5A59800E6089A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		00389EEA2AA5A59800E6089A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		00389EEC2AA5A59800E6089A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65E6E1952EA4193F93A93A62 /* Pods-MeituWhee.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = MeituWhee/MeituWhee.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MeituWhee/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WHEE;
				INFOPLIST_KEY_NSCameraUsageDescription = "为了您可以使用相机拍摄照片进行头像更改，WHEE希望使用你的相机权限。";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "开启后才可以正常使用视频声音录制等功能";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "用于将您生成的图片或视频保存到相册";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "为了便于您在WHEE上查看上传储存最新照片或视频，请您选择“允许访问所有照片”";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen_en;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_ROOT}/Headers/Public\"",
					"-isystem",
					"\"${PODS_ROOT}/Headers/Public/FMDB\"",
					"-isystem",
					"\"${PODS_ROOT}/Headers/Public/FBSDKShareKit\"",
					"-iframework",
					"\"${PODS_ROOT}/AICodec/frameworks\"",
					"-iframework",
					"\"${PODS_ROOT}/AIDetectionPlugin/frameworks\"",
					"-iframework",
					"\"${PODS_ROOT}/AlipaySDK\"",
					"-iframework",
					"\"${PODS_ROOT}/DoraemonKit/iOS/DoraemonKit/Framework\"",
					"-iframework",
					"\"${PODS_ROOT}/MTABTesting\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTABTesting\"",
					"-iframework",
					"\"${PODS_ROOT}/MTAPM\"",
					"-iframework",
					"\"${PODS_ROOT}/MTAccount/Source/GeYan\"",
					"-iframework",
					"\"${PODS_ROOT}/MTAdAttribution\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTAdAttribution\"",
					"-iframework",
					"\"${PODS_ROOT}/MTAnalytics\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTAnalytics\"",
					"-iframework",
					"\"${PODS_ROOT}/MTAnalyticsBase\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTAnalyticsBase\"",
					"-iframework",
					"\"${PODS_ROOT}/MTCipherSuite\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTCipherSuite\"",
					"-iframework",
					"\"${PODS_ROOT}/MTDataFinder\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTDataFinder\"",
					"-iframework",
					"\"${PODS_ROOT}/MTGID\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTGID\"",
					"-iframework",
					"\"${PODS_ROOT}/MTIAPSDK\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTIAPSDK\"",
					"-iframework",
					"\"${PODS_ROOT}/MTImageKit/MTImageKit\"",
					"-iframework",
					"\"${PODS_ROOT}/MTMVCore\"",
					"-iframework",
					"\"${PODS_ROOT}/MTPayBase\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTPayBase/Core\"",
					"-iframework",
					"\"${PODS_ROOT}/MTSSOShareKit/MTSSOShareKit/SDKs/TencentOpenAPI\"",
					"-iframework",
					"\"${PODS_ROOT}/MTSSOShareKit/MTSSOShareKit/SDKs/XiaoHongShuOpenSDK\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTSSOShareKit/Xiaohongshu\"",
					"-iframework",
					"\"${PODS_ROOT}/MTSecLicense/ios\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTSecLicense\"",
					"-iframework",
					"\"${PODS_ROOT}/MTSig\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTSig\"",
					"-iframework",
					"\"${PODS_ROOT}/MTSubscriptionSDK\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/MTSubscriptionSDK\"",
					"-iframework",
					"\"${PODS_ROOT}/MVARExtension/frameworks\"",
					"-iframework",
					"\"${PODS_ROOT}/libMTPlayer\"",
					"-iframework",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/libMTPlayer\"",
					"-iframework",
					"\"${PODS_ROOT}/libcronet\"",
					"-iframework",
					"\"${PODS_ROOT}/libmanis/iOS/universal\"",
					"-iframework",
					"\"${PODS_ROOT}/libmtaiinterface/iOS\"",
					"-iframework",
					"\"${PODS_ROOT}/libmtcvlite/iOS_lite\"",
					"-iframework",
					"\"${PODS_ROOT}/libmtlab-base/iOS\"",
					"-iframework",
					"\"${PODS_ROOT}/mtlabrecord/apple/ios/shared/release/universal\"",
					"-iframework",
					"\"${PODS_ROOT}/mvgif/ios\"",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -Wno-error=non-modular-include-in-framework-module";
				PRODUCT_BUNDLE_IDENTIFIER = com.meitu.whee;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "MeituWhee/WHEE-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		00389EED2AA5A59800E6089A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6BD40224DA8A682B059D32B9 /* Pods-MeituWhee.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = MeituWhee/MeituWhee.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MeituWhee/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WHEE;
				INFOPLIST_KEY_NSCameraUsageDescription = "为了您可以使用相机拍摄照片进行头像更改，WHEE希望使用你的相机权限。";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "开启后才可以正常使用视频声音录制等功能";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "用于将您生成的图片或视频保存到相册";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "为了便于您在WHEE上查看上传储存最新照片或视频，请您选择“允许访问所有照片”";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen_en;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld_classic",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -Wno-error=non-modular-include-in-framework-module";
				PRODUCT_BUNDLE_IDENTIFIER = com.meitu.whee;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "MeituWhee/WHEE-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		03995B322DA4BF7E001C856D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B61DB9DEF4B1B8B1F4F3D446 /* Pods-NotificationService.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.meitu.whee.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		03995B332DA4BF7E001C856D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DCF9F3218D8FDADE1312047B /* Pods-NotificationService.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.meitu.whee.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00389ED22AA5A59600E6089A /* Build configuration list for PBXProject "MeituWhee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00389EE92AA5A59800E6089A /* Debug */,
				00389EEA2AA5A59800E6089A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		00389EEB2AA5A59800E6089A /* Build configuration list for PBXNativeTarget "MeituWhee" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00389EEC2AA5A59800E6089A /* Debug */,
				00389EED2AA5A59800E6089A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		03995B352DA4BF7E001C856D /* Build configuration list for PBXNativeTarget "NotificationService" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03995B322DA4BF7E001C856D /* Debug */,
				03995B332DA4BF7E001C856D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 00389ECF2AA5A59600E6089A /* Project object */;
}
