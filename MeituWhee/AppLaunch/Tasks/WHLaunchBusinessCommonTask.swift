//
//  WHLaunchBusinessCommonTask.swift
//  MeituWhee
//
//  Created by 王耀 on 2025/9/16.
//

import Foundation
import WHBusinessCommon

class WHLaunchBusinessCommonTask: WHLaunchTask {
    override func main() {
        WHBusinessCommonManager.shared.setUp()
    }
}

class WHBusinessCommonManager: NSObject {
    
    static let shared = WHBusinessCommonManager()
    
    func setUp() {
        WHBusinessCommonServicer.delegate = self
    }
}

extension WHBusinessCommonManager:WHBusinessCommonServiceDelegate {
    //打开订阅半窗
    func showSubMeidouWindow(_ controller: UIViewController, params: [String : Any],isSelectMeidou:Bool) {
        WHVipSdkManager.shared.showSubMeidouWindow(controller,params: params,isSelectMeidou:isSelectMeidou)
    }
    //打开我的作品页面
    func showMineWorksViewController(_ controller: UIViewController, worksType: Int, worksTabsType: WHBusinessCommon.WHTaskType) {
        let vc = WHMineWorksViewController.init()
        vc.worksType = WHMineWorksType(rawValue: worksType) ?? .myWorks
        vc.worksTabsType = worksTabsType
        controller.wh_show(vc)
    }
    //打开AI配方结果页
    func showAIFormulaResultViewController(_ controller: UIViewController, textListModelDic: [String : Any]) {
        if let model = WHTextListModel.yy_model(with: textListModelDic) {
            WHRouter.route(with: "wheeai://app/sop_details",params: ["model": model])
        } else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("数据错误，请重试！"))
        }
    }
}
