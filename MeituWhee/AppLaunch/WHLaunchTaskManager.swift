//
//  WHLaunchTaskManager.swift
//  MeituWhee
//
//  Created by xiaoqi on 2023/9/4.
//

import Foundation

class WHLaunchTaskManager: NSObject{
    static func launchTask() -> [WHLaunchTask] {
        let lanchArray: [WHLaunchTask] = [
            ///环境配置
            WHLaunchEnviromentTask(stage: .willFinishLaunching, context: .main),
            ///统计
            WHLaunchAnalyticsTask(stage: .willFinishLaunching, context: .main),
            ///灵问
            WHLaunchCIATask(stage: .didFinishLaunching, context: .main),
            ///大账号
            WHLaunchAccountTask(stage: .didFinishLaunching, context: .main),
            /// DoraemonKit
            WHLaunchDoraemonTask(stage: .didFinishLaunching, context: .main),
            /// 启动配置
            WHLaunchConfigTask(stage: .didFinishLaunching, context: .main),
            /// 推送
            WHLaunchAPNSTask(stage: .didFinishLaunching, context: .main),
            ///分享
            WHLaunchSSOShareTask(stage: .didFinishLaunching, context: .main),
            /// 设置window
            WHLaunchWindowTask(stage: .didFinishLaunching, context: .main),
            /// 设置root vc
            WHLaunchRootVCTask(stage: .didFinishLaunching, context: .main),
            /// 启动弹窗
            WHLaunchAlertDispathTask(stage: .didFinishLaunching, context: .main),
            /// vip订阅
            WHLaunchVipSdkTask(stage: .didFinishLaunching, context: .main),
            /// 视频播放
            WHLaunchMFFPlayerTask(stage: .didFinishLaunching, context: .asyncMain),
            /// Common库模块
            WHLaunchBusinessCommonTask(stage: .didFinishLaunching, context: .main),
            /// 编辑器模块
            WHLaunchEditorTask(stage: .didFinishLaunching, context: .main),
        ]
        return lanchArray
    }
}
