//
//  FlowAbstract.swift
//  swiftTest
//
//  Created by meitu on 2020/11/19.
//  Copyright © 2020 meitu. All rights reserved.
//

import UIKit
import YYModel
import MTLegoBlocksLib
import WHBaseLibrary
import WHBusinessCommon

public enum WHFeedType: Int {
    case feed = 1   // 灵感feed
    case unPublishFeed = 11  // 未发布feed
    case publishedFeed = 12  // 已发布feed
}

@objcMembers public class WHFlowAbstract: MTLegoBaseAbstract, YYModel, NSCoding {
    
    // 业务id
    public var templateId: String?
    // 秀秀feed_id
    public var feedId: String?
    // 算法msg_id 用于上报
    public var msgId: String = ""
    // 模型id
    public var modelId: String?
    // 任务种类：txt2img=文生图,img2img=图生图
    public var taskcategory: String?
    // 标题
    public var caption: String = ""
    // 描述
    public var desString: String = ""
    // 图片地址
    public var coverImg: String = ""
    // 图片同款原图
    public var originPic: String?
    // 图片数量
    public var picCount: Int = 0
    
    public var imageKey: URL?
    public var originImgKey: String?

    public var imageWidth: CGFloat = 0
    public var imageHeight: CGFloat = 0
    // 收藏数
    public var favorCount: Int = 0
    // 是否收藏 1-已被自己收藏 2-未被自己收藏
    public var isFavor: Int = 2
    // 是否可以创作同款 1可以0不可以
    public var canUseSame: Bool = false
    // 效果id
    public var effectId: Int = 0
    // 使用的风格模型ids
    public var styleModelIds: String = ""
    // 跳转详情页链接
    public var detailsUrl: String = ""
    // 一键应用模型跳转链接
    public var modelUrl: String = ""
    // 一键创建同款跳转链接
    public var templateUrl: String = ""
    
    public var statusMessage: String?
    //生成进度
    public var gcProgress: String = ""

    public var scm: String?
    
    public var user: WHUserInfo?
    
    public var resultVideos: WHResultVideos?
    
    public var liveTypeId : Int64 = 0
    var liveTypeName: String?
    
    public var feedTypeValue: Int = 1
    public lazy var feedType: WHFeedType = {
        var type:WHFeedType = .feed
        if let isMyFeed = config?.isMyFeed, isMyFeed == true {
            if status == 5 && feedId?.count ?? 0 > 0 {
                type = .publishedFeed
            } else {
                type = .unPublishFeed
            }
        }
        if config?.feedFromType == .model, status != 5 {
            type = .unPublishFeed
        }
        if config?.feedFromType == .history {
            type = .unPublishFeed
        }
        return type
    }()
    /// 创作同款数量
    public var effectCount: Int = 0
    
    /// 作品状态    1初始 2 处理中 3失败 4成功 5 发布 10 发布审核中（模型）
    public var status: Int = 5
    /// 超分任务：0-无超分任务，1-存在进行中超分任务，2-超分任务都已经完成
    public var hdStatus: Int = 0
    /// 超分任务：1-视频超清，2-live超清（注意：服务端下发的字段为task_type，跟本地历史定义的重了，这里使用taskSubType）
    public var taskSubType: Int = 0
    ///配置项
    public var config: MTFlowEntityConfig?
    
    public var originVideo: String = ""
    /// 视频大小。单位：MB
    var originVideoSize: CGFloat = 0
    
    public override var layoutStyleValue: Int {
        get {
            return WHFlowAbstract.getlayoutStyle(feedType)
        }
        set {
            super.layoutStyleValue = newValue
        }
    }
    
    public var coverColor: UIColor = .white
    public var colorNumber: String = "" {
        didSet {
            if colorNumber.count > 0 {
                self.coverColor = UIColor(wh_hexString: colorNumber)
            } else {
                self.coverColor = .white
            }
        }
    }
    // 内容类型
    public lazy var taskType: WHTaskType = {
        var type:WHTaskType = .textImg
        switch taskcategory {
        case "txt2img":
            type = .textImg
        case "img2img":
            type = .imgImg
        case "model":
            type = .model
        case "extend":
            type = .extend
        case "inpaint":
            type = .inpaint
        case "ai_video":
            type = .aiVideo
        case "ai_eraser":
            type = .aiEraser
        case "magicsr":
            type = .aiMagicsr
        case "ai_template":
            type = .aiTemplate
        case "project":
            type = .project
        case "image_to_live":
            type = .aiLive
        case "ai_video_magicsr":
            type = .aiVideoMagicsr
        case "ai_formula":
            type = .aiFormula
        default:
            type = .textImg
        }
        return type
    }()
    
    /// 模型默认强度
    var weight: Int = 0
    
    public required init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["user": WHUserInfo.classForCoder(),
                "resultVideos": WHResultVideos.classForCoder()
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "templateId": "id",
            "feedId": "feed_id",
            "msgId":"msg_id",
            "taskcategory":"task_category",
            "feedTypeValue":"item_type",
            "coverImg": "pic_url",
            "originPic": "origin_pic",
            "picCount": "pic_count",
            "imageWidth": "width",
            "imageHeight": "height",
            "caption": "title",
            "favorCount": "favor_count",
            "isFavor": "is_favor",
            "canUseSame": "can_use_same",
            "effectId": "effect_id",
            "scm": "scm",
            "styleModelIds": "style_model_ids",
            "status" : "status",
            "effectCount" : "template_use_count",
            "modelId" : "model_id",
            "detailsUrl" : "jump_url",
            "modelUrl" : "apply_style_jump_url",
            "templateUrl" : "apply_template_jump_url",
            "hdStatus" : "hd_status",
            "statusMessage" : "status_message",
            "gcProgress" : "progress",
            "desString" : "desc",
            "resultVideos" : "result_video",
            "liveTypeId" : "template_id",
            "liveTypeName": "template_name",
            "taskSubType": "task_type",
            "originVideo": "origin_video",
            "originVideoSize": "origin_video_size",
        ]
    }
    
    func getEffectCount() -> String {
        if effectCount >= 10000 {
            return countTrans(count: effectCount)
        }
        return "\(effectCount)"
    }
    
    func getFavorCount() -> String {
        if favorCount >= 10000 {
            return countTrans(count: favorCount)
        }
        return "\(favorCount)"
    }
    
    func getFavorWidth() -> CGFloat {
        var width = getFavorCount().getStringWidth(FlowBaseWidth - 8*2 , font: .systemFont(ofSize: 13))
        width = width < 26 ? 26 : width
       return width
    }
    
    func countTrans(count: Int) -> String {
        let num: Int = count
        let numberFormatter = NumberFormatter()
        numberFormatter.numberStyle = .decimal
        numberFormatter.minimumFractionDigits = 0
        numberFormatter.maximumFractionDigits = 1
        if num >= 10000 {
            let number = Double(num) / 10000.0
            return (numberFormatter.string(from: NSNumber(value: number)) ?? "") + "万"
        } else {
            return numberFormatter.string(from: NSNumber(value: num))!
        }
    }
}

extension WHFlowAbstract {

    public class func getlayoutStyle(_ feedType: WHFeedType) -> Int {
            return WHFlowLayout.feed.rawValue
    }

    public class func getlayoutStyle(_ feedValue: NSNumber) -> WHFlowLayout? {

        guard let feedType = WHFeedType.init(rawValue: feedValue.intValue) else {
            return WHFlowLayout.feed
        }
        return WHFlowLayout(rawValue: getlayoutStyle(feedType))
    }
}

@objcMembers
public class WHResultVideos: NSObject, YYModel, NSCoding {
    
    public var url: String = ""
    public var noWaterMarkUrl: String = ""
    public var cover: String = ""
    //视频状态：0-无结果,1-正常
    public var videoStatus: Int = 0
    /// 视频大小。单位：MB
    var size: CGFloat = 0
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "url"    : "url",
            "noWaterMarkUrl"    : "url_no_watermark",
            "cover"  : "cover",
            "videoStatus" : "video_status",
            "size": "size",
        ]
    }
    
}

