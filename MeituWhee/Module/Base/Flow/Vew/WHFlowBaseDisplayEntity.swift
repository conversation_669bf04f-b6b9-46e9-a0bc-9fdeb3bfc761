//
//  FlowBaseDisplayEntity.swift
//
//
//  Created by meitu on 2020/11/24.
//  Copyright © 2020 meitu. All rights reserved.
//

import UIKit
import MTLegoBlocksLib
import SDWebImage
import WHBaseLibrary
import WHBusinessCommon
import Lottie

let kTextColor = UIColor(wh_hexString:"#5C5F66")
let kRedBgColor = UIColor(wh_hexString:"#FD3960")
let kNormalFont = UIFont.systemFont(ofSize: 10, weight: .regular)
let kNormalFont11 = UIFont.systemFont(ofSize: 11, weight: .regular)
let kWeightFont11 = UIFont.systemFont(ofSize: 11, weight: .medium)
let kNormalFont13 = UIFont.systemFont(ofSize: 13, weight: .regular)

class WHFlowBaseDisplayEntity: MTLegoDisplayEntity {
    
    private var cellHeight: CGFloat = 0.0
    /// 保存视图的依赖关系
    private var layoutMap: [WHFlowViewType : CGFloat] = [:]
    
    public var allowLongPress: Bool?
    
    public var numbersOfLine: Int = 1
    
    public var coverScale: Bool = false
    // 是否我的feed
    public var isMyFeed: Bool = false
    //
    public var heightSpace: CGFloat = 0.0
    
    private var countWidth: CGFloat = 34
    
    private let leftSpace: CGFloat = 8, collectBtnWidth: CGFloat = 36, superWidth: CGFloat = 42
    
    //子视图 有顺序依赖关系 后视图基于前视图的定位
    var subViews:[WHFlowViewType] = []

    var ignoreView: [WHFlowViewType] = []
    
    lazy var workdLoadingView: WHWorksLoadingView = {
        let make = WHWorksLoadingView()
        make.tag = 11110
        return make
    }()
    
    private var liveTagIcon: UIImageView = {
        let icon = UIImageView()
        icon.image = UIImage(named: "icon_feed_live_tag")
        icon.tag = 11111
        return icon
    }()
    
    required init(with abstract: MTLegoBaseAbstract) {
        if let model = abstract as? WHFlowAbstract {
            numbersOfLine = model.config?.numbersOfLine ?? 1
            subViews = model.config?.subViews ?? []
            allowLongPress = model.config?.allowLongPress
            coverScale = model.config?.coverScale ?? false
            ignoreView = model.config?.ignoreView ?? []
            isMyFeed = model.config?.isMyFeed ?? false
        }
        self.countWidth = WHAppShareLanguage.wh_isLanguageZH() ? 34 : 24
        super.init(with: abstract)
    }
    
    override func creatDisplayInfo() {
        self.displayInfos = creatFlowDisplayInfos()
        let cardHeight = cellHeight + heightSpace // content的两个间距
        self.contentSize = CGSize(width: FlowBaseWidth , height: cardHeight)
        self.cardHeight = cardHeight
    }
}

//根据model创建info
extension WHFlowBaseDisplayEntity {
    
    func creatFlowDisplayInfos() -> [MTLegoDisplayInfo]? {
        
        guard let model = self.abstract as? WHFlowAbstract else {
            return nil
        }
        switch model.feedType {
        case .feed:
            if subViews.count == 0 {
                subViews = [.cover, .content, .effectButton, .collect, .picCount, .showOrigin]
            }
            heightSpace = model.config?.feedFromType == .model ? 16 + 12 : 16
        case .unPublishFeed:
            subViews =  [.placeholder,.cover, .picCount, .showOrigin, .worksStatus, .superPic, .playButton]
            heightSpace = 0
        case .publishedFeed:
            subViews = [.placeholder,.cover, .content, .collect, .picCount, .showOrigin, .collectCount, .worksStatus, .superPic, .playButton]
            heightSpace = 8 + 12
        }
        subViews = subViews.filter { !ignoreView.contains($0) }

        let infos = subViews.compactMap {[weak self] (type) -> MTLegoDisplayInfo? in
            guard let self = self else { return nil }
            let info = self.createSubInfo(type: type)
            if !fixedArray.contains(type) {
                self.cellHeight += info?.frame.height ?? 0
            }
            return info
        }
        return infos
    }
    
    func createSubInfo(type: WHFlowViewType) -> MTLegoDisplayInfo? {
        
        guard let data = abstract as? WHFlowAbstract else { return nil }
        var info: MTLegoDisplayInfo?
        switch type {
        case .placeholder:
            if data.taskType != .project {return nil}
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .image,
                                                          rect: CGRect(x: 0, y: 0, width: imageSize().width, height: imageSize().height),
                                                               tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let imageView = view as? UIImageView else { return }
                if isFirst {
                    imageView.layer.cornerRadius = 12
                    imageView.layer.masksToBounds = true
                    imageView.contentMode = .scaleAspectFill
                    imageView.image = UIImage(named: "wh_mine_feed_project_bg_image_normal")
                }
            }
        case .cover:
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .image,
                                                          rect: CGRect(x: 0, y: 0, width: imageSize().width, height: imageSize().height),
                                                               tag: type.rawValue)
            info?.viewDidLoadBlock = { [weak self] (view, model, isFirst) in
                guard let imageView = view as? UIImageView, let self = self else { return }
                if isFirst {
                    imageView.layer.cornerRadius = 12
                    imageView.layer.masksToBounds = true
                    if data.taskType == .project {
                        imageView.contentMode = .scaleAspectFit
                    } else {
                        imageView.contentMode = .scaleAspectFill
                    }
                    imageView.layer.borderWidth = 0.5
                    imageView.layer.borderColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.06).cgColor
                }
                self.resetop(type: type, view: imageView)
                imageView.viewWithTag(11110)?.removeFromSuperview()
                imageView.viewWithTag(11111)?.removeFromSuperview()
                
                let coverImg = data.coverImg.thumbCover(width: 540)
                var url = URL(string: coverImg)
                data.imageKey = url
                let image = UIImage(named: "icon_feed_placeholder")
                let placeholder = UIImage.placeholderForRectangle(size: self.imageSize(),
                                                                  heightRatio: 64 / self.imageSize().height,
                                                                  bgColor: UIColor(wh_hexString: "#141414"),
                                                                  logoImage: image ?? UIImage())
                self.workdLoadingView.frame = imageView.bounds
                self.workdLoadingView.isHidden = true
                self.liveTagIcon.frame = CGRect(x: imageView.bounds.size.width - 28,
                                                y: imageView.bounds.size.height - 28,
                                                width: 20, height: 20)
                self.liveTagIcon.isHidden = true
                if self.isMyFeed && data.status == 2 { // 生成中
                    if data.taskType == .aiFormula { //走新的流程
                        let progressString = data.gcProgress
                        self.workdLoadingView.newUILoadingProgress(progress: progressString, text: WHLocalizedString("效果融合中..."))
                    } else {
                        let text = (data.taskType == .aiVideo || data.taskType == .aiLive) ? data.statusMessage ?? WHLocalizedString("正在生成中...") : WHLocalizedString("正在生成中...")
                        self.workdLoadingView.loadingtext(text: text)
                    }
                    self.workdLoadingView.isHidden = false
                    imageView.addSubview(self.workdLoadingView)
                } else if self.isMyFeed && data.status == 3 {  // 生成失败
                    self.workdLoadingView.lottie.stop()
                    imageView.sd_setImage(with: url)
                } else if self.isMyFeed && data.hdStatus == 1 {
                    self.workdLoadingView.isHidden = false
                    self.workdLoadingView.loadingtext(text: WHLocalizedString("分辨率提升中..."))
                    imageView.addSubview(self.workdLoadingView)
                } else {
                    if self.isMyFeed,
                       data.taskType == .aiLive,
                       let video = data.resultVideos {
                        if data.status == 4 {
                            let originImg = data.coverImg
                            if !originImg.isEmpty, let originURL = URL(string: originImg.thumbCover(width: 720))  {
                                url = originURL
                                data.imageKey = url
                            }else{
                                url = URL(string: video.cover)
                                data.imageKey = url
                            }
                            self.liveTagIcon.isHidden = false
                            imageView.addSubview(self.liveTagIcon)
                        }
                    }
                    self.workdLoadingView.lottie.stop()
                    imageView.sd_setImage(with: url, placeholderImage: placeholder) 
                   
                }
                
                //预加载原图
                if let originImg = data.originPic, !originImg.isEmpty, let originURL = URL(string: originImg.thumbCover(width: 540))  {
                    if data.taskType == .aiLive || data.taskType == .aiMagicsr || data.taskType == .aiVideoMagicsr {
                        return
                    }
                    SDWebImagePrefetcher.shared.prefetchURLs([originURL])
                    data.originImgKey = originImg.thumbCover(width: 540)
                } else {
                    // 防止复用
                    if let button = view?.superview?.viewWithTag(WHFlowViewType.showOrigin.rawValue) {
                        button.removeFromSuperview()
                    }
                }
            }
        case .content:
            if data.caption.count == 0 { return nil }
            let contentHeight = data.caption.getStringHeight(with: FlowBaseWidth - 16.0, font: kNormalFont13,limitHeight: CGFloat(numbersOfLine) * 18)
            let width = FlowBaseWidth - leftSpace * 2
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .label, rect: CGRect(x: leftSpace, y: 0, width: width, height: 18), tag: type.rawValue)
            info?.viewDidLoadBlock = { [weak self] (view, model, isFirst) in
                guard let label = view as? UILabel, let self = self else { return }
                if isFirst {
                    label.font = kNormalFont13
                    label.numberOfLines = self.numbersOfLine
                    label.textColor = kTextColor
                }
                label.height = self.numbersOfLine == 1 ? 18 : contentHeight + 3
                let margin = data.getFavorWidth() + 4 + 14
                label.width = self.isMyFeed ? width + 8 - margin : width
                label.text = data.caption
                self.resetop(type: type, view: label)
                label.isUserInteractionEnabled = false
            }
        case .effectButton:
            let width = FlowBaseWidth - leftSpace * 3 - collectBtnWidth
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: nil, attrViewType: WHEffectButton.self, rect: CGRect(x: leftSpace, y: 0, width: width, height: 36), tag: type.rawValue)

            info?.viewDidLoadBlock = {[weak self] (view, model, isFirst) in
                guard let self = self ,let effect = view as? WHEffectButton, let data = model as? WHFlowAbstract else { return }
                if isFirst {
                    effect.layer.cornerRadius = 18
                    effect.layer.borderWidth = 1
                    effect.layer.borderColor = UIColor(red: 1, green: 1, blue: 1, alpha: 0.06).cgColor
                }
                effect.effectText(data.taskcategory ?? "txt2img",count: data.effectCount, coutString: data.getEffectCount())
                self.resetop(type: type, view: effect)
                effect.action = {
                    effect.routerEventWithName(WHFlowEventType.effect.rawValue, params: ["model": data, "view": effect])
                }
            }
        case .collect:
            if isMyFeed && data.caption.count == 0 {return nil}
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .button, rect: CGRect(x: 0, y: 0, width: collectBtnWidth, height: collectBtnWidth), tag: type.rawValue)
            info?.viewDidLoadBlock = { [weak self] (view, model, isFirst) in
                guard let collect = view as? UIButton, let cover = view?.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView,
                      let data = model as? WHFlowAbstract,
                      let self = self else { return }
                if isFirst {
                    if self.isMyFeed {
                        collect.backgroundColor = .clear
                    } else {
                        collect.backgroundColor = UIColor(wh_hexString: "#222326")
                        collect.layer.cornerRadius = 18
                        collect.layer.borderWidth = 1
                        collect.layer.borderColor = UIColor(red: 1, green: 1, blue: 1, alpha: 0.06).cgColor
                    }
                    collect.setImage(UIImage.init(named: "icon_feed_collect_default"), for: .normal)
                    collect.setImage(UIImage.init(named: "icon_feed_collect_selected"), for: .selected)
                }
                var offsetY: CGFloat = 34
                if data.caption.count == 0 {
                    offsetY = 10
                }
                collect.width = self.isMyFeed ? 14 : self.collectBtnWidth
                collect.height = self.isMyFeed ? 14 : self.collectBtnWidth
                collect.top = self.isMyFeed ? cover.bottom + 10 : cover.bottom + offsetY
                collect.right = self.isMyFeed ? cover.right - data.getFavorWidth() - 4 : cover.right - 8
                collect.isSelected = data.isFavor == 1 ? true : false
                collect.wh_setEnlargeEdgeWith(top: 4, right: 4, bottom: 4, left: 2)
                var canClick: Bool = true
                collect.wh_addEventAction(for: .touchUpInside) { (btn) in
                    if !canClick { return }
                    canClick = false
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        canClick = true
                    }
                    btn.routerEventWithName(WHFlowEventType.collect.rawValue, params: ["model": data, "view": btn])
                }
            }
        case .collectCount:
            if data.caption.count == 0 { return nil}
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .label, rect: CGRect(x: 0, y: 0, width: 26, height: 18), tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let collectCountLabel = view as? UILabel, let cover = view?.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView,
                      let data = model as? WHFlowAbstract else { return }
                if isFirst {
                    collectCountLabel.font = kNormalFont13
                    collectCountLabel.textColor = kTextColor
                }
                collectCountLabel.top = cover.bottom + 8
                collectCountLabel.width = data.getFavorWidth()
                collectCountLabel.right = cover.right
                collectCountLabel.text = data.getFavorCount()
            }
        case .showOrigin:
//            guard let origin = data.originPic, !origin.isEmpty else { return nil }
            if self.isMyFeed, (data.taskType == .aiLive || data.taskType == .aiMagicsr || data.taskType == .aiFormula) {
                return nil
            }
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .button, rect: CGRect(x: 0, y: 0, width: 30, height: 30), tag: type.rawValue)
            info?.viewDidLoadBlock = {(view, model, isFirst) in
                guard let button = view as? UIButton, let cover = view?.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView, let data = model as? WHFlowAbstract else { return }
                if isFirst {
                    button.setImage(UIImage(named: "icon_feed_origin_show"), for: .normal)
                    button.wh_setEnlargeEdgeWith(top: 10, right: 10, bottom: 10, left: 10)
                    button.isExclusiveTouch = true
                }
                button.bottom = cover.bottom - 8
                button.right = cover.right - 8
                button.wh_addEventAction(for: .touchDown) { (btn) in
                    btn.routerEventWithName(WHFlowEventType.showOrigin.rawValue, params: ["model": data,"show":true,"view":btn])
                }
                button.wh_addEventAction(for: [.touchUpOutside, .touchUpInside, .touchCancel]) { (btn) in
                    btn.routerEventWithName(WHFlowEventType.showOrigin.rawValue, params: ["model": data,"show":false,"view":btn])
                }
                button.isHidden = data.originPic?.count ?? 0 == 0
//                button.isHidden = data.status > 3 ? false : true
            }
        case .picCount:
//            if data.picCount < 2 {return nil}
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .label, rect: CGRect(x: 6, y: 6, width: countWidth, height: 20), tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let countLabel = view as? UILabel, let cover = view?.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView, let data = model as? WHFlowAbstract else { return }
                if isFirst {
                    countLabel.backgroundColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.5)
                    countLabel.font = .systemFont(ofSize: 11)
                    countLabel.textColor = .white
                    countLabel.textAlignment = .center
                    countLabel.layer.cornerRadius = 6
                }
                countLabel.top = cover.top + 6
                if data.status < 4 || data.hdStatus == 1 {
                    countLabel.isHidden = true
                } else {
                    countLabel.isHidden = data.picCount < 2 ? true : false
                }
                let count = data.picCount
                countLabel.text = WHAppShareLanguage.wh_isLanguageZH() ? "\(count)张" : "\(count)"
            }
        case .worksStatus:
            if data.status != 10 {return nil}
            var magin: CGFloat = 6
            if data.picCount > 1, data.hdStatus == 2 {
                magin = 6 + countWidth + 4 + superWidth + 4
            } else if data.picCount > 1, data.hdStatus != 2 {
                magin = 6 + countWidth + 4
            } else if data.picCount < 2, data.hdStatus == 2 {
                magin = 6 + superWidth + 4
            }
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .label, rect: CGRect(x: magin, y: 6, width: 71, height: 20), tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let statusLabel = view as? UILabel else { return }
                if isFirst {
                    statusLabel.backgroundColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.5)
                    statusLabel.font = .systemFont(ofSize: 11)
                    statusLabel.textColor = .white
                    statusLabel.textAlignment = .center
                    statusLabel.layer.cornerRadius = 6
                }
                statusLabel.text = WHLocalizedString("发布审核中")
                if let width = statusLabel.text?.getStringWidth(20, font: .systemFont(ofSize: 11)) {
                    statusLabel.width = width + 14
                }
            }
        case .superPic:
            if data.hdStatus == 0 {return nil}
            let magin: CGFloat = data.picCount > 1 ? 6 + countWidth + 4 : 6
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .label, rect: CGRect(x: magin, y: 6, width: superWidth, height: 20), tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let superLabel = view as? UILabel, let data = model as? WHFlowAbstract else { return }
                if isFirst {
                    superLabel.backgroundColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.5)
                    superLabel.font = .systemFont(ofSize: 11)
                    superLabel.textColor = .white
                    superLabel.textAlignment = .center
                    superLabel.layer.cornerRadius = 6
                }
                superLabel.text = WHLocalizedString("AI超清")
                if data.taskType == .aiMagicsr || data.taskType == .aiVideoMagicsr {
                    superLabel.isHidden = data.status > 3 ? false : true
                } else {
                    superLabel.isHidden = data.hdStatus == 2 ? false : true
                }
            }
        case .playButton:
            if data.taskType != .aiVideo, data.taskType != .aiVideoMagicsr,data.taskType != .aiFormula {return nil}
            info = MTLegoDisplayInfo.creatDisplayItemInfo(viewType: .button, rect: CGRect(x: 0, y: 8, width: 24, height: 24), tag: type.rawValue)
            info?.viewDidLoadBlock = { (view, model, isFirst) in
                guard let playButton = view as? UIButton, let cover = view?.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView else { return }
                if isFirst {
                    playButton.setImage(UIImage(named: "icon_feed_play"), for: .normal)
                }
                if data.taskSubType == 2 { //live超清
                    playButton.setImage(UIImage(named: "icon_feed_live_tag"), for: .normal)
                } else if data.taskSubType == 1 || data.taskType == .aiVideo { //视频
                    playButton.setImage(UIImage(named: "icon_feed_play"), for: .normal)
                } else { //图片类型
                    playButton.setImage(nil, for: .normal)
                }
                playButton.right = cover.right - 8
                playButton.isHidden = data.status < 4 ? true : false
            }
        default:
            return nil
        }
        
        info?.entity = self.abstract
        return info
    }
    
    // 找到顶部视图
    func findTopView(type: WHFlowViewType, view: UIView?) -> UIView? {
        guard let superView = view?.superview else {
            return nil
        }
        var viewIndex = 0
        for (index , item) in subViews.enumerated() {
            if item == type {
                viewIndex = index
                break
            }
        }
        while viewIndex > 0 {
            viewIndex -= 1
            if fixedArray.contains(subViews[viewIndex]) || superView.viewWithTag(subViews[viewIndex].rawValue) == nil || superView.viewWithTag(subViews[viewIndex].rawValue)?.isHidden ?? false {
                continue
            }
            return superView.viewWithTag(subViews[viewIndex].rawValue)
        }
        return nil
    }
    // 重置顶部距离
    func resetop(type: WHFlowViewType, view: UIView) {
        if layoutMap.keys.contains(type) {
            view.top = layoutMap[type] ?? 0
        } else {
            let topView = findTopView(type: type, view: view)
            if let top = topView {
                view.top = top.bottom + 8
            } else {
                view.top = 0
            }
            layoutMap[type] = view.top
        }
    }
}

extension WHFlowBaseDisplayEntity {
    
    private func imageSize() -> CGSize {
        guard let feedItemModel = abstract as? WHFlowAbstract else { return CGSize.zero }
        let singleWidth = FlowBaseWidth
        let ratio = displayableImageRatio(width: feedItemModel.imageWidth, height: feedItemModel.imageHeight)
        let height = singleWidth * ratio
        
        return CGSize(width: singleWidth, height: height)
    }
    
    private func displayableImageRatio(width: CGFloat, height: CGFloat) -> CGFloat {
        if width == 0 || height == 0 {
            return CGFloat(1)
        }
        
        if let model = abstract as? WHFlowAbstract {
            return CGFloat(height / width).wh_clamped(to: (9 / 16)...(21 / 9))
        }
        return CGFloat(height / width)
    }
}
/// 外部配置
public class MTFlowEntityConfig: NSObject {
    public var numbersOfLine: Int?
    public var subViews:[WHFlowViewType]?
    public var allowLongPress: Bool?
    public var isSimple: Bool?
    public var ignoreView:[WHFlowViewType]?
    public var isBlackMode: Bool?
    public var coverScale: Bool?
    public var isMyFeed: Bool?
    /// feed页面来源
    public var feedFromType: WHFeedFromType = .inspiration
}


//// MARK: extension
//
//extension Comparable {
//    func wh_clamped(to range: ClosedRange<Self>) -> Self {
//        return max(range.lowerBound, min(self, range.upperBound))
//    }
//}

class WHEffectButton: UIView {
    
    var action: (() -> Void)?
    
    lazy var titleLabel: UILabel = {
        let make: UILabel = UILabel.init()
        make.font = .systemFont(ofSize: 12, weight: .medium)
        make.textColor = .white
        make.text = WHLocalizedString("创作同款")
        make.textAlignment = .center
        return make
    }()
    
    lazy var countLabel: UILabel = {
        let make: UILabel = UILabel.init()
        make.font = .systemFont(ofSize: 11)
        make.textColor = .white
        make.textAlignment = .left
        make.text = ""
        return make
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor(wh_hexString: "#282B43")
        addSubviews()
        self.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tap)))
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func addSubviews() {
        addSubview(titleLabel)
        addSubview(countLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(16)
            make.width.equalTo(54)
            make.height.equalTo(18)
        }
        
        countLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(titleLabel.snp.right).offset(4)
            make.height.equalTo(15)
            make.right.equalToSuperview().offset(-4)
        }
    }
    
    @objc func tap() {
        action?()
    }
    
    func effectText(_ taskcategory: String, count: Int, coutString: String) {
        var text = WHLocalizedString("创作同款")
        if taskcategory == "txt2img" || taskcategory == "img2img" {
            text = WHLocalizedString("创作同款")
        } else {
            text = WHLocalizedString("应用风格")
        }
        titleLabel.text = text
        countLabel.text = coutString + WHLocalizedString("次")
        let width = text.getStringWidth(18, font: UIFont.systemFont(ofSize: 12, weight: .medium))
        if count == 0 {
            countLabel.isHidden = true
            titleLabel.snp.remakeConstraints { make in
                make.center.equalToSuperview()
                make.width.equalTo(width)
                make.height.equalTo(18)
            }
        } else {
            countLabel.isHidden = false
            var left = count > 100 ? 20 : 26
            left = WHAppShareLanguage.wh_isLanguageZH() ? 16 : left
            titleLabel.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.left.equalTo(left)
                make.width.equalTo(width)
                make.height.equalTo(18)
            }
        }
    }
}

class WHWorksLoadingView: UIView {
    
    lazy var bgImageView: UIImageView = {
        let make = UIImageView.init()
        make.contentMode = .scaleAspectFill
//        make.image = UIImage(named: "bg_feed_loading")
        make.image = UIImage(named: "bg_feed_loading_new")
        return make
    }()
    
    lazy var lottie: LottieAnimationView = {
        let lottie = LottieAnimationView(name: "feed_status")
        lottie.isUserInteractionEnabled = true
        lottie.loopMode = .loop
        lottie.play()
        lottie.backgroundBehavior = .pauseAndRestore
        return lottie
   }()
    
    lazy var titleLabel: UILabel = {
        let make: UILabel = UILabel.init()
        make.font = .systemFont(ofSize: 14, weight: .medium)
        make.textColor = UIColor(wh_hexString: "#5C5F66")
        make.numberOfLines = 0
        make.textAlignment = .center
        return make
    }()
    
    lazy var progressLabel: UILabel = { //默认隐藏
        let make: UILabel = UILabel.init()
        make.font = .systemFont(ofSize: 24, weight: .medium)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        make.numberOfLines = 0
        make.textAlignment = .center
        make.isHidden = true
        return make
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor(wh_hexString: "#121212")
        setUp()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setUp() {
        addSubview(bgImageView)
        addSubview(lottie)
        addSubview(titleLabel)
        addSubview(progressLabel)
        
        bgImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        lottie.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-22)
            make.width.height.equalTo(48)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(lottie.snp.bottom)
            make.left.right.equalToSuperview().inset(8)
        }
        
        progressLabel.snp.makeConstraints { make in
            make.bottom.equalTo(titleLabel.snp.top).offset(0)
            make.left.right.equalToSuperview().inset(8)
            make.height.equalTo(34)
        }
    }
    //目前AI配方会走这个
    func newUILoadingProgress(progress:String,text:String) {
        lottie.stop()
        lottie.isHidden = true
        progressLabel.isHidden = false
        progressLabel.text = progress
        titleLabel.text = text
    }
    
    func loadingtext(text: String) {
        if lottie.isAnimationPlaying == false {
            lottie.play()
        }
        lottie.isHidden = false
        progressLabel.isHidden = true
        titleLabel.text = text
    }
}
