//
//  WHFlowViewController.swift
//
//  Created by meitu on 2020/6/3.
//  Copyright © 2020 meitu. All rights reserved.
//

import UIKit
import SnapKit
import YYModel
import MTLegoBlocksLib
import CHTCollectionViewWaterfallLayout
import MTAccount
import WHBaseLibrary
import WHBusinessCommon

extension Notification.Name {
    static let CollectDidChanged = Notification.Name("CollectDidChanged")
}

@objc open class WHFlowViewController: WHViewController {
    
    /// 网络请求成功的回调
    public typealias RequestCompleteBlock = ([String: Any]?, Bool, [MTLegoDisplayEntity]?) -> Void
    
    public var requestCompleteBlock: RequestCompleteBlock?
    
    /// 配置项
    public var config: MTFlowEntityConfig = MTFlowEntityConfig()
    
    /// 网络请求接口
    public var urlString: String = "/inspiration/feed.json"
        
    /// 外界传入的tab信息
    public var tabInfo: WHTabInfo?
    
    /// 黑暗模式
    public var isBlackModel: Bool = false {
        didSet {
            config.isBlackMode = isBlackModel
        }
    }
        
    public var extraRequestParams: [String: Any] = [:]
     
    /// 是否允许下拉
    public var allowRefreshDown: Bool = true
    /// 是否允许上拉
    public var allowRefreshUp: Bool = true

    /// 自定义视图偏移
    public var emptyOffset: CGFloat?
    /// 空数据的默认显示文案
    public var emptyTitle: String?
    /// 空数据的默认显示子标题
    public var emptyDesString: String?
    /// 自定义无数据视图
    public var emptyView: UIView?
    
    public var emptyBackgroundColor: UIColor = .clear
    
    public var isShowEmptyView: Bool?

    /// 是否使用缓存
    public var useCache: Bool = true

 /// 点击子视图埋点的时候使用
    public var focusIndex: IndexPath?
    /// 底部导航的高度
    public lazy var bottomOffset: CGFloat = {
//        if let tabbar = MTCurrentRootViewController, tabbar.rootTabBar.window != nil, !tabbar.rootTabBar.isHidden {
//            return tabbar.rootTabBar.height
//        }
        return 0.0
    }()
        
    // 下拉次数
    private var refreshNumber = 0
    // 下拉总次数
    private var refreshTotalNumber = 0
    // 上推计数
    private var loadMoreNumber = 0
    // 上推总次数
    private var loadMoreTotalNumber = 0
    // 方向 1下拉，0上拉
    private(set) var refreshDirection = 0 {
        didSet {
            if refreshDirection != oldValue {
                // 刷新方向改变
                if refreshDirection == 1 {
                    refreshNumber = 0
                } else {
                    loadMoreNumber = 0
                }
            }
        }
    }
    // 是否自动刷新
    private var autoRefresh = true
    
    public var cursor: String?
    
    /// 不满一屏幕是否自动加载下一页
    public var needAutoLoadMore: Bool = true
    
    /// 强制加载下一页
    public var needLoadMore: Bool = false
    
    /// 是否显示空页面
    public var shouldShowEmpty: Bool = false
    // 超时刷新
    public var overTimeRefresh: Bool = false
    
    public var displayEntitys: [MTLegoDisplayEntity] = []
    
    open var sectionInset: UIEdgeInsets {
        return UIEdgeInsets.init(top: 2.5, left: 4, bottom: 10, right: 4)
    }
    /// 刷新控件样式
    public var headerRefreshStyle: WHPullHeaderStyle = .picDefaultText
    /// feed页面来源
    public var feedFromType: WHFeedFromType = .unkown
    
    private var feedList: [String] = []
    /// 轮训数组
    public var batchList: [String] = []
        
    public lazy var flowLayout: CHTCollectionViewWaterfallLayout = {
      let layout = CHTCollectionViewWaterfallLayout()
      layout.minimumInteritemSpacing = 16
      layout.minimumColumnSpacing = 4
      layout.columnCount = 2
      layout.sectionInset = sectionInset
      return layout
    }()
    
    public lazy var collectionView: UICollectionView = {
        let view = UICollectionView.init(frame: UIScreen.main.bounds, collectionViewLayout: flowLayout)
        view.delegate = self
        view.backgroundColor = WHFigmaColor.backgroundGlobalPage
        view.dataSource = self
        view.mt_emptyDataSetSource = self
        view.mt_emptyDataSetDelegate = self
        view.showsVerticalScrollIndicator = false
        view.showsHorizontalScrollIndicator = false
        view.keyboardDismissMode = .onDrag
        let identifier = String(describing: MTLegoCollectionViewCell.self)
        view.register(MTLegoCollectionViewCell.self, forCellWithReuseIdentifier: identifier)
        WHFlowManager.registerCell(collectoin: view)
        if allowRefreshDown {
            let header = WHPullHeader(style: headerRefreshStyle)
            view.addPullToRefresh(header) { [weak self] in
                guard let self = self else { return }
                self.getDataFromWeb()
            }
        }
        if allowRefreshUp {
            let footer = WHPullAutoFooter(style: .picCustomText((initialDesc: nil, releasingDesc: nil, loadingDesc: nil, noMoreDesc: WHLocalizedString("没有更多啦～"))))
            view.addPullToRefresh(footer) { [weak self] in
                guard let self = self else { return }
                self.getDataFromWeb(cursor: self.cursor ?? "", isMore: true, isAuto: false)
            }
        }
        return view
    }()
    
    // MARK: - 生命周期
    deinit {
        WHPrint("双列流已经被销毁")
        NotificationCenter.default.removeObserver(self)
    }
    
    open override func viewDidLoad() {
        navigationBarStyle = .none
        super.viewDidLoad()
   
        self.view.backgroundColor = WHFigmaColor.backgroundGlobalPage
        createSubViews()
        addObserber()
    }
    
    public override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }
    
    open override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    public override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
 
    }
    public override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
    
    }
    
    public override var preferredScreenEdgesDeferringSystemGestures: UIRectEdge {
        return [.right]
    }

    // MARK: - 共有方法
    // 重置刷新
    public func resetRefreshState() {
        if collectionView.topPullToRefresh == nil, allowRefreshDown {
            let header = WHPullHeader(style: headerRefreshStyle)
            collectionView.addPullToRefresh(header) { [weak self] in
                guard let self = self else { return }
                    self.getDataFromWeb(isAuto:true)
            }
        }
        if collectionView.bottomPullToRefresh == nil, allowRefreshUp {
            let footer = WHPullAutoFooter(style: .picCustomText((initialDesc: nil, releasingDesc: nil, loadingDesc: nil, noMoreDesc: WHLocalizedString("没有更多啦～"))))
            collectionView.addPullToRefresh(footer) { [weak self] in
                guard let self = self else { return }
                self.getDataFromWeb(cursor: self.cursor ?? "", isMore: true, isAuto: false)
            }
        }
    }
    
    // 刷新事件
    open func beginRefresh(cause: WHFeedListRefreshCause? = nil) {
        guard !(self.collectionView.topPullToRefresh?.isRefreshing ?? false) else {
            return
        }
        resetRefreshState()
        if collectionView.topPullToRefresh != nil {
            DispatchQueue.main.asyncAfter(deadline: .now()) {
                self.collectionView.startRefreshing(at: .top)
            }
        } else {
            getDataFromWeb()
        }
    }
    
    // MARK: - 私有方法
    
    /// 初始化方法
    func createSubViews() {
        if #available(iOS 11.0, *) {
            collectionView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
        self.view.addSubview(collectionView)
        
        WHFlowManager.registerCell(collectoin: collectionView)
        let bottom = self.bottomOffset > 0 ? (self.bottomOffset - 12) : self.bottomOffset
        self.collectionView.snp.makeConstraints { (make) in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-bottom)
        }
        WHUserPrivacyManager.addAgreementHandler {
            self.beginRefresh()
        }
    }
    
    func addObserber() {
        NotificationCenter.default.addObserver(self, selector: #selector(collectDidchanged(_ :)), name: .CollectDidChanged, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(feedStatusDidChanged(_ :)), name: .myFeedStatusDidChanged, object: nil)
        NotificationCenter.default.addObserver(self,
                               selector: #selector(applicationActiveChange),
                               name: UIApplication.didBecomeActiveNotification,
                               object: nil)
        NotificationCenter.default.addObserver(self,
                               selector: #selector(productPushedDidChanged(_ :)),
                               name: Notification.Name("WHEEDeleteProductPush"),
                               object: nil)
    }
    
    open func getDataFromWeb(cursor: String = "" , isMore: Bool = false, isAuto: Bool = true,completion: ((Bool) -> Void)? = nil) {
        self.shouldShowEmpty = false
        
        if isMore , cursor.isEmpty {
            self.collectionView.endRefreshingWithNoData()
            completion?(true)
            return
        }
        
        refreshDirection = isMore ? 0 : 1
        autoRefresh = isAuto
        if isMore {
            loadMoreNumber += 1
            loadMoreTotalNumber += 1
        } else {
            refreshNumber += 1
            refreshTotalNumber += 1
        }
        
        var params: [String : Any] = [:]
        if let tabID = tabInfo?.tabID, tabID.count > 0 {
            params["tab_id"] = tabID
            params["tab_type"] = tabInfo?.tabType
        }
        params["from"] = 1
        if self.feedFromType == .works || self.feedFromType == .model || self.feedFromType == .collects {
            params["from"] = 4
        }
        // 添加额外参数
        params.merge(extraRequestParams)
        WHFlowRequest.requestFlowEntiryRequest(url: urlString,
                                               cursor: cursor,
                                               count: 12,
                                               isMore: isMore,
                                               needCache: self.displayEntitys.count == 0 && self.useCache,
                                               config: self.config,
                                               reqFeed: true,
                                               otherParams: params,
                                               controller: self) {[weak self] (displayEntitys) in
            guard let self = self else { return }
            /// 处理缓存
            if let data = displayEntitys, self.displayEntitys.count == 0, self.useCache {
                self.displayEntitys = data
                self.collectionView.reloadData()
            }
            completion?(true)
        } completion: { [weak self] (response, displayEntitys) in
            guard let self = self else { return }
            self.shouldShowEmpty = true

            let result = response.data() as? [String: Any]
            if !response.isSuccess() {
                // 这里错误提示
                if let block = self.requestCompleteBlock {
                    block(nil, isMore, nil)
                }
                // 结束loading
                if isMore {
                    self.collectionView.endRefreshing(at: .bottom)
                } else {
                    self.collectionView.endRefreshing(at: .top)
                    self.collectionView.reloadData()
                }
                completion?(false)
                if !WHNetwork.isReachable() {
                    WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                } else {
                    WHRouter.topViewController?.showToast(title: response.message() ??  WHLocalizedString("网络好像出问题了，请重试。"))
                }
                if self.displayEntitys.count == 0 {
                    self.collectionView.removePullToRefresh(at: .bottom)
                }
                return
            }

            self.cursor = result?["cursor"] as? String ?? ""
            if let block = self.requestCompleteBlock, let data = result {
                block(data, isMore, displayEntitys)
            }

            if !isMore {
                self.displayEntitys.removeAll()

                if self.needAutoLoadMore {
                    DispatchQueue.main.asyncAfter(deadline: .now()) {
                        if self.collectionView.contentSize.height < self.collectionView.height, self.displayEntitys.count < 5 {
                            self.getDataFromWeb(cursor: self.cursor ?? "", isMore: true, isAuto: true)
                        } else if self.needLoadMore, self.displayEntitys.count == 12 {
                            self.getDataFromWeb(cursor: self.cursor ?? "", isMore: true, isAuto: true)
                        }

                    }
                }
            }
            self.displayEntitys += displayEntitys

            if self.cursor?.isEmpty ?? true {
                self.collectionView.endRefreshingWithNoData()
                if !isMore {
                    self.collectionView.endRefreshing(at: .top)
                }
                if self.displayEntitys.count == 0 {
                    self.collectionView.removePullToRefresh(at: .bottom)
                }
            } else {
                if self.collectionView.bottomPullToRefresh?.isNoDataStatus ?? false {
                    self.collectionView.endRefreshing(at: .bottom)
                }
                if !isMore {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.collectionView.endRefreshing(at: .top)
                        // 防止下拉刷新向下移动
                        if self.collectionView.contentInset.top > 44 {
                            self.collectionView.contentInset = UIEdgeInsets.init(top: 44, left: 0, bottom: 0, right: 0)
                        }
                    }
                } else {
                    self.collectionView.endRefreshing(at: .bottom)
                }
            }
            self.collectionView.reloadData()
            completion?(true)
        }
    }
    
    /// cell的类型 子类需复写
    public func layoutStyle() -> WHFlowLayout? {
        return nil
    }
    
    @objc func collectDidchanged(_ notify: Notification) {
        if let dict = notify.object as? [String : Any],
           let feedId = dict["feedId"] as? String,
           let isCollect = dict["isCollect"] as? Int {
            
            let feedStatus = dict["feedStatus"] as? Int
            let collectType = dict["collectType"] as? Int
            let favorCount = dict["favorCount"] as? Int
            if feedFromType == .collects {  // 我的收藏
                if isCollect == 1 {  // 其他feed收藏或取消后我的收藏页面刷新
                    beginRefresh()
                } else { // 我的收藏页面取消收藏
                    for (index, displayEntity) in self.displayEntitys.enumerated() {
                        let model = displayEntity.abstract as? WHFlowAbstract
                        if model?.feedId == feedId {
                            self.displayEntitys.safeRemove(at: index)
                            if self.displayEntitys.count == 0 {
                                self.collectionView.removePullToRefresh(at: .bottom)
                            }
                            collectionView.reloadData()
                        }
                    }
                }
            } else {
                // 判断是否feed内点赞
                if feedStatus != 1 {
                    displayEntitys.forEach { displayEntity in
                        let model = displayEntity.abstract as? WHFlowAbstract
                        if collectType == 2, model?.modelId == feedId {
                            model?.isFavor = isCollect
                            collectionView.reloadData()
                        } else if model?.feedId == feedId {
                            model?.isFavor = isCollect
                            if let count = model?.favorCount {
                                model?.favorCount = isCollect == 2 ? count - 1 : count + 1
                            }
                            collectionView.reloadData()
                        }
                    }
                }
            }
        }
    }
    
    @objc func feedStatusDidChanged(_ notify: Notification) {
        if let flowModel = notify.object as? WHFlowAbstract {
            // 修改状态
            displayEntitys.forEach { displayEntity in
                let model = displayEntity.abstract as? WHFlowAbstract
                if model?.templateId == flowModel.templateId {
                    model?.status = flowModel.status
                    model?.hdStatus = flowModel.hdStatus
                    model?.coverImg = flowModel.coverImg
                    model?.originPic = flowModel.originPic
                    model?.statusMessage = flowModel.statusMessage
                    model?.taskcategory = flowModel.taskcategory
                    model?.picCount = flowModel.picCount
                    model?.resultVideos = flowModel.resultVideos
                    collectionView.reloadData()
                }
            }
        }
    }
    // 防止后面时间太久生成中不能轮训问题
    @objc func applicationActiveChange() {
        if self.feedFromType != .works {return}
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) { // 延时防止重复请求轮训接口
            if self.batchList.count > 0, let taskCategory = self.tabInfo?.taskCategory, taskCategory.count > 0 {
                var params: [String: Any] = [:]
                params[taskCategory] = self.batchList.joined(separator: ",")
                WHFlowRequest.batchFeedRequest(param: params, flowVc: self)
            }
        }
    }
    
    // 作品删除
    @objc func productPushedDidChanged(_ notify: Notification) {
        if let dict = notify.object as? [String : Any],
           let feedId = dict["businessId"] as? String {
            if feedFromType == .works || self.feedFromType == .model || feedFromType == .history {
                for (index, displayEntity) in self.displayEntitys.enumerated() {
                    let model = displayEntity.abstract as? WHFlowAbstract
                    if model?.templateId == feedId {
                        self.displayEntitys.safeRemove(at: index)
                        if self.displayEntitys.count == 0 {
                            self.collectionView.removePullToRefresh(at: .bottom)
                        }
                        collectionView.reloadData()
                    }
                }
            }
        }
    }
}

// MARK: - collection代理方法
extension WHFlowViewController: UICollectionViewDelegate, UICollectionViewDataSource, CHTCollectionViewDelegateWaterfallLayout {

    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return displayEntitys.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let displayEntity = displayEntitys[indexPath.row]
        let identifier = String(describing: displayEntity.collectionCellClass)
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: identifier, for: indexPath) as? MTLegoCollectionViewCell
        cell?.setDisplayEntity(displayEntity, indexPath: indexPath)
        return cell ?? UICollectionViewCell()
    }
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return displayEntitys[indexPath.row].contentSize
    }
    
    public func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        let displayEntity = displayEntitys[indexPath.row]
        // 防止重复曝光
        if let abstract = displayEntity.abstract as? WHFlowAbstract, !feedList.contains(abstract.templateId ?? ""), self.shouldShowEmpty {
            feedList.append(abstract.templateId ?? "")
            feedEvent(event: "whee_feed_work_expo", model: abstract)
        }
    }
    
    public func collectionView(_ collectionView: UICollectionView, didEndDisplaying cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        
    }
}

// MARK: - 点击事件
extension WHFlowViewController {
    
    public override func handleEventWithName(_ name: String, params: Dictionary<String, Any>?) {
        if let view = params?["view"] as? UIView {
            var cardView = view
            if let cell = cardView as? MTLegoCollectionViewCell, let card = cell.cardView {
                cardView = card
            } else {
                while let superView = cardView.superview, !(cardView is MTLegoCardView) {
                    cardView = superView
                }
            }
            if let card = cardView as? MTLegoCardView {
                self.focusIndex = card.indexPath
            }
        }
        if let tapType = WHFlowEventType.init(rawValue: name) {
            switch tapType {
            case .showOrigin:
                if let model = params?["model"] as? WHFlowAbstract, let show = params?["show"] as? Bool, let view = params?["view"] as? UIView {
                    showOriginPic(view, model, show)
                }
            case .collect:
                if let model = params?["model"] as? WHFlowAbstract, let button = params?["view"] as? UIButton {
                    if !WHNetwork.isReachable() {
                        WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                        return
                    }
                    if WHAccountShareManager.isLogin() {
                        handleCollectAction(model, button)
                    } else {
                        WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                            self?.handleCollectAction(model, button)
                        } failure: {
                        }
                    }
                }
            case .effect:  //创作同款
                if let model = params?["model"] as? WHFlowAbstract {
                    if !WHNetwork.isReachable() {
                        WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                        return
                    }
                    //先登录
                    if WHAccountShareManager.isLogin() {
                       jumpUrl()
                    } else {
                        WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) { sucess, info in
                            WHLoginViewsharedManager.successBlock = nil
                            WHLoginViewsharedManager.failBlock = nil
                            jumpUrl()
                        } failure: {
                        }
                    }
                    func jumpUrl() {
                        if model.taskType == .model {
                            WHRouter.route(with: "wheeai://app/text2img?needsLogin=1&id=\(model.templateId ?? "")&model_id=\(model.modelId ?? "")&source=\(feedFromType.getLoaction())&feed_id=\(model.feedId ?? "")")
                        } else if model.taskType == .imgImg {
                            WHRouter.route(with: "wheeai://app/img2img?needsLogin=1&id=\(model.templateId ?? "")&source=\(feedFromType.getLoaction())&feed_id=\(model.feedId ?? "")")
                       } else if model.taskType == .textImg{
                           WHRouter.route(with: "wheeai://app/text2img?needsLogin=1&id=\(model.templateId ?? "")&source=\(feedFromType.getLoaction())&feed_id=\(model.feedId ?? "")")
                       } else {
                            WHRouter.route(with: model.templateUrl)
                        }
                    }
                    feedEvent(event: "whee_create_same_click", model: model)
                }
            default:
                break
            }
        } else {
            // cell的点击事件
            switch name {
            case MTLegoBlocksEventStrategy.tap:    // 详情页跳转
                // 打开之前先关闭小程序防止有缓存详情页重复
                if !WHNetwork.isReachable() {
                    WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                    return
                }
                if let model = params?["model"] as? WHFlowAbstract {
                    if model.status == 2 {
                        WHRouter.topViewController?.showToast(title: WHLocalizedString("正在生成中，请稍后再来"))
                        return
                    }
                    if model.taskType == .aiVideo {
                        let videoVC = WHAIVideoDetailViewController()
                        videoVC.temId = model.templateId ?? ""
                        videoVC.coverImgString = model.status == 3 ? "" : model.coverImg //失败情况下，就不穿图了
                        videoVC.fromPage = self.feedFromType.getLoaction()
                        WHRouter.topViewController?.navigationController?.pushViewController(videoVC, animated: true)
                    } else if model.taskType == .aiMagicsr {
                        let originPic = model.originPic?.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics)
                        let reslutPic = model.coverImg.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics)
                        let imageKey = model.imageKey?.absoluteString.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics)
                        let reslutImgKey = model.originImgKey?.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics)
                        WHRouter.route(with: "wheeai://app/ai_upscalerDetail?id=\(model.templateId ?? "")&msg_id=\(model.msgId)&status=\(model.status)&originPic=\(originPic ?? "")&reslutPic=\(reslutPic ?? "")&imageKey=\(imageKey ?? "")&reslutImgKey=\(reslutImgKey ?? "")")
                    } else if model.taskType == .project { //项目历史
                        let projectScheme = "wheeai://app/ai_editor?needsLogin=1&project_id="+(model.templateId ?? "")
                        WHRouter.route(with: projectScheme)
                    } else if model.taskType == .aiLive { // 跳转到aiLive详情页
                        if model.status == 2 {
                            WHRouter.topViewController?.showToast(title: WHLocalizedString("正在生成中，请稍后再来"))
                            return
                        }
                        let liveVC = WHAILiveDetailViewController()
                        liveVC.coverUrl = model.resultVideos?.cover
                        liveVC.originVideoUrl = model.resultVideos?.url
                        liveVC.originNoWaterMarkUrl = model.resultVideos?.noWaterMarkUrl
                        liveVC.originUrl = model.originPic
                        liveVC.videoId = model.templateId
                        liveVC.typeId = model.liveTypeId
                        liveVC.typeName = model.liveTypeName
                        liveVC.status = model.status
                        liveVC.imageKey = model.imageKey
                        liveVC.source = model.scm
                        liveVC.msgId = model.msgId
                        WHRouter.topViewController?.navigationController?.pushViewController(liveVC, animated: true)
                    } else if model.taskType == .aiVideoMagicsr { //视频超清详情页
                        let routeUrl = "wheeai://app/video_hd_detail?tabId=\(tabInfo?.tabID ?? "")&msg_id=\(model.msgId)&from=\(self.feedFromType.rawValue)"
                        WHRouter.route(with: routeUrl, params: ["model": model])
                    } else if model.taskType == .aiFormula {
                        var formulaRouter = "wheeai://app/sop_details?id=\(model.templateId ?? "")&task_category=ai_formula"
                        if let keyStr = model.imageKey?.absoluteString, !keyStr.isEmpty,
                           let reslutKey = keyStr.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) {
                            formulaRouter = formulaRouter + "&imageKey=\(reslutKey)"
                        }
                        WHRouter.route(with: formulaRouter)
                    } else {
                        var routeUrl = "wheeai://app/detail?task_category=\(model.taskcategory ?? "" )&id=\(model.templateId ?? "")&feed_id=\(model.feedId ?? "")&msg_id=\(model.msgId)&tab=\(tabInfo?.tabID ?? "")&from=\(self.feedFromType.rawValue)"
                        if let taskcategoryStr = model.taskcategory, taskcategoryStr == "model" {
                            routeUrl = routeUrl + "&modelStatus=\(model.status)"
                        }
                        
                        if let keyStr = model.imageKey?.absoluteString, !keyStr.isEmpty,
                           let reslutKey = keyStr.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) {
                            routeUrl = routeUrl + "&imageKey=\(reslutKey)"
                        }
                     
                        WHRouter.route(with: routeUrl)
                    }
                    feedEvent(event: "whee_feed_work_click", model: model)
                }
            case MTLegoBlocksEventStrategy.buttonTap: break
            case MTLegoBlocksEventStrategy.longPress:
                if let model = params?["model"] as? WHFlowAbstract {
                    if model.status == 2 {
                        WHRouter.topViewController?.showToast(title: WHLocalizedString("正在生成中，请稍后再来"))
                        return
                    }
                    if model.taskType == .aiLive {
                        if let cardView = params?["view"] as? UIView,
                           let cell = cardView as? WHFlowBaseCollectionViewCell {
                            cell.autoPlay()
                        }
                    }
                }
            case WHLegoBlocksEventStrategy.longPressEndCancel:
                if let model = params?["model"] as? WHFlowAbstract {
                    if model.status == 2 {
                        WHRouter.topViewController?.showToast(title: WHLocalizedString("正在生成中，请稍后再来"))
                        return
                    }
                    if model.taskType == .aiLive {
                        if let cardView = params?["view"] as? UIView,
                           let cell = cardView as? WHFlowBaseCollectionViewCell {
                            cell.stopPlay()
                        }
                    }
                }
                
            default:
                break
            }
        }
    }
    
    /// 收藏事件
    @objc public func handleCollectAction(_ model: WHFlowAbstract, _ button: UIButton) {
        let isCollect = button.isSelected
        var parameters: [String: Any] = [:]
        parameters["id"] = model.taskType == .model ? model.modelId : model.feedId
        // 收藏业务类型 1 灵感feed 2 风格模型
        parameters["type"] = model.taskType == .model ? 2 : 1
        // 1收藏 2取消收藏
        parameters["action"] = isCollect ? 2 : 1
        WHSharedRequest.POST("/collect/do.json", params: parameters) {[weak self] response in
            let data = response.data() as? [String: Any]
            if let result = data?["result"] as? Bool, result == true {
                if isCollect {
                    button.isSelected = false
                    model.isFavor = 2
                    model.favorCount -= 1
                }else {
                    button.isSelected = true
                    model.isFavor = 1
                    model.favorCount += 1
                    let title = model.taskType == .model ? WHLocalizedString("已收藏风格") : WHLocalizedString("已收藏作品")
                    WHRouter.topViewController?.showToast(title: title)
                }
                // 0:小程序 1:灵感feed 2:我的收藏feed 3:详情页
                let feedStatus = self?.feedFromType == .collects ? 2 : 1
                NotificationCenter.default.post(name: .CollectDidChanged,
                                                object: ["feedId" : model.feedId ?? "", "isCollect": button.isSelected ? 1 : 2, "feedStatus": feedStatus])
            } else {
                WHRouter.topViewController?.showToast(title: WHLocalizedString("收藏失败"))
            }
            if let collectLabel = button.superview?.viewWithTag(WHFlowViewType.collectCount.rawValue) as? UILabel {
                let favorCount = model.favorCount
                collectLabel.text = "\(favorCount)"
            }
        }
        feedEvent(event: "whee_favor_click", model: model, favorType: isCollect ? "cancel" : "favor")
    }
    
    /// 显示原图
    public func showOriginPic(_ view: UIView , _ model: WHFlowAbstract , _ show: Bool) {
        guard let cover = view.superview?.viewWithTag(WHFlowViewType.cover.rawValue) as? UIImageView, let origin = model.originPic else {
            return
        }
        let coverImg = model.coverImg.thumbCover(width: 540)
        let originImg = origin.thumbCover(width: 540)
        model.originImgKey = originImg
        let feedplaceholderURL = URL(string: !show ? coverImg : originImg)
        if !show {
            view.isUserInteractionEnabled = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                cover.sd_setImage(with: feedplaceholderURL, placeholderImage: nil, options: .queryDiskDataSync, completed: nil)
                view.isUserInteractionEnabled = true
            }
        } else {
            cover.sd_setImage(with: feedplaceholderURL, placeholderImage: nil, options: .queryDiskDataSync, completed: nil)
        }
    }
    
    // type "1"点击 "3"曝光
    private func feedEvent(event: String, model: WHFlowAbstract, type: String = "1", favorType: String = "") {
        var params: [String : Any] = [:]
        params["favor_count"] = model.favorCount
        params["is_favor"] = model.isFavor == 1 ? 1 : 0
        params["user_name"] = model.user?.screenName
        params["title"] = model.caption
        params["tab"] = tabInfo?.tabID
        params["feed_id"] = model.feedId
        params["location"] = self.feedFromType.getLoaction()
        params["work_or_model"] = model.taskType == .model ? "model" : "work"
        if let modelId = model.modelId {
            params["model_id"] = modelId
        }
        if let temId = model.templateId {
            params["id"] = temId
        }
        if favorType.count > 0 {
            params["favor_type"] = favorType
            params["source"] = "feed_page"
        }
        WHAnalyticsManager.otherTrackEvent(event, params: params, type: type)
    }
}

extension WHFlowViewController: WHSubPageViewProtocol {
    public func didSelectTab() {
        
    }
    
    public func connectScrollView() -> UIScrollView? {
        return self.collectionView
    }
    
    public func selectTabRefresh(_ refreshCause: WHFeedListRefreshCause) {
        self.beginRefresh(cause: refreshCause)
    }
    public func mineTabClick(isShow: Bool){}
}

// MARK: - MTEmptyDataSetSource

extension WHFlowViewController: MTEmptyDataSetSource, MTEmptyDataSetDelegate {
    open func mt_customView(forEmptyDataSet scrollView: UIScrollView) -> UIView? {
        if !WHNetwork.isReachable() {
            let networkErrorView = WHNetworkErrorView.init(showImage: true, showOpenSetting: false)
            networkErrorView.tapRetryAction = {[weak self] in
                if let self = self {
                    self.beginRefresh()
                    self.wh_passEventWith("flowNetworkChange")
                }
            }
            networkErrorView.tapOpenSettingAction = {
                WHNetworkErrorView.openSetting()
            }
            networkErrorView.networkAvailableAction = { [weak self] in
                guard let self = self else { return }
                if self.displayEntitys.count == 0 {
                    self.beginRefresh()
                    self.wh_passEventWith("flowNetworkChange")
                }
            }
            return networkErrorView
        }
        // 需要无网视图
        if !shouldShowEmpty {
            return UIView()
        } else {
            return emptyView
        }
    }
    
    public func mt_image(forEmptyDataSet scrollView: UIScrollView) -> UIImage? {
        let image = UIImage(named: "bg_search_empty")
        return image
    }
    // 标题
    public func mt_title(forEmptyDataSet scrollView: UIScrollView) -> NSAttributedString? {
        var text = WHLocalizedString("没有内容呢", comment: "")
        if let alert = emptyTitle {
            text = alert
        }
        return NSAttributedString(string: text, attributes: emptyTitleAttribute())
    }
    // 子标题
    public func mt_description(forEmptyDataSet scrollView: UIScrollView) -> NSAttributedString? {
        if let des = emptyDesString {
            return NSAttributedString(string: des, attributes: emptyTitleAttribute())
        }
        return nil
    }
    
    public func mt_backgroundColor(forEmptyDataSet scrollView: UIScrollView) -> UIColor? {
        return self.emptyBackgroundColor
    }
    // 上下偏移的高度
    open func mt_verticalOffset(forEmptyDataSet scrollView: UIScrollView) -> CGFloat {
        if let offset = emptyOffset {
            return offset
        }
        if !WHNetwork.isReachable() {
            return -150
        }
        return -100
    }
    
    // 图片和文字的间距
    public func mt_spaceHeight(forEmptyDataSet scrollView: UIScrollView) -> CGFloat {
        return 1
    }
    
    open func mt_emptyDataSetShouldAllowScroll(_ scrollView: UIScrollView) -> Bool {
        return true
    }
    
    public func mt_emptyDataSetShouldDisplay(_ scrollView: UIScrollView) -> Bool {
        if let isShow = isShowEmptyView {
            return isShow
        }
        return self.displayEntitys.count <= 0
    }
    
    func emptyTitleAttribute() -> [NSAttributedString.Key : Any] {
        return [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 16, weight: .medium),
                NSAttributedString.Key.foregroundColor: UIColor(wh_hexString: "#F7F8FA")]
    }
    
    func emptyDescriptionAttribute() -> [NSAttributedString.Key : Any] {
        return [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14),
                NSAttributedString.Key.foregroundColor: UIColor(wh_hexString: "#7A7E85")]
    }
}

