//
//  WHSOPDetailsButtonCell.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/13.
//

import Foundation
import UIKit
import Lottie

class WHSOPDetailsButtonCell: UICollectionViewCell {
    
    var type: WHSOPDetailsButtonType? {
        didSet {
            guard let type = type else { return }
            if type.animated {
                lottieView.play()
                lottieView.isHidden = false
            } else {
                lottieView.isHidden = true
                lottieView.pause()
            }
            icon.image = UIImage(named: type.imageName)
            icon.isHidden = !lottieView.isHidden
            textLabel.text = type.title
        }
    }
    
    private lazy var icon: UIImageView = {
        let view = UIImageView()
        return view
    }()
    
    private lazy var lottieView: LottieAnimationView = {
        let lottie = LottieAnimationView(name: "sop_details_again")
        lottie.isUserInteractionEnabled = true
        lottie.loopMode = .loop
        lottie.backgroundBehavior = .pauseAndRestore
        lottie.isHidden = true
        return lottie
   }()
    
    private lazy var textLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 12)
        label.textColor = UIColor(rgb: 0xE0E0E0)
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = UIColor(rgb: 0x6A6A6A, alpha: 0.3)
        contentView.layer.cornerRadius = 16
        contentView.addSubview(icon)
        contentView.addSubview(lottieView)
        contentView.addSubview(textLabel)
        icon.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(16)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }
        lottieView.snp.makeConstraints { make in
            make.edges.equalTo(icon)
        }
        textLabel.snp.makeConstraints { make in
            make.top.equalTo(icon.snp.bottom).offset(6)
            make.left.right.equalToSuperview()
            make.height.equalTo(16)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
