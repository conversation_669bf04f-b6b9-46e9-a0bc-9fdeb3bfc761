//
//  WHSOPDetailsImgInfoView.swift
//  MeituWhee
//
//  Created by <PERSON> on 2025/9/15.
//

import Foundation
import WHBusinessCommon

class WHSOPDetailsImgInfoView: UIView {
    
    var playedPercentBlock: ((CGFloat) -> ())?
    
    private lazy var playBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "wh_sop_video_play_icon"), for: .normal)
        btn.addTarget(self, action: #selector(playAction(_:)), for: .touchUpInside)
        btn.isHidden = true
        return btn
    }()
    
    private lazy var liveBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "wh_sop_details_live_btn"), for: .normal)
        btn.addTarget(self, action: #selector(liveAction(_:)), for: .touchUpInside)
        btn.isHidden = true
        return btn
    }()
    
    private lazy var imgView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFit
        return view
    }()
    
    private var model: WHTextListModel?
    private var currentType: WHSOPDetailsSegmentType = .before
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
        setupGestureRecognizer()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configData(with model: WHTextListModel, type: WHSOPDetailsSegmentType) {
        self.model = model
        var videoUrl: String = ""
        switch model.resultType {
        case 0:
            // 图片
            playBtn.isHidden = true
            liveBtn.isHidden = true
        case 1:
            // 视频
            liveBtn.isHidden = true
            videoUrl = (WHAITagAuthAlertShareManager.getIsShowWaterMark() ? model.resultVideo?.url : model.resultVideo?.noWaterMarkUrl) ?? ""
        case 2:
            // LIVE
            playBtn.isHidden = true
            videoUrl = (WHAITagAuthAlertShareManager.getIsShowWaterMark() ? model.resultVideo?.url : model.resultVideo?.noWaterMarkUrl) ?? ""
        default:
            break
        }
        if videoUrl.count > 0 {
            VideoPlayerManager.shared.load(videoId: model.templateId,
                                           url: videoUrl,
                                           coverImage: nil,
                                           on: self,
                                           autoLoop: true,
                                           externalPlayer: nil,
                                           preservedCallbacks: [])?
                .playing(capture: self, block: { object, currentId in
                    object.sendSubviewToBack(object.imgView)
                })
                .paused(capture: self, block: { (object, currentId) in
                    if object.currentType == .after {
                        if model.resultType == 1 {
                            object.playBtn.isHidden = false
                        } else if model.resultType == 2 {
                            object.liveBtn.isHidden = false
                        }
                    }
                })
                .onPrrogress(capture: self, block: { object, currentId in
                    object.playedPercentBlock?(VideoPlayerManager.shared.playedPercent.wh_clamped(to: 0...1))
                })
                .onReachEnd(capture: self, block: { (object, currentId) in
                    DispatchQueue.main.async {
                        VideoPlayerManager.shared.pause()
                    }
                })
                .done()
            VideoPlayerManager.shared.player.view.contentMode = .scaleAspectFill
            VideoPlayerManager.shared.set(mute: true, manual: false)
            bringSubviewToFront(imgView)
            bringSubviewToFront(playBtn)
            bringSubviewToFront(liveBtn)
        }
        selectedSegment(type: type)
    }
    
    func selectedSegment(type: WHSOPDetailsSegmentType) {
        self.currentType = type
        guard let model = model else {
            return
        }
        switch type {
        case .before:
            switch model.resultType {
            case 0:
                // 图片
                imgView.sd_setImage(with: URL(string: model.originPic))
            case 1:
                // 视频
                imgView.sd_setImage(with: URL(string: model.originPic))
                VideoPlayerManager.shared.pause()
                bringSubviewToFront(imgView)
            case 2:
                // LIVE
                imgView.sd_setImage(with: URL(string: model.originPic))
                VideoPlayerManager.shared.pause()
                bringSubviewToFront(imgView)
            default:
                break
            }
        case .after:
            switch model.resultType {
            case 0:
                // 图片
                if let image = model.images.first {
                    let url = WHAITagAuthAlertShareManager.getUsePicUrl(watermarkUrl: image.previewUrlWatermark, unWaterMarkUrl: image.url)
                    imgView.sd_setImage(with: URL(string: url))
                }
            case 1:
                // 视频
                imgView.sd_setImage(with: URL(string: model.picUrl))
                playBtn.isHidden = true
                VideoPlayerManager.shared.play()
            case 2:
                // LIVE
                imgView.sd_setImage(with: URL(string: model.picUrl))
                bringSubviewToFront(imgView)
                bringSubviewToFront(liveBtn)
            default:
                break
            }
        }
    }
    
    func willDisappear() {
        guard subviews.contains(VideoPlayerManager.shared.playerContainer) else {
            return
        }
        VideoPlayerManager.shared.clearDiskCache {
        }
    }
    
    private func setupSubviews() {
        addSubview(imgView)
        addSubview(playBtn)
        addSubview(liveBtn)
        imgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        playBtn.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(64)
        }
        liveBtn.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().inset(16)
            make.size.equalTo(CGSize(width: 62, height: 38))
        }
    }
    
    private func setupGestureRecognizer() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(tapAction(_:)))
        addGestureRecognizer(tap)
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(longPressAction(_:)))
        addGestureRecognizer(longPress)
    }
    
    @objc
    private func playAction(_ sender: UIButton) {
        VideoPlayerManager.shared.play()
        sender.isHidden = true
    }
    
    @objc
    private func liveAction(_ sender: UIButton) {
        VideoPlayerManager.shared.play()
        sender.isHidden = true
    }
    
    @objc
    private func tapAction(_ recognizer: UITapGestureRecognizer) {
        guard currentType == .after,
              model?.resultType == 1 else {
            return
        }
        // 视频
        VideoPlayerManager.shared.pause()
    }
    
    @objc
    private func longPressAction(_ recognizer: UILongPressGestureRecognizer) {
        guard currentType == .after,
              model?.resultType == 2 else {
            return
        }
        // LIVE
        if recognizer.state == .began {
            VideoPlayerManager.shared.play()
            liveBtn.isHidden = true
        }
        if recognizer.state == .ended || recognizer.state == .cancelled {
            VideoPlayerManager.shared.pause()
        }
    }
    
}
