//
//  WHSOPDetailsViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/13.
//

import Foundation
import WHBaseLibrary
import WHBusinessCommon
import JXSegmentedView
import SnapKit
import MTPhotoLibrary
import MTLivePhotoKit

class WHSOPDetailsViewController: WHViewController {
    
    private var id: String?
    private var msgId: String?
    private var taskCategory: String?
    private var from: String?
    private var model: WHTextListModel?
    private var priceModel: WHGcPriceModel?
    private var balanceModel: WHBeansBalanceModel?
    
    private var downloader: WHMediaDownloadPlugin?
    private var progressLoadingView: WHProgressLoadingView?
    private var liveDownloadRequest: WHBaseRequest?

    private let cellHeight: CGFloat = 78
    
    private lazy var backBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "wh_navigation_bar_back_white"), for: .normal)
        btn.addTarget(self, action: #selector(backAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var shareBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "wh_navigation_bar_share_white"), for: .normal)
        btn.addTarget(self, action: #selector(shareAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var slider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.value = 0
        slider.minimumTrackTintColor = .white
        slider.maximumTrackTintColor = UIColor(rgb: 0x6A6A6A, alpha: 0.3)
        slider.setThumbImage(UIImage(named: "wh_sop_video_slider_thumb"), for: .normal)
        slider.addTarget(self,
                         action: #selector(progressSliderValueChanged(_:event:)),
                         for: .valueChanged)
        slider.isUserInteractionEnabled = false
        slider.isHidden = true
        return slider
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        layout.minimumLineSpacing = 4
        layout.scrollDirection = .horizontal
        let view = UICollectionView(frame: .zero, collectionViewLayout: layout)
        view.backgroundColor = .clear
        view.delegate = self
        view.dataSource = self
        view.register(WHSOPDetailsButtonCell.self,
                      forCellWithReuseIdentifier: "WHSOPDetailsButtonCell")
        return view
    }()
    
    private lazy var segmentedView: JXSegmentedView = {
        let view = JXSegmentedView()
        view.backgroundColor = UIColor(rgb: 0x454545, alpha: 0.3)
        view.isHidden = true
        view.delegate = self
        view.dataSource = segmentedDataSource
        view.defaultSelectedIndex = WHSOPDetailsSegmentType.allCases.firstIndex(of: WHSOPDetailsSegmentType.after) ?? 0
        view.layer.cornerRadius = 16
        view.contentEdgeInsetLeft = 4
        view.contentEdgeInsetRight = 4
        view.collectionView.isScrollEnabled = false
        //配置指示器
        let indicator = JXSegmentedIndicatorBackgroundView()
        indicator.indicatorWidth = 62
        indicator.indicatorHeight = 24
        indicator.indicatorWidthIncrement = 0
        indicator.indicatorColor = UIColor(rgb: 0xC3C3C3, alpha: 0.3)
        view.indicators = [indicator]
        return view
    }()
    
    private lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let ds = JXSegmentedTitleDataSource()
        ds.titles = WHSOPDetailsSegmentType.allCases.map { $0.title }
        ds.titleNormalFont = .pingFangSCFont(ofSize: 12, weight: .medium)
        ds.titleSelectedFont = .pingFangSCFont(ofSize: 12, weight: .medium)
        ds.titleNormalColor = .white
        ds.titleSelectedColor = .white
        ds.isItemSpacingAverageEnabled = true
        ds.itemWidth = 62
        ds.itemSpacing = 0
        return ds
    }()
    
    private lazy var imgInfoView: WHSOPDetailsImgInfoView = {
        let view = WHSOPDetailsImgInfoView()
        view.playedPercentBlock = { [weak self] percent in
            self?.slider.value = Float(percent)
        }
        return view
    }()
    
    private var collectionViewBottom: Constraint?
    private var btnTypes: [WHSOPDetailsButtonType] = []
    
    override func loadRouteParams(_ params: [String : Any]) {
        if let taskCategory = params["task_category"] as? String {
            self.taskCategory = taskCategory
        }
        if let id = params["id"] as? String {
            self.id = id
        }
        if let msg_id = params["msg_id"] as? String {
            self.msgId = msg_id
        }
        if let from = params["from"] as? String {
            self.from = from
        }
        if let model = params["model"] as? WHTextListModel {
            self.model = model
            self.id = model.templateId
            self.taskCategory = model.taskcategory
            self.msgId = model.msgId
        }
    }
    
    override func viewDidLoad() {
        navigationBarStyle = .none
        panBackEnabled = false
        super.viewDidLoad()
        loadData()
        loadBalance()
        addObserver()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        do {
            // playback: 表示音频是”专门用于播放“，即使设备处于静音模式，也会播放音频。
            // moviePlayback: 优化用于视频播放。
            try AVAudioSession.sharedInstance().setCategory(.playback,
                                                            mode: .moviePlayback,
                                                            options: [])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            WHPrint("音频会话设置失败: \(error)")
        }
        
        if viewIsLoaded {
            loadBalance()
            loadPrice()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        imgInfoView.willDisappear()
    }
    
    override func setupSubviews() {
        super.setupSubviews()
        view.backgroundColor = .black
        view.addSubview(backBtn)
        view.addSubview(shareBtn)
        view.addSubview(imgInfoView)
        view.addSubview(segmentedView)
        view.addSubview(collectionView)
        view.addSubview(slider)

        backBtn.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(WH_STATUS_BAR_HEIGHT + 2)
            make.left.equalToSuperview().inset(4)
            make.width.height.equalTo(40)
        }
        shareBtn.snp.makeConstraints { make in
            make.size.centerY.equalTo(backBtn)
            make.right.equalToSuperview().inset(4)
        }
        imgInfoView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview().inset(WH_NAVIGATION_BAR_HEIGHT + 8)
            make.bottom.equalTo(segmentedView.snp.top).offset(-8)
        }
        segmentedView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(collectionView.snp.top).offset(-20)
            make.width.equalTo(133)
            make.height.equalTo(32)
        }
        collectionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(cellHeight)
            collectionViewBottom = make.bottom.equalToSuperview().offset(0).constraint
        }
        slider.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().inset(34)
            make.height.equalTo(24)
        }
    }
    
    // MARK: - Events
    @objc
    private func backAction(_ sender: UIButton) {
        wh_handleEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
    }
    
    @objc
    private func shareAction(_ sender: UIButton) {
        guard let model = model else {
            return
        }
        let detail = WHMoreAlterView()
        detail.clickBlock = { [weak self] (eventType) in
            self?.didClickButtonWith(event: eventType)
        }
        var array: [WHDetailEventType] = [.report]
        if model.resultType == 0 {
            array.append(.share)
        }
        if model.user?.isMine() == true {
            array.append(.delet)
        }
        detail.updateListWith(array: array)
        detail.show()
    }
    
    @objc
    private func progressSliderValueChanged(_ sender: UISlider,
                                            event: UIEvent) {
//        guard let touchEvent = event.allTouches?.first else {
//            return
//        }
//        let position = Int(Float(totalDuration) * sender.value)
//        switch touchEvent.phase {
//        case .began:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchDown)
//        case .moved:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchSlide)
//        case .ended:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchUp)
//        default:
//            break
//        }
//        progressDidChange()
    }
    
    // MARK: - Private Methods
    private func addObserver() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(vipStatusDidChanged),
                                               name: .VipStatusDidChanged,
                                               object: nil)
    }
    
    @objc
    private func vipStatusDidChanged() {
        loadBalance()
        loadPrice()
    }
    
    private func setupContentInfo() {
        guard let model = model else {
            return
        }
        imgInfoView.configData(with: model,
                               type: WHSOPDetailsSegmentType(rawValue: segmentedView.selectedIndex) ?? .after)
        segmentedView.isHidden = false
        if model.resultType == 0 {
            btnTypes = [.save, .again, .HD, .oneSentence]
        } else {
            btnTypes = [.save, .again, .videoHUD]
        }
        collectionViewBottom?.update(offset: model.resultType == 1 ? -74 : -50)
        collectionView.reloadData()
        refreshSliderState()
    }
    
    private func loadData() {
        guard model == nil,
            let taskCategory = taskCategory,
            let id = id else {
            setupContentInfo()
            loadPrice()
            return
        }
        let ids: [String: String] = [
            taskCategory: id,
        ]
        let params: [String : Any] = [
            "type_ids": ids.wh_toJsonString() ?? "",
            "from": from ?? "6",
        ]
        WHSharedRequest.GET("/inspiration/batch_feed.json",
                            params: params) { [weak self] (response: WHOriginalResponse) in
            guard let self = self else {
                return
            }
            if response.isSuccess(),
               let value = response.value as? [String: Any],
               let data = value["data"] as? [String: Any],
               let list = data["list"] as? [[String: Any]],
               let item = list.first,
               let model = WHTextListModel.yy_model(with: item) {
                self.model = model
                self.setupContentInfo()
                self.loadPrice()
            } else {
                self.showToast(title: response.message() ?? WHLocalizedString("网络异常，请重试"))
            }
        }
    }
    
    private func loadPrice() {
        guard let model = model,
              model.limitVip else {
            return
        }
        let ids: [String: String] = [
            "template_ids": String(model.tempId)
        ]
        let params: [String: Any] = [
            "function_code": "ai_template",
            "function_body": ids.wh_toJsonString() ?? "",
        ]
        WHSharedRequest.POST("/sub/get_price_batch.json",
                             params: params) { [weak self] (response: WHOriginalResponse) in
            guard let self = self else {
                return
            }
            if response.isSuccess(),
               let data = response.data() as? [String: Any],
               let list = data["list"] as? [[String: Any]],
               let dict = list.filter({ ($0["template_id"] as? Int) == model.tempId }).first,
               let priceData = dict["price_data"] as? [String: Any] {
                self.priceModel = WHGcPriceModel.yy_model(with: priceData)
            }
        }
    }
    
    private func loadBalance() {
        WHServerReport.requestBalanceAmount { [weak self] model in
            self?.balanceModel = model
        }
    }
    
    private func refreshSliderState() {
        slider.isHidden = !(model?.resultType == 1 && WHSOPDetailsSegmentType(rawValue: segmentedView.selectedIndex) == .after)
    }
    
    private func didClickButtonWith(event: WHDetailEventType) {
        switch event {
        case .report:
            reportClick()
        case .delet:
            deleteMy()
        case .share:
            shareClick()
        default:
            break
        }
    }
    
    // 举报
    private func reportClick(){
        if WHEnvConfigShareManager.environment == .pre {
            WHRouter.route(with: "wheeai://web?url=https%3A%2F%2Fpre-feedback.meitu.com%2Fm%2Freport%2Fsubmit")
        }else {
            WHRouter.route(with: "wheeai://web?url=https%3A%2F%2Ffeedback.meitu.com%2Fm%2Freport%2Fsubmit")
        }
    }
    
    // 删除
    private func deleteMy(){
        let alertView = WHCommonAlertView(title: WHLocalizedString("是否要删除该作品？"), desStr: WHLocalizedString(""), alertViewType: .allStyle, sureButtonText: WHLocalizedString("确认"))
        alertView.show(in: self.view)
        alertView.alertSureHandler = {[weak self]() in
            self?.deleteCurrentProduct()
        }
    }
    
    private func shareClick() {
        if let listModel = model {
            if (listModel.isExcellent == false && listModel.isMine() == false) || (listModel.isExcellent == false && listModel.isMine() == true && listModel.status == 5) {
                UIViewController.wh_top().showToast(title: WHLocalizedString("为确保内容品质，该作品的分享功能暂已关闭"))
                return;
            }
            if listModel.isExcellent == true { //优质内容，分享的是链接
                WHTextToImageRequest.requestShareInfo(feedId: listModel.templateId, taskCategory: listModel.taskcategory ?? "") { model in
                    if let model = model {
                        var picList:[String] = []
                        for picModel in listModel.images {
                            if picModel.urlWatermark.count > 0,picModel.imageStatus == 1 {
                                picList.append(picModel.urlWatermark)
                            }
                        }
                        let content = ["title":model.title,
                                       "content":model.content,
                                       "url":model.link,
                                       "pic":model.pic,
                                       "pic_list":picList,
                                       "from_page_type": listModel.isMine() ? 0 : 1]
                        WHShareViewManager.showShareSheet(with: content) { resp in
                        }
                    } else {
                        UIViewController.wh_top().showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                    }
                }
            } else if listModel.images.count > listModel.imageIndex {
                let model = listModel.images[listModel.imageIndex]
                WHShareViewManager.showShareSheet(with: ["ui_type":0,"pic": model.url,"from_page_type": listModel.isMine() ? 0 : 1]) { resp in
                }
            } else {
                UIViewController.wh_top().showToast(title: WHLocalizedString("请重试"))
            }
        } else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("请重试"))
        }
    }
    
    private func deleteCurrentProduct() {
        guard let taskcategory = taskCategory,
              let templateId = id  else {
            return
        }
        var params:[String : Any] = [:]
        params["task_category"] = taskcategory
        params["id"] = templateId
        WHSharedRequest.POST("/task/delete.json", params: params) { [weak self] response in
            let result = response.data() as? [String: Any]
            if let result = result?["result"] as? Bool, result{
                self?.wh_passEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
                NotificationCenter.default.post(name: NSNotification.Name.WHEEStartGC, object: nil) //更新“我的”列表
            }else {
                self?.showToast(title: response.message() ?? WHLocalizedString("网络错误请重试"))
            }
        }
    }
    
    private func save() {
        guard WHNetwork.isReachable(),
        model != nil else {
            return
        }
        MTPhotoLibrary.requestAuthorization { [weak self] (status) in
            guard let self = self else { return }
            switch status {
            case .authorized, .limited:
                WHAITagAuthAlertShareManager.showAITagAuthAlertIfNeeded(inView: view) { [weak self] isWaterMark in
                    self?.saveWith(waterMark: isWaterMark)
                }
            default:
                WHInpaindingManager.shared.showAlbumAuthorizationAlert()
            }
        }
    }
    
    private func saveWith(waterMark: Bool) {
        guard let model = model else {
            return
        }
        var url: String?
        switch model.resultType {
        case 0:
            url = waterMark ? model.images.first?.urlWatermark : model.images.first?.url
            if let url = url,
               url.count > 0 {
                downloader = WHMediaDownloadPlugin(mediaType: .image,
                                                   containerVC: self,
                                                   urlStrs: [url])
                downloader?.delegate = self
                downloader?.setup(useDefaultUI: false)
                downloader?.originMediaUrlStrs = [url]
                downloader?.currentIndex = 0
                downloader?.mediaType = .image
                downloader?.start()
            }
        case 1:
            url = waterMark ? model.resultVideo?.url : model.resultVideo?.noWaterMarkUrl
            if let url = url,
               url.count > 0 {
                downloader = WHMediaDownloadPlugin(mediaType: .video,
                                                   containerVC: self,
                                                   urlStrs: [url])
                downloader?.delegate = self
                downloader?.setup(useDefaultUI: false)
                downloader?.originMediaUrlStrs = [url]
                downloader?.currentIndex = 0
                downloader?.watermarkType = .none
                downloader?.mediaType = .video
                downloader?.start()
            }
        case 2:
            url = waterMark ? model.resultVideo?.url : model.resultVideo?.noWaterMarkUrl
            if let url = url,
               let path = createLocalVideoPath(with: url),
               path.count > 0 {
                if WHFileManager.fileExists(atPath: path) {
                    saveLiveWith(path: path)
                } else {
                    progressLoadingView = WHProgressLoadingView.showLoading(text: WHLocalizedString("正在保存中 0%"), in: view) { [weak self] in
                        self?.liveDownloadRequest?.cancel()
                    }
                    liveDownloadRequest = WHBaseRequest.downloadData(with: url, saveTo: path) { [weak self] completedUnitCount, totalUnitCount in
                        self?.progressLoadingView?.progressText = String(format: WHLocalizedString("正在保存中 %.0f%%"), CGFloat(completedUnitCount) / CGFloat(totalUnitCount))
                    } cancel: { [weak self] in
                        self?.progressLoadingView?.hide(animated: true)
                        self?.progressLoadingView = nil
                    } success: { [weak self] in
                        guard let self = self else {
                            return
                        }
                        self.progressLoadingView?.hide(animated: true)
                        self.progressLoadingView = nil
                        self.saveLiveWith(path: path)
                    } failure: { [weak self] error in
                        self?.progressLoadingView?.hide(animated: false)
                        self?.progressLoadingView = nil
                        self?.showToast(title: WHLocalizedString("网络错误请重试"))
                    }
                }
            }
        default:
            break
        }
        if let url = url,
           url.count > 0 {
            WHServerReport.reportSaveSuccess(msgID: msgId ?? "",
                                             taskCategory: taskCategory ?? "",
                                             download: url)
        }
    }
    
    private func saveLiveWith(path: String) {
        let liveGenerator = MTLivePhotoGenerator()
        let options = MTLivePhotoGeneratorOptions()
        options.coverStyle = .capturePercentFrame
        options.captureFramePercent = 0
        liveGenerator.generateLivePhoto(withVideo: path, options: options) {
        } running: { proess in
        } success: { [weak self] asset, count in
            self?.showToast(title: WHLocalizedString("已保存到系统相册"))
        } failure: { [weak self]  error in
            self?.showToast(title: WHLocalizedString("未成功保存至相册"))
        } cancel: {
        }
    }
    
    private func createLocalVideoPath(with urlStr: String) -> String? {
        let cachePath = WHSandbox.libraryPath()+"/"+kWHVideoCacheName
        guard let url = URL(string: urlStr),
              WHFileManager.createDirectory(atPath: cachePath) else {
            return ""
        }
        return cachePath + "/" + url.lastPathComponent
    }
    
    private func again() {
        guard let scheme = model?.scheme,
              scheme.count > 0 else {
            return
        }
        if let priceModel = priceModel {
            if priceModel.freeNum <= 0 {
                if priceModel.isVip {
                    if priceModel.rightNum <= 0,
                       let availableAmount = Int(balanceModel?.availableAmount ?? "0"),
                       let priceAmount = Int(priceModel.amount ?? "0"),
                       availableAmount < priceAmount {
                        // 调起美豆半窗
                        WHVipSdkManager.shared.showSubMeidouWindow(self,
                                                                   params: ["source_page": ""],
                                                                   isSelectMeidou: true)
                        return
                    }
                } else {
                    // 调起会员半窗
                    WHVipSdkManager.shared.showSubscribeWindow(self,
                                                               params: ["source_page": ""])
                    return
                }
            }
        }
        WHRouter.route(with: scheme)
    }
    
    private func aiHD() {
        guard let model = model,
            let url = model.images.first?.url,
            url.count > 0 else {
            return
        }
        let paramsStr = "image_url="+(url.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? "")
        var jumpSchema = "wheeai://app/ai_upscaler?needsLogin=1&" + paramsStr
        if let temId = id,
           temId.count > 0 {
            let temIDParams = "&id="+temId
            jumpSchema = jumpSchema + temIDParams
        }
        if let srNum = model.params?.srNum {
            jumpSchema = jumpSchema + "&srNum=\(srNum)"
        }
        WHRouter.route(with: jumpSchema)
    }
    
    private func oneSentence() {
        guard let model = model else {
            return
        }
        WHRouter.route(with: "wheeai://app/one_sentence?needsLogin=1&image_url=\(model.images.first?.url.urlEncodeString ?? "")&source=", params: ["prompt": model.params?.prompt ?? ""])
    }
    
    private func videoHUD() {
        guard let model = model else {
            return
        }
        let vc = WHVideoHudCreateViewController()
        vc.videoYunUrl = model.resultVideo?.url ?? ""
        vc.temId = "0"
        vc.isLivePhoto = model.resultType == 2 ? true : false
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
}

// MARK: - UICollectionViewDelegateFlowLayout
extension WHSOPDetailsViewController: UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = floor((WH_SCREEN_WIDTH - 32 - CGFloat(btnTypes.count - 1) * 4) / CGFloat(btnTypes.count))
        return CGSize(width: width, height: cellHeight)
    }
    
}

// MARK: - UICollectionViewDataSource
extension WHSOPDetailsViewController: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return btnTypes.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "WHSOPDetailsButtonCell",
                                                      for: indexPath) as! WHSOPDetailsButtonCell
        cell.type = btnTypes[indexPath.row]
        return cell
    }
    
}

// MARK: - UICollectionViewDelegate
extension WHSOPDetailsViewController: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        switch btnTypes[indexPath.row] {
        case .save:
            save()
        case .again:
            again()
        case .HD:
            aiHD()
        case .oneSentence:
            oneSentence()
        case .videoHUD:
            videoHUD()
        }
    }
    
}

// MARK: - JXSegmentedViewDelegate
extension WHSOPDetailsViewController: JXSegmentedViewDelegate {
    
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        guard let type = WHSOPDetailsSegmentType(rawValue: index) else {
            return
        }
        imgInfoView.selectedSegment(type: type)
        refreshSliderState()
    }
    
    func segmentedView(_ segmentedView: JXSegmentedView, canClickItemAt index: Int) -> Bool {
        return segmentedView.selectedIndex != index
    }
    
}

// MARK: - NewMediaDownloadPluginDelegate
extension WHSOPDetailsViewController: NewMediaDownloadPluginDelegate {
    
    func beginLayout(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
    func willBeginDownload(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
    func didBeginDownload(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
    func downloadCancelled(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
    func downloadSuccess(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
    func downloadFailure(_ plugin: WHBusinessCommon.WHMediaDownloadPlugin) {
        
    }
    
}

enum WHSOPDetailsButtonType {
    
    case save
    case again
    case HD
    case oneSentence
    case videoHUD
    
    var title: String {
        switch self {
        case .save:
            return WHLocalizedString("保存")
        case .again:
            return WHLocalizedString("再次创作")
        case .HD:
            return WHLocalizedString("AI超清")
        case .oneSentence:
            return WHLocalizedString("一句话修图")
        case .videoHUD:
            return WHLocalizedString("视频超清")
        }
    }
    
    var imageName: String {
        switch self {
        case .save:
            return "wh_sop_details_save"
        case .again:
            return "wh_sop_details_again"
        case .HD, .videoHUD:
            return "wh_sop_details_hd"
        case .oneSentence:
            return "wh_sop_details_oneSentence"
        }
    }
    
    var animated: Bool {
        return self == .again
    }
    
}

enum WHSOPDetailsSegmentType: Int, CaseIterable {
    
    case before
    case after
    
    var title: String {
        switch self {
        case .before:
            return WHLocalizedString("原图")
        case .after:
            return WHLocalizedString("处理后")
        }
    }
    
}
