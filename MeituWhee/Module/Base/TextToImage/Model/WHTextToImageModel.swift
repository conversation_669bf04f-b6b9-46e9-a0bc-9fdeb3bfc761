//
//  WHTextToImageModel.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2024/3/18.
//

import Foundation
import YYModel
import M<PERSON>ego<PERSON>locksLib
import WHBaseLibrary
import WHBusinessCommon

@objcMembers
class WHTextToImageModel: NSObject, YYModel, NSCoding {
    /// 描述词相关配置
    var prompt: [String : Any]?
    /// 负向词相关配置
    var negativePrompt: [String : Any]?
    /// 基础 模型配置
    var baseModel: WHTextBaseModel?
    /// 尺寸配置
    var size: [WHAICreateVideoSizeModel]?
    /// 张数配置
    var picNum: [String : Any]?
    /// 风格模型配置
    var styleModel: WHStyleModel?
    /// 高级配置
    var senior: WHTextSeniorModel?
    /// 人脸相似度配置
    var faceGeneration: WHStyleModel?
    
    var editModel: WHTextEditModel?
    /// 活动tips
    var actTips: WHTextActTipsModel?
    
    var selectedMVModelIndex: Int = 0
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
        self.updateMVModelIndex()
    }
    
    override class func yy_model(with dictionary: [AnyHashable : Any]) -> Self? {
        let model = super.yy_model(with: dictionary)
        model?.updateMVModelIndex()
        return model
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["baseModel"  : WHTextBaseModel.classForCoder(),
                "size"       : WHAICreateVideoSizeModel.classForCoder(),
                "styleModel" : WHStyleModel.classForCoder(),
                "senior"     : WHTextSeniorModel.classForCoder(),
                "faceGeneration": WHStyleModel.classForCoder(),
                "editModel"  : WHTextEditModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["prompt"        : "prompt",
                "negativePrompt": "negative_prompt",
                "baseModel"     : "base_model",
                "size"          : "size",
                "picNum"        : "pic_num",
                "styleModel"    : "style_model",
                "senior"        : "senior",
                "faceGeneration": "face_generation",
                "editModel"     : "params",
                "actTips"       : "tip"
        ]
    }
    
    func updateMVModelIndex() {
        if let editModel = editModel,
           let baseModel = baseModel {
            for (index, item) in baseModel.listModel.enumerated() {
                if item.modelId == editModel.baseModelId {
                    selectedMVModelIndex = index
                    return
                }
            }
        }
    }
    
    // 描述词字符限制
    func promtLimit() -> Int {
        if let count = prompt?["limit_character"] as? Int {
            return count
        }
        return 800
    }
    // 负向词字符限制
    func negativePromtLimit() -> Int {
        if let count = negativePrompt?["limit_character"] as? Int {
            return count
        }
        return 800
    }
    // 默认张数
    func picNumDefault() -> Int {
        if let count = picNum?["default"] as? Int {
            return count
        }
        return 4
    }
}

@objcMembers
class WHTextBaseModel: NSObject, YYModel, NSCoding {
    
    var name: String = ""
    var listModel: [WHTextBaseListModel] = []
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["listModel": WHTextBaseListModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["name": "name",
                "listModel": "list",
        ]
    }
}

@objcMembers
class WHTextBaseListModel: NSObject, YYModel, NSCoding {
    
    var modelId: Int = 0
    var name: String = ""
    var images: String = ""
    var mvType: Int = -1
    var defaultParams: WHModelDefaultModel?
    
    var isSelected: Bool = false

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["defaultParams": WHModelDefaultModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["modelId": "id",
                "name"   : "name",
                "images" : "images",
                "mvType": "mv_type",
                "defaultParams": "default_params",
        ]
    }
}

@objcMembers
class WHModelDefaultModel: NSObject, YYModel, NSCoding {
    /// 提示词相关性
    var cfgScale: NSNumber = 0
    /// 采样方法 采样器
    var samplerIndex: String = ""
    /// 模型采样步数/采样迭代步数
    var steps: NSInteger = 0
    /// 随机数种子
    var seed: NSInteger = 0

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["cfgScale"    : "cfg_scale",
                "samplerIndex": "sampler_index",
                "steps": "steps",
                "seed" : "seed",
        ]
    }
}

@objcMembers
class WHStyleModel: NSObject, YYModel, NSCoding {
    
    var max: Int = 0
    var name: String = ""
    var tips: WHTextTipsModel?
    var defaultNum: NSNumber = 0

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["tips": WHTextTipsModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["max": "max",
                "name": "name",
                "tips": "tips",
                "defaultNum": "default",
        ]
    }
}

@objcMembers
class WHTextTipsModel: NSObject, YYModel, NSCoding {
    
    var title: String = ""
    var content: String = ""

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["title": "title",
                "content": "content",
        ]
    }
}

// 高级配置
@objcMembers
class WHTextSeniorModel: NSObject, YYModel, NSCoding {
    /// 提示词相关性
    var cfgScale: WHStyleModel?
    /// 采样步数
    var steps: WHStyleModel?
    /// 种子
    var seed: WHStyleModel?
    /// 重绘幅度
    var denoising: WHStyleModel?
    /// 画面控制
    var controlnet: WHControlnetModel?

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["cfgScale": WHStyleModel.classForCoder(),
                "steps": WHStyleModel.classForCoder(),
                "seed": WHStyleModel.classForCoder(),
                "denoising": WHStyleModel.classForCoder(),
                "controlnet": WHControlnetModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["cfgScale": "cfg_scale",
                "steps"   : "steps",
                "seed"    : "seed",
                "denoising"    : "denoising",
                "controlnet": "controlnet"
        ]
    }
}

// 画面控制
@objcMembers
class WHControlnetModel: NSObject, YYModel, NSCoding {
    /// 权重,控制程度 默认值
    var weightDefault: NSNumber = 0
    /// 权重,控制程度  最大值
    var weightMax: Int = 0
    /// 引导介入时机 默认值
    var startDefault: NSNumber = 0
    /// 引导介入时机最大值
    var startMax: Int = 0
    /// 引导退出时机默认值
    var endDefault: NSNumber = 0
    /// 引导退出时机最大值
    var endMax: Int = 0
    /// 预处理器模型列表
    var moduleList: [WHModuleListModel] = []

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["moduleList": WHModuleListModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["weightDefault": "weight_default",
                "weightMax"    : "weight_max",
                "startDefault" : "guidance_start_default",
                "startMax"     : "guidance_start_max",
                "endDefault"   : "guidance_end_default",
                "endMax"       : "guidance_end_max",
                "moduleList"   : "module_list"
        ]
    }
}

// 画面控制-预处理器模型列表
@objcMembers
class WHModuleListModel: NSObject, YYModel, NSCoding {
    /// 名称
    var moduleId: Int = 0
    /// 名称
    var name: String = ""
    /// 预处理器
    var module: String = ""
    /// 对应模型
    var model: String = ""
    var desc: String = ""
    var coverPic:String = ""
    var sampleTips: [String: Any]?
    var choosable_pics: [String] = []
    
    var weight: NSNumber = 0
    /// 权重,控制程度  最大值
    var weightMax: Int = 0
    var weightStep: CGFloat = 0
    /// 引导介入时机 默认值
    var start: NSNumber = 0
    /// 引导介入时机最大值
    var startMax: Int = 0
    /// 引导退出时机默认值
    var end: NSNumber = 0
    /// 引导退出时机最大值
    var endMax: Int = 0

    var guidanceStep: CGFloat = 0

    var interval: NSNumber = 0
    
    var isSelected: Bool = false

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["moduleId" : "id",
                "name"     : "name",
                "module"   : "module",
                "model"    : "model",
                "desc"     : "desc",
                "coverPic" : "cover_pic",
                "sampleTips" : "sample_tips",
                "choosablePics" : "choosable_pics",
                "weight"       : "weight",
                "weightMax"    : "weight_max",
                "weightStep"   : "weight_step",
                "start"        : "guidance_start",
                "startMax"     : "guidance_start_max",
                "end"          : "guidance_end",
                "endMax"       : "guidance_end_max",
                "guidanceStep" : "guidance_step",
                "interval"     : "guidance_interval",
        ]
    }
    
    func choosepicData() -> [WHReferTypePicData] {
        var list:[WHReferTypePicData] = []
        choosable_pics.forEach { pic in
            var model = WHReferTypePicData()
            model.coverPic = pic
            list.append(model)
        }
        return list
    }
}

struct WHReferTypePicData {
    var coverPic: String = ""
    var isSelected: Bool = false
}

// 二次编辑 或者详情页数据
@objcMembers
class WHTextListModel: NSObject, YYModel, NSCoding {
    // 效果id
    var effectId: Int = 0
    var templateId: String = ""
    var msgId: String = ""
    public var imageWidth: CGFloat = 0
    public var imageHeight: CGFloat = 0

    public var favorCount: Int = 0
    // 模型id
    public var modelId: String?
    // 任务种类：txt2img=文生图,img2img=图生图
    public var taskcategory: String?
    // 标题
    public var caption: String = ""
    // 描述
    public var desString: String = ""
    var canUseSame: Int = 0
    //状态 1初始 2 处理中 3失败 4成功 5 发布,10-发布审核中（模型）    
    var status: Int = 0
    var statusMessage: String?
    //是否收藏 1-已被自己收藏 2-未被自己收藏
    var isFavor: Int = 2
    var originPic: String = ""
    var imageKey: String = ""
    var picUrl: String = ""
    // 秀秀feed_id
    public var feedId: String = ""
    var user: WHUserInfo?
    var params: WHTextEditModel?
    var images: [WHTextImageModel] = [] {
        didSet {
            var newImages = images
            for (index, model) in newImages.enumerated() {
                // 报错图片置后
                if model.imageStatus != 1 {
                    newImages.remove(at: index)
                    newImages.append(model)
                }
            }
            images = newImages
        }
    }
    var resultVideo: WHResultVideos?
    /// 0图片，1视频，2live
    var resultType: Int = 0
    var imageIndex: Int = 0
    var bottomOpen: Bool = true
    var infoList:[WHMyCreatInfoModel] = []
    var effectCount: Int = 0 // 超分任务：0-无超分任务，1-存在进行中超分任务，2-超分任务都已经完成    
    var hdStatus: Int = 0
    var modelName: String = ""
    var createTime: NSInteger = 0
    var isExcellent: Bool = false //是否优质内容
    
    // 一键应用模型跳转链接
    public var modelUrl: String = ""
    // 一键创建同款跳转链接
    public var templateUrl: String = ""
    
    var scheme: String = ""
    var tempId: Int = 0
    var limitVip: Bool = false
    
    ///详情页顶部图片的高度
    public var imageCellHeight: CGFloat {
        if images.count > 1 {
            return imgHeight + 80
        }
        return imgHeight
    }
    public var imgHeight: CGFloat = 500 {
        didSet {
            if let layout = extendOriginalImgLayout() {
                images.forEach { $0.extendOriginalImgLayout = layout }
            }
        }
    }
    
    ///作者高度
    public var userInfoHeight: CGFloat = 0
    
    ///创作信息底部高度
    public var myInfoHeight: CGFloat = 400

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["params": WHTextEditModel.classForCoder(),
                "images": WHTextImageModel.classForCoder(),
                "user": WHUserInfo.classForCoder(),
                "resultVideo": WHResultVideos.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["params"   : "params",
                "effectId" : "effect_id",
                "modelId" : "model_id",
                "images" : "result_images",
                "resultVideo": "result_video",
                "templateId": "id",
                "msgId":"msg_id",
                "caption" : "title",
                "user" : "user",
                "canUseSame" : "can_use_same",
                "taskcategory":"task_category",
                "desString" : "desc",
                "status" : "status",
                "statusMessage": "status_message",
                "isFavor" : "is_favor",
                "imageWidth": "width",
                "imageHeight": "height",
                "modelName": "model.name",
                "effectCount" : "template_use_count",
                "favorCount": "favor_count",
                "originPic" : "origin_pic",
                "picUrl" : "pic_url",
                "hdStatus" : "hd_status",
                "feedId" : "feed_id",
                "createTime"   : "created_at",
                "modelUrl" : "apply_style_jump_url",
                "templateUrl" : "apply_template_jump_url",
                "isExcellent" : "is_excellent",
                "resultType": "task_type",
                "scheme": "scheme",
                "tempId": "template_id",
                "limitVip": "limit_vip",
        ]
    }
    
    func getEffectCount() -> String {
        if effectCount >= 10000 {
            return countTrans(count: effectCount)
        }
        return "\(effectCount)"
    }
    
    func getFavorCount() -> String {
        if favorCount >= 10000 {
            return countTrans(count: favorCount)
        }
        return "\(favorCount)"
    }
    
    func getFavorWidth() -> CGFloat {
        var width = getFavorCount().getStringWidth(FlowBaseWidth - 8*2 , font: .systemFont(ofSize: 13))
        width = width < 26 ? 26 : width
       return width
    }
    
    func countTrans(count: Int) -> String {
        let num: Int = count
        let numberFormatter = NumberFormatter()
        numberFormatter.numberStyle = .decimal
        numberFormatter.minimumFractionDigits = 0
        numberFormatter.maximumFractionDigits = 1
        if num >= 10000 {
            let number = Double(num) / 10000.0
            return (numberFormatter.string(from: NSNumber(value: number)) ?? "") + "万"
        } else {
            return numberFormatter.string(from: NSNumber(value: num))!
        }
    }
    
    ///大图的高度
    private func getHeight() -> CGFloat {
        if taskType == .model {
            return WH_SCREEN_WIDTH
        }
        var height: CGFloat = 500
        if imageWidth > 0 && imageHeight > 0 {
            let ration = (imageWidth) / (imageHeight)
            if ration < (3.0 / 4.0) {
                height = WH_SCREEN_WIDTH * 4.0 / 3.0
            } else if ration > (4.0 / 3.0) {
                height = WH_SCREEN_WIDTH * 3.0 / 4.0
            } else {
                height = WH_SCREEN_WIDTH / ration
            }
        }
        return height
    }
    
    public lazy var taskType: WHTaskType = {
        var type:WHTaskType = .textImg
        switch taskcategory {
        case "txt2img":
            type = .textImg
        case "img2img":
            type = .imgImg
        case "model":
            type = .model
        case "extend":
            type = .extend
        case "inpaint":
            type = .inpaint
        case "ai_video":
            type = .aiVideo
        case "ai_eraser":
            type = .aiEraser
        case "magicsr":
            type = .aiMagicsr
        case "ai_template":
            type = .aiTemplate
        case "text_image_editing":
            type = .oneSentence
        default:
            type = .textImg
        }
        return type
    }()
    
    public lazy var taskAnatics: String = {
        switch taskcategory {
        case "txt2img":
            return "text_to_image"
        case "img2img":
            return "image_to_image"
        case "extend":
            return "image_extension"
        case "inpaint":
            return "local_modification"
        case "ai_video":
            return "ai_to_video"
        case "ai_eraser":
            return "ai_clear"
        case "text_image_editing":
            return "text_edit_image"
//        case "project":
//            type = .project
        default:
//            type = .textImg
            return taskcategory ?? ""
        }
    }()
    
    func updateImageHeight(_ type: WHFeedFromType) {
        if taskType == .model {
            imgHeight = getHeight()
        } else if isMine(), type != .collects {
            if taskType == .imgImg || taskType == .textImg {
                imgHeight = getHeight()
            } else {
                // 只显示图片时，扩大显示区域
                imgHeight = WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT - WH_SCREEN_BOTTOM_SPACE - 8 - 56 - 8
                if images.count > 1 {
                    imgHeight -= 80
                }
            }
        } else {
            imgHeight = getHeight()
        }
    }
    
    func updateInfoHeight(_ type: WHFeedFromType = .unkown){
        if taskType == .model{
            updateModelInfoHeight()
        }else if isMine() , type != .collects {
            updateMyInfoHeight()
        } else {
            updateOtherInfoHeight()
        }
        var height = 0.0
        if desString.count > 0 {
            height = desString.getStringHeight(with: kScreenW - 32, font: UIFont.systemFont(ofSize: 13, weight: .regular)) + 5
        }
        
         userInfoHeight = 98 + height
    }
    
    ///模型创作信息高度
    func updateModelInfoHeight(){
        if let params = params {
            var wordModel = WHMyCreatInfoModel()
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "模型"
            wordModel.content = modelName
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            wordModel = WHMyCreatInfoModel()
            wordModel.type = .title
            wordModel.cellHeight = 48
            let createTime = Date(timeIntervalSince1970:TimeInterval(createTime))
            wordModel.title = createTime.formattedString(with: "yyyy-MM-dd HH:mm")
            infoList.append(wordModel)
            var origHeight = 0.0
            for model in infoList {
                origHeight = origHeight + model.cellHeight
            }
            myInfoHeight = origHeight
        }
    }
    
    ///客态创作信息高度
    func updateOtherInfoHeight(){
        if let params = params {
            var wordModel = WHMyCreatInfoModel()
            wordModel.title = "提示词"
            wordModel.type = .contentBottom
            wordModel.cellHeight = 48
            wordModel.copyContent = true
            if params.prompt.count > 0 {
                let height = params.prompt.getStringHeight(with: WH_SCREEN_WIDTH - 32,font: UIFont.systemFont(ofSize: 18, weight: .medium)) + 5
                wordModel.content = params.prompt
                wordModel.cellHeight = 60.0 + height
                infoList.append(wordModel)
            }
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "基础模型"
            wordModel.content = modelName
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            
            let styleList = params.stylelistModel()
            if styleList.count > 0 {
                wordModel = WHMyCreatInfoModel()
                wordModel.title = "风格叠加"
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
                var titleArray: [String] = []
                for model in styleList {
                    titleArray.append(model.name)
                }
                wordModel = WHMyCreatInfoModel()
                wordModel.type = .titleArray
                wordModel.titleArray = titleArray
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
            }
            wordModel = WHMyCreatInfoModel()
            wordModel.type = .title
            wordModel.cellHeight = 48
            let createTime = Date(timeIntervalSince1970:TimeInterval(createTime))
            wordModel.title = createTime.formattedString(with: "yyyy-MM-dd HH:mm")
            infoList.append(wordModel)
            var origHeight = 0.0
            for model in infoList {
                origHeight = origHeight + model.cellHeight
            }
            myInfoHeight = origHeight
        }
    }
    var adaptInfoWidth: CGFloat = (WH_SCREEN_WIDTH - 32.0)
    //更新提示词的高度，用于发布页的切换
    func updateMyInfoPromptHeight() {
        if let params = params {
            for wordModel in infoList {
                if wordModel.title == "提示词", params.prompt.count > 0 {
                    let oriCellHeight = wordModel.cellHeight
                    let promptHeight = params.prompt.getStringHeight(with: adaptInfoWidth,font: UIFont.systemFont(ofSize: 18, weight: .medium)) + 5
                    wordModel.cellHeight = 60.0 + promptHeight
                    let increaseHeight = wordModel.cellHeight - oriCellHeight
                    myInfoHeight = myInfoHeight + increaseHeight
                }
            }
        }
    }
    ///主态创作信息高度
    func updateMyInfoHeight(){
        var origHeight = 0.0
        if let params = params {
            var wordModel = WHMyCreatInfoModel()
            wordModel.title = "提示词"
            wordModel.type = .contentBottom
            wordModel.cellHeight = 48
            if params.prompt.count > 0 {
                let height = params.prompt.getStringHeight(with: adaptInfoWidth,font: UIFont.systemFont(ofSize: 18, weight: .medium)) + 5
                wordModel.content = params.prompt
                wordModel.cellHeight = 60.0 + height
                infoList.append(wordModel)
            }
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "模型"
            wordModel.content = modelName
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            if originPic.count > 0 {
                wordModel = WHMyCreatInfoModel()
                wordModel.title = "原始图片"
                wordModel.image = originPic
                wordModel.type = .imageRight
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
            }
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "尺寸"
            
            let ratioString = params.picRatio
            let whString = "(\(params.width)x\(params.height))"
            let sizeString = ratioString+"  "+whString
            if ratioString == "self-adaption" {
                wordModel.content = params.ratioName
            } else if ratioString == "custom" {
                wordModel.content = whString
            } else {
                wordModel.content = sizeString
            }
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            
            if taskType == .imgImg {//图生图
                wordModel = WHMyCreatInfoModel()
                wordModel.title = "缩放模式"
                /////画面缩放模式：0拉伸 1裁剪 2填充（自动缩放）
                switch params.resizeModel {
                case 0:
                    wordModel.content = "拉伸"
                case 1:
                    wordModel.content = "裁剪"
                case 2:
                    wordModel.content = "自动缩放"
                default:
                    wordModel.content = ""
                }
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
                let styleList = params.stylelistModel()
                if styleList.count > 0 {
                    wordModel = WHMyCreatInfoModel()
                    wordModel.title = "风格叠加"
                    wordModel.cellHeight = wordModel.type.cellHeight()
                    infoList.append(wordModel)
                    var titleArray: [String] = []
                    for model in styleList {
                        titleArray.append(model.name)
                    }
                    wordModel = WHMyCreatInfoModel()
                    wordModel.type = .titleArray
                    wordModel.titleArray = titleArray
                    wordModel.cellHeight = wordModel.type.cellHeight()
                    infoList.append(wordModel)
                    
                }
                
                wordModel = WHMyCreatInfoModel()
                wordModel.title = "重绘幅度"
                wordModel.content = params.denoising.stringValue
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
                
                
                wordModel = WHMyCreatInfoModel()
                wordModel.title = "人脸相似度"
                wordModel.content = String(params.faceGeneration)
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
            } else if taskType == .textImg {
                let styleList = params.stylelistModel()
                if styleList.count > 0 {
                    wordModel = WHMyCreatInfoModel()
                    wordModel.title = "风格叠加"
                    wordModel.cellHeight = wordModel.type.cellHeight()
                    infoList.append(wordModel)
                    var titleArray: [String] = []
                    for model in styleList {
                        titleArray.append(model.name)
                    }
                    wordModel = WHMyCreatInfoModel()
                    wordModel.type = .titleArray
                    wordModel.titleArray = titleArray
                    wordModel.cellHeight = wordModel.type.cellHeight()
                    infoList.append(wordModel)
                }
            }
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "提示词相关性"
            wordModel.content = params.cfgScale.stringValue
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "采样步骤"
            wordModel.content = String(params.steps)
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            
            wordModel = WHMyCreatInfoModel()
            wordModel.title = "Seed"
            if params.seed == -1 {
                wordModel.content = "随机"
            } else {
                wordModel.content = String(params.seed)
            }
            wordModel.cellHeight = wordModel.type.cellHeight()
            infoList.append(wordModel)
            
            if params.controlnetUnits.count > 0 {
                wordModel = WHMyCreatInfoModel()
                wordModel.type = .controlnet
                var controlnets:[WHControlnetInfoModel] = []
                for model in params.controlnetUnits {
                    let controlnet = WHControlnetInfoModel()
                    if model.name.count > 0 {
                        controlnet.title = model.name
                    }
                    controlnet.image = model.inputImage + "?imageMogr2/auto-orient/thumbnail/!10p"
                    controlnet.weight = String(format: "%.2f",model.weight.doubleValue)
                    controlnet.interval = String(format: "%.2f-%.2f",model.start.doubleValue,model.end.doubleValue)
                    controlnets.append(controlnet)
                }
                wordModel.controlnets = controlnets
                wordModel.cellHeight = wordModel.type.cellHeight()
                infoList.append(wordModel)
            }
        }
        for model in infoList {
            origHeight = origHeight + model.cellHeight
        }
        myInfoHeight = origHeight
    }
    
    func isMine() -> Bool {
        return user?.isMine() ?? false
    }
    
    private func extendOriginalImgLayout() -> (size: CGSize, centerOffset: CGPoint)? {
        guard imgHeight > 0,
              taskType == .extend,
              let centerX = params?.centerX,
              let centerY = params?.centerY,
              let originalWidth = params?.originalWidth,
              let originalHeight = params?.originalHeight else {
            return nil
        }
        var scale: Double
        if imageWidth / imageHeight > WH_SCREEN_WIDTH / imgHeight {
            scale = imageWidth / WH_SCREEN_WIDTH
        } else {
            scale = imageHeight / imgHeight
        }
        return (CGSize(width: originalWidth / scale,
                       height: originalHeight / scale),
                CGPoint(x: centerX / scale,
                        y: -centerY / scale))
    }
    
}

@objcMembers
class WHTextImageModel: NSObject, YYModel, NSCoding {
    var url: String = ""           //2.11版本，不带水印的结果
    var urlWatermark: String = "" //水印图片地址，结果页下发
    var previewUrlWatermark: String = "" //预览时用的URL，带AI水印
    
    var hdOriginUrl: String = "" //水印图片地址，结果页下发
    // 图片状态1:成功 2：失败
    var imageStatus: NSInteger = 1
    /// 超分任务：0-无超分任务，1-存在进行中超分任务，2-超分任务都已经完成    
//    var hadSatisfied: Int = 0
    var upscalerInfo: WHUpscalerInfoModel?
    
    var extendOriginalImgLayout: (size: CGSize, centerOffset: CGPoint)?
    
    ///是否超分
    func isSuperDef() -> Bool{
        if let info = upscalerInfo, info.status != 0 {
            return true
        }
        return false
    }
    ///是否展示超分的loading
    func showSuperDefLoading() -> Bool{
        if let info = upscalerInfo, info.status == 2  {
            return true
        }
        return false
    }
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["upscaler_info":WHUpscalerInfoModel.classForCoder()]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "url" : "url",
            "urlWatermark" : "url_watermark",
            "previewUrlWatermark" : "preview_url_watermark",
            "imageStatus" : "image_status",
            "hadSatisfied" : "had_satisfied",
            "upscalerInfo" : "upscaler_info",
            "hdOriginUrl" : "hd_origin_url"
        ]
    }
}
@objcMembers
class WHUpscalerInfoModel: NSObject, YYModel, NSCoding {
      // id
    var infoId:  String = ""
    //任务状态：1-初始化，2-生成中，3-生成成功，4-生成失败
    var status:  Int = 0
    var hdOriginUrl: String = ""
    var urlWatermark: String = ""
    var previewUrlWatermark: String = ""
    var url: String = ""
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return [:]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "infoId"   : "id",
            "hdOriginUrl" : "hd_origin_url",
            "status" : "status",
            "urlWatermark" : "result_image.url_watermark",
            "previewUrlWatermark": "result_image.preview_url_watermark",
            "url" : "result_image.url"
        ]
    }
}

@objcMembers
class WHTextEditModel: NSObject, YYModel, NSCoding {
  
    var prompt: String = ""
    // 基础模型id
    var baseModelId: Int = 0
    
    var picRatio: String = ""
    
    var ratioName: String = ""

    var width: Int = 0

    var height: Int = 0
    // 选择生成的图片数量
    var batchSize: Int = 0
     
    var styleModelConfig:[[String: Any]] = []
    
    var cfgScale: NSNumber = 0
    
    var steps: Int = 0

    var seed: Int = 0
    
    var controlnetUnits: [WHEditControlnetModel] = []
    //图生图原图
    var initImages: [String] = []
    //缩放模式 0拉伸 1裁剪 2填充（自动缩放）
    var resizeModel: NSInteger = 1
    //重绘幅度
    var denoising: NSNumber = 0
    //人脸相似度
    var faceGeneration: NSInteger = 0
    //图片识别
    var facialDetection: Bool = true
    
    var srNum: Int = 0
    
    var centerX: Double = 0
    var centerY: Double = 0
    var originalWidth: Double = 0
    var originalHeight: Double = 0
    
    var scene: String?
    var clearMode: String?

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["controlnetUnits": WHEditControlnetModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["prompt"   : "prompt",
                "baseModelId" : "base_model_id",
                "picRatio" : "pic_ratio",
                "width" : "width",
                "height" : "height",
                "batchSize" : "batch_size",
                "styleModelConfig" : "style_model_config",
                "cfgScale" : "cfg_scale",
                "steps" : "steps",
                "seed" : "seed",
                "controlnetUnits" : "controlnet_units",
                "initImages" : "init_images",
                "resizeModel" : "resize_mode",
                "denoising" : "denoising_strength",
                "faceGeneration" : "face_generation_scale",
                "facialDetection" : "facial_detection",
                "ratioName" : "ratio_name",
                "resizeMode" : "resize_mode",
                "srNum" : "sr_num",
                "centerX": "center_x",
                "centerY": "center_y",
                "originalWidth": "original_width",
                "originalHeight": "original_height",
                "scene": "scene",
                "clearMode": "clear_mode",
        ]
    }
    
    func stylelistModel() -> [WHTextStyleListModel] {
        var styleList: [WHTextStyleListModel] = []
        styleModelConfig.forEach { item in
            let model = WHTextStyleListModel()
            if let modelId = item["style_model_id"] as? Int {
                model.modelId = modelId
            }
            if let weight = item["style_model_weight"] as? Int {
                model.weight = weight
            }
            if let name = item["style_model_name"] as? String {
                model.name = name
            }
            if let image = item["style_model_image"] as? String {
                model.images = image
            }
            styleList.append(model)
        }
        return styleList
    }
}

// MARK: -  风格模型列表
@objcMembers
class WHEditControlnetModel: NSObject, YYModel, NSCoding {
    /// 是否启用
    var enabled: Bool = false
    /// 控制器上传的图片地址
    var inputImage: String = ""
    var name: String = ""
    /// 预处理器
    var module: String?
    /// 预处理器模型id
    var modelId: Int = 0
    // 预处理器模型
    var model: String?
    var weight: NSNumber = 0
    var start: NSNumber = 0
    var end: NSNumber = 0
   
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["enabled"    : "enabled",
                "inputImage" : "input_image",
                "module"     : "module",
                "modelId"    : "model_id",
                "model"      : "model",
                "weight"     : "weight",
                "start"      : "guidance_start",
                "end"        : "guidance_end",
                "name"       : "name"
        ]
    }
    
    func transControlnetModel() -> WHModuleListModel {
        let referModel = WHModuleListModel()
        referModel.name = name
        referModel.model = model ?? ""
        referModel.module = module ?? ""
        referModel.moduleId = modelId
        referModel.start = start
        referModel.end = end
        return referModel
    }
}


// MARK: -  风格模型列表
@objcMembers
class WHTextStyleModel: NSObject, YYModel, NSCoding {
    /// 分类id
    var categoryId: Int = 0
    /// 分类名称
    var categoryName: String = ""
    /// 分页
    var cursor: String = ""
   
    var stylelist:[WHTextStyleListModel] = []

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["stylelist": WHTextStyleListModel.classForCoder(),
                ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["categoryId"   : "category_id",
                "categoryName" : "category_name",
                "cursor"       : "cursor",
                "stylelist"    : "list"
        ]
    }
    
    func transEntityList() -> [MTLegoDisplayEntity]{
        var displayEntitys: [MTLegoDisplayEntity] = []
        stylelist.forEach { item in
            displayEntitys.append(item.transModel())
        }
        return displayEntitys
    }
}

@objcMembers
class WHTextStyleListModel: NSObject, YYModel, NSCoding {
    /// 模型id
    var modelId: Int = 0
    /// 名称
    var name: String = ""
    /// 封面
    var images: String = ""
    /// 模型描述
    var desc: String = ""
    /// 类型6 lora
    var type: Int = 0
    /// 类型名称 lora
    var typeName: String = ""
    /// 默认强度
    var weight: Int = 0
    /// 应用次数
    var applyNum: String = ""
    /// 是否收藏
    var isCollect: Bool = false
    
    var currentIndex: Int = 0

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return ["modelId"   : "id",
                "name"      : "name",
                "images"    : "images",
                "desc"      : "desc",
                "type"      : "type",
                "typeName"  : "type_name",
                "weight"    : "weight",
                "applyNum"  : "apply_num",
                "isCollect" : "is_collect"
        ]
    }
    
    func transModel() -> MTLegoDisplayEntity{
        let model = WHFlowAbstract()
        model.feedId = String(modelId)
        model.modelId = String(modelId)
        model.coverImg = images
        model.caption = name
        model.isFavor = isCollect ? 1 : 2
        model.taskcategory = typeName
        model.taskType = .model
        model.weight = weight
        if let entity = MTLegoDisplayEntityFactory.creatDisplayEntityWithAbstract(model) {
            return entity
        }
        return MTLegoDisplayEntity(with: model)
    }
}

@objcMembers public class WHTextActTipsModel: NSObject, YYModel, NSCoding {
    
    public var isShowTips:Bool = false
    public var tipsTitle:String = ""
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "isShowTips": "show_zcool_new_user_award_tip",
            "tipsTitle": "new_user_award_tip_title",
        ]
    }
}

// 文生图配置参数
struct WHTextToImageParams {
    var prompt: String = ""
    // 用户原始输入（未经过智能联想的输入）
    var userPrompt: String?
    // 负向提示词
    var negativePrompt: String?
    // 基础模型id
    var modelId: Int = 0
    // 尺寸-比例名称
    var ratioName: String = ""
    // 尺寸-比例
    var picRatio: String = ""
    //
    var width: Int = 0
    //
    var height: Int = 0
    // 选择生成的图片数量
    var batchSize: Int = 4
    // 风格模型配置
    var styleModelConfig: [Any]?
    // 风格模型id
    var styleModelId: Int = 0
    // 风格模型-权重，数值1-100
    var styleModelWeight: Int = 0
    // 提示词相关性（输入的prompt和negative prompt对生成结果的影响程度系数）,[1-30],步进0.5，默认7
    var cfgScale: Float = 0
    // 模型采样步数/采样迭代步数，默认20
    var steps: NSInteger = 20
    // 随机数种子，默认-1（开启随机就是传值-1）
    var seed: NSInteger = -1
    // 画面控制
    var controlnetUnits:[Any]?
    // 是否启用
    var enabled: Bool = false
    // 控制器上传的图片地址
    var inputImage: String = ""
    // 预处理器
    var module: String?
    // 预处理器模型
    var model: String?
    // 权重控制程度
    var weight: Int?
    // 引导介入时机（Start）
    var guidanceStart: Double?
    // 引导退出时机（End
    var guidanceEnd: Double?

    // 图片缩放模式--图生图使用
    var zoomType:WHZoomType = .unkown
    // 图生图原图--图生图使用
    var initImageUrl:String = ""
    // 重绘幅度--图生图使用
    var denoising:NSInteger = 80
    // 人脸相似度--图生图使用
    var faceGeneration:NSInteger = 30
    // 图片原始宽度
    var oriWidth: Int = 0
    // 图片原始高度
    var oriHeight: Int = 0
    // 用于埋点
    var controlnetParams:[String : Any] = [:]
    // 最终生成的词和补全的词是否一致 0不一致  1一致(用于埋点)  没有使用过传-1
    var promptSame: Int = -1

    func toJsonString() -> String{
        var params:[String : Any] = [:]
        params["prompt"] = prompt
        params["user_prompt"] = userPrompt
        params["negative_prompt"] = negativePrompt
        params["base_model_id"] = modelId
        params["ratio_name"] = ratioName
        params["pic_ratio"] = picRatio
        params["width"] = width
        params["height"] = height
        params["batch_size"] = batchSize
        params["style_model_config"] = styleModelConfig
        params["cfg_scale"] = cfgScale
        params["steps"] = steps
        params["seed"] = seed
        params["controlnet_units"] = controlnetUnits

        return params.wh_toJsonString() ?? ""
    }

    //图生图获取参数json
    func imageToJsonString() -> String{
        var params:[String : Any] = [:]
        params["prompt"] = prompt
        params["user_prompt"] = userPrompt
        params["negative_prompt"] = negativePrompt
        params["base_model_id"] = modelId
        params["ratio_name"] = ratioName
        params["pic_ratio"] = picRatio
        params["width"] = width
        params["height"] = height
        params["batch_size"] = batchSize
        params["style_model_config"] = styleModelConfig
        params["cfg_scale"] = cfgScale
        params["steps"] = steps
        params["seed"] = seed
        params["controlnet_units"] = controlnetUnits
        //以下是图生图特有
        params["init_images"] = [initImageUrl]
        params["denoising_strength"] = denoising
        params["face_generation_scale"] = faceGeneration
        if zoomType != .unkown {
            params["resize_mode"] = zoomType.rawValue
        }
        
        return params.wh_toJsonString() ?? ""
    }

}

