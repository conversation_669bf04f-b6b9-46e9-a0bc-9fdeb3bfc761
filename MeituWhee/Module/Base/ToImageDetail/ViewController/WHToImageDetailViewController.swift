//
//  WHToImageDetailViewController.swift
//  MeituWhee
//
//  Created by z<PERSON>life<PERSON> on 2024/4/17.
//

import WHBaseLibrary
import WHBusinessCommon
import WHEditorModule
import Alamofire

class WHToImageDetailViewController: WHViewController {
    /// 下载控件
    private var downloader: WHMediaDownloadPlugin?
    public var temId: String?
    public var taskcategory: String?
    public var listModel: WHTextListModel?
    public var funcList: [WHHomeConfigFuncListModel] = []
    private var currentBeansBalance: WHBeansBalanceModel?
    public var superIDarray:[String] = []
    public var feedFromType: WHFeedFromType = .inspiration
    public var tabId: String = ""
    public var imageKey: String = ""
    public var msgId: String = "" //msg_id 用于上报
    //当前Gc消费明细
    var currrentGcPriceModel: WHGcPriceModel?
    private lazy var rightButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_right_detail"), for: .normal)
        button.addTarget(self, action: #selector(rightButtonClick), for: .touchUpInside)
        return button
    }()
    
    private lazy var publicButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "icon_publish_fill_samll"), for: .normal)
        button.setImage(UIImage(named: "icon_publish_fill_samll"), for: .highlighted)
        button.setTitle(WHLocalizedString("发布"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13,weight: .medium)
        button.setTitleColor(UIColor.white, for: .normal)
        button.addTarget(self, action: #selector(publicButtonClick), for: .touchUpInside)
        button.backgroundColor = WHFigmaColor.init(rgb: 0x3549FF)
        button.layer.cornerRadius = 16
        button.clipsToBounds = true
        button.layoutButton(style: .Left, imageTitleSpace: 4)
        button.isHidden = true
        return button
    }()
    
    private lazy var contentView: WHToImageDetailView = {
        let view = WHToImageDetailView()
        view.imageChange = { [weak self] model in
            self?.imageChange(model: model)
        }
        return view
    }()
    
    private lazy var gradientView: WHGradientLayerView = {
        let view = WHGradientLayerView()
        view.startPoint = CGPoint(x: 0.0, y: 0.0)
        view.endPoint = CGPoint(x: 0.0, y: 0.7)
        view.colors = [
            UIColor(r: 0, g: 0, b: 0, a: 0),
            UIColor(r: 0, g: 0, b: 0, a: 1)
        ]
        return view
    }()
    
    private lazy var bottomView: WHDetailBottomView = {
        let view = WHDetailBottomView()
        view.feedFromType = feedFromType
        view.delegate = self
        view.isHidden = true
        return view
    }()
    
    private lazy var myBottomView: WHDetailMyBottoView = {
        let view = WHDetailMyBottoView()
        view.updateMyBottom(evets:[.save, .edit,.superDef,.change,.extend,.aiClear,.more])
        view.clickBlock = { [weak self] (eventType) in
            self?.didClickButtonWith(event: eventType)
        }
        return view
    }()
    
    override func loadRouteParams(_ params: [String : Any]) {
        super.loadRouteParams(params)
        if let temId = params["id"] as? String, temId.count > 0 {
            self.temId = temId
        }
        
        if let msg_id = params["msg_id"] as? String, msg_id.count > 0 {
            self.msgId = msg_id
        }
        
        if let temId = params["task_category"] as? String, temId.count > 0 {
            self.taskcategory = temId
        }
        
        if let temId = params["tab"] as? String, temId.count > 0 {
            self.tabId = temId
        }
        
        if let temId = params["from"] as? String, temId.count > 0 {
            self.feedFromType = WHFeedFromType(rawValue: Int(temId) ?? 0) ?? .unkown
        }
        
        if let orgUrl = params["ori_schema"] as? String,
           let url = URL(string: orgUrl),
           let schemeModel = WHRouterSchemeModel.modelWithSchemeUrl(schemeUrl: url) {
            self.imageKey = schemeModel.queryDic?["imageKey"] as? String ?? ""
        }

    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = WHFigmaColor.backgroundGlobalPage
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(vipStatusDidChanged),
                                               name: .VipStatusDidChanged,
                                               object: nil)
        requestDate()
        requestFuncList()
        requestCurrentGcPrice()
        requestBeansAmount()
    }
    // MARK: - Anaytics
    func pageId()-> String {
        return "my_work_page"
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        MTAnalytics.beginEvent("whee_pages_expo")
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        var params: [String: Any] = [:]
        params["page_name"] = "whee_feed_work_detail_expo"
        params["favor_count"] = "\(listModel?.favorCount ?? 0)"
        params["is_favor"] = "\(listModel?.isFavor ?? 0)"
        params["user_name"] = "\(listModel?.user?.screenName ?? "")"
        params["feed_id"] = listModel?.feedId ?? ""
        params["title"] = listModel?.caption ?? ""
        params["tab"] = tabId
        MTAnalytics.endEvent("whee_pages_expo",attributes: params, type: .PV, source: .default)
    }
    
    override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        super.wh_handleEventWith(name, params: params)
        WHAnalyticsManager.otherTrackEvent("whee_results_page_click", params: ["click_type":"return_click","source": listModel?.taskAnatics ?? ""])
    }
    
    // MARK: - request
    ///请求详情数据
    func requestDate(){
        guard let templateId = self.temId, let cate = taskcategory else {return}
        var typeParams:[String : Any] = [:]
        typeParams[cate] = templateId
        var params:[String : Any] = [:]
        params["type_ids"] = typeParams.wh_toJsonString()
        if (self.feedFromType == .works || self.feedFromType == .history) {
            params["from"] = 6
        } else {
            params["from"] = 5
        }
        WHSharedRequest.GET("/inspiration/batch_feed.json", params: params) { [weak self] response in
            guard let self = self else {return}
            let result = response.data() as? [String: Any]
            if let lists = result?["list"] as? Array<[String: Any]>, let list = lists.first {
                if let model = WHTextListModel.yy_model(with: list) {
                    model.updateImageHeight(self.feedFromType)
                    model.updateInfoHeight(self.feedFromType)
                    self.updateViewWith(model: model)
                    self.updateBottomView()
                }
            }
            self.checkIsShowActivityFloatingView(resultDic: result ?? [:])
        }
    }
    
    func checkIsShowActivityFloatingView(resultDic:[String:Any]) {
        //只有文/图生图的详情页才会展示浮窗
        if let taskcategory = self.taskcategory,taskcategory == "txt2img" || taskcategory == "img2img" {
            let isShow = resultDic["show_jump_zcool_btn"] as? Bool ?? false
            let toast = resultDic["click_btn_toast"] as? String ?? ""
            let picUrl = resultDic["zcool_btn_pic_url"] as? String ?? ""
            let actSource = resultDic["activity_type"] as? String ?? ""
            if isShow {
                let floatingView = WHCommonDetailActView()
                floatingView.settingImageView(imageUrl: picUrl)
                floatingView.toastString = toast
                floatingView.actSource = actSource
                self.view.addSubview(floatingView)
                floatingView.snp.makeConstraints { make in
                    make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE-100)
                    make.right.equalToSuperview().offset(-8.0)
                    make.size.equalTo(CGSizeMake(69.0, 93.0))
                }
            }
        }
    }
    
    func updateBottomView(){
        if let model = self.listModel, model.status != 3{
            if model.user?.isMine() ?? false,
                feedFromType != .collects {
                if model.taskType == .imgImg || model.taskType == .textImg {
//                    publicButton.isHidden = false    //3.0.0版本，隐藏发布按钮，先隐藏，后面社区可能还会用到
                }
                bottomView.isHidden = true
                view.addSubview(myBottomView)
                myBottomView.snp.makeConstraints { make in
                    make.bottom.equalTo(-WH_SCREEN_BOTTOM_SPACE)
                    make.height.equalTo(78)
                    make.left.right.equalToSuperview()
                }
                contentView.snp.remakeConstraints { make in
                    make.top.equalTo(myNavigationBar.snp.bottom)
                    make.left.right.equalToSuperview()
                    make.bottom.equalTo(myBottomView.snp.top)
                }
                self.querySuperDef()
            } else {
                bottomView.feedFromType = self.feedFromType
                bottomView.isHidden = false
                bottomView.tab = self.tabId
            }
        } else {
            publicButton.isHidden = true
            contentView.isHidden = true
            bottomView.isHidden = true
            gradientView.isHidden = true
            self.myNavigationBar.backgroundColor = .clear
            let bgImgView = UIImageView(image: UIImage(named: "wh_common_fail_bg_view_normal"))
            bgImgView.contentMode = .scaleAspectFill
            bgImgView.clipsToBounds = true
            self.view.insertSubview(bgImgView, belowSubview: self.myNavigationBar)
            bgImgView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            let imageView = UIImageView(image: UIImage(named: "wh_common_fail_image_tan_image_normal"))
            self.view.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.size.equalTo(CGSize(width: 100, height: 100))
                make.top.equalTo(250)
                make.centerX.equalToSuperview()
            }
            let label = UILabel()
            label.font = UIFont.pingFangSCFont(ofSize: 20,weight: .medium)
            label.textColor = UIColor(rgb: 0x5C5F66, alpha: 1)
            label.text = "生成失败"
            self.view.addSubview(label)
            label.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.top.equalTo(imageView.snp.bottom)
            }
        }
    }
    
    //消费明细请求
    func requestCurrentGcPrice() {
        WHInpaindingRequest.requestGcPrice(functionCode: "upscaler", count: "1") {[weak self] whResponse, model in
            if whResponse.isSuccess() {
                self?.currrentGcPriceModel = model //刷新
            }
        }
    }
    
    //美豆余额请求
    func requestBeansAmount(sucess:(()->())? = nil) {
        WHInpaindingRequest.requestBalanceAmount {[weak self] whResponse, model in
            if whResponse.isSuccess() {
                self?.currentBeansBalance = model
                sucess?()
            }
        }
    }
    
    
    func requestFuncList(){
        guard let taskcategory = taskcategory else {return}
        var params:[String : Any] = [:]
        params["task_category"] = taskcategory
        WHSharedRequest.GET("/result/extra_config.json", params: params) { [weak self] response in
            let result = response.data() as? [String: Any]
            if let lists = result?["func_list"] as? Array<[String: Any]>,let array = NSArray.yy_modelArray(with: WHHomeConfigFuncListModel.self, json: lists) as? [WHHomeConfigFuncListModel]{
                self?.funcList = array
            }
        }
    }
    
    @objc func vipStatusDidChanged() {
        requestBeansAmount {[weak self] in
            self?.requestSuperDef()
        }
    }
    
    ///超分
    func requestSuperDef(){
        guard let balance = currentBeansBalance, let priceModel = currrentGcPriceModel else {return}
        if Int(balance.availableAmount ?? "") ?? 0 >= Int(priceModel.amount ?? "") ?? 0 {
            guard let listModel = listModel else {return}
            if listModel.images.count > listModel.imageIndex{
                let model = listModel.images[listModel.imageIndex]
                var params:[String : Any] = [:]
                params["msg_id"] = listModel.templateId
                params["image_file"] = model.url
                //1-图生图/文生图（默认值）；2-图像扩展；3-局部重绘；4-AI消除
                if listModel.taskType == .imgImg || listModel.taskType == .textImg {
                    params["msg_type"] = "1"
                } else if listModel.taskType == .extend {
                    params["msg_type"] = "2"
                } else if listModel.taskType == .inpaint {
                    params["msg_type"] = "3"
                } else if listModel.taskType == .aiEraser {
                    params["msg_type"] = "4"
                } else if listModel.taskType == .oneSentence {
                    params["msg_type"] = "8"
                }
                let info = WHUpscalerInfoModel()
                info.status = 2
                model.upscalerInfo = info
                //清除下AI超清失败关闭弹窗的记录
                let urlCacheKey = WHEditorPicHelper.getSDImageCacheKey(model.url)
                WHUserDefaults.removeRecordAIMaigicsrFailAlert(urlCacheKey)
                self.updateViewWith(model: listModel)
                let httpHeader = HTTPHeader(name: "x-mtcc-client", value: WHSharedRequest.requestHeadersMTCC(position_level1: "CreativeTools", function_name: "whee.chaofen.tst"))
                WHSharedRequest.POST("/task/image_upscaler/do.json", params: params,headers: [httpHeader]) { [weak self] response in
                    guard let self = self else {return}
                    let result = response.data() as? [String: Any]
                    if let id = result?["id"] as? String{
                        self.superIDarray.append(id)
                        NotificationCenter.default.post(name: NSNotification.Name.WHEEStartGC, object: nil)
                        let info = WHUpscalerInfoModel()
                        info.status = 2
                        info.infoId = id
                        model.upscalerInfo = info
                        self.batchUpscalerRequest()
                        self.meitouDelet()
                        WHAnalyticsManager.otherTrackEvent("whee_ultra_definition_success",
                                                           params: ["source": listModel.taskAnatics])
                    }else {
                        self.showToast(title: response.message() ?? "网络错误请重试")
                        model.upscalerInfo = nil
                        self.updateViewWith(model: listModel)
                        WHAnalyticsManager.otherTrackEvent("whee_ultra_definition_fail",
                                                           params: ["source": listModel.taskAnatics])
                    }
                }
            }
        } else {
            WHVipSdkManager.shared.showSubMeidouWindow(self,params: [:],isSelectMeidou: priceModel.isVip)
        }
    }
    
    func batchUpscalerRequest(){///轮询超分状态
        if self.superIDarray.count <= 0 {
            return
        }
        WHSharedRequest.GET("/task/image_upscaler/query.json", params: ["ids":superIDarray.joined(separator: ",")]) { [weak self] response in
            guard let self = self, let listModel = self.listModel else {return }
            if let result = response.data() as? Array<[String: Any]>, let array = NSArray.yy_modelArray(with: WHUpscalerInfoModel.self, json: result) as? [WHUpscalerInfoModel] {
                array.forEach { (model) in
                    if model.status > 2 {  // 作品生成中
                        for (index, id) in self.superIDarray.enumerated() {
                            if id == model.infoId {
                                self.superIDarray.safeRemove(at: index)
                                for imageModel in listModel.images {
                                    if let info =  imageModel.upscalerInfo, info.infoId == id {
                                        if model.status == 3 {//生成成功
                                            imageModel.hdOriginUrl = model.hdOriginUrl
                                            imageModel.urlWatermark = model.urlWatermark.count > 0 ? model.urlWatermark : model.url
                                            imageModel.previewUrlWatermark = model.previewUrlWatermark
                                            imageModel.upscalerInfo = model
                                            info.status = 3
                                            self.updateViewWith(model: listModel)
                                        } else if model.status == 4{ //生成失败
                                            info.status = 4
                                            self.updateViewWith(model: listModel)
//                                            self.showToast(title: "AI超清失败，美豆已返还您的账户")
                                            self.meitouAdd()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if self.superIDarray.count > 0 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                        self.batchUpscalerRequest()
                    }
                }
            } else {
                self.showToast(title: response.message() ?? "网络错误请重试")

            }
        }
    }
    
    func meitouDelet(){
        let allCount = Int(self.currentBeansBalance?.availableAmount ?? "0") ?? 0
        let useCount = Int(self.currrentGcPriceModel?.amount ?? "0") ?? 0
        self.currentBeansBalance?.availableAmount =  String(allCount - useCount)
    }
    
    func meitouAdd(){
        let allCount = Int(self.currentBeansBalance?.availableAmount ?? "0") ?? 0
        let useCount = Int(self.currrentGcPriceModel?.amount ?? "0") ?? 0
        self.currentBeansBalance?.availableAmount =  String(allCount + useCount)
    }
    
    ///删除
    func deleteMy(){
        let alertView = WHCommonAlertView(title: WHLocalizedString("是否要删除该作品？"), desStr: WHLocalizedString(""), alertViewType: .allStyle, sureButtonText: WHLocalizedString("确认"))
        alertView.show(in: self.view)
        alertView.alertSureHandler = {[weak self]() in
            self?.deleteCurrentProduct()
        }
    }
    func deleteCurrentProduct() {
        guard let taskcategory = taskcategory, let templateId = temId  else {return}
        var params:[String : Any] = [:]
        params["task_category"] = taskcategory
        params["id"] = templateId
        WHSharedRequest.POST("/task/delete.json", params: params) { [weak self] response in
            let result = response.data() as? [String: Any]
            if let result = result?["result"] as? Bool, result{
                self?.wh_passEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
                NotificationCenter.default.post(name: NSNotification.Name.WHEEStartGC, object: nil) //更新“我的”列表
            }else {
                self?.showToast(title: response.message() ?? "网络错误请重试")
            }
        }
    }
    ///扩图
    func extendClick(){
        if let listModel = listModel, listModel.images.count > listModel.imageIndex{
            let model = listModel.images[listModel.imageIndex]
            var url = model.url
            if let hdUrl = model.upscalerInfo?.url,
               hdUrl.count > 0 {
                url = hdUrl
            }
            let paramsStr = "image_url="+(url.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? "")
            let jumpSchema = "wheeai://app/ai_expand?" + paramsStr
            WHRouter.route(with: jumpSchema)
        }
    }
    ///局部修改
    func changeClick(){
        if let listModel = listModel, listModel.images.count > listModel.imageIndex{
            let model = listModel.images[listModel.imageIndex]
            var url = model.url
            if let hdUrl = model.upscalerInfo?.url,
               hdUrl.count > 0 {
                url = hdUrl
            }
            WHRouter.route(with: "wheeai://app/image_part?needsLogin=1&image_url=\(url)")
        }
    }
    
    //
    func clearClick(){
        if let listModel = listModel, listModel.images.count > listModel.imageIndex{
            let model = listModel.images[listModel.imageIndex]
            var url = model.url
            if let hdUrl = model.upscalerInfo?.url,
               hdUrl.count > 0 {
                url = hdUrl
            }
            WHRouter.route(with: "wheeai://app/ai_erase?image_url=\(url.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? "")")
        }
    }
    
    
    func imageChange(model: WHTextImageModel){
        if let listModel = self.listModel {
            if listModel.user?.isMine() ?? false {
                var array:[WHDetailEventType] = [.save, .edit,.change,.extend,.aiClear,.more]

                if model.imageStatus == 2 {
                    array = [.edit]
                } else if model.isSuperDef() == false || model.upscalerInfo?.status == 4 {
                    array = [.save, .edit,.superDef,.change,.extend,.aiClear,.more]
                } else {
                    array = [.save, .edit,.change,.extend,.aiClear,.more]
                }
                
                if listModel.taskType != .imgImg && listModel.taskType != .textImg && listModel.taskType != .oneSentence && model.imageStatus != 2{
                    array.removeAll { type in
                        type == .edit
                    }
                }
                myBottomView.updateMyBottom(evets: array)
            }
        }
    }
    
    func updateViewWith(model:WHTextListModel){
        self.listModel = model
        self.listModel?.imageKey = imageKey
        contentView.updateViewWith(list: model, type: self.feedFromType)
        bottomView.config(model: model)
        if model.taskType == .imgImg || model.taskType == .textImg {
            if model.status == 5 {//已发布
                publicButton.setImage(nil, for: .normal)
                publicButton.setTitle(WHLocalizedString("已发布"), for: .normal)
                publicButton.isEnabled = false
                publicButton.backgroundColor = WHFigmaColor.init(rgb: 0x222326)
            }
        } else {
            publicButton.isHidden = true
        }
        if let listModel = listModel, listModel.images.count > listModel.imageIndex {
            let imageModel = listModel.images[listModel.imageIndex]
            imageChange(model: imageModel)
        }
//        if let imageModel = listModel?.images.first {
//            imageChange(model: imageModel)
//        }
    }
    ///查询超分
    func querySuperDef(){
        guard let listModel = self.listModel else {return}
        for model in listModel.images {
            if let info = model.upscalerInfo, info.status == 2 {
                self.superIDarray.append(info.infoId)
            }
        }
        batchUpscalerRequest()
        
        if listModel.images.count == 1,
           let info = listModel.images[0].upscalerInfo,
           info.status == 4 {
           // self.showToast(title: "AI超清失败，美豆已返还您的账户")
            self.meitouAdd()
        }else{
            
        }
    }
    
    override func setupSubviews() {
        super.setupSubviews()
        view.addSubview(rightButton)
        view.addSubview(publicButton)
        view.addSubview(contentView)
        view.addSubview(gradientView)
        
        rightButton.snp.makeConstraints { make in
            make.bottom.equalTo(myNavigationBar.snp.bottom).offset(-12)
            make.right.equalToSuperview().offset(-12)
            make.size.equalTo(CGSize(width: 24, height:24))
        }
        
        publicButton.snp.makeConstraints { make in
            make.bottom.equalTo(myNavigationBar.snp.bottom).offset(-8)
            make.right.equalTo(rightButton.snp.left).offset(-16)
            make.size.equalTo(CGSize(width: 74, height:32))
        }
        
        contentView.snp.makeConstraints { make in
            make.top.equalTo(myNavigationBar.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE-8-56-8)
        }
        
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE)
            make.height.equalTo(110)
        }
        
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE-8)
            make.height.equalTo(56)
        }
    }
    
    @objc func rightButtonClick() {
        if let model = self.listModel {
            let detail = WHMoreAlterView()
            if let listModel = listModel, ((listModel.isExcellent == false && listModel.isMine() == false) || (listModel.isExcellent == false && listModel.isMine() == true && listModel.status == 5)) {
                detail.isGrayShare = true
            }
            detail.clickBlock = { [weak self] (eventType) in
                self?.didClickButtonWith(event: eventType)
            }
            var array:[WHDetailEventType] = [.report,.share,.delet]
            if model.user?.isMine() ?? false {
            }else {
                array = [.report,.share]
            }
            if model.status == 3 {
                array.removeFirst { type in
                    type == .share
                }
            }
            detail.updateListWith(array: array)

            detail.show()
        }
        
    }
    
    @objc func publicButtonClick() {
        WHAnalyticsManager.otherTrackEvent("whee_results_page_click", params:[
            "click_type": "publish_click",
            "source": listModel?.taskAnatics ?? ""])
        let vc = WHPublishViewController()
        if let listModel = self.listModel, let dic = listModel.yy_modelToJSONObject(),let model = WHTextListModel.yy_model(withJSON: dic){
            model.updateMyInfoHeight()
            model.bottomOpen = true
            vc.listModel = model
            vc.pushSucess = { [weak self] in
                self?.publicButton.setImage(nil, for: .normal)
                self?.publicButton.setTitle(WHLocalizedString("已发布"), for: .normal)
                self?.publicButton.backgroundColor = WHFigmaColor.init(rgb: 0x222326)
                self?.publicButton.isEnabled = false
                //更新当前作品是否优质
                self?.updateProductExcellent()
            }
            self.navigationController?.pushViewController(vc, animated: true)
        }
    }
    //更新已发布的左边是否为优质内容
    func updateProductExcellent() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            guard let templateId = self.temId, let cate = self.taskcategory else {return}
            var typeParams:[String : Any] = [:]
            typeParams[cate] = templateId
            var params:[String : Any] = [:]
            params["type_ids"] = typeParams.wh_toJsonString()
            WHSharedRequest.GET("/inspiration/batch_feed.json", params: params) { [weak self] response in
                let result = response.data() as? [String: Any]
                if let lists = result?["list"] as? Array<[String: Any]>, let list = lists.first {
                    if let model = WHTextListModel.yy_model(with: list) {
                        self?.listModel?.isExcellent = model.isExcellent
                        self?.listModel?.status = model.status
                    }
                }
            }
        }
    }
    
    func didClickButtonWith(event: WHDetailEventType) {
        switch event {
        case .save:
            downloadMedia()
        case .edit:
            editClick()
        case .superDef:
            superDefClick()
        case .change:
            changeClick()
        case .extend:
            extendClick()
        case .aiClear:
            clearClick()
        case .more:
            moreClick()
        case .share:
            shareClick()
            if let listModel = listModel {
                WHAnalyticsManager.otherTrackEvent("whee_share_click", params:[
                    "location":listModel.isMine() ? "results_page" : "work_detail_page"])
            }
        case .report:
            reportClick()
            if let listModel = listModel {
                WHAnalyticsManager.otherTrackEvent("whee_popup_click", params:[
                    "click_type":"report_click","source":listModel.isMine() ? "results_page" : "work_detail_page"])
            }
        case .delet:
            deleteMy()
        }
        if event.anaticEvent().count > 1 {
            var params: [String: Any] = [
                "click_type":event.anaticEvent(),
                "source": listModel?.taskAnatics ?? ""
            ]
            if taskcategory == "ai_eraser",
               event == .save {
                params["clear_mode"] = listModel?.params?.clearMode ?? ""
                params["clear_type"] = listModel?.params?.scene ?? ""
            }
            WHAnalyticsManager.otherTrackEvent("whee_results_page_click", params:params)
        }
    }
    //超分
    func superDefClick() {
        if let priceModel = self.currrentGcPriceModel{
            if WHDetailInfoManager.share.showAlter {
                let alertView = WHDetailInfoManager.share.showAlterView(title:  WHLocalizedString("快捷AI超清"),desString: "将通过算法对您的图片进行处理，将消耗\(priceModel.amount ?? "0")美豆，是否继续？")
                alertView.alertSureHandler = { [weak self] in
                    self?.requestSuperDef()
                    WHAnalyticsManager.otherTrackEvent("whee_popup_click", params:[
                        "click_type":"ultra_definition_confirm_click",
                        "is_check_box": (WHDetailInfoManager.share.showAlter ? 0 : 1)])
                }
                alertView.alertCancleHandler = { [weak self] in
                    WHAnalyticsManager.otherTrackEvent("whee_popup_click", params:[
                        "click_type":"ultra_definition_cancel_click",
                        "is_check_box": (WHDetailInfoManager.share.showAlter ? 0 : 1)])
                    WHDetailInfoManager.share.showAlter = true
                }
                alertView.show()
                //上报曝光
                WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"ultra_definition", "source": (listModel?.isMine() ?? false) ? "results_page" : "work_detail_page"])
            } else {
                requestSuperDef()
            }
        }
    }
    
    
    
    func editClick() {
        if let listModel = listModel{
            if listModel.taskType == .oneSentence {
                WHRouter.route(with: "wheeai://app/one_sentence?needsLogin=1&image_url=\(listModel.originPic.urlEncodeString ?? "")&source=\(feedFromType.getLoaction())", params: ["prompt": listModel.params?.prompt])
            } else if listModel.taskType == .imgImg {
               WHRouter.route(with: "wheeai://app/img2img?needsLogin=1&id=\(listModel.templateId)&source=\(feedFromType.getLoaction())")
           } else {
               WHRouter.route(with: "wheeai://app/text2img?needsLogin=1&id=\(listModel.templateId)&source=\(feedFromType.getLoaction())")
           }
        }
    }
    
    func moreClick(){
        if let listModel = listModel, listModel.images.count > listModel.imageIndex{
            let model = listModel.images[listModel.imageIndex]
            var url = model.url
            if let hdUrl = model.upscalerInfo?.url,
               hdUrl.count > 0 {
                url = hdUrl
            }
            let more = WHMoreEditeAlterView()
            more.updateListWith(array: self.funcList)
            more.imageUrl = url
            more.source = listModel.taskAnatics
            more.show()
        }
    }
    
    func reportClick(){
        if WHEnvConfigShareManager.environment == .pre {
                        WHRouter.route(with: "wheeai://web?url=https%3A%2F%2Fpre-feedback.meitu.com%2Fm%2Freport%2Fsubmit")
                    }else {
                        WHRouter.route(with: "wheeai://web?url=https%3A%2F%2Ffeedback.meitu.com%2Fm%2Freport%2Fsubmit")
                    }
    }
    
    func shareClick(){
        if let listModel = listModel {
            if (listModel.isExcellent == false && listModel.isMine() == false) || (listModel.isExcellent == false && listModel.isMine() == true && listModel.status == 5) {
                UIViewController.wh_top().showToast(title: WHLocalizedString("为确保内容品质，该作品的分享功能暂已关闭"))
                return;
            }
            if listModel.isExcellent == true { //优质内容，分享的是链接
                WHTextToImageRequest.requestShareInfo(feedId: listModel.templateId, taskCategory: listModel.taskcategory ?? "") { model in
                    if let model = model {
                        var picList:[String] = []
                        for picModel in listModel.images {
                            if picModel.urlWatermark.count > 0,picModel.imageStatus == 1 {
                                picList.append(picModel.urlWatermark)
                            }
                        }
                        let content = ["title":model.title,
                                       "content":model.content,
                                       "url":model.link,
                                       "pic":model.pic,
                                       "pic_list":picList,
                                       "from_page_type": listModel.isMine() ? 0 : 1]
                        WHShareViewManager.showShareSheet(with: content) { resp in
                        }
                    } else {
                        UIViewController.wh_top().showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                    }
                }
            } else if listModel.images.count > listModel.imageIndex {
                let model = listModel.images[listModel.imageIndex]
                WHShareViewManager.showShareSheet(with: ["ui_type":0,"pic": model.url,"from_page_type": listModel.isMine() ? 0 : 1]) { resp in
                }
            } else {
                UIViewController.wh_top().showToast(title: WHLocalizedString("请重试"))
            }
        } else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("请重试"))
        }
    }
    ///下载操作
    @objc func downloadMedia() {
        if !WHNetwork.isReachable() {
            return
        }
        if let listModel = listModel, listModel.images.count > listModel.imageIndex{
            let model = listModel.images[listModel.imageIndex]
            // 如果是 ai_eraser，直接下载
            if taskcategory == "ai_eraser" {
                startImageDownload(with: model.url, msgID: msgId, taskCategory: taskcategory)
                return
            }

            // 否则先检查 AI 授权弹窗
            WHAITagAuthAlertShareManager.showAITagAuthAlertIfNeeded(inView: self.view) { [weak self] isWaterMark in
                guard let self = self else { return }
                let url = isWaterMark ? model.urlWatermark : model.url
                self.startImageDownload(with: url, msgID: msgId, taskCategory: taskcategory)
            }
        }
    }
    
    private func startImageDownload(with url: String, msgID: String, taskCategory: String?) {
        if downloader == nil {
            downloader = WHMediaDownloadPlugin(mediaType: .image,
                                               containerVC: self,
                                               urlStrs: [url])
            downloader?.delegate = self
            downloader?.setup(useDefaultUI: false)
        }
        downloader?.originMediaUrlStrs = [url]
        downloader?.currentIndex = 0
        downloader?.mediaType = .image
        downloader?.start()

        WHServerReport.reportSaveSuccess(msgID: msgID, taskCategory: taskCategory ?? "", download: url)
    }
}

extension WHToImageDetailViewController: WHDetailBottomViewDelegate {
    
    func creatbuttonClick() {
        //
    }
}

extension WHToImageDetailViewController: NewMediaDownloadPluginDelegate {
    func beginLayout(_ plugin: WHMediaDownloadPlugin) {
        
    }
    
    func willBeginDownload(_ plugin: WHMediaDownloadPlugin) {
        
    }
    
    func didBeginDownload(_ plugin: WHMediaDownloadPlugin) {
        
    }
    
    func downloadCancelled(_ plugin: WHMediaDownloadPlugin) {
        
    }
    
    func downloadSuccess(_ plugin: WHMediaDownloadPlugin) {
        WHAnalyticsManager.otherTrackEvent("whee_results_save_success", params:[
            "initial_source": self.feedFromType.getLoaction(), // 上级页面
            "task_id": listModel?.templateId ?? "",
            "source": listModel?.taskAnatics ?? "", // 当前页面
        ])
    }
    
    func downloadFailure(_ plugin: WHMediaDownloadPlugin) {
        WHAnalyticsManager.otherTrackEvent("whee_results_save_fail", params:[
            "initial_source": self.feedFromType.getLoaction(),
            "task_id": listModel?.templateId ?? "",
            "source": listModel?.taskAnatics ?? ""])
    }
    
    
}
