//
//  WHRouterSetViewController.swift
//  MeituWhee
//
//  Created by xiaoqi on 2023/10/5.
//

import Foundation
import UIKit
import WHBusinessCommon
#if DEBUG
import DoraemonKit


class WHRouterSetViewController: UITableViewController{
    var dataArray:[(String,String)] = [("首页","wheeai://app/home"),
                                       ("灵感","wheeai://app/inspiration"),
                                       ("我的","wheeai://app/mine"),
                                       ("设置",WHRouteSetting),
                                       ("小程序","wheeai://miniapp?appid=&"),
                                       ("给个好评","wheeai://app/good_review"),
                                       ("账号和安全","wheeai://app/account_security"),
                                       ("视频编辑入口", "wheeai://app/mediaEditEntrance"),
                                       ("SOP中转页", "wheeai://app/sop_pre?id=3&estimatedCount=5"),
                                       ("SOP详情页", "wheeai://app/sop_details?id=86941791ec8f39ce30ddb038607dccc8&task_category=ai_formula"),
    ]
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        var cell = tableView.dequeueReusableCell(withIdentifier: "UITableViewCell") as? UITableViewCell
        if cell == nil {
            cell = UITableViewCell.init(style: .subtitle, reuseIdentifier: "UITableViewCell")
        }
        cell?.textLabel?.text = dataArray[indexPath.row].0
        cell?.detailTextLabel?.text = dataArray[indexPath.row].1
        return cell ?? UITableViewCell()
    }
    override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataArray.count
    }
    override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        DoraemonManager.shareInstance().hiddenHomeWindow()
        WHRouter.route(with: dataArray[indexPath.row].1)
    }
}
#endif
