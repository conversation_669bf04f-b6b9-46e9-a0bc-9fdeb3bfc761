//
//  WHFlowCategory.swift
//  MeituWhee
//
//  Created by DehengXu on 2025/9/11.
//

import UIKit
import WHBusinessCommon
import YYModel

@objcMembers
open class WHYYModelBase: NSObject, NSCoding, YYModel {
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required override init() {
        super.init()
    }

    required convenience public init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return [:]
    }
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [:]
    }
}

@objcMembers
public class WHFlowCategory: WHYYModelBase, Codable {
    var id: Int = -1
    var name: String = ""
    var icon: String = ""
    
    private var _icons: [String] = []
    private var _ids: [String] = []
    

}

public typealias WHFlowCategories=[WHFlowCategory]

extension WHFlowCategories {
    public func generateTitles() -> [String] {
        return self.map({ $0.name })
    }

    public func generateIds() -> [Int] {
        return self.map( { $0.id })
    }
    
    public func generateIcons() -> [String] {
        return self.map( { $0.icon })
    }
}

public class WHFlowInfoPayload: WHYYModelBase, Codable {
    var cursor: String = "0"
    var list: [WHFlowInfo] = []
}

public class WHFlowInfo_OutputMedia: WHYYModelBase, Codable {
    var type: String = ""
    var cover_url: String = ""
    var url: String = ""
    var width: Int = 0
    var height: Int = 0
}

public class WHCollectResult: WHYYModelBase, Codable {
    var result: Bool? = false
}

typealias WHFlowInfo_InputMedia=WHFlowInfo_OutputMedia

/// 支持的右上角图标类型
enum WHFlowMediaType: Int {
    case image           // 无图标
    case livePhoto      // LivePhoto图标
    case video     // 播放按钮
}

public class WHFlowInfo: WHYYModelBase, Codable {
    var feed_id: Int = -1
    var title: String = ""
    var func_type: Int = -1
    var jump_url: String = ""
    var template_use_count: Int = -1
    var template_use_count_format: String = ""
    var user: _WHUserInfo?
    var enter_medias: [WHFlowInfo_InputMedia]? = []
    var output_medias: [WHFlowInfo_OutputMedia] = []
    var template_id: Int = -1
    var enable_live: Int = -1
    var media_type: String = ""
    var pic_url: String = ""
    var pic_count: Int = -1
    var width: Int = -1
    var height: Int = -1
    var video_url: String = ""
    var origin_pic: String = ""
    var origin_video: String = ""
    var is_collect: Int? = 0 // 是否收藏
    var collect_num: Int? = 0 // 收藏数量
    var message: String? = ""
    var limit_vip: Bool? = false //
    
    func getCoverURL() -> URL? {
        if self.getMediaType() == .image {
            return URL(string: self.output_medias.first?.url ?? "")
        }
        return URL(string: self.output_medias.first?.cover_url ?? "")
    }

    func getOriginURL() -> URL? {
        if self.getMediaType() == .image {
            return URL(string: self.enter_medias?.first?.url ?? "")
        }
        return URL(string: self.enter_medias?.first?.url ?? "")
    }

    func getMediaULR() -> URL? {
        return URL(string: self.output_medias.first?.url ?? "")
    }
    
    func getAvatarURL() -> URL? {
        return URL(string: self.user?.avatar ?? "")
    }
    
    func getMediaType() -> WHFlowMediaType {
        if self.enable_live == 1 {
            return .livePhoto
        }
        if self.media_type != "image" {
            return .video
        }
        return .image
    }
    
    func isLivePhoto() -> Bool {
        return getMediaType() == .livePhoto
    }
    
    func isVideo() -> Bool {
        return getMediaType() == .video
    }
    
    func isImage() -> Bool {
        return getMediaType() == .image
    }
    
    func isSOP() -> Bool {
        return func_type == 3
    }
    
    func isLimitVip() -> Bool {
        return self.limit_vip ?? false
    }
    
    /// 1. 根据 view 的宽度((collectionView.width - 24) / 2)，计算媒体的高度
    /// 2. 媒体高度 + 56
    func getViewSize(width: Float) -> CGSize {
        if width < 0.01 {
            return .zero
        }
        let hwRatio = Float(self.height) / Float(self.width)
        var size: CGSize = .zero
        size.width = CGFloat(width)
        size.height = CGFloat(hwRatio * width + 56)
        return size
    }
}

public enum WHFolowFilterType: Int {
    case all
    case latest
    case hot
}

@objcMembers
public class WHFlowFilter: WHYYModelBase, Codable
//NSObject, NSCoding, Codable, YYModel
{
    var key: Int = -1
    var name: String = ""
    var `default`: Bool = false
    var aKey: Int = -1
    
//    public func encode(with coder: NSCoder) {
//        self.yy_modelEncode(with: coder)
//    }
//    required convenience public init?(coder: NSCoder) {
//        self.init()
//        self.yy_modelInit(with: coder)
//    }
//
//
//    public required override init() {
//        super.init()
//    }
//    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
//        return [:]
//    }
    public override class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "aKey":"key"
        ]
    }
    
}
