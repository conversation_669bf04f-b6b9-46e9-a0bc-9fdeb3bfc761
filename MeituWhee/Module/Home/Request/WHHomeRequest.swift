//
//  WHHomeRequest.swift
//  MeituWhee
//
//  Created by <PERSON> on 2023/10/5.
//

import Foundation
import YYModel
import WHBusinessCommon
import WHBaseLibrary

public class WHHomeRequest: NSObject {
    public static func requestBannerAndFuncData(completion: ((_ whResponse: WHOriginalResponse, _ model: WHHomeConfigModel? ) -> Void)?) {
        WHSharedRequest.GET("/home/<USER>",params: nil) { response in
            let result = response.data() as? [String: Any]
            let model = WHHomeConfigModel.yy_model(with: result ?? [:])
            if ((model == nil)) {
                completion?(response,nil)
            } else {
                completion?(response,model)
            }
        }
    }
    
    struct FlowDataRequestBody {
        var categoryID: String
        var filter: String = "1"
        var cursor: String = ""
        var from = "1" // 1: 首页, 2: 详情
        var excludeFedd = "" // 排除 feed id
    }
    
    public static func requestFlowData(categoryID: String, filter: String="1", cursor: String = "", from: String = "1", excludeFeed: String = "", completion: ((_ whResponse: WHOriginalResponse, _ model: WHFlowInfoPayload? ) -> Void)?) {
        let params = [
            "category": categoryID,
            "filter": filter,
            "cursor": cursor,
            "from": from,
            "excldue_feed_id": excludeFeed
        ]
        WHSharedRequest.GET("/home/<USER>",params: params) { response in
            if let result: WHFlowInfoPayload = response.extractData() {
                completion?(response, result)
            } else {
                completion?(response, nil)
            }
        }
    }
    
    /// URL: /collect/do.json,   https://api-mock.meitu-int.com/project/2406/interface/api/134516
    /// 收藏 feedID,
    /// type: Int: 1 灵感feed 2 风格模型, 3 SOP模版
    /// action: Int 1收藏 2取消收藏
    public static func requestCollect(feedID: String, type: Int, action: Int, completion: ((_ whResponse: WHOriginalResponse, _ model: WHCollectResult? ) -> Void)?) {
        var parameters: [String: Any] = [:]
        parameters["id"] = feedID
        // 收藏业务类型 1 灵感feed 2 风格模型, 3 SOP模版
        parameters["type"] = type
        // 1收藏 2取消收藏
        parameters["action"] = action
        WHSharedRequest.POST("/collect/do.json", params: parameters) {response in
            if let result: WHCollectResult = response.extractData() {
                completion?(response, result)
                if !(result.result ?? false) {
                    WHRouter.topViewController?.showToast(title: WHLocalizedString("收藏失败"))
                }
            } else {
                completion?(response, nil)
                WHRouter.topViewController?.showToast(title: WHLocalizedString("收藏失败"))
            }
        }
    }

    /// URL: /feed_card/info.json, https://api-mock.meitu-int.com/project/2406/interface/api/196343
    /// 根据 id 查询详情,
    /// feedID: String 
    public static func requestFeedCardInfo(feedID: String, completion: ((_ whResponse: WHOriginalResponse, _ model: WHFlowInfo? ) -> Void)?) {
        var parameters: [String: Any] = [:]
        parameters["id"] = feedID
        WHSharedRequest.GET("/feed_card/info.json", params: parameters) {response in
            if let result: WHFlowInfo = response.extractData() {
                completion?(response, result)
            } else {
                completion?(response, nil)
            }
        }
    }
    
}
