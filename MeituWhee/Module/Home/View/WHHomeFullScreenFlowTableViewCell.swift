//
//  WHHomeFullScreenFlowTableViewCell.swift
//  MeituWhee
//
//  Created by DehengXu on 2025-09-15
//  Copyright © 2025 DehengXu. All rights reserved.
//

import UIKit
import SnapKit
import Kingfisher
import SDWebImage
import WHBusinessCommon

// 视频播放cell的代理协议
protocol WHHomeFullScreenFlowTableViewCellDelegate: AnyObject {
    func cellDidTapLike(_ cell: WHHomeFullScreenFlowTableViewCell, _ completion: ((_ result: Bool, _ status: Int) -> Void)?)
    func cellDidTapUse(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidTapAvatar(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidTapBack(_ cell: WHHomeFullScreenFlowTableViewCell)
    //func cellDidTapShare(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidTapDetail(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidProgressChanged(_ cell: WHHomeFullScreenFlowTableViewCell, progress: Float)
    func cellDidStartDragging(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidEndDragging(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellDidTapMedia(_ cell: WHHomeFullScreenFlowTableViewCell)
    func cellFetchBalance() -> WHBeansBalanceModel?
}

class WHHomeFullScreenFlowTableViewCell: UITableViewCell {
    
    // MARK: - Properties
    weak var delegate: WHHomeFullScreenFlowTableViewCellDelegate?
    private(set) var viewModel: WHHomeFullScreenFlowDataViewModel?
    
    // 媒体宽高比 (高度/宽度)
    private var hwRatio: Float = 1.0 {
        didSet {
            updateMediaConstraints()
        }
    }
    
    // 菜单显示状态
    private var isMenuVisible = true {
        didSet {
            updateMenuVisibility()
        }
    }
    
    // 进度条拖拽状态
    private var isDraggingProgress = false {
        didSet {
            updateProgressSliderAppearance()
        }
    }
    
    // MARK: - UI Components
    
    // 模糊背景视图
    private lazy var blurBackgroundView: WHBlurImageView = {
        let blurView = WHBlurImageView()
        //blurView.setImage(UIImage(named: "SampleBackground"))
        return blurView
    }()
    
    // 媒体容器视图 (临时占位)
    private lazy var mediaContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear // 设置为透明，显示底层模糊背景
        return view
    }()
    
    // 媒体图片视图
    private lazy var coverImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.clear
        return imageView
    }()
    
    private lazy var originImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.clear
        return imageView
    }()
    
    // 视频容器视图
    private lazy var videoView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.red.alpha(0.5)
        return view
    }()
    
    private var playButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "wh_home_full_screen_play"), for: .normal)
        btn.frame = CGRect(x: 0, y: 0, width: 64, height: 64)
        btn.isUserInteractionEnabled = false
        return btn
    }()
    
    // 半透明遮罩层 - 暂时注释掉
    /*
    private lazy var gradientMaskView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = false
        view.backgroundColor = .yellow
        view.alpha = 1.0  // 确保初始状态为完全可见
        return view
    }()
    
    private lazy var gradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor.black.withAlphaComponent(0.8).cgColor,
            UIColor.clear.cgColor,
            UIColor.clear.cgColor,
            UIColor.black.withAlphaComponent(0.8).cgColor
        ]
        layer.locations = [0.0, 0.15, 0.75, 1.0]
        return layer
    }()
    */
    
    
    
    
    
    
    
    // 底部信息容器
    private lazy var bottomInfoContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }()
    
    // 用户句柄标签 (如 @Rav01)
    private lazy var userHandleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = .white
        return label
    }()
    
    // 描述文本
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.numberOfLines = 2
        return label
    }()
    
    // 操作按钮容器
    private lazy var actionButtonsContainerView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 右侧边栏容器
    private lazy var rightSidebarContainerView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 收藏按钮
    private lazy var likeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "wh_home_unfavor"), for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 收藏数量标签
    private lazy var likeCountLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        label.textColor = .white
        label.textAlignment = .center
        label.adjustsFontSizeToFitWidth = true
        label.layer.shadowColor = UIColor.black.cgColor
        label.layer.shadowOffset = CGSize(width: 0, height: 0.5)
        label.layer.shadowOpacity = 0.2
        label.layer.shadowRadius = 0.5
        return label
    }()
    
    // 使用按钮
    
    private lazy var useButton: WHStateButton = {
        let button = WHStateButton()
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(useButtonTapped), for: .touchUpInside)
        return button
        
//        let button = UIButton(type: .custom)
//        
//        button.layer.cornerRadius = 16
//        button.clipsToBounds = true
//        
//        // 金色渐变背景
//        let gradientLayer = CAGradientLayer()
//        gradientLayer.colors = [
//            UIColor(red: 249/255, green: 233/255, blue: 189/255, alpha: 1).cgColor,
//            UIColor(red: 247/255, green: 221/255, blue: 166/255, alpha: 1).cgColor,
//            UIColor(red: 247/255, green: 212/255, blue: 143/255, alpha: 1).cgColor
//        ]
//        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
//        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
//        gradientLayer.cornerRadius = 16
//        button.layer.insertSublayer(gradientLayer, at: 0)
//        
//        button.setTitle("使用 20.3万人", for: .normal)
//        button.setTitleColor(.black, for: .normal)
//        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
//        
//        button.addTarget(self, action: #selector(useButtonTapped), for: .touchUpInside)
//        return button
    }()
    
    // Detail 按钮
    private lazy var detailButton: UIButton = {
        let button = UIButton(type: .custom)
        button.layer.cornerRadius = 12
        button.clipsToBounds = true
        button.backgroundColor = UIColor.black.withAlphaComponent(0.2)
        
        button.setImage(UIImage(named: "wh_home_switch"), for: .normal)
        button.tintColor = .white
        
        button.addTarget(self, action: #selector(detailButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 进度条
    private lazy var progressSlider: UISlider = {
        let slider = UISlider()
        
        // 设置轨道图片 - 3像素高的细线效果
        slider.setMinimumTrackImage(createTrackImage(color: .white), for: .normal)
        slider.setMaximumTrackImage(createTrackImage(color: .white), for: .normal)
        //slider.setMaximumTrackImage(createTrackImage(color: UIColor(red: 67/255, green: 70/255, blue: 77/255, alpha: 1)), for: .normal)
        
        // 设置8x8像素的拖拽手柄
        slider.setThumbImage(createThumbImage(size: 6), for: .normal)
        slider.setThumbImage(createThumbImage(size: 8), for: .highlighted)
        
        slider.addTarget(self, action: #selector(progressChanged(_:)), for: .valueChanged)
        slider.addTarget(self, action: #selector(progressDragBegan(_:)), for: .touchDown)
        slider.addTarget(self, action: #selector(progressDragEnded(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])
        return slider
    }()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 禁用隐式动画来避免gradientLayer的布局动画 - 暂时注释掉
        /*
        print("layoutSubviews gradient layer: \(gradientMaskView.bounds)")
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        gradientLayer.frame = gradientMaskView.bounds
        CATransaction.commit()
        */
        
        // 更新使用按钮的渐变层大小
        /*
        if let gradientLayer = useButton.layer.sublayers?.first as? CAGradientLayer {
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            gradientLayer.frame = useButton.bounds
            CATransaction.commit()
        }
         */
    }
    
    override func layoutSublayers(of layer: CALayer) {
        super.layoutSublayers(of: layer)
        // gradientLayer相关代码暂时注释掉
        /*
        gradientLayer.frame = gradientMaskView.bounds
        print("layoutSublayers gradient layer: \(gradientMaskView.bounds)")
        */
    }
    
    // MARK: - Setup UI
    
    private func setupUI() {
        backgroundColor = .black
        selectionStyle = .none
        
        // 添加子视图 - 按层级顺序：模糊背景 -> 视频容器 -> 其他UI组件
        contentView.addSubview(blurBackgroundView)
        contentView.addSubview(mediaContainerView)
        
        // 向媒体容器添加媒体视图
        mediaContainerView.addSubview(originImageView)
        mediaContainerView.addSubview(coverImageView)
        mediaContainerView.addSubview(videoView)
        videoView.isHidden = true
        
        mediaContainerView.addSubview(playButton)
        playButton.isHidden = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleGestureTap))
        tapGesture.cancelsTouchesInView = false  // 不阻止其他手势（如滚动）
        tapGesture.delegate = self
        mediaContainerView.addGestureRecognizer(tapGesture)


        // contentView.addSubview(gradientMaskView) // 暂时注释掉
        contentView.addSubview(bottomInfoContainerView)
        contentView.addSubview(rightSidebarContainerView)
        contentView.addSubview(progressSlider)
        
        // gradientMaskView.layer.addSublayer(gradientLayer) // 暂时注释掉
        
        // 立即设置gradientLayer的初始frame，避免显示延迟 - 暂时注释掉
        /*
        // 注意：此时gradientMaskView还没有最终frame，先用屏幕尺寸
        let screenBounds = UIScreen.main.bounds
        gradientLayer.frame = screenBounds
        */
        
        
        // 底部信息组件
        contentView.addSubview(detailButton)
        bottomInfoContainerView.addSubview(userHandleLabel)
        bottomInfoContainerView.addSubview(descriptionLabel)
        bottomInfoContainerView.addSubview(actionButtonsContainerView)
        
        actionButtonsContainerView.addSubview(likeButton)
        actionButtonsContainerView.addSubview(likeCountLabel)
        actionButtonsContainerView.addSubview(useButton)
        
        setupConstraints()
    }
    
    @objc private func handleGestureTap() {
        self.delegate?.cellDidTapMedia(self)
    }
    
    private func setupConstraints() {
        // 模糊背景视图占满整个cell
        blurBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 视频容器占满整个cell
        mediaContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 媒体视图的基础约束设置
        setupMediaConstraints()
        
        // 渐变遮罩 - 暂时注释掉
        /*
        gradientMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        */
        
        
        
        // 底部信息容器
        bottomInfoContainerView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            
            make.top.equalTo(safeAreaLayoutGuide.snp.bottom).offset(-140)
            make.bottom.equalToSuperview()
            
            //make.height.equalTo(140)
            //make.bottom.equalTo(safeAreaLayoutGuide.snp.bottom)

            //make.leading.trailing.equalToSuperview().inset(12)
            //make.bottom.equalTo(progressSlider.snp.top).offset(-16)
        }
        
        // Detail 按钮（在 userHandleLabel 上方）
        detailButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.bottom.equalTo(bottomInfoContainerView.snp.top)
            make.size.equalTo(24)
        }
        
        // 用户句柄
        userHandleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(24)
        }
        
        // 描述文本
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(userHandleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(38)
        }
        
        // 操作按钮容器
        actionButtonsContainerView.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(48)
        }
        
        // 收藏按钮
        likeButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalToSuperview().offset(8)
            make.size.equalTo(24)
        }
        
        // 收藏数量
        likeCountLabel.snp.makeConstraints { make in
            make.top.equalTo(likeButton.snp.bottom).offset(4)
            make.centerX.equalTo(likeButton)
            make.width.equalTo(40)
        }
        
        // 使用按钮
        useButton.snp.makeConstraints { make in
            make.leading.equalTo(likeButton.snp.trailing).offset(12)
            make.trailing.equalToSuperview().inset(12)
            make.top.equalToSuperview().offset(2)
            make.height.equalTo(48)
        }
        
        
        // 进度条
        progressSlider.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(12)
            //make.top.equalTo(bottomInfoContainerView.snp.bottom).offset(-12)
            make.top.equalTo(actionButtonsContainerView.snp.bottom)
            make.height.equalTo(24)
        }
        
        // 强制立即布局，确保gradientLayer有正确的frame
        self.setNeedsLayout()
        self.layoutIfNeeded()
        
        // 布局完成后，立即更新gradientLayer为正确的frame - 暂时注释掉
        /*
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        gradientLayer.frame = gradientMaskView.bounds
        CATransaction.commit()
        */
        
        // 设置进度条初始状态
        updateProgressSliderAppearance()
    }
    
    // MARK: - Configuration
    
    func configure(with data: WHHomeFullScreenFlowDataViewModel) {
        self.viewModel = data
        guard let info = data.info else { return }
        
        if let url = self.viewModel?.info?.getCoverURL() {
            coverImageView.wh_setImage(url: url) // 封面
            blurBackgroundView.imageView.wh_setImage(url: url) // 背景
        }
        
        if let url = self.viewModel?.info?.getOriginURL() {
            originImageView.wh_setImage(url: url) // 封面
        }
        
        setMediaAspectRatio(Float(info.height) / Float(info.width)) // 更新比例        
        
        useButton.setTitle(info.template_use_count_format , for: .normal)
        useButton.setTitle(info.template_use_count_format , for: .highlighted)
    
        // 配置用户信息
        userHandleLabel.text = data.userHandle
        descriptionLabel.text = info.message
        likeCountLabel.text = "\(data.likeCount)"
        likeButton.isSelected = data.isLiked
        detailButton.isHidden = !info.isImage()
        
        // 配置进度条
        progressSlider.minimumValue = 0
        progressSlider.maximumValue = Float(100.0)
        progressSlider.value = 0
        
        updateUseButton()
    }
    
    func updateUseButton() {
        if let currentItem = self.viewModel,
           let price = currentItem.priceModel {
            if price.freeNum > 0 {
                // SOP有限免次数
                useButton.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_star_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.freeNum)))
                useButton.setBackgroundColor(.color(color: .white))
            } else {
                if price.isVip {
                    if price.rightNum > 0 {
                        useButton.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.rightNum)))
                        useButton.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
                    } else {
                        useButton.setState(.meidou(meidou: Int(price.amount ?? "0") ?? 0, title: WHLocalizedString("立即体验")))
                        useButton.setBackgroundColor(.color(color: .white))
                    }
                } else {
                    useButton.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: nil))
                    useButton.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
                }
            }
        } else {
            useButton.setState(.normal(icon: nil, title: WHLocalizedString(self.viewModel?.info?.template_use_count_format ?? ""), subTitle: nil))
            useButton.setBackgroundColor(.color(color: .white))
        }
    }
    
    // MARK: - Menu Visibility
    
    private func updateMenuVisibility() {
        let alpha: CGFloat = isMenuVisible ? 1.0 : 0.0
        if VideoPlayerManager.shared.isPlaying {
            self.pause()
        } else {
            self.play()
        }
        
        UIView.animate(withDuration: 0.3) {
            //self.safeAreaView.alpha = alpha
            //self.navigationContainerView.alpha = alpha
            //self.bottomInfoContainerView.alpha = alpha
            
            self.userHandleLabel.alpha = alpha
            self.likeButton.alpha = alpha
            self.useButton.alpha = alpha
            self.likeCountLabel.alpha = alpha
            self.descriptionLabel.alpha = alpha
            self.detailButton.alpha = alpha
        }
    }
    
    private func updateProgressSliderAppearance() {
        
        if let isImage = self.viewModel?.info?.isImage() {
            self.progressSlider.isHidden = isImage
        }
        
        UIView.animate(withDuration: 0.2) {
            if self.isDraggingProgress {
                // 拖拽时增强进度条的可见性
                //self.progressSlider.transform = CGAffineTransform(scaleX: 1.0, y: 1.0)
                self.progressSlider.alpha = 1.0
            } else {
                // 正常状态
                //self.progressSlider.transform = .identity
                self.progressSlider.alpha = 0.8
            }
        }
    }
    
    func hideMenu() {
        isMenuVisible = false
    }
    
    func showMenu() {
        isMenuVisible = true
    }
    
    // MARK: - Actions
    
    
    @objc private func likeButtonTapped() {
        delegate?.cellDidTapLike(self) { result, status in
            if result {
                if status == 1 {
                    self.likeButton.setImage(UIImage(named: "wh_home_favor"), for: .normal)
                } else {
                    self.likeButton.setImage(UIImage(named: "wh_home_unfavor"), for: .normal)
                }
            }
        }
    }
    
    @objc private func useButtonTapped() {
        delegate?.cellDidTapUse(self)
        
        guard let currentItem = self.viewModel else {
            return
        }
        
        if let price = currentItem.priceModel {
            if price.freeNum <= 0 {
                if price.isVip {
                    if price.rightNum <= 0,
                       let availableAmount = Int(self.delegate?.cellFetchBalance()?.availableAmount ?? "0"),
                       let priceAmount = Int(price.amount ?? "0"),
                       availableAmount < priceAmount {
                        // 调起美豆半窗
                        if let vc = self.delegate as? UIViewController {
                            let params = WHSubMeidouWindowNotificationParams(viewController: vc, params: ["source_page": ""], selectMeidou: true)
                            NotificationCenter.default.post(name: .showSubMeidouWindow,
                                                            object: nil,
                                                            userInfo: ["params": params])
                        }
                        return
                    }
                } else {
                    // 调起会员半窗
                    if let vc = self.delegate as? UIViewController {
                        let params = WHSubscribeWindowNotificationParams(viewController: vc, params: ["source_page": ""])
                        NotificationCenter.default.post(name: .showSubscribeWindow,
                                                        object: nil,
                                                        userInfo: ["params": params])
                    }
                    return
                }
            }
        }
        
        // 制作
        if let url = currentItem.info?.jump_url {
            var params: [String: Any] = [:]
            WHRouter.route(with: url, viewController: UIViewController.wh_top(), params: params)
        }
    }
    
    @objc private func detailButtonTapped() {
        delegate?.cellDidTapDetail(self)
        
    }
        
    @objc private func progressChanged(_ slider: UISlider) {
        delegate?.cellDidProgressChanged(self, progress: slider.value)
    }
    
    @objc private func progressDragBegan(_ slider: UISlider) {
        isDraggingProgress = true
        delegate?.cellDidStartDragging(self)
    }
    
    @objc private func progressDragEnded(_ slider: UISlider) {
        isDraggingProgress = false
        delegate?.cellDidEndDragging(self)
    }
    
    // MARK: - Public Methods
    
    func updateProgress(_ progress: Float) {
        progressSlider.value = progress
    }
    
    // MARK: - Private Methods
    
    private func createThumbImage(size: Int = 8) -> UIImage? {
        let size = CGSize(width: size, height: size)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        let image = renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            
            // 纯白色圆形，无阴影效果
            UIColor.white.setFill()
            context.cgContext.fillEllipse(in: rect)
        }
        
        // 防止拉伸变形：将整个图片设为不可拉伸区域
        return image.resizableImage(withCapInsets: UIEdgeInsets(top: 4, left: 4, bottom: 4, right: 4), resizingMode: .stretch)
    }
    
    private func createTrackImage(color: UIColor = .white, size: CGSize = CGSize(width: 3, height: 3)) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            color.setFill()
            context.cgContext.fill(rect)
        }
    }
    
    // MARK: - Media Constraints
    
    private func setupMediaConstraints() {
        // 媒体图片视图约束 - 在媒体容器中居中，宽度等于容器宽度
        coverImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalTo(coverImageView.snp.width).multipliedBy(hwRatio)
        }
        originImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 视频容器视图约束 - 与媒体图片视图完全重叠
        videoView.snp.makeConstraints { make in
            make.edges.equalTo(coverImageView)
        }
        
        playButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(64)
        }
    }
    
    private func updateMediaConstraints() {
        // 更新媒体图片视图的高度约束
        coverImageView.snp.remakeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalTo(coverImageView.snp.width).multipliedBy(hwRatio)
        }
    }
    
    // 设置媒体宽高比的公共方法
    func setMediaAspectRatio(_ ratio: Float) {
        hwRatio = ratio
    }
    
    // MARK: - Cell Lifecycle
    
    override func prepareForReuse() {
        super.prepareForReuse()
//        titleLabel.text = nil
//        nicknameLabel.text = nil
//        avatarImageView.image = nil
//        mainImageView.image = nil
//        topIconImageView.isHidden = true
        
        videoView.isHidden = true
        playButton.isHidden = true
    }
}

// MARK: - Reuse Identifier

extension WHHomeFullScreenFlowTableViewCell {
    static var reuseIdentifier: String {
        return String(describing: self)
    }
}

extension WHHomeFullScreenFlowTableViewCell {
    // MARK: - 公共播放方法
    private func loadAndPlayMedia(autoLoop: Bool, logPrefix: String, onStopped: @escaping (String) -> Void) {
        guard let _data = self.viewModel?.info else { return }
        
        //let videoUrl = VideoPlayerManager.shared.player.urlStr ?? ""
        //guard videoUrl != _data.video_url else { return }
        
        let cacheKey = SDImageCache.shared.cachePath(forKey: _data.getCoverURL()?.absoluteString)
        let cover = SDImageCache.shared.imageFromCache(forKey: cacheKey)
        //TODO: 检测系统音量
        //VideoPlayerManager.shared.stop()
        VideoPlayerManager.shared.load(
            videoId: "\(_data.feed_id)",
            url: _data.video_url,
            coverImage: cover,
            on: self.videoView,
            autoLoop: autoLoop,
            maxPlayedTime: .max
        ) { currentId in
            print("\(logPrefix) loading: \(currentId ?? "")")
        } playingBlock: { currentId in
            print("\(logPrefix) playing: \(currentId ?? "")")
            DispatchQueue.main.async {
                self.videoView.isHidden = false
                self.playButton.isHidden = true
            }
            print("\(logPrefix) playing: \(self.videoView.isHidden)")
        } pausedBlock: { currentId in
            print("\(logPrefix) paused: \(currentId ?? "")")
            if !autoLoop {
                self.videoView.isHidden = true
                self.playButton.isHidden = false
            }
        } stoppedBlock: { currentId in
            print("\(logPrefix) stopped: \(currentId ?? "")")
            onStopped(currentId ?? "")
            DispatchQueue.main.async {
                self.videoView.isHidden = true
                self.playButton.isHidden = true
            }
            if !autoLoop {
                //self.videoView.isHidden = true
            }
        }
        // 获取系统音量值
        let systemVolume = AVAudioSession.sharedInstance().outputVolume
        print("\(logPrefix) 当前系统音量: \(systemVolume)")
        VideoPlayerManager.shared.set(mute: systemVolume == 0.0, manual: false)
        print("\(logPrefix) video=\(_data.feed_id), autoLoop=\(autoLoop),url=\(_data.video_url)")

        VideoPlayerManager.shared.preparedBlock = {
            let timeLabel = "00:00/\(Int(VideoPlayerManager.shared.player.duration * 1000).convertMillisecondToHHmmString())"
            print("\(logPrefix) timeLabel=\(timeLabel)")
            self.progressSlider.value = 0
        }
        VideoPlayerManager.shared.seek(toTime: 0)
        //VideoPlayerManager.shared.play()
    }
    
    public func play() {
        guard let data = self.viewModel?.info else { return }
        switch data.getMediaType() {
        case .image:
            break
        case .livePhoto:
            playLivePhoto()
        case .video:
            playVideo()
        }
    }

    public func pause() {
        self.playButton.isHidden = false
        VideoPlayerManager.shared.player.pause()
    }
    
    public func stop() {
        self.playButton.isHidden = true
        VideoPlayerManager.shared.player.stop()
    }
    

    func playVideo() {
        loadAndPlayMedia(
                autoLoop: true,
                logPrefix: "[FullScreen] playable video=\(self.viewModel?.id ?? "")"
            ) { _ in
                
            }
    }
    
    func playLivePhoto() {
//        if hasPlayed {// 是否需要重复触发播放?
//            return
//        }
        loadAndPlayMedia(
                autoLoop: true,
                logPrefix: "[FullScreen] playable livePhoto=\(self.viewModel?.id ?? "")"
            ) { [weak self] currentId in
                guard let self = self else { return }
                print("[FlowCell] playable livePhoto and has played")
                //self.hasPlayed = true
//                self.videoManager.player.seek(toTime: 0.0) { finished in
//                    print("[FlowCell] playable livePhoto, finished: \(finished)")
//                }
            }
    }

}

extension WHHomeFullScreenFlowTableViewCell {
    override func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 如果触摸点在 UIControl 上，手势识别器不处理该触摸
        if touch.view is UIControl {
            return false
        }
                
        return true
    }
  }
