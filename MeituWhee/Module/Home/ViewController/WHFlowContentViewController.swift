//
//  WHFlowContentViewController.swift
//  MeituWhee
//
//  Created by DehengXu on 2025/9/9.
//
//  嵌套滚动内容视图控制器
//  
//  功能说明：
//  - 父容器为 WHHomeFlowViewController
//  - 实现瀑布流布局的 CollectionView
//  - 支持嵌套滚动：当滚动到顶部时，阻止继续向下滚动，让父 TableView 接管
//  - 集成 JXPagingView 框架，作为分页视图的子页面
//  - 支持上拉加载更多功能
//  - 下拉刷新禁用
//
//  核心逻辑：
//  - 通过 UIScrollViewDelegate 监听滚动事件
//  - 计算滚动方向和顶部状态
//  - 在顶部且向下滚动时，限制 contentOffset 防止继续滚动
//  - 向上滚动时完全不受限制，保证用户体验
//

import UIKit
import JXPagingView
import CHTCollectionViewWaterfallLayout
import SnapKit
import WHBusinessCommon

class WHFlowContentData: NSObject, Codable {
    private(set) var category = ""
    private(set) var name = ""
    var cursor: String = ""
    var datas: [WHFlowInfo] = []
    init(name: String, category: String) {
        super.init()
        self.name = name
        self.category = category
    }
}

/// 内容视图控制器
/// 实现瀑布流布局的 CollectionView，支持嵌套滚动
/// 当滚动到顶部时，自动将滚动事件传递给父视图
class WHFlowContentViewController: UIViewController {
    
    // MARK: - UI 组件
    private let pageTitle: String                                    // 页面标题
    private var collectionView: UICollectionView!                   // 瀑布流集合视图
    private(set) var contentData: WHFlowContentData!
    private var waterfallLayout: CHTCollectionViewWaterfallLayout!   // 瀑布流布局
    private var loadingFooterView: UIView!                          // 底部加载视图
    private var loadingLabel: UILabel!                              // 加载提示标签
    private var activityIndicator: UIActivityIndicatorView!         // 加载指示器
    
    // MARK: - 数据源
    //private var dataSource: [String] = []                           // 数据源数组
    private var dataSource: [WHFlowInfo] {
        return contentData.datas
    }
    private var itemHeights: [CGFloat] = []                         // 每个 item 的高度数组
    
    // MARK: - JXPagingView 集成
    private var scrollCallback: ((UIScrollView) -> Void)?           // JXPagingView 滚动回调
    weak var parentFlowViewController: WHHomeFlowViewController?     // 父控制器引用
    var headerConfigModel: WHHomeConfigModel? {
        return self.parentFlowViewController?.headerConfigModel
    }
    
    var filter: String {
        return self.parentFlowViewController?.filter ?? ""
    }
    
    var filterKey: String {
        return self.parentFlowViewController?.filterKey ?? DEFAULT_FLOW_FILTER
    }
    
    // MARK: - 嵌套滚动状态管理
    private var lastContentOffset: CGFloat = 0                      // 上次滚动位置（用于计算滚动方向）
    private var isAtTop: Bool = true                                 // 是否在顶部标志（初始为 true）
    private var lastHorizontalOffset: CGFloat = 0                   // 上次横向滚动位置（用于计算横向滚动变化）
    private var panGestureRecognizer: UIPanGestureRecognizer {
        return collectionView.panGestureRecognizer
    }
    
    // MARK: - 上拉刷新状态管理
    private var isLoadingMore: Bool = false                         // 是否正在加载更多
    private var canLoadMore: Bool = true                            // 是否可以加载更多
    private let loadMoreThreshold: CGFloat = 100                    // 触发加载更多的阈值距离
    
    init(title: String = "", categoryID: String = "", parentViewController: WHHomeFlowViewController? = nil) {
        self.pageTitle = title
        self.parentFlowViewController = parentViewController
        self.contentData = WHFlowContentData(name: title, category: categoryID)
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupData()
        setupCollectionView()
        setupLoadingFooter()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.navigationBar.isHidden = true
        if VideoPlayerManager.shared.isPlaying {
            VideoPlayerManager.shared.pause()
        }
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
    }
    
    public func loadDataIfEmpty() {
        if self.dataSource.count == 0 {
            self.loadDataFromRequest()
        }
    }
    
    public func reloadData() {
        self.contentData.cursor = ""
        self.loadDataFromRequest()
    }
    
    /// 加载信息流数据
    /// - Parameter needMore: true 加载更多， false 重新加载
    private func loadDataFromRequest(needMore: Bool = false) {
        if self.contentData.cursor == "" && needMore {
            // 无需加载更多
            return
        }
        WHHomeRequest.requestFlowData(categoryID: self.contentData.category, filter: self.filterKey, cursor: needMore ? self.contentData.cursor : "") { whResponse, model in
            guard let model = model else { return }
            
            //let flowData: WHFlowInfoPayload = whResponse.extractData() ?? WHFlowInfoPayload()
            print("load more model: \(model)")
            
            if !needMore {// reset data
                self.contentData.datas = []
            }
            self.contentData.datas.append(contentsOf: model.list)
            
//            #if DEBUG
//            print("")
//            for i in 0..<4 {
//                self.contentData.datas.append(contentsOf: model.list)
//            }
//            #endif
            self.contentData.cursor = model.cursor
            self.collectionView.reloadData()
        }
    }
    
    private func setupData() {
        //
        self.loadDataFromRequest()
        // 生成示例数据和随机高度
//        for i in 1...30 {
//            dataSource.append("\(pageTitle) - 第 \(i) 项内容")
//            // 生成 80-200 之间的随机高度
//            let randomHeight = CGFloat.random(in: 80...200)
//            itemHeights.append(randomHeight)
//        }
    }
    
    private func setupCollectionView() {
        // 创建瀑布流布局
        waterfallLayout = CHTCollectionViewWaterfallLayout()
        waterfallLayout.columnCount = 2
        waterfallLayout.minimumColumnSpacing = 10
        waterfallLayout.minimumInteritemSpacing = 10
        waterfallLayout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 60, right: 10) // 底部留出空间给加载视图
        
        // 创建 CollectionView
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: waterfallLayout)
        collectionView.backgroundColor = .black//UIColor.systemBackground
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.bounces = false
        collectionView.register(WHHomeFlowCollectionViewCell.self, forCellWithReuseIdentifier: WHHomeFlowCollectionViewCell.identifier)
        
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let footer = WHPullAutoFooter(style: .picCustomText((initialDesc: nil, releasingDesc: nil, loadingDesc: nil, noMoreDesc: WHLocalizedString("没有更多啦～"))))
        collectionView.addPullToRefresh(footer) { [weak self] in
            guard let self = self else { return }
            print("load more ...")
            collectionView.endRefreshing(at: .bottom)
            self.loadDataFromRequest(needMore: true)
            //self.loadFlowData(cursor: self.cursor, )
            //self.getDataFromWeb(cursor: "", isMore: true, isAuto: false)
        }

    }
    
    /// 设置底部加载视图
    /// 创建包含加载指示器和文本的底部视图
    private func setupLoadingFooter() {
        // 创建底部加载容器视图
        loadingFooterView = UIView()
        loadingFooterView.backgroundColor = UIColor.systemBackground
        
        // 创建加载指示器
        activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.color = UIColor.systemGray
        activityIndicator.hidesWhenStopped = true
        
        // 创建加载文本标签
        loadingLabel = UILabel()
        loadingLabel.text = "加载中..."
        loadingLabel.font = UIFont.systemFont(ofSize: 14)
        loadingLabel.textColor = UIColor.systemGray
        loadingLabel.textAlignment = .center
        
        // 添加子视图
        loadingFooterView.addSubview(activityIndicator)
        loadingFooterView.addSubview(loadingLabel)
        view.addSubview(loadingFooterView)
        
        // 设置自动布局约束
        loadingFooterView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(60)
        }
        
        activityIndicator.snp.makeConstraints { make in
            make.centerX.equalTo(loadingFooterView.snp.centerX).offset(-30)
            make.centerY.equalTo(loadingFooterView.snp.centerY)
        }
        
        loadingLabel.snp.makeConstraints { make in
            make.leading.equalTo(activityIndicator.snp.trailing).offset(8)
            make.centerY.equalTo(loadingFooterView.snp.centerY)
            make.trailing.lessThanOrEqualTo(loadingFooterView.snp.trailing).offset(-16)
        }
        
        // 初始状态隐藏加载视图
        hideLoadingFooter()
    }
    
    /// 更新顶部状态
    /// 判断 CollectionView 是否滚动到接近顶部位置
    /// 用于嵌套滚动逻辑的状态管理
    private func updateTopState() {
        let contentOffsetY = collectionView.contentOffset.y    // 当前滚动位置
        
        // 设置顶部阈值，提前判断接近顶部的状态
        let topThreshold: CGFloat = 10.0                       // 10点阈值
        
        // 判断是否在顶部区域内（contentOffset 接近 0）
        isAtTop = contentOffsetY <= topThreshold
    }
    
    /// 检查是否需要触发加载更多
    /// 当滚动接近底部时触发加载更多逻辑
    private func checkLoadMore() {
        return //
        guard canLoadMore && !isLoadingMore else { return }
        
        let contentHeight = collectionView.contentSize.height
        let scrollViewHeight = collectionView.bounds.height
        let currentOffset = collectionView.contentOffset.y
        
        // 计算距离底部的距离
        let distanceFromBottom = contentHeight - currentOffset - scrollViewHeight
        
        // 当距离底部小于阈值时触发加载更多
        if distanceFromBottom < loadMoreThreshold {
            print("触发加载更多，距离底部: \(distanceFromBottom)")
            loadMoreData()
        }
    }
    
    /// 显示底部加载视图
    /// 开始加载动画并显示加载文本
    private func showLoadingFooter() {
        loadingFooterView.isHidden = false
        activityIndicator.startAnimating()
        loadingLabel.text = "加载中..."
        print("显示加载指示器")
    }
    
    /// 隐藏底部加载视图
    /// 停止加载动画并隐藏视图
    private func hideLoadingFooter() {
        loadingFooterView.isHidden = true
        activityIndicator.stopAnimating()
        print("隐藏加载指示器")
    }
    
    /// 显示加载完成状态
    /// 显示加载完成的提示文本
    private func showLoadingComplete() {
        activityIndicator.stopAnimating()
        loadingLabel.text = "加载完成"
        
        //TODO: 立刻隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.0) {
            self.hideLoadingFooter()
        }
    }
    
    /// 显示没有更多数据状态
    /// 显示没有更多数据的提示文本
    private func showNoMoreData() {
        activityIndicator.stopAnimating()
        loadingLabel.text = "没有更多数据"
        canLoadMore = false
        
        //TODO: 隐藏 或 Toast
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.0) {
            self.hideLoadingFooter()
        }
    }
    
    override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        if name == "CELL_REDRAW" {
            if let cell = params?["info"] as? WHHomeFlowCollectionViewCell {
                print("[cell] : \(cell.frame)")
                if let data = params?["data"] as? WHFlowInfo {
                    print("[cell] : \(data.video_url)")
                }
                
            }
        }
    }
    
    func playIfPossible() {
        let visibleCells = collectionView.visibleCells
        var mostTopPlayableCell: WHHomeFlowCollectionViewCell?
        print("[FlowCell] playable >>", String(repeating: "=", count: 20))
        for c in visibleCells {
            if let cell = c as? WHHomeFlowCollectionViewCell, let parent = self.parentFlowViewController, let data = cell.data, data.getMediaType() != .image {
//                if data.getMediaType() == .image {
//                    continue
//                }
                let frame1 = collectionView.convert(collectionView.bounds, to: parent.pagingView)
                let frame2 = cell.mediaContainerView.convert(cell.mediaContainerView.bounds, to: parent.pagingView)
                let contains = CGRectContainsRect(frame1, frame2)
                print("[FlowCell] playable title=\(data.title), isContains=\(contains), frame1=\(frame1), y:\(frame2.origin.y), frame2=\(frame2)")
                if contains {
                    if let topCell = mostTopPlayableCell {
                        let fr1 = topCell.mediaContainerView.convert(topCell.mediaContainerView.bounds, to: parent.pagingView)
                        //let fr2 = cell.frame
                        print("[FlowCell] playable compare title=\(data.title) fr1=\(fr1), frame2=\(frame2)")
                        if frame2.origin.x <= fr1.origin.x &&
                            frame2.origin.y <= fr1.origin.y {
                            print("[FlowCell] playable select title=\(data.title) fr1=\(fr1), frame2=\(frame2)")
                            mostTopPlayableCell = cell
                        }
                    } else {
                        mostTopPlayableCell = cell
                    }
                }

            }
        }
        if let cell = mostTopPlayableCell, let data = cell.data {
            print("[FlowCell] playable mostTopCell title: \(data.title)")
            cell.play()
        } else {
            VideoPlayerManager.shared.pause() // 暂停视频素材
        }
        print("[FlowCell] playable <<", String(repeating: "=", count: 20), String(repeating: "\n", count: 3))
        
    }
}

// MARK: - UICollectionViewDataSource
extension WHFlowContentViewController: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.dataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: WHHomeFlowCollectionViewCell.identifier, for: indexPath) as! WHHomeFlowCollectionViewCell
        //let itemHeight = itemHeights[indexPath.item]
        let data = self.dataSource[indexPath.item]
        //cell.configure(title: data.title, nickname: data.user?.screenName ?? "Guest", avatarImageUrl: data.user?.avatar, mediaUrl: data.output_medias.first?.url, coverUrl: data.output_medias.first?.cover_url, topIconType: data.getMediaType())
        cell.configure(data: data)
        return cell
    }
    
}

// MARK: - UICollectionViewDelegate
extension WHFlowContentViewController: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        print("[cell] \(cell.frame) , \(indexPath.item)")
        playIfPossible()
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        print("didselect: \(self.dataSource[indexPath.item])")
        if let data = (collectionView.cellForItem(at: indexPath) as? WHHomeFlowCollectionViewCell)?.data {
            if data.func_type == 3 {
                // 进入全屏模式
                let fullScreen = WHHomeFullScreenViewController()
                fullScreen.viewModel.videoDataList = [WHHomeFullScreenFlowDataViewModel.fromFlowInfo(info: data)]
                fullScreen.viewModel.cursor = self.contentData.cursor
                fullScreen.viewModel.filterKey = self.filterKey
                fullScreen.viewModel.filter = self.filter
                fullScreen.viewModel.categoryID = self.contentData.category
                UIViewController.wh_top().wh_show(fullScreen)
            } else {
                // 走路由
                if data.jump_url.hasPrefix("wheeai://") {
                    WHRouter.route(with: data.jump_url, params: [:])
                } else if data.jump_url.hasPrefix("https://") || data.jump_url.hasPrefix("http://") {
                    //Browser
                    WHRouter.route(with: data.jump_url, params: [:])
                }
            }
        }
    }
}

// MARK: - JXPagingViewListViewDelegate
/// JXPagingView 列表视图代理
/// 提供必要的视图和滚动回调，实现与 JXPagingView 的集成
extension WHFlowContentViewController: JXPagingViewListViewDelegate {
    
    /// 返回列表视图
    /// JXPagingView 用此视图作为页面内容
    func listView() -> UIView {
        return view
    }
    
    /// 设置滚动回调
    /// JXPagingView 通过此回调监听子视图的滚动状态
    /// 这是实现嵌套滚动的关键接口
    func listViewDidScrollCallback(callback: @escaping (UIScrollView) -> Void) {
        // 保存回调，在 scrollViewDidScroll 中调用
        self.scrollCallback = callback
    }
    
    /// 返回可滚动视图
    /// JXPagingView 需要知道哪个视图负责滚动
    func listScrollView() -> UIScrollView {
        return collectionView
    }
    
    /// 当 JXPagingView 需要重置子视图的 contentOffset 时调用
    /// 这是嵌套滚动的关键方法
    func listScrollViewWillResetContentOffset() {
        print("🔒 JXPagingView 将重置 CollectionView 的 contentOffset")
        // 确保状态同步
        updateTopState()
    }
}

// MARK: - CHTCollectionViewDelegateWaterfallLayout
extension WHFlowContentViewController: CHTCollectionViewDelegateWaterfallLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 获取视图宽度
        let usableWidth = (self.collectionView.width - 24) / 2
        let cellSize = self.dataSource[indexPath.item].getViewSize(width: Float(usableWidth))
        return cellSize
    }
}

extension WHFlowContentViewController: UIScrollViewDelegate {
    
    /// 滚动事件处理 - 实现嵌套滚动的核心逻辑
    /// 根据 segmentView 位置和 collectionView 状态决定滚动行为
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 1. 获取滚动相关参数
        let currentOffset = scrollView.contentOffset.y        // 当前滚动位置
        scrollView.bounces = (currentOffset > 10) // 当滚动到顶部时，禁止滚动
        
        // 2. 更新顶部状态
        let wasAtTop = isAtTop                                // 记录上一次是否在顶部
        let topThreshold: CGFloat = 5.0                       // 顶部阈值（提前5点判断）
        isAtTop = currentOffset <= topThreshold               // 当前是否在顶部
        
        // 3. 计算滚动方向
        let scrollDirection = currentOffset - lastContentOffset      // 滚动距离差值
        let isScrollingDown = scrollDirection > 0                    // 是否向下滚动
        
        // 4. 获取 segmentView 位置状态
        let isSegmentAtTop = parentFlowViewController?.isSegmentViewAtTop() ?? false
        
        // 5. 核心嵌套滚动逻辑
        let shouldBlockDownScroll: Bool
        
        if !isSegmentAtTop {
            // segmentView 还不在屏幕顶部时的逻辑
            shouldBlockDownScroll = isAtTop && isScrollingDown && wasAtTop
        } else {
            // segmentView 在屏幕顶部时的逻辑
            if !isAtTop {
                shouldBlockDownScroll = false
            } else {
                shouldBlockDownScroll = isScrollingDown && wasAtTop
            }
        }
        
        if shouldBlockDownScroll {
            // 限制 contentOffset 在 0，阻止继续向下滚动
            scrollView.contentOffset.y = 0
            print("阻止向下滚动：交给 mainTableView 处理")
        }
        
        // 6. 检查是否需要加载更多（只在向上滚动时检查）
        if !isScrollingDown && !shouldBlockDownScroll {
            checkLoadMore()
        }
        
        // 7. 处理横向滚动时的 segmentView 同步
        handleHorizontalScrollSync(scrollView)
        
        // 8. 通知 JXPagingView 当前滚动状态（必需的回调）
        scrollCallback?(scrollView)
        
        // 9. 记录当前位置供下次计算使用
        lastContentOffset = scrollView.contentOffset.y
        
        playIfPossible()
    }
    
    /// 处理横向滚动时的 segmentView 同步
    /// 当 collectionView 横向滚动时，同步更新 segmentView 的滚动位置
    private func handleHorizontalScrollSync(_ scrollView: UIScrollView) {
        // 获取当前横向滚动偏移量
        let currentHorizontalOffset = scrollView.contentOffset.x
        
        // 计算横向滚动的变化量
        let horizontalDelta = currentHorizontalOffset - lastHorizontalOffset
        
        // 只有当横向滚动发生实际变化时才进行同步
        if abs(horizontalDelta) > 0.5 {  // 设置一个小的阈值避免频繁调用
            print("检测到横向滚动变化，当前偏移: \(currentHorizontalOffset), 变化量: \(horizontalDelta)")
            
            // 通知父控制器同步 segmentView 的滚动位置
            parentFlowViewController?.syncSegmentViewScroll(with: horizontalDelta)
            
            // 更新上次横向滚动位置
            lastHorizontalOffset = currentHorizontalOffset
        }
    }
    
    /// 开始拖拽时的处理
    /// 初始化滚动状态，为滚动方向计算做准备
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // 记录拖拽开始时的位置，用于后续滚动方向判断
        lastContentOffset = scrollView.contentOffset.y
        lastHorizontalOffset = scrollView.contentOffset.x  // 记录横向滚动起始位置
        updateTopState()  // 更新顶部状态
    }
    
    /// 拖拽结束时的处理
    /// 确保状态同步，处理拖拽结束后的状态更新
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        updateTopState()  // 同步顶部状态
        
        // 如果没有减速，直接检查加载更多
        if !decelerate {
            checkLoadMore()
        }
    }
    
    /// 减速结束时的处理
    /// 滚动完全停止后的最终状态更新
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateTopState()  // 确保最终状态正确
        checkLoadMore()   // 检查是否需要加载更多
    }
    
    /// 滚动动画结束时的处理
    /// 处理程序化滚动（如 setContentOffset）结束后的状态
    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        updateTopState()  // 动画结束后更新状态
        checkLoadMore()   // 检查是否需要加载更多
    }
}

// MARK: - 公共接口方法
extension WHFlowContentViewController {
    
    /// 手动触发刷新
    /// 外部调用此方法可以主动刷新数据
    func refresh() {
        print("手动触发刷新")
        
        // 重置状态
        canLoadMore = true
        isLoadingMore = false
        
        // 模拟刷新数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 重新生成数据
            //self.dataSource.removeAll()
            //self.itemHeights.removeAll()
            self.setupData()
            
            // 刷新 UI
            self.collectionView.reloadData()
            self.hideLoadingFooter()
            
            print("刷新完成")
        }
    }
    
    /// 加载更多数据
    /// 模拟从服务器加载更多数据的过程
    func loadMoreData() {
        guard !isLoadingMore && canLoadMore else { return }
        
        print("开始加载更多数据")
        isLoadingMore = true
        showLoadingFooter()
        
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            let currentCount = self.dataSource.count
            
            // 模拟加载更多数据（每次加载10条）
            if currentCount < 100 { // 假设最多100条数据
//                for i in (currentCount + 1)...(currentCount + 10) {
//                    self.dataSource.append("\(self.pageTitle) - 第 \(i) 项内容")
//                    let randomHeight = CGFloat.random(in: 80...200)
//                    self.itemHeights.append(randomHeight)
//                }
                self.loadDataFromRequest(needMore: true)
                // 刷新 CollectionView
                self.collectionView.reloadData()
                self.showLoadingComplete()
                
                print("加载更多完成，新增10条数据")
            } else {
                // 没有更多数据
                self.showNoMoreData()
                print("没有更多数据")
            }
            
            self.isLoadingMore = false
        }
    }
    
    /// 重置加载状态
    /// 重置所有加载相关的状态标志
    func resetLoadingState() {
        isLoadingMore = false
        canLoadMore = true
        hideLoadingFooter()
        print("重置加载状态")
    }
}
