//
//  FullScreenVideoViewController.swift
//  MeituWhee
//
//  Created by DehengXu on 2025-09-15
//  Copyright © 2025 DehengXu. All rights reserved.
//

import UIKit
import SnapKit
//import Kingfisher
import WHBaseLibrary
import WHB<PERSON>inessCommon

@objc protocol WHFlowListDelegate {
    func currrentIndex() -> Int
    func loadDataFinish()
}

class WHFlowListViewModel {
    //var list: [WHFlowInfo] = []
    weak var uiDelegate: WHFlowListDelegate?
    var videoDataList: [WHHomeFullScreenFlowDataViewModel] = []

    var cursor: String = ""
    var filter: String = ""
    var filterKey: String = "1"
    var categoryID = ""
    var pageNum: Int = 0
    var currentIndex: Int {
        return uiDelegate?.currrentIndex() ?? 0
    }
    
    private(set) var isLoading: Bool = false
    
    func append(infos: [WHFlowInfo] = []) {
        do {
            let list = infos.map( { WHHomeFullScreenFlowDataViewModel.fromFlowInfo(info: $0)})
            self.videoDataList.append(contentsOf: list)
        } catch  {
            print("append error: \(error)")
        }
    }
    
    var currentData: WHHomeFullScreenFlowDataViewModel? {
        if self.currentIndex >= 0 && self.currentIndex < self.videoDataList.count {
            return self.videoDataList[self.currentIndex]
        }
        return nil
    }
    
    private func excludeFeedID() -> String {
//        if self.pageNum == 0 {
//            return self.videoDataList.first?.id ?? ""
//        }
        return ""
    }
    
    func loadDataFromRequest(needMore: Bool = false) {
        if isLoading {
            print("[FullScreen] data is loading.")
            return
        }
        
        if self.cursor == "" && // cursor 为空，说明没有后续数据
            needMore &&  // 加载更多时如果 cursor 为空，则无需加载
            pageNum != 0 // 第一页需要强制加载
        {
            // 无需加载更多
            return
        }
                
        isLoading = true
        
        WHHomeRequest.requestFlowData(categoryID: self.categoryID, filter: self.filterKey, cursor: needMore ? self.cursor : "", from: "2", excludeFeed: self.excludeFeedID()) { whResponse, model in
            
            defer {
                self.isLoading = false
            }
            
            guard let model = model else { return }
            
            //let flowData: WHFlowInfoPayload = whResponse.extractData() ?? WHFlowInfoPayload()
            print("load more model: \(model)")
            
            if !needMore {// reset data
                //self.contentData.datas = []
                self.videoDataList = []
            }
            let list = model.list.map { WHHomeFullScreenFlowDataViewModel.fromFlowInfo(info: $0) }
//            #if DEBUG
//            if list.count > 0 {
//                list[0].info?.limit_vip = true
//            }
//            #endif
            self.videoDataList.append(contentsOf: list)
            self.pageNum += 1
            self.cursor = model.cursor
//            #if DEBUG
//            for i in 0..<4 {
//                self.videoDataList.append(contentsOf: list)
//            }
//            #endif
            
            self.uiDelegate?.loadDataFinish()
        }
    }
}

/// 全屏视频播放控制器
/// 实现仿抖音风格的全屏垂直滚动视频播放界面
/// 支持菜单显示/隐藏、进度条控制、侧滑返回等功能
class WHHomeFullScreenViewController: WHViewController {
    
    // MARK: - Properties
    
    /// 视频数据源数组
    lazy var viewModel: WHFlowListViewModel = {
        let vm = WHFlowListViewModel()
        vm.uiDelegate = self
        return vm
    }()
//    private var videoDataList: [VideoDataModel] = []
//    private var cursor: String = ""
//    private var filter: String = ""
//    private var categoryID = ""
//    private var pageNum = 0
    
    /// 当前播放视频的索引
    /// 当索引改变时自动更新视频播放状态
    private var currentIndex: Int = 0 {
        didSet {
            updateVideoState()
        }
    }
    
    /// 菜单显示状态
    /// true: 显示操作菜单（点赞、分享、进度条等）
    /// false: 隐藏菜单，进入沉浸式观看模式
    private var isMenuVisible = true
    
    
    /// 安全区域遮罩视图
    /// 用于适配刘海屏和灵动岛，在菜单显示时提供黑色半透明遮罩
    /// 避免内容被状态栏区域遮挡
    private lazy var safeAreaMaskView: UIView = {
        let view = UIView()
        view.backgroundColor = .black.withAlphaComponent(0.5)  // 半透明黑色背景
        view.alpha = 1.0  // 默认可见，通过菜单状态控制显示
        return view
    }()
    
    /// 导航栏容器
    /// 固定在控制器顶部，显示当前视频的用户信息和操作按钮
    private lazy var navigationContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .black.withAlphaComponent(0.5)
        return view
    }()
    
    /// 返回按钮
    private lazy var backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(.init(named: "icon_navigation_back_white"), for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 用户信息容器
    private lazy var userInfoContainerView: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 用户头像
    private lazy var userAvatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 13
        imageView.clipsToBounds = true
        imageView.isUserInteractionEnabled = true
        //let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        //imageView.addGestureRecognizer(tapGesture)
        return imageView
    }()
    
    /// 用户名标签
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = .white
        return label
    }()
    
    /// 分享按钮
    private lazy var shareButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "wh_home_share"), for: .normal)
        button.tintColor = .white
        button.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - UI Components
    
    /// 主要的视频展示TableView
    /// 使用分页模式实现全屏垂直滚动效果
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.isPagingEnabled = true    // 启用分页滚动，每次滚动一个完整的屏幕高度
        tableView.showsVerticalScrollIndicator = false  // 隐藏滚动指示器，保持界面简洁
        tableView.separatorStyle = .none    // 移除分割线
        tableView.backgroundColor = .black  // 黑色背景，营造视频播放氛围
        tableView.bounces = false          // 禁用弹性滚动，避免露出边界
        tableView.register(WHHomeFullScreenFlowTableViewCell.self, forCellReuseIdentifier: WHHomeFullScreenFlowTableViewCell.reuseIdentifier)
        return tableView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadVideoData()
    }
    
    /// 禁用点击状态栏回到顶部的功能
    /// 在视频播放场景下，避免用户误操作导致跳转到第一个视频
    func scrollViewShouldScrollToTop(_ scrollView: UIScrollView) -> Bool {
        return false
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 隐藏导航栏，营造全屏沉浸式体验
        // 用户通过系统侧滑返回手势或返回按钮退出
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 恢复导航栏显示，确保返回到其他页面时UI正常
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 设置tableView的行高为屏幕高度，实现全屏分页效果
        tableView.rowHeight = view.bounds.height
        
        // 配置TableView的安全区域适配，确保第一个Cell从灵动岛下方开始显示
        let safeAreaTop = view.safeAreaInsets.top
        let currentInsetTop = tableView.contentInset.top
        
        // 移除安全区域的contentInset设置，避免产生多余的滚动空间
        // 让Cell内部通过safeAreaLayoutGuide来处理安全区域适配
        print("安全区域信息 - safeAreaTop: \(safeAreaTop), 移除contentInset设置")
    }
    
    /// 状态栏样式：白色文字，适配黑色背景
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    /// 状态栏显示控制：菜单隐藏时同时隐藏状态栏
    /// 实现完全沉浸式的视频观看体验
    override var prefersStatusBarHidden: Bool {
        return !isMenuVisible
    }
    
    // MARK: - Setup UI
    
    /// 设置用户界面
    private func setupUI() {
        view.backgroundColor = .black
        
        // 添加主要的视频展示TableView
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            //make.edges.equalToSuperview()  // 全屏布局
            make.left.right.top.bottom.equalToSuperview()
        }
        
        // 添加安全区域遮罩视图
        // 用于适配iPhone的刘海屏和灵动岛
        // 在状态栏区域提供半透明黑色遮罩，确保状态栏文字可见性
        view.addSubview(safeAreaMaskView)
        safeAreaMaskView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.top)  // 覆盖状态栏区域
        }
        
        // 添加导航栏组件
        view.addSubview(navigationContainerView)
        navigationContainerView.addSubview(backButton)
        navigationContainerView.addSubview(userInfoContainerView)
        navigationContainerView.addSubview(shareButton)
        
        userInfoContainerView.addSubview(userAvatarImageView)
        userInfoContainerView.addSubview(usernameLabel)
        
        // 设置导航栏约束
        navigationContainerView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(48)
        }
        
        // 返回按钮
        backButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.size.equalTo(40)
        }
        
        // 分享按钮
        shareButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.size.equalTo(40)
        }
        
        // 用户信息容器
        userInfoContainerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.greaterThanOrEqualTo(backButton.snp.trailing).offset(8)
            make.trailing.lessThanOrEqualTo(shareButton.snp.leading).offset(-8)
        }
        
        // 用户头像
        userAvatarImageView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.size.equalTo(26)
        }
        
        // 用户名
        usernameLabel.snp.makeConstraints { make in
            make.leading.equalTo(userAvatarImageView.snp.trailing).offset(8)
            make.trailing.centerY.equalToSuperview()
        }
        
        
        // 禁用系统的自动contentInset调整，我们将手动管理
        tableView.contentInsetAdjustmentBehavior = .never
        
    }
    
    // MARK: - Data Loading
    
    /// 加载视频数据
    /// 当前使用模拟数据，实际项目中可替换为网络请求
    private func loadVideoData() {
        //videoDataList = VideoDataModel.mockData()
        //self.viewModel.videoDataList = VideoDataModel.mockData()
        tableView.reloadData()
        viewModel.loadDataFromRequest(needMore: true)
        // 初始化导航栏数据
        updateNavigationBar()
    }
    
    // MARK: - Actions
        
    /// 切换菜单可见性
    /// 实现沉浸式观看和操作界面之间的切换
    private func toggleMenuVisibility() {
        isMenuVisible = !isMenuVisible
        updateMenuAndStatusBar()
    }
    
    /// 更新菜单和状态栏显示状态
    /// 包含动画效果，提供流畅的视觉体验
    private func updateMenuAndStatusBar() {
        // 动画更新状态栏和导航栏
        UIView.animate(withDuration: 0.3) {
            self.setNeedsStatusBarAppearanceUpdate()  // 触发状态栏显示/隐藏
            
            // 控制导航栏元素显示/隐藏的透明度
            let alpha: CGFloat = self.isMenuVisible ? 1.0 : 0.0
            
            // 只控制导航栏容器及其子视图的显示/隐藏
            // safeAreaMaskView 始终保持可见，确保状态栏文字可见性
            // self.navigationContainerView.alpha = alpha
            self.backButton.alpha = alpha
            self.shareButton.alpha = alpha
            self.userAvatarImageView.alpha = alpha
            self.usernameLabel.alpha = alpha
        }
        
        // 通知当前可见的cell更新菜单状态
        // Cell内部会控制具体的UI元素（按钮、进度条等）显示/隐藏
        if let visibleCell = getCurrentVisibleCell() {
            if isMenuVisible {
                visibleCell.showMenu()  // 显示操作按钮、进度条等
            } else {
                visibleCell.hideMenu()  // 隐藏UI，进入沉浸模式
            }
        }
    }
    
    /// 拖拽进度条时隐藏菜单
    /// 避免拖拽时UI元素干扰操作
    private func hideMenuDuringDrag() {
        guard isMenuVisible else { return }
        isMenuVisible = false
        updateMenuAndStatusBar()
    }
    
    /// 结束拖拽后显示菜单
    /// 恢复正常的操作界面
    private func showMenuAfterDrag() {
        guard !isMenuVisible else { return }
        isMenuVisible = true
        updateMenuAndStatusBar()
    }
    
    // MARK: - Helper Methods
    
    /// 获取当前可见的视频Cell
    /// 用于控制当前播放视频的菜单状态
    private func getCurrentVisibleCell() -> WHHomeFullScreenFlowTableViewCell? {
        let visibleCells = tableView.visibleCells
        return visibleCells.first as? WHHomeFullScreenFlowTableViewCell
    }
    
    /// 更新视频播放状态
    /// 当用户滚动到不同视频时调用
    /// TODO: 在实际项目中可以添加视频播放控制逻辑
    private func updateVideoState() {
        // 这里可以添加视频播放状态管理逻辑
        // 例如：暂停当前播放的视频，开始播放新的视频
        print("当前视频索引: \(currentIndex)")
        
        // 更新导航栏数据
        updateNavigationBar()
    }
    
    /// 根据滚动位置计算当前视频索引
    /// 用于跟踪用户当前观看的是哪个视频
    private func calculateCurrentIndex() {
        let offsetY = tableView.contentOffset.y
        let newIndex = Int(round(offsetY / tableView.bounds.height))
        if newIndex != currentIndex && newIndex >= 0 && newIndex < viewModel.videoDataList.count {
            currentIndex = newIndex
        }
    }
    
    /// 更新导航栏显示内容
    /// 根据当前视频索引更新导航栏中的用户信息
    private func updateNavigationBar() {
        guard currentIndex >= 0 && currentIndex < viewModel.videoDataList.count else { return }
        
        let currentVideo = viewModel.videoDataList[currentIndex]
        
        // 更新用户信息
        usernameLabel.text = currentVideo.username
        
        // 加载用户头像
        if let url = URL(string: currentVideo.userAvatar) {
            userAvatarImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "person.circle"))
        }
    }
    
    // MARK: - Navigation Actions
    
    /// 处理返回按钮点击
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    /// 处理分享按钮点击
    @objc private func shareButtonTapped() {
        guard currentIndex >= 0 && currentIndex < viewModel.videoDataList.count else { return }
        print("点击分享按钮 - 当前视频: \(viewModel.videoDataList[currentIndex].username)")
        guard let currentInfo = self.viewModel.currentData?.info else {
            print("\(#function) currentData is absence.")
            return
        }
        let picList:[String] = currentInfo.output_medias.map({
            return $0.url
        })
        let sharedContent = [
            "title":currentInfo.title,
            "content":currentInfo.message ?? "",
            "url":currentInfo.video_url,
            "pic":currentInfo.pic_url,
            "pic_list":picList,
            "from_page_type": 4
        ] as [String : Any]
        WHShareViewManager.showShareSheet(with: sharedContent) { resp in
        }
    }
    
    /// 处理用户头像点击
    @objc private func avatarTapped() {
        guard currentIndex >= 0 && currentIndex < viewModel.videoDataList.count else { return }
        print("点击用户头像 - 用户: \(viewModel.videoDataList[currentIndex].username)")
        // 这里可以添加用户资料页面跳转逻辑
    }
    
    // MARK: - Business
    private var priceDict: [Int: WHGcPriceModel] = [:]
    
    private func loadItemPrice() {
        // 过滤 要求vip 且 未加载过的模版
        let list = self.viewModel.videoDataList.filter { $0.isLimitVip && priceDict[$0.idNum] == nil }
        guard list.count > 0 else {
            return
        }
        let ids: [String: String] = [
            "template_ids": self.viewModel.videoDataList.map({
                return String($0.idNum)
            }).joined(separator: ",")
        ]
        let params: [String: Any] = [
            "function_code": "ai_template",
            "function_body": ids.wh_toJsonString() ?? "",
        ]
        WHSharedRequest.POST("/sub/get_price_batch.json",
                             params: params) { [weak self] (response: WHOriginalResponse) in
            guard let self = self else {
                return
            }
            if response.isSuccess(),
               let data = response.data() as? [String: Any],
               let list = data["list"] as? [[String: Any]],
               list.count > 0 {
                for dict in list {
                    if let templateId = dict["template_id"] as? Int,
                       let priceData = dict["price_data"] as? [String: Any] {
                        self.priceDict[templateId] = WHGcPriceModel.yy_model(with: priceData)
                    }
                }
                //self.refreshDoneBtn()
                self.tableView.reloadData()
            }
        }
    }
    
    var balance: WHBeansBalanceModel?
    private func loadBalance() {
        WHServerReport.requestBalanceAmount { [weak self] model in
            self?.balance = model
        }
    }
    
    // MARK: - Private Methods
    private func addObserver() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(vipStatusDidChanged),
                                               name: .VipStatusDidChanged,
                                               object: nil)
    }
    
    @objc
    private func vipStatusDidChanged() {
        loadBalance()
        loadItemPrice()
    }

    
    private func refreshDoneBtn() {
//        if let currentItem = currentItem,
//           let price = priceDict[currentItem.id] {
//            if price.freeNum > 0 {
//                // SOP有限免次数
//                doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_star_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.freeNum)))
//                doneBtn.setBackgroundColor(.color(color: .white))
//            } else {
//                if price.isVip {
//                    if price.rightNum > 0 {
//                        doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.rightNum)))
//                        doneBtn.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
//                    } else {
//                        doneBtn.setState(.meidou(meidou: Int(price.amount ?? "0") ?? 0, title: WHLocalizedString("立即体验")))
//                        doneBtn.setBackgroundColor(.color(color: .white))
//                    }
//                } else {
//                    doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: nil))
//                    doneBtn.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
//                }
//            }
//        } else {
//            doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_star_icon"), title: WHLocalizedString("立即体验"), subTitle: nil))
//            doneBtn.setBackgroundColor(.color(color: .white))
//        }
    }
    
    override func loadRouteParams(_ params: [String : Any]) {
        //TODO: load feed detail
        if let feedID = params["feed_id"] as? String {
            //查询详情
            WHHomeRequest.requestFeedCardInfo(feedID: feedID) { response, info in
                if let data = info {
                    self.viewModel.videoDataList = [WHHomeFullScreenFlowDataViewModel.fromFlowInfo(info: data)]
                    self.loadVideoData()
                }
            }
        }
    }
}

// MARK: - UITableViewDataSource

extension WHHomeFullScreenViewController: UITableViewDataSource {
    
    /// 返回视频数据源的数量
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.videoDataList.count
    }
    
    /// 配置每个视频Cell
    /// 设置代理并传入视频数据进行显示
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: WHHomeFullScreenFlowTableViewCell.reuseIdentifier, for: indexPath) as! WHHomeFullScreenFlowTableViewCell
        cell.delegate = self  // 设置代理，用于处理Cell内的用户交互
        var data = viewModel.videoDataList[indexPath.row]
        data.priceModel = self.priceDict[data.idNum]
        cell.configure(with: data)  // 传入视频数据
        if let info = data.info {
            userAvatarImageView.sd_setImage(with: info.getAvatarURL())
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if let cell = tableView.dequeueReusableCell(withIdentifier: WHHomeFullScreenFlowTableViewCell.reuseIdentifier, for: indexPath) as? WHHomeFullScreenFlowTableViewCell {
            var data = viewModel.videoDataList[indexPath.row]
            data.priceModel = self.priceDict[data.idNum]
            cell.configure(with: data)  // 传入视频数据
            if let info = data.info {
                userAvatarImageView.sd_setImage(with: info.getAvatarURL())
            }
            cell.layoutSubviews()
            DispatchQueue.main.async {
                cell.play()
            }
        }
    }
    
    func tableView(_ tableView: UITableView, didEndDisplaying cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if let cell = tableView.dequeueReusableCell(withIdentifier: WHHomeFullScreenFlowTableViewCell.reuseIdentifier, for: indexPath) as? WHHomeFullScreenFlowTableViewCell {
            cell.layoutSubviews()
            cell.stop()
        }
    }
}

// MARK: - UITableViewDelegate

extension WHHomeFullScreenViewController: UITableViewDelegate {
    
    /// 滚动减速结束时调用
    /// 用于确定用户最终停留在哪个视频
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        calculateCurrentIndex()
    }
    
    /// 拖拽结束时调用
    /// 如果没有减速阶段（停止得很快），立即计算当前索引
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            calculateCurrentIndex()
        }
        if currentIndex >= self.viewModel.videoDataList.count - 4 {
            self.viewModel.loadDataFromRequest(needMore: true)
        }
    }
}

// MARK: - VideoTableViewCellDelegate

/// 视频Cell代理方法实现
/// 处理Cell内各种用户交互事件
extension WHHomeFullScreenViewController: WHHomeFullScreenFlowTableViewCellDelegate {
    
    /// 处理点赞/收藏按钮点击
    /// TODO: 实际项目中可以添加点赞接口调用和UI状态更新
    func cellDidTapLike(_ cell: WHHomeFullScreenFlowTableViewCell, _ completion: ((_ result: Bool, _ status: Int) -> Void)?) {
        print("点击收藏按钮")
        //调用收藏API
        if let data = cell.viewModel?.info {
            var action = data.is_collect ?? 1
            action = (action == 1 ? 2 : 1)
            WHHomeRequest.requestCollect(feedID: "\(data.feed_id)", type: data.func_type, action: action) { whResponse, model in
                if let m = model, m.result! {
                    data.is_collect = action
                    data.collect_num = (data.collect_num ?? 0) + 1
                    completion?(true, action)
                } else {
                    completion?(false, data.is_collect ?? 0)
                }
            }
        }
    }
    
    /// 处理"使用"按钮点击
    /// TODO: 根据具体业务需求实现相应功能
    func cellDidTapUse(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("点击使用按钮")
        // 这里可以添加使用逻辑
        // 例如：跳转到编辑页面、下载素材等
    }
    
    /// 处理用户头像点击
    /// TODO: 跳转到用户详情页面
    func cellDidTapAvatar(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("点击用户头像")
        // 这里可以添加用户资料页面跳转逻辑
        // 例如：获取用户ID、推送用户详情页面等
    }
    
    /// 处理返回按钮点击
    /// 直接使用导航控制器返回上一页面
    func cellDidTapBack(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("点击返回按钮")
        navigationController?.popViewController(animated: true)
    }
    
    /// 处理分享按钮点击
    /// TODO: 实现分享功能
//    func cellDidTapShare(_ cell: WHHomeFullScreenFlowTableViewCell) {
//        print("点击分享按钮")
//    }
    
    /// 处理详情按钮点击
    /// TODO: 显示视频详情信息
    func cellDidTapDetail(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("点击详情按钮")
    }
    
    /// 处理视频进度条变化
    /// TODO: 控制视频播放进度
    func cellDidProgressChanged(_ cell: WHHomeFullScreenFlowTableViewCell, progress: Float) {
        print("进度条改变: \(progress)")
        // 这里可以添加视频进度控制逻辑
        // 例如：调用视频播放器的seek方法
    }
    
    /// 开始拖拽进度条时调用
    /// 隐藏菜单以避免拖拽时的视觉干扰
    func cellDidStartDragging(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("开始拖拽进度条")
        hideMenuDuringDrag()  // 拖拽时隐藏菜单，提供更好的操作体验
    }
    
    /// 结束拖拽进度条时调用
    /// 恢复菜单显示
    func cellDidEndDragging(_ cell: WHHomeFullScreenFlowTableViewCell) {
        print("结束拖拽进度条")
        showMenuAfterDrag()  // 拖拽结束后恢复菜单显示
    }
    
    func cellDidTapMedia(_ cell: WHHomeFullScreenFlowTableViewCell) {
        toggleMenuVisibility()
    }
    
    func cellFetchBalance() -> WHBeansBalanceModel? {
        return self.balance
    }
}

extension WHHomeFullScreenViewController: WHFlowListDelegate {
    func currrentIndex() -> Int {
        return self.currentIndex
    }
    
    func loadDataFinish() {
        self.loadItemPrice()
        self.loadBalance()
        self.tableView.reloadData()
    }
    
    
}
