//
//  WHMineViewController.swift
//  MeituWhee
//
//  Created by <PERSON><PERSON>oqi on 2023/9/7.
//

import Foundation
import UIKit
import WHBaseLibrary
import WHBusinessCommon
import JXPagingView
import JXSegmentedView
import WHEditorModule

extension Notification.Name {
    static let WHEEStartGC = Notification.Name("WHEEStartGC")
}

///正常情况下头部高度
let WHMineHeadViewNormolHeight: CGFloat = (175 + 16  + WH_NAVIGATION_BAR_HEIGHT)
//let WHMineHeadViewNoLoginHeight: CGFloat = (100 + 16  + WH_NAVIGATION_BAR_HEIGHT)
private let WHMineHeadViewHiddenHeight: CGFloat = WH_NAVIGATION_BAR_HEIGHT
extension JXPagingListContainerView: JXSegmentedViewListContainer {}

class WHMineViewController: WHViewController {
    var titles:[String] = [WHLocalizedString("作品"),
                           WHLocalizedString("模型"),
                           WHLocalizedString("收藏"),
                           WHLocalizedString("项目")]
    
    var tabInfos: [WHTabInfo] = []
    
    var subViewController : [WHViewController] = []
    // 是否展示右边全部按钮
    var isShowPopBtn: Bool = true
    
    var isRelaodAll: Bool = false
    
    override func viewDidLoad() {
        let tabInfo = WHTabInfo()
        tabInfo.name = WHLocalizedString("作品")
        tabInfo.urlString = "/user_task/feed.json"
        tabInfo.feedFromType = .works
        tabInfo.analyString = "work_tab"
        tabInfo.subTabs = [
            WHSubTabInfo(name: WHLocalizedString("全部"), type: 1,analy: "all"),
            WHSubTabInfo(name: WHLocalizedString("已发布"), type: 2,analy: "published"),
            WHSubTabInfo(name: WHLocalizedString("未发布"), type: 3,analy: "not_published")
        ]
        tabInfos.append(tabInfo)
        let tabInfo1 = WHTabInfo()
        tabInfo1.name = WHLocalizedString("模型")
        tabInfo1.urlString = "/user_model/feed.json"
        tabInfo1.feedFromType = .model
        tabInfo1.analyString = "model_tab"
        tabInfo1.subTabs = [
            WHSubTabInfo(name: WHLocalizedString("全部"), type: 1,analy: "all"),
            WHSubTabInfo(name: WHLocalizedString("已发布"), type: 2,analy: "published"),
            WHSubTabInfo(name: WHLocalizedString("未发布"), type: 3,analy: "not_published")
        ]
        tabInfos.append(tabInfo1)
        let tabInfo2 = WHTabInfo()
        tabInfo2.name = WHLocalizedString("收藏")
        tabInfo2.urlString = "/collect/list.json"
        tabInfo2.feedFromType = .collects
        tabInfo2.analyString = "collect_tab"
        tabInfo2.extraRequestParams = ["type": 3]
        tabInfo2.subTabs = [
            WHSubTabInfo(name: WHLocalizedString("配方"), type: 3,analy: "formula"),
            WHSubTabInfo(name: WHLocalizedString("作品"), type: 1,analy: "work"),
            WHSubTabInfo(name: WHLocalizedString("模型"), type: 2,analy: "model"),
        ]
        tabInfos.append(tabInfo2)
        
        let tabInfo3 = WHTabInfo()
        tabInfo3.name = WHLocalizedString("项目")
        tabInfo3.urlString = "/user_project/feed.json"
        tabInfo3.feedFromType = .project
        tabInfo3.analyString = "project_tab"
        tabInfo3.subTabs = [
            WHSubTabInfo(name: WHLocalizedString("全部"), type: 1,analy: "all"),
            WHSubTabInfo(name: WHLocalizedString("已发布"), type: 2,analy: "published"),
            WHSubTabInfo(name: WHLocalizedString("未发布"), type: 3,analy: "not_published")
        ]
        tabInfos.append(tabInfo3)
        
        for (index, info) in tabInfos.enumerated() {
            if index == 0 {
                let vc = WHMineWorksViewController()
                vc.mineVc = self
                subViewController.append(vc)
            } else {
                let vc = WHMineContentViewController.init()
                vc.urlString = info.urlString
                vc.feedFromType = info.feedFromType
                vc.extraRequestParams = info.extraRequestParams
                vc.useCache = false
                subViewController.append(vc)
                info.selectTab = info.subTabs.first
            }
        }
        
        super.viewDidLoad()
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginSuccess,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginOut,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(vipStatusDidChanged),
                                               name: .VipStatusDidChanged,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(startGC),
                                               name: NSNotification.Name.WHEEStartGC,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(receiveAwardNotification),
                                               name: .receiveAwardNotification,
                                               object: nil)
        NotificationCenter.default.addObserver(forName: .WHACCountDidModifyPersonalInfo,
                                               object: nil,
                                               queue: .main) { [weak self] _ in
            guard let self = self else {
                return
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
                guard let self = self else {
                    return
                }
                self.pagingView.mainTableView.startRefreshing(at: .top)
            }
        }
        
    }
    
    ///开始创作更新美豆和作品
    @objc func startGC(notify: Notification){
        if self.subViewController.count > 0 && tabInfos.count > 0 {
            if self.subViewController.count > 3, let dict = notify.object as? [String : Any],let feedType = dict["subType"] as? String,feedType == "project" {
                let vc = self.subViewController[3] as? WHMineContentViewController
                let currentVC = self.subViewController[segmentedView.selectedIndex]
                if vc == currentVC {
                    selectTabRefresh(.byClick(.bottomTab))
                } else {
                    vc?.beginRefresh()
                }
            } else {
                let vc = self.subViewController[0] as? WHMineWorksViewController
                let model = tabInfos[0]
                vc?.getData(subType: model.selectTab?.type ?? 1, isRelaodAll: false) { sucess in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.pagingView.mainTableView.endRefreshing(at: .top)
                    }
                }
            }
        }
        getUserInfo()
    }
    
    @objc func loginStatusChange(){
        self.getUserInfo()
        self.headView.upAccountInfo()
        segmentedView.updateView()
        
        self.subViewController.forEach { subVC in
            if let vc = subVC as? WHMineContentViewController {
                vc.loginStatusChange()
            } else if let vc = subVC as? WHMineWorksViewController {
                vc.loginStatusChange()
            }
        }
        //审核期间、海外地区始终显示美豆。审核后，根据登录态判定
        let meidouIsHidden = (WHConfigCenterManager.getWheekk() == false && WHVipSdkManager.shared.getVipEnvIsChinaMainLand() == false) ? false : !WHAccountShareManager.isLogin()
        var items: [WHMineButtonItem] = [.tasks]
        if meidouIsHidden == false {
            items.append(.meidou(count: 0))
        }
        btnView.showItems(items)
        pagingView.mainTableView.bounces = WHAccountShareManager.isLogin()
//        if !WHAccountShareManager.isLogin() {
//            self.meitButton.snp.remakeConstraints { make in
//                make.height.equalTo(32)
//                make.right.equalTo(self.setButton.snp.left).offset(-16)
//                make.width.equalTo(64)
//                make.centerY.equalTo(self.setButton.snp.centerY)
//            }
//        }
    }
    
    @objc func vipStatusDidChanged() {
        getUserInfo()
    }
    
    @objc
    private func receiveAwardNotification() {
        getUserInfo()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        WHAnalyticsManager.pageExpo(pageName: "mine_page")
        headView.upAccountInfo()
//        meitButton.isHidden = !WHAccountShareManager.isLogin()
        pagingView.mainTableView.bounces = WHAccountShareManager.isLogin()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        segmentedView.hidePopView()
    }
    
    override func setupSubviews() {
        navigationBarStyle = .custom
        super.setupSubviews()
        pagingView.backgroundColor = WHFigmaColor.black
        pagingView.frame = CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: WH_SCREEN_HEIGHT)
        pagingView.mainTableView.gestureDelegate = self
        pagingView.mainTableView.backgroundColor = WHFigmaColor.clear
        pagingView.mainTableView.tableFooterView = nil
        ///顶部偏移量
        self.pagingView.pinSectionHeaderVerticalOffset = Int(WHMineHeadViewHiddenHeight)
        self.view.addSubview(pagingView)
        segmentedView.setupUI(tabInfos: tabInfos, delegate: self, listContainer: pagingView.listContainerView)
        //扣边返回处理，下面的代码要加上
        pagingView.listContainerView.scrollView.panGestureRecognizer.require(toFail: self.navigationController!.interactivePopGestureRecognizer!)
        pagingView.mainTableView.panGestureRecognizer.require(toFail: self.navigationController!.interactivePopGestureRecognizer!)
        pagingView.frame = CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: self.view.frame.size.height + 17)
        self.pagingView.backgroundColor = WHFigmaColor.black
        
        view.bringSubviewToFront(myNavigationBar)
        myNavigationBar.alpha = 0
        myNavigationBar.backgroundColor = WHFigmaColor.black
        myNavigationBar.addSubview(headView.navIcon)
        
        view.addSubview(setButton)
        setButton.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 24, height: 24))
            make.right.equalTo(-16)
            make.top.equalTo(WH_STATUS_BAR_HEIGHT + 12)
        }
        view.addSubview(btnView)
        btnView.snp.makeConstraints { make in
            make.right.equalTo(setButton.snp.left).offset(-16)
            make.centerY.equalTo(setButton.snp.centerY)
        }
//        view.addSubview(meitButton)
//        meitButton.snp.makeConstraints { make in
//            make.height.equalTo(32)
//            make.right.equalTo(setButton.snp.left).offset(-16)
//            make.width.equalTo(64)
//            make.centerY.equalTo(setButton.snp.centerY)
//        }
//        meitButton.layoutButton(style: .Right, imageTitleSpace: 0)
        headView.navIcon.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
            make.right.lessThanOrEqualTo(btnView.snp.left).offset(-8)
            make.top.equalTo(WH_STATUS_BAR_HEIGHT)
        }
        
        var items: [WHMineButtonItem] = [.tasks]
        let meidouIsHidden = (WHConfigCenterManager.getWheekk() == false && WHVipSdkManager.shared.getVipEnvIsChinaMainLand() == false) ? false : true
        if meidouIsHidden == false {
            items.append(.meidou(count: 0))
        }
        btnView.showItems(items)
        
        ///下拉刷新
        let header = WHPullHeader(style: .picDefaultText)
        self.pagingView.mainTableView.addPullToRefresh(header) { [weak self] in
            guard let self = self else { return }
            self.updata()
            self.getUserInfo()
            self.headView.upAccountInfo()
        }
//        view.addSubview(rewardsBtn)
//        rewardsBtn.snp.makeConstraints { make in
//            make.height.equalTo(32)
//            make.right.equalTo(meitButton.snp.left).offset(-16)
//            make.width.equalTo(64)
//            make.centerY.equalTo(setButton.snp.centerY)
//        }
    }
    
    override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        super.wh_handleEventWith(name, params: params)
        if name == WHMinePopMeu_Select_Event,let params = params,
           let subInfo = params["info"] as? WHSubTabInfo {
            self.segmentedView.upButton(title: subInfo.name)
            tabInfos[segmentedView.selectedIndex].selectTab = subInfo
            let vc = self.subViewController[segmentedView.selectedIndex] as? WHMineContentViewController
            vc?.extraRequestParams = ["type": subInfo.type]
            self.isRelaodAll = true
            selectTabRefresh(.byClick(.bottomTab))
            let model = tabInfos[segmentedView.selectedIndex]
            WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheeMineTabDropdownClick,params:[WHAnalyticsKey.tabName: model.analyString,.dropdownName:model.selectTab?.analyString ?? "all"])
        }
        if name == "flowNetworkChange" {
            getUserInfo()
        }
        if name == WH_MINE_BUTTON_VIEW_EVENT,
           let type = params?["type"] as? WHMineButtonItem {
            switch type {
            case .tasks:
                WHRouter.route(with: "wheeai://app/task_center")
            case .meidou(_):
                meidouClick()
            }
        }
    }
    
    ///刷新列表
    func updata(){
        if WHAccountShareManager.isLogin() {
            let model = tabInfos[segmentedView.selectedIndex]
            let vc = self.subViewController[segmentedView.selectedIndex]
            if let currentVc = vc as? WHMineContentViewController {
                currentVc.extraRequestParams = ["type": model.selectTab?.type ?? 1]
                currentVc.resetRefreshState()
                currentVc.getDataFromWeb { sucesss in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.pagingView.mainTableView.endRefreshing(at: .top)
                    }
                }
            } else if let currentVc = vc as? WHMineWorksViewController {
                currentVc.getData(subType: model.selectTab?.type ?? 1, isRelaodAll: self.isRelaodAll) { sucess in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.pagingView.mainTableView.endRefreshing(at: .top)
                    }
                }
                self.isRelaodAll = false
            }
        } else {
            self.pagingView.mainTableView.endRefreshing(at: .top)
        }
    }
    
    func getUserInfo(){
        if WHAccountShareManager.isLogin() {
            WHMineShareManager.userInfoRequest { [weak self] userInfo in
                guard let userInfo = userInfo, let self = self else { return }
                DispatchQueue.main.async {
                    self.headView.update(userInfo: userInfo)
                    self.headView.upAccountInfo()
//                    self.meitButton.setTitle(" \(userInfo.balanceAmount)", for: .normal)
//                    self.meitButton.layoutButton(style: .Right, imageTitleSpace: 0)
//                    let width = userInfo.balanceAmount.description.getStringHeight(with: 20, font: UIFont.pingFangSCFont(ofSize: 14)) + 38
//                    self.meitButton.snp.remakeConstraints { make in
//                        make.height.equalTo(32)
//                        make.right.equalTo(self.setButton.snp.left).offset(-16)
//                        make.width.equalTo(max(width, 64))
//                        make.centerY.equalTo(self.setButton.snp.centerY)
//                    }
//                    self.meitButton.isHidden = false
                    self.btnView.showItems([.tasks, .meidou(count: userInfo.balanceAmount)])
                    //设置美豆自动消耗状态
                    WHEdCommonManager.shared.isAutoUseMeidou = userInfo.personalSetting?.autoPay ?? false
                    WHEdCommonManager.shared.availableAmount = userInfo.balanceAmount
                    
                    // 在“我的”页面点助力弹窗，触发登录时，需要控制“助力得美豆”和“新用户得美豆”弹窗显示顺序，这种场景不在此处触发“新用户弹窗”
                    if WHAlertPrioritySharedManager.boostLoginInMinePage == false {
                        if let tipInfo = userInfo.awardTip, tipInfo.isShow, tipInfo.tipDesc.count > 0 {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                let tipView = WHAwardTipView(title: tipInfo.tipTitle, desc: tipInfo.tipDesc)
                                tipView.show()
                            }
                        }
                    }
                }
            }
        } else {//如果没有登录的话，需要拉取下游客会员信息
            WHMineRequest.visitorUserInfoRequest { [weak self] visitorInfo in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    if visitorInfo == nil { //也相当于没请求成功
                        self.resetMeidouButton()
                    } else {
                        self.headView.visitorUpdateBanner(visitorModel: visitorInfo)
                        if (visitorInfo?.balanceAmount ?? -1) > 0 {
//                            self.meitButton.setTitle(" \(visitorInfo!.balanceAmount)", for: .normal)
//                            self.meitButton.layoutButton(style: .Right, imageTitleSpace: 0)
//                            let width = (visitorInfo?.balanceAmount.description.getStringHeight(with: 20, font: UIFont.pingFangSCFont(ofSize: 14)) ?? 0) + 38
//                            self.meitButton.snp.remakeConstraints { make in
//                                make.height.equalTo(32)
//                                make.right.equalTo(self.setButton.snp.left).offset(-16)
//                                make.width.equalTo(max(width, 64))
//                                make.centerY.equalTo(self.setButton.snp.centerY)
//                            }
//                            self.meitButton.isHidden = false
                            self.btnView.showItems([.tasks, .meidou(count: visitorInfo!.balanceAmount)])
                        } else {
                            self.resetMeidouButton()
                        }
                    }
                }
            }
        }
    }
    
    func resetMeidouButton() {
        var items: [WHMineButtonItem] = [.tasks]
//        self.meitButton.setTitle("0", for: .normal)
//        self.meitButton.layoutButton(style: .Right, imageTitleSpace: 0)
        let meidouIsHidden = (WHConfigCenterManager.getWheekk() == false && WHVipSdkManager.shared.getVipEnvIsChinaMainLand() == false) ? false : true
        if meidouIsHidden == false {
            items.append(.meidou(count: 0))
        }
        btnView.showItems(items)
    }
    
    
    func upNavView(){
        let scrollHeight = pagingView.mainTableView.contentOffset.y
        let headerTotalOffset = WHMineHeadViewNormolHeight - WHMineHeadViewHiddenHeight
        let percent: CGFloat
        if scrollHeight < 0.0 {
            percent = 0.0
        } else {
            percent = CGFloat(scrollHeight) / CGFloat(headerTotalOffset)
        }
        myNavigationBar.alpha = percent
        if !WHAccountShareManager.isLogin() {return}
        if percent >= 1 {
            headView.navIcon.isHidden = false
        } else {
            headView.navIcon.isHidden = true
        }
    }
    
    @objc func buttonClick(){
        WHRouter.route(with: WHRouteSetting)
        segmentedView.hidePopView()
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheeMineClick,params: [WHAnalyticsKey.clickType: "setting_click"])
    }
    
    private func meidouClick() {
        segmentedView.hidePopView()
        WHVipSdkManager.shared.showMeiDouWindow(self)
        
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheeBeautyCoinClick,params: [WHAnalyticsKey.location: "mine_corner"])
    }
    
    lazy var headView: WHMineHeadView = {
        let head = WHMineHeadView(frame: CGRectMake(0, 0, WH_SCREEN_WIDTH, WHMineHeadViewNormolHeight))
        return head
    }()
    
    lazy var imageViewFrame:CGRect = .zero
    
    private lazy var setButton: UIButton = {
        let button = UIButton(type: .custom)
        button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 20)
        button.addTarget(self, action: #selector(buttonClick), for: .touchUpInside)
        button.setImage(UIImage(named: "mine_setting_icon"), for: .normal)
        return button
    }()
    
    private lazy var btnView: WHMineButtonView = {
        let view = WHMineButtonView()
        return view
    }()
        
    private let dataSource: JXSegmentedTitleDataSource = JXSegmentedTitleDataSource()
    
    private lazy var segmentedView: WHMineSegemateView = WHMineSegemateView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.size.width, height: CGFloat(WHMineSegemateView.segmentedHeight)))
    private lazy var pagingView: JXPagingView = {
        let view = JXPagingView(delegate: self)
        if #available(iOS 13.0, *) {
        } else {
            view.mainTableView.estimatedRowHeight = 0
            view.mainTableView.estimatedSectionHeaderHeight = 0
            view.mainTableView.estimatedSectionFooterHeight = 0
        }
        if #available(iOS 15.0, *) {
            view.mainTableView.sectionHeaderTopPadding = 0
        }
        view.backgroundColor = UIColor.black
        view.listContainerView.listCellBackgroundColor = .clear
        view.addSubview(headView.headerBgView)
        view.sendSubviewToBack(headView.headerBgView)
        return view
    }()
}

extension WHMineViewController: JXPagingMainTableViewGestureDelegate {
    func mainTableViewGestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        //禁止segmentedView左右滑动的时候，上下和左右都可以滚动
        if otherGestureRecognizer == segmentedView.segmentedView.collectionView.panGestureRecognizer {
            return false
        }
        return gestureRecognizer.isKind(of: UIPanGestureRecognizer.self) && otherGestureRecognizer.isKind(of: UIPanGestureRecognizer.self)
    }
}

// MARK: - JXPagingViewDelegate
extension WHMineViewController: JXPagingViewDelegate {
    func tableHeaderViewHeight(in pagingView: JXPagingView) -> Int {
        return Int(WHMineHeadViewNormolHeight)
    }
    
    func tableHeaderView(in pagingView: JXPagingView) -> UIView {
        return headView
    }
    
    func heightForPinSectionHeader(in pagingView: JXPagingView) -> Int {
        return 40
    }
    
    func viewForPinSectionHeader(in pagingView: JXPagingView) -> UIView {
        return segmentedView
    }
    
    func numberOfLists(in pagingView: JXPagingView) -> Int {
        return tabInfos.count
    }
    
    func pagingView(_ pagingView: JXPagingView, initListAtIndex index: Int) -> JXPagingViewListViewDelegate {
        return subViewController[index] as! JXPagingViewListViewDelegate
    }
    
    func pagingView(_ pagingView: JXPagingView, mainTableViewDidScroll scrollView: UIScrollView) {
        upNavView()
        headView.bgImageChangView(scrollView: scrollView)
        self.segmentedView.hidePopView()
    }
    
}

extension WHMineViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        let model = tabInfos[index]
        let isShowPopBtn = model.feedFromType == .project ? false : self.isShowPopBtn
        self.segmentedView.segmentedView(didChangSelectedItemAt: index, isShowPopBtn: isShowPopBtn)
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheeMineTabDropdownClick,params:[WHAnalyticsKey.tabName: model.analyString,.dropdownName:model.selectTab?.analyString ?? "all"])
    }
}

extension WHMineViewController: WHSubPageViewProtocol {
    func didSelectTab() {
        getUserInfo()
    }
    
    func connectScrollView() -> UIScrollView? {
        return nil
    }
    
    public func selectTabRefresh(_ refreshCause: WHFeedListRefreshCause) {
        if WHAccountShareManager.isLogin() {
            pagingView.mainTableView.setContentOffset(CGPoint(x: 0, y: 0), animated: true)
            let vc = self.subViewController[segmentedView.selectedIndex]
            if let currentVc = vc as? WHMineContentViewController {
                currentVc.collectionView.setContentOffset(CGPoint(x: 0, y: 0), animated: false)
            } else if let currentVc = vc as? WHMineWorksViewController {
                currentVc.setContentOffset()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.pagingView.mainTableView.startRefreshing(at: .top)
            }
        }
    }
    
    func mineTabClick(isShow: Bool) {
        segmentedView.subButton.isHidden = !isShow
        self.isShowPopBtn = isShow
        segmentedView.subButton.isSelected = false
        segmentedView.subButton.layer.borderWidth = 0
        segmentedView.popView.isHidden = true
    }
}
