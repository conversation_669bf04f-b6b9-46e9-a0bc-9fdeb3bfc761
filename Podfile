source 'https://github.com/CocoaPods/Specs.git'
source '*********************:iosmodules/specs.git'
source '*********************:iOSModules_public/specs.git'
source '*********************:MTLabEngines/MTLabSpecs.git'
source '*********************:mtxxbase_public/specs.git'
source '*********************:MTLabEngines/MTLabLdsSpecs.git'
source '*********************:MTLabEngines/mtlabdevspecs.git'
source "*********************:videoedit-ios/Specs.git"

platform :ios, '13.0'
inhibit_all_warnings!
use_modular_headers!

def debug
  
  # xcode_version = `xcodebuild -version | grep Xcode`.split(' ').join('')
  xc_version = `xcodebuild -version | grep Xcode`.split(' ')[1]
  xcVer=Gem::Version.new xc_version
  xc15=Gem::Version.new "15"
  if xcVer > xc15 # xcode > 15 的版本, xcode_version["Xcode16.4"]
    pod 'DoraemonKit/Core', :path => 'LocalPods/DoKit', :configurations => ['Debug'] #必选
    pod 'DoraemonKit/WithLoad', :path => 'LocalPods/DoKit', :configurations => ['Debug'] #可选
    pod 'DoraemonKit/WithLogger', :path => 'LocalPods/DoKit', :configurations => ['Debug'] #可选
    pod 'DoraemonKit/WithDatabase', :path => 'LocalPods/DoKit', :configurations => ['Debug'] #可选
  else
    pod 'DoraemonKit/Core', '~> 3.1.2', :configurations => ['Debug'] #必选
    pod 'DoraemonKit/WithLoad', '~> 3.1.2', :configurations => ['Debug'] #可选
    pod 'DoraemonKit/WithLogger', '~> 3.1.2', :configurations => ['Debug'] #可选
    pod 'DoraemonKit/WithDatabase', '~> 3.1.2', :configurations => ['Debug'] #可选
  end
  
  pod 'LookinServer', :subspecs => ['Swift'], :configurations => ['Debug']
  pod 'MTAppCIADebugger', '0.4.0', :configurations => ['Debug']
  pod 'CocoaLumberjack', '3.8.5'
  pod 'LabLogToolsKit', '*******' # 查看模型下载状态
end

def meitu_modules
  
  # MTWebKit 需要 4.0.0 以上
  pod 'MTWebKit', :git => '*********************:iosmodules/MTWebKit.git', :tag => '5.3.1', :subspecs=>['Core', 'Upload', 'Request'], :modular_headers => true
#  pod 'OKMiniAppSDK', :git => '*********************:mtminiapp/ios/OKMiniAppSDK.git', :subspecs=>['Core', 'Pay', 'Media', 'ClipBoard'], :tag => '1.2.1'
  
  pod 'MTAccount', '*******', :subspecs => ['Core', 'MT', 'GeYan', 'WClient']
  
  pod 'MTAnalytics', :git => '*********************:iosmodules/MTAnalytics.git', :tag => '7.5.3'
  pod 'MTAnalyticsBase', :git => '*********************:iosmodules/MTAnalyticsBase.git', :tag =>'5.4.0'
  pod 'MTAnalyticsConfiguration', '1.5.0', :subspecs => ['MT']
  pod 'libMTPlayer', :git => '*********************:flymedia/libMTPlayer.git', :tag => '3.4.3'
  
  pod 'MTSSOShareKit', '2.2.24', :subspecs => ['WeChat', 'Tencent', 'Weibo', 'Xiaohongshu','Apple','FaceBook']
#  pod 'MTSSOShareKit', '2.2.24', :subspecs => ['Apple']
  
  pod 'MTAppCIA', '5.2.0'
  pod 'MTQRCodeScanSDK', :git => "*********************:iosmodule_dev/MTQRCodeSDK.git", :branch => 'feature/subspec'
  pod 'MTPushNotification', '4.1.10'
  pod 'MTDataFinder', '2.6.0'
  pod 'MTAPM', '3.0.0'
  pod 'MTSig', '2.0.7'
  pod 'MTResourceUpload', '2.3.2', :modular_headers => true
  pod 'MTCipherSuite', '0.1.11'
  pod 'MTIPBus', ' 0.2.1'
  
  pod 'MTLegoBlocksLib', '0.1.2'
  pod 'MTPullToRefresh', '0.0.6'
  pod 'JXPagingView','2.1.3-xiuxiu' # 官方 2.1.0 已解决 TableView Section Padding > 0 的问题
  pod 'JXSegmentedView', '1.3.0'
  pod 'MTPhotoLibrary', '********'
  pod 'Kingfisher', '7.11.0'
  # 工具线底层和架构层
  pod 'MTImageKit', '********-whee-08', :subspecs =>['DEFAULT']
  pod 'mtlablds', '0.10.5-support-hbgc-ios-04', :subspecs => ["ai", "effect_image"]
  pod 'mvgif', '*********.10'
  pod 'PVGVideoCodec', '********.5-Mobile-FFmpeg4.4-33378'
  pod 'MTMFFPlayer/MTURLSession', '3.5.0'
  pod 'MTLivePhotoKit', '1.1.25'
  
  pod 'MTMediaKit', '**********.1'
  pod 'MTMediaKit-UI', '1.6.5'
  pod "MFXKit", '********.34'
  pod 'MTMVCore', '**********.1'
  pod 'AICodec', '*********.1'
  pod 'FFmpeg-4.4/default', '********'
  pod 'AIDetectionPlugin', '***********-OnlyIOS-39882'
  pod 'ARKernelInterface', '32.0.2-beta-3'
    
  pod 'MVARExtension', '*********.8'
  
  pod 'MTMBCamera', '1.4.7-beta.26'
  pod 'libmanis', '3.4.5.15-rc3'
  pod 'libmtaiinterface', '0.4.0.2010.29.4.0.3-OnlyIOS'
  pod 'AIModelKit', '2.0.0.6' , :subspecs=>['Core', 'HttpInternal']
  pod 'libmtcvlite', '0.3.2.3-r21e'
  pod 'libmtlab-base', '0.0.51.0'
  pod 'libyuv', '1.8.2.22'
  pod "libmtaurora", "1.1.6.0-beta-5", :subspecs => ["core", "resource"]
  pod 'MVAuroraKit', '20.1.0.3.11'

#  pod 'MTVTimeline', '2.0.3.7'
#  pod 'MTVEffectTimeline', '2.1.4.3'
    
end

def thirdPartModule
  pod 'RxSwift', '~> 6.5'
  pod 'lottie-ios', '4.5.0'
  pod 'CHTCollectionViewWaterfallLayout/ObjC', '0.9.8'
  pod 'YYText', '10.2.0'
  pod 'YYModel', '1.0.4'
  pod 'SDWebImage', '5.18.8'
  pod 'SDWebImageWebPCoder', '0.14.0'
  pod 'IQKeyboardManagerSwift', '7.0.3'
  pod 'Alamofire', '5.8.1'
  pod 'RoutableLogger', '12.3.3'
  pod 'ObjectMapper','4.2.0', :modular_headers => false
  
  xcode_version = `xcodebuild -version`.split(' ').join('')
  if xcode_version["Xcode16"]
    pod 'YYCache', :path => 'LocalPods/YYCache'
  else
    pod 'YYCache', '1.0.4'
  end
  pod 'SnapKit', '~> 5.7.1'
  
end

def meitu_vip_modules
  pod "WechatOpenSDK", '2.0.2'
  pod 'MTPayBase', '4.0.1', :subspecs =>['MT']
  pod 'MTIAPSDK', :git => "*********************:iosmodules/MTIAPSDK.git", :tag => '5.8.0'
#  pod "AlipaySDK", :git => "*********************:iosmodules/AlipaySDK.git", :tag => '15.8.04'
  pod 'MTSubscriptionSDK', :git => '*********************:iosmodules/MTSubscriptionSDK.git', :tag => '5.21.0'
  pod 'MTPayWindow', :git => '*********************:iosmodules/MTPayWindow.git', :tag => '5.22.1', :subspecs =>['Core']
end

def local_modules
  object = ENV['e']
  if object && object.eql?('dev')
    pod 'WHBaseLibrary', :path => '../WHBaseLibrary'
    pod 'WHBusinessCommon', :path => '../WHBusinessCommon'
    pod 'WHEditorModule', :path => '../WHEditorModule'
#    pod 'LabLogToolsKit', '*******' # 查看模型下载状态
  else
    pod 'WHBaseLibrary', :git => '*********************:miraclevision-whee/WHBaseLibrary.git', :branch => 'develop'
    pod 'WHBusinessCommon', :git => '*********************:miraclevision-whee/WHBusinessCommon.git', :branch => 'feature/3.0.0'
    pod 'WHEditorModule', :git => '*********************:miraclevision-whee/WHEditorModule.git', :branch => 'develop'
  end
end

def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  framework_path = File.join(Dir.pwd, framework_relative_path)
  command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
  puts "Stripping bitcode: #{command}"
  system(command)
end

target 'MeituWhee' do
  # 默认强制开启static_framework 所有的源码pod组件都会被打包成static_framework 类型（提前暴露头文件引用不规范和编译报错问题）
  use_frameworks! :linkage => :static
  
  debug
  meitu_modules
  meitu_vip_modules
  local_modules
  thirdPartModule
end

target 'NotificationService' do
  
  use_frameworks! :linkage => :static
  pod 'MTPushNotification', '4.1.10'
  
end

post_install do |installer|
  
  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
  framework_paths = [
  "Pods/MTDeviceInfo/Platform-SDK/SDK/iOS/libDeviceinfo/deviceinfo.framework/deviceinfo"
  ]
  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end
#  find_and_append("Pods/DoraemonKit/iOS/DoraemonKit/Src/Core/Plugin/Platform/FileSync/Function/DoraemonFileSyncManager.m", "#import <FMDB/FMDB.h>", "#import <FMDB/FMDatabase.h>")
  
  # 适配xcode 14 针对pod组件报错Select a development team in the Signing & Capabilities editor 兼容方案
  xcode_version = `xcodebuild -version`.split(' ').join('')
#  if xcode_version["Xcode14"]
    installer.generated_projects.each do |project|
      project.targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings['CODE_SIGN_IDENTITY'] = ''
        end
      end
#    end
  end
  
  installer.pods_project.targets.each do |target|
    # 检查是否是iOS平台的target
    if target.platform_name == :ios
      # 修改IPHONEOS_DEPLOYMENT_TARGET的值为你需要的版本
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      end
    end
  end
    
end

def find_and_append(dir, findstr, appendstr)
  # 遍历指定目录下的所有文件
  Dir[dir].each do |name|
    FileUtils.chmod("+w", name)  # 确保文件可写
    text = File.read(name)       # 读取文件内容

    # 检查文件中是否已经包含 appendstr，如果包含则跳过
    unless text.include?(appendstr)
      # 找到目标字符串的位置并在其下一行添加字符串
      replace = text.gsub(/(#{Regexp.escape(findstr)}.*\n)/, "\\1#{appendstr}\n")

      if text != replace
        puts "Modified: " + name   # 输出修改文件的名称
        File.open(name, "w") { |file| file.puts replace }  # 将修改后的内容写回文件
        STDOUT.flush
      end
    end
  end

  # 递归处理子目录
  Dir[dir + '*/'].each(&method(:find_and_append))
end
