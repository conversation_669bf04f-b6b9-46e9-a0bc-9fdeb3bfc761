
Pod::Spec.new do |spec|

  spec.name         = "WHBaseLibrary"
  spec.version      = "2.0.0"
  spec.summary      = "A short description of WHBaseLibrary"

  spec.homepage     = "https://techgit.meitu.com/miraclevision-whee/WHBaseLibrary"

  spec.license      = {
    :type => 'Commercial',
    :text => <<-LICENSE
    © 2008-2022 Meitu.  All rights reserved.
    LICENSE
  }
  spec.author       = { "xiaoqi" => "<EMAIL>" }
  spec.source       = { :git => "*********************:miraclevision-whee/WHBaseLibrary.git", :tag => "#{spec.version}" }

  spec.platform = :ios, '13.0'
  
  spec.swift_versions = '5.3'
  spec.requires_arc = true
  spec.static_framework = true

  spec.pod_target_xcconfig = {
    'SWIFT_COMPILATION_MODE' => 'wholemodule',
    'OTHER_SWIFT_FLAGS' => '-Xcc -Wno-error=non-modular-include-in-framework-module',
    'CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES' => 'YES'
  }
  
  spec.user_target_xcconfig = { 'DEFINES_MODULE' => 'YES' }
  
  spec.resource_bundles = {
     'WHBaseLibrary' => ['WHBaseLibrary/Assets/*']
  }

  spec.subspec 'AIEngine' do |c|
    c.source_files = 'WHBaseLibrary/Classes/AIEngine/*'
    c.dependency 'libmtaiinterface'
    c.dependency 'libmanis'
    c.dependency 'mvgif'
    c.dependency 'AIModelKit'
  end

  spec.subspec 'Constants' do |c|
    c.source_files = 'WHBaseLibrary/Classes/Constants/*'
    c.dependency 'MTDeviceComponent'
  end
  
  spec.subspec 'Extension' do |e|
    e.source_files = 'WHBaseLibrary/Classes/Extension/*'
#    e.dependency 'Kingfisher'
    e.dependency 'SDWebImage'
  end
  
  spec.subspec 'Manager' do |m|
    m.source_files = 'WHBaseLibrary/Classes/Manager/*'
  end
  
  spec.subspec 'Network' do |n|
    n.source_files = 'WHBaseLibrary/Classes/Network/*'
    n.dependency 'Alamofire'
    # 美图网络请求签名加密库~
    n.dependency 'MTSig'
    
    n.dependency 'ObjectMapperAdditions/Core'

  end
  
  spec.subspec 'UI' do |u|
    u.source_files = 'WHBaseLibrary/Classes/UI/**/*'
    u.dependency 'SnapKit'
#    u.dependency 'lottie-ios'
    u.dependency 'MJRefresh'
  end
  
  spec.subspec 'Utils' do |u|
    u.source_files = 'WHBaseLibrary/Classes/Utils/*'
  end
  
end
