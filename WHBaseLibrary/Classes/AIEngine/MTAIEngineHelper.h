//
//  MTAIEngineHelper.h
//  MTAISDK-Demo
//
//  Created by DehengXu on 2025/7/28.
//

#ifndef MTAIEngineHelper_h
#define MTAIEngineHelper_h
#import <MTAiInterface/MeituAiEngine.h>

@interface MTAIEngineHelper : NSObject <MTAiErrorCallbackDetegate>
+ (instancetype)sharedInstance;
- (void)fetchModelIfNeed;
@end

/**
 建议app 启动后，先调用 [[MTAIEngineHelper sharedInstance] fetchModelIfNeed] ，提高模型下载率
 直接调用会自动创建 MTAIEngineHelper 实例，并触发模型下载
 返回结果文档: https://cf.meitu.com/confluence/pages/viewpage.action?pageId=594411989
 */
NSDictionary* RecognizeGeneralObjectsInImage(UIImage *image);

#endif /* MTAIEngineHelper_h */
