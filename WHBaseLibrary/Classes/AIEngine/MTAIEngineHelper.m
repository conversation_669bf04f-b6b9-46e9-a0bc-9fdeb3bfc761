//
//  MTAIEngineHelper.m
//  MTAISDK-Demo
//
//  Created by DehengXu on 2025/7/28.
//

#import <Foundation/Foundation.h>
#import "MTAIEngineHelper.h"

#import "AIModelKit/AIModelKit-umbrella.h"
#import "AFNetworking/AFNetworkReachabilityManager.h"

const NSString *kEffectKey = @"MTAi_ImageRecognition";
const NSString *kMTAIAppKey = @"704955da49bd4ad1bc2b4acaa07cdc0f";
const NSString *kMTAISecretKey = @"ffe932e517574e748cecdd995304d58d";
const NSString *kMTAIAppName = @"whee";

@interface MTAIEngineHelper ()
@property MeituAiEngine *engine;
@property MTAIModelKit *modelKit;

- (id)init;
- (MTAiEngineOption*)engineOption;

@end


@implementation MTAIEngineHelper

//models: {
//    MTAIENGINE_MODEL_FACE_FA_MEDIUM = "mtface_fa_medium.bin";
//    MTAIENGINE_MODEL_FACE_REFINE_EYES_LIGHT = "mtface_refine_eyes_light.bin";
//    MTAIENGINE_MODEL_FACE_FA_HEAVY = "mtface_fa_heavy.bin";
//    MTAIENGINE_MODEL_FACE_REFINE_MOUTH = "mtface_refine_mouth.bin";
//    MTAIENGINE_MODEL_FACE_FD = "mtface_fd.bin";
//    MTAIENGINE_MODEL_FACE_REFINE_EYES = "mtface_refine_eyes.bin";
//    MTAIENGINE_MODEL_SCENE_BASE = "sceneBase.manis";
//    MTAIENGINE_MODEL_PREGNANT_WOMAN = "pregnantWoman.manis";
//    MTAIENGINE_MODEL_SCENE_BASE_DETECT = "sceneBaseDetect.manis";
//}

- (id)init {
    self = [super init];
    if (self) {
        self.engine = [[MeituAiEngine alloc] initWithMode:MTDetectorMode_IMAGE];
        [self.engine SetErrorCallbackWithObj:self];
        
        NSString *modelDir = [[NSString alloc] initWithFormat:@"%@/MTAiModel", [[NSBundle mainBundle] resourcePath]];
        [self.engine setModel:modelDir];
        
        
        MTAiEngineOption *option = self.engineOption;
        BOOL moduleStatus = [self.engine registerModule:option];
        NSLog(@"register module status: %d", moduleStatus);
        
        NSDictionary *models = [self.engine getCurrentModelsName:option];
        NSLog(@"models: %@", models);
        MTAIModelKit *aiModelKit = [MTAIModelKit sharedInstance];
        MTAIAppInfo *info = [MTAIAppInfo new];
        info.appKey = kMTAIAppKey;
        info.screctKey = kMTAISecretKey;
        info.appName = kMTAIAppName;
        NSString *ver = [[NSBundle.mainBundle infoDictionary] valueForKey:@"CFBundleShortVersionString"];
        info.appVersion = ver;
        [aiModelKit registerWithAppInfo:info andPathDir:modelDir];
        self.modelKit = aiModelKit;
    }
    return self;
}

static MTAIEngineHelper *_sharedInstance = nil;
static dispatch_once_t onceToken;

+ (instancetype)sharedInstance {
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[self alloc] init];
    });
    return _sharedInstance;
}

- (void)fetchModelIfNeed {
    if (!AFNetworkReachabilityManager.sharedManager.isReachable) {
        return;
    }
    
    BOOL ready = [self.modelKit isReadyForKey:kEffectKey];
    NSLog(@"fetch effect is ready: %d, %@", ready, ready ? @"no need downloading." : @"will download models.");
    if (!ready) {
        // 2.9.0 之后，支持模型下发
        [self.modelKit asyncFetchAllEffectStrategy:^(NSError * _Nullable error) {
            NSLog(@"fetch effect strategy error: %@", error);
            if (error == nil) {
                [self.modelKit asyncFetchModelEffectKey:kEffectKey complete:^(MTAIResultCallBackModel * _Nullable resultModel) {
                    NSLog(@"fetch effect result model: %@", resultModel);
                }];
            }
        }];
    }
}

- (void)AiErrorCallbackWithType:(MTAI_ERROR_TYPE)type AndWhat:(NSString *)what {
    NSLog(@"type: %ld, what: %@", type, what);
}

- (void)dealloc {
    NSLog(@"dealloc Engine delegator");
}

- (MTAiEngineOption*)engineOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置faceOption中的检测开关
    option.faceOption.mode = MT_FACE_MODULE_IMAGE_FD_FA;
    unsigned long long enableFlag = 0;
    enableFlag |= MT_FACE_ENABLE_FACE |
                  MT_FACE_ENABLE_REFINE_EYE |
                  MT_FACE_ENABLE_REFINE_MOUTH |
                  MT_FACE_ENABLE_POSEESTIMATION |
                  MT_FACE_ENABLE_VISIBILITY;
    option.faceOption.option = enableFlag;
    
    // 设置图像识别模块配置
    option.imageRecognitionOption = [[MTAiImageRecognitionOption alloc] init];
    option.imageRecognitionOption.option = MT_IMAGE_RECOGNITION_ENABLE_RECOGNITION;
    option.imageRecognitionOption.labelLevel = MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL;
    option.imageRecognitionOption.mode = MT_IMAGE_RECOGNITION_SCENE_BASE;
    option.imageRecognitionOption.deviceType = MT_IMAGE_RECOGNITION_DEVICE_TYPE_HIGH;

    // ---------- begin set coreml-------------
    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };

    return option;
}

- (MTAiEngineOption*)faceOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置faceOption中的检测开关
    option.faceOption.mode = MT_FACE_MODULE_IMAGE_FD_FA;
    unsigned long long enableFlag = 0;
    enableFlag |= MT_FACE_ENABLE_FACE |
                  MT_FACE_ENABLE_REFINE_EYE |
                  MT_FACE_ENABLE_REFINE_MOUTH |
                  MT_FACE_ENABLE_POSEESTIMATION |
                  MT_FACE_ENABLE_VISIBILITY;
    option.faceOption.option = enableFlag;

    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };
    return option;
}

- (MTAiEngineOption*)recognitionOption {
    // 创建option对象
    MTAiEngineOption *option = [[MTAiEngineOption alloc] init];

    // 设置图像识别模块配置
    option.imageRecognitionOption = [[MTAiImageRecognitionOption alloc] init];
    option.imageRecognitionOption.option = MT_IMAGE_RECOGNITION_ENABLE_RECOGNITION;
    option.imageRecognitionOption.labelLevel = MT_IMAGE_RCOGNITION_MODULE_THIRD_LEVEL;
    option.imageRecognitionOption.mode = MT_IMAGE_RECOGNITION_SCENE_BASE;
    option.imageRecognitionOption.deviceType = MT_IMAGE_RECOGNITION_DEVICE_TYPE_HIGH;

    //使用本地模型（用智枢时，配置无效），且设置coreml
    option.faceOption.useCoreML= @{
        @(MT_FACE_ENABLE_FACE): @(YES),
        @(MT_FACE_ENABLE_FR) : @(YES),
        @(MT_FACE_ENABLE_PARSING) : @(YES),
        @(MT_FACE_ENABLE_FD_CONTROL) : @(YES)
    };
    return option;
}

@end

static MeituAiEngine *_engine;

NSDictionary* RecognizeGeneralObjectsInImage(UIImage *image) {
    MTAIEngineHelper *delegator = [MTAIEngineHelper sharedInstance];
    [delegator fetchModelIfNeed];
    NSMutableDictionary *result = [@{
        @"face": [@{
            @"face_size": @0,
            @"face_rectangle": [@[] mutableCopy]
        } mutableCopy],
        @"image_recognition_result": [@{
            @"first": @0,
            @"second": @0,
            @"third": @0
        } mutableCopy]
    } mutableCopy];
    
    if (delegator.engine == nil || image == nil) {
        NSLog(@"AI引擎未初始化或图片为空");
        return result;
    }
    
    // 获取图片的像素数据
    CGImageRef imageRef = image.CGImage;
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    UInt8 *rawData = (UInt8 *)calloc(height * width * bytesPerPixel, sizeof(UInt8));
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                              bitsPerComponent, bytesPerRow, colorSpace,
                                              kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    // 创建AI引擎图像对象
    int orientation = 2;
    int stride = (int)(width * bytesPerPixel);
    MTAiEngineImage *aiImage = [[MTAiEngineImage alloc] initWithRgbaData:rawData width:width height:height orientation:orientation stride:stride];
    MTAiEngineFrame *frame = [[MTAiEngineFrame alloc] initWithImage:aiImage];
    
    // 创建包含人脸检测和图像识别的配置
    MTAiEngineOption *option = delegator.engineOption;
        
    // 执行检测和识别
    MTAiEngineResult *engineResult = [delegator.engine runWithFrame:frame option:option orientation:orientation];
    
    // 处理人脸检测结果
    MTAiFaceResult *faceResult = engineResult.faceResult;
    if (faceResult && faceResult.faces.count > 0) {
        NSMutableArray *faceRectangles = [@[] mutableCopy];
        
        for (MTAiFace *face in faceResult.faces) {
            CGRect faceBounds = face.faceBounds;
            // 人脸框体在图片中的占比
            NSDictionary *faceRectangle = @{
                @"w_ratio": @(faceBounds.size.width),
                @"h_ratio": @(faceBounds.size.height)
            };
            [faceRectangles addObject:faceRectangle];
        }
        
        // 更新人脸信息
        NSMutableDictionary *faceInfo = result[@"face"];
        faceInfo[@"face_size"] = @(faceResult.faces.count);
        faceInfo[@"face_rectangle"] = faceRectangles;
    }
    
    // 处理图像识别结果
    BOOL isEffectReady = [delegator.modelKit isReadyForKey:kEffectKey];
    if (isEffectReady) {
        MTAiImageRecognitionResult *recognitionResult = engineResult.imageRecognitionResult;
        if (recognitionResult && recognitionResult.thirdLevelRecognitions.count > 0) {
            // 获取置信度最高的识别结果
            MTAiImageRecognition *topRecognition = nil;
            float maxScore = 0.0f;
            topRecognition = recognitionResult.thirdLevelRecognitions.firstObject;
            maxScore = topRecognition.score;
            //recognitionResult.thirdLevelRecognitions 已经排过序，最高分排在第一位。
            //        for (MTAiImageRecognition *recognition in recognitionResult.thirdLevelRecognitions) {
            //            if (recognition.score > maxScore) {
            //                maxScore = recognition.score;
            //                topRecognition = recognition;
            //            }
            //        }
            
            if (topRecognition) {
                // 获取三级标签信息
                // 这里需要根据实际的标签体系来解析 category 到三级标签
                // 暂时使用 category 值作为 third 级别，first 和 second 需要根据实际标签体系计算
                //int category = topRecognition.category;
                
                // 简化的三级标签计算（实际使用时需要根据标签体系调整）
                int first = topRecognition.firstCategory;  // 一级标签
                int second = topRecognition.secondCategory;   // 二级标签
                int third =  topRecognition.category;  // 三级标签
                
                NSMutableDictionary *recognitionInfo = result[@"image_recognition_result"];
                recognitionInfo[@"first"] = @(first);
                recognitionInfo[@"second"] = @(second);
                recognitionInfo[@"third"] = @(third);
            }
        }
    }
    // 释放内存
    free(rawData);

    if (!isEffectReady) {
        result[@"image_recognition_result"] = [@{} mutableCopy];
    }

    // 将 result 转换为 JSON 字符串
    NSString *resultString = @"";
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:result
                                                       options:NSJSONWritingPrettyPrinted
                                                         error:&error];
    if (jsonData && !error) {
        resultString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    } else {
        NSLog(@"JSON 序列化错误: %@", error.localizedDescription);
        resultString = @"{}";
    }
    
    NSLog(@"识别结果 JSON: %@", resultString);
        
    return result;
}
