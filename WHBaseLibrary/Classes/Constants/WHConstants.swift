//
//  WHConstants.swift
//
//  Created by Devin on 2022/2/10.
//

import Foundation

/// 屏幕宽度
public let WH_SCREEN_WIDTH = UIScreen.main.bounds.width
/// 屏幕高度
public let WH_SCREEN_HEIGHT = UIScreen.main.bounds.height
/// 状态栏高度
public let WH_STATUS_BAR_HEIGHT : CGFloat = {
    var statusBarHeight: CGFloat = 0
        if #available(iOS 13.0, *) {
            let window = UIApplication.shared.windows.first
            let topPadding = window?.safeAreaInsets.top
            statusBarHeight = topPadding ?? 20.0
        } else {
            statusBarHeight = UIApplication.shared.statusBarFrame.height
        }
    return statusBarHeight == 0 ? 20 : statusBarHeight
} ()
/// 不含状态栏的导航栏高度
public let WH_NAVIGATION_NO_STATUS_BAR_HEIGHT: CGFloat = 44.0
/// 导航栏高度
public let WH_NAVIGATION_BAR_HEIGHT = WH_STATUS_BAR_HEIGHT + WH_NAVIGATION_NO_STATUS_BAR_HEIGHT
/// 屏幕安全区底部间隙
public let WH_SCREEN_BOTTOM_SPACE: CGFloat = {
    if #available(iOS 11.0, *),
       let safeBtm = UIApplication.wh_currentWindow?.safeAreaInsets.bottom {
        return safeBtm
    } else {
        return 0
    }
} ()
/// 1像素
public let WH_ONE_PIXEL = 1.0 / UIScreen.main.scale

/// 是否为iPhoneX系列
public func WH_IS_IPHONE_X() -> Bool {
    guard UIDevice.current.userInterfaceIdiom == .phone else {
        return false
    }
    if #available(iOS 11.0, *) {
        if let bottom = UIApplication.wh_currentWindow?.safeAreaInsets.bottom {
            return bottom > 0
        }
    }
    return false
}

/// ipad屏幕宽度
public let WH_IPAD_SCREEN_WIDTH = UIScreen.main.bounds.height/16*9
/// ipad屏幕高度
public let WH_IPAD_SCREEN_HEIGHT = UIScreen.main.bounds.height
/// ipad边框距离
public let WH_IPAD_BORDER_SPACE = (UIScreen.main.bounds.width - WH_IPAD_SCREEN_WIDTH) / 2

public func WH_IS_IPAD_FOR_SCREEN() -> Bool {
    
    if WH_SCREEN_WIDTH == 1024.0, WH_SCREEN_HEIGHT == 1366.0 {
        return true
    }
    return false
}


// MARK: - 响应链事件名称
/// 返回事件名称
public let WH_NAVIGATION_BACK_EVENT_NAME = "WH_NAVIGATION_BACK_EVENT_NAME"

// MARK: - 无参数闭包名称
public typealias WHBackActionBlock = () -> Void
