//
//  WHCountDownManager.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/4/25.
//

import Foundation

public class WHCountDownManager {
    
    private lazy var timerManage : WHTimerManager? = {
        let time = WHTimerManager()
        return time
    }()
    
    private var countTime = 300
    
    public init() {}
    
    public func startCountDown(_ countTime: NSInteger = 300, timeInterval: NSInteger = 1, complete: (()->())?) {
        self.countTime = countTime
        timerManage?.dispatchTimer(timeInterval: Double(timeInterval)) { [weak self] timer in
            guard let self = self else {return}
            if self.countTime == 0 {
                self.stopCountDown()
                if let block = complete {
                    block()
                }
            } else {
                self.countTime -= 1
            }
        }
    }
    
    public func stopCountDown(){
        timerManage?.cancleTimer()
        timerManage = nil
    }
    
    deinit {
        timerManage?.cancleTimer()
    }
}


class WHTimerManager {
    
    var timer : DispatchSourceTimer?
    
    init() {
        timer = DispatchSource.makeTimerSource(flags: [], queue: DispatchQueue.global())
    }

    func dispatchTimer(timeInterval: Double, handler:@escaping (DispatchSourceTimer?)->()) {
        timer?.schedule(deadline: .now(), repeating: timeInterval)
        timer?.setEventHandler {
            DispatchQueue.main.async {
                handler(self.timer)
            }
        }
        timer?.resume()
    }

    func cancleTimer() {
        if timer != nil {
            timer?.cancel()
            timer = nil
        }
    }
    
    deinit {
        cancleTimer()
    }
}
