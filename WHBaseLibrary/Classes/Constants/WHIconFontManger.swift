//
//  WHIconFontManger.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/2/21.
//

import Foundation

public class WHIconFontManger: NSObject {
    
    /// 记录已经注册过的 IconFont 字体库，重复注册同一个字体库文件会出错
    private static var registerIconFontRecords : [Int : Bool] = [:]
    
    /// 加载 IconFont 字体库（字体库已加入项目内的）
    /// - Parameters:
    ///   - fileName: IconFont 字体库文件名
    ///   - type: IconFont 字体库文件名后缀（默认 ttf）
    public static func wh_loadIconFont(with fileName: String, ofType type: String = "ttf") {
        if let filePath = Bundle.main.path(forResource: fileName, ofType: type) {
            loadIconFont(from: filePath)
        } else {
            assert(false, "加载字体库失败")
        }
    }
    
    /// 加载 IconFont 字体库
    /// - Parameter filePath: IconFont 字体库文件的文件地址
    public static func loadIconFont(from filePath: String) {
        let hashValue: Int = filePath.hashValue
        if registerIconFontRecords[hashValue] ?? false {
            return
        }
        if FileManager.default.fileExists(atPath: filePath) {
            if let data = NSData.init(contentsOfFile: filePath),
               let provider = CGDataProvider.init(data: data) {
                if registerIconFont(with: provider) {
                    registerIconFontRecords[hashValue] = true
                }
            } else {
                assert(false, "字体库文件获取数据失败:\(filePath)")
            }
        } else {
            assert(false, "字体库文件不存在:\(filePath)")
        }
    }
    
    /// 注册字体库
    /// - Parameter data: 字体库数据
    public static func registerIconFont(with data: CGDataProvider) -> Bool {
        if let font = CGFont.init(data) {
            let error: UnsafeMutablePointer<Unmanaged<CFError>?>? = nil
            if !CTFontManagerRegisterGraphicsFont(font, error) {
                // 注册失败
                if let unError = error?.pointee?.takeUnretainedValue(), let description = CFErrorCopyDescription(unError) {
                    assert(false, "字体库注册失败:\(description)")
                } else {
                    assert(false, "字体库注册失败:Unknown error")
                }
                return false
            }
            return true
        }
        return false
    }
}


@objc
extension NSString {

    public func iconfontImage(name: String,
                              size: CGFloat,
                              isRound: Bool = false,
                              color: UIColor = .black,
                              backgroundColor: UIColor = .clear,
                              insets: UIEdgeInsets = .zero) -> UIImage? {
        let iconString = self as NSString
        let attributes = [NSAttributedString.Key.font : UIFont.iconfont(name: name, size: size), NSAttributedString.Key.foregroundColor : color]
        let rect = iconString.boundingRect(with: CGSize.zero, options: .usesLineFragmentOrigin, attributes: attributes, context: nil)
        let size = CGSize.init(width: rect.size.width + insets.left + insets.right, height: rect.size.height + insets.bottom + insets.top)
        let targetRect = CGRect.init(x: insets.left, y: insets.top, width: rect.size.width, height: rect.size.height)
        return autoreleasepool { () -> UIImage? in
            UIGraphicsBeginImageContextWithOptions(size, false, UIScreen.main.scale)
            if isRound {
                let ctx = UIGraphicsGetCurrentContext()
                let innerPath = UIBezierPath(
                    roundedRect: .init(origin: .init(x: targetRect.minX + 0.5,
                                                     y: targetRect.minY + 0.5),
                                       size: .init(width: targetRect.width - 1,
                                                   height: targetRect.height - 1)),
                    cornerRadius: targetRect.size.width / 2 - 0.5)
                innerPath.lineWidth = 0.5
                let outerPath = UIBezierPath(roundedRect: targetRect, cornerRadius: targetRect.size.width / 2)
                ctx?.addPath(innerPath.cgPath)
                ctx?.setStrokeColor(UIColor.black.withAlphaComponent(0.7).cgColor)
                ctx?.strokePath()
                ctx?.addPath(outerPath.cgPath)
                ctx?.clip()
            }
            if backgroundColor != .clear {
                let ctx = UIGraphicsGetCurrentContext()
                ctx?.setFillColor(backgroundColor.cgColor)
                ctx?.fill(rect)
            }
            iconString.draw(with: targetRect, options: .usesLineFragmentOrigin, attributes: attributes, context: nil)
            let image = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            return image
        }
    }
}

@objc
extension UIFont {
    
    /// 初始化 IconFont 字体
    /// - Parameters:
    ///   - fontName: 字体名称
    ///   - fontSize: 字体大小
    public static func iconfont(name fontName: String, size fontSize: CGFloat) -> UIFont {
        return UIFont.init(name: fontName, size: fontSize) ?? UIFont.systemFont(ofSize: fontSize)
    }
}


public extension URL {
    func decodeToJson() -> [String: Any]? {
        do {
            /// Decode
            let jsonData: Data = try Data(contentsOf: self)
            let info = try? JSONSerialization.jsonObject(with: jsonData, options: [])
            if let info = info as? [String: Any] {
                return info
            }
        } catch {
            /// Decoding error.
            return nil
        }
        
        return nil
    }
}



