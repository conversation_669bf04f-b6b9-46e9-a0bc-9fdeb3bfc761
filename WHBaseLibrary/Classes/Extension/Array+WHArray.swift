//
//  Array+MBArray.swift
//  MBBaseLibrary
//
//  Created by <PERSON> on 2021/4/2.
//

import Foundation

/// MARK: - Methods
extension Array {
    
    public func object(at index: Int) -> Element? {
        return (0..<count).contains(index) ? self[index] : nil
    }
    
    public subscript (safe index: Int) -> Element? {
        return (0..<count).contains(index) ? self[index] : nil
    }
}

/// MARK: - Methods (Equatable)
extension Array where Element: Equatable {
    /// 防止数组越界崩溃
    @discardableResult
    public mutating func safeRemove(at index: Index) -> [Element] {
        guard !self.isEmpty else { return self }
        if index <= self.count-1 {
            remove(at: index)
        }
        return self
    }
    
    /// 移除数组某个元素
    @discardableResult
    public mutating func safeRemoveAll(_ item: Element) -> [Element] {
        removeAll(where: { $0 == item })
        return self
    }
    
    /// 移除数组多个元素
    @discardableResult
    public mutating func safeRemoveAll(_ items: [Element]) -> [Element] {
        guard !items.isEmpty else { return self }
        removeAll(where: { items.contains($0) })
        return self
    }
    
    /// 去重
    public mutating func safeRemoveDuplicates() {
        self = reduce(into: [Element]()) {
            if !$0.contains($1) {
                $0.append($1)
            }
        }
    }
}

extension Array {
    /// 根据指定规则进行去重，保留前者
    public func filterDuplicates<E: Equatable>(_ filter: (Element) -> E) -> [Element] {
        var result = [Element]()
        for value in self {
            let key = filter(value)
            if !result.map({filter($0)}).contains(key) {
                result.append(value)
            }
        }
        return result
    }
    
    /// 数组转json
    public func arryTojson() -> String {
        if (!JSONSerialization.isValidJSONObject(self)) {
            return ""
        }

        //利用OC的json库转换成OC的NSData，
        //如果设置options为NSJSONWritingOptions.PrettyPrinted，则打印格式更好阅读
        let data : Data! = try? JSONSerialization.data(withJSONObject: self, options: [])
        //NSData转换成NSString打印输出
        let str = NSString(data:data, encoding: String.Encoding.utf8.rawValue)
        //输出json字符串
        return str! as String
    }
}

public extension Array where Element: Equatable {
    
    /// 去除数组重复元素
    /// - Returns: 去除数组重复元素后的数组 保持顺序不变
    func removeDuplicate() -> Array {
       return self.enumerated().filter { (index,value) -> Bool in
            return self.firstIndex(of: value) == index
        }.map { (_, value) in
            value
        }
    }
    
    /// SwifterSwift: Remove all instances of an item from array.
    ///
    ///        [1, 2, 2, 3, 4, 5].removeAll(2) -> [1, 3, 4, 5]
    ///        ["h", "e", "l", "l", "o"].removeAll("l") -> ["h", "e", "o"]
    ///
    /// - Parameter item: item to remove.
    /// - Returns: self after removing all instances of item.

    @discardableResult
    mutating func removeAll(_ item: Element) -> [Element] {
        removeAll(where: { $0 == item })
        return self
    }
    
    /// SwifterSwift: Remove all instances contained in items parameter from array.
        ///
        ///        [1, 2, 2, 3, 4, 5].removeAll([2,5]) -> [1, 3, 4]
        ///        ["h", "e", "l", "l", "o"].removeAll(["l", "h"]) -> ["e", "o"]
        ///
        /// - Parameter items: items to remove.
        /// - Returns: self after removing all instances of all items in given array.
        @discardableResult
        mutating func removeAll(_ items: [Element]) -> [Element] {
            guard !items.isEmpty else { return self }
            removeAll(where: { items.contains($0) })
            return self
        }

}
