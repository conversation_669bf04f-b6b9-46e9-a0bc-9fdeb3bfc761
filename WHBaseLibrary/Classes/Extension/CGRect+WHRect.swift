//
//  CGRect+WHRect.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/4/15.
//

import Foundation

extension CGRect {
    
    public static func * (rect: CGRect,
                          edgeInsets: UIEdgeInsets) -> CGRect {
        return rect.inset(by: UIEdgeInsets(top: edgeInsets.top * rect.height,
                                           left: edgeInsets.left * rect.width,
                                           bottom: edgeInsets.bottom * rect.height,
                                           right: edgeInsets.right * rect.width))
    }
    
    public static func *= (rect: inout CGRect,
                           edgeInsets: UIEdgeInsets) {
        rect = rect * edgeInsets
    }
    
    public func enlarge(multiple: CGFloat) -> CGRect {
        let enlargeWidth = size.width * multiple
        let enlargeHeight = size.height * multiple
        let decreaseX = enlargeWidth / 2
        let decreaseY = enlargeHeight / 2
        return CGRect(
            x: origin.x - decreaseX,
            y: origin.y - decreaseY,
            width: size.width + enlargeWidth,
            height: size.height + enlargeHeight
        )
    }
    
}
