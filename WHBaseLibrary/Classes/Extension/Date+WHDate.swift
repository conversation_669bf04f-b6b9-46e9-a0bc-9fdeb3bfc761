//
//  Date+WHDate.swift
//
//  Created by <PERSON> on 2022/2/11.
//

import Foundation

extension Date {
    
    /// 时间戳字符串。单位：毫秒（13位）
    public static var timeStamp: String {
        return String(timeStampInt)
    }
    
    /// 时间戳int型。单位：毫秒（13位）
    public static var timeStampInt: Int {
        return Int(Date().timeIntervalSince1970 * 1000)
    }
    
    /// 时间戳字符串。单位：秒（10位）
    public static var timeStampSecond: String {
        return String(timeStampSecondInt)
    }
    
    /// 时间戳int型。单位：秒（10位）
    public static var timeStampSecondInt: Int {
        return Int(Date().timeIntervalSince1970)
    }
    
    
    public static func secondFormatted(_ second: Int) -> String {
        let sec = second % 60
        let min = (second / 60) % 60
        let hour = second / 60 / 60
        if hour > 0 {
            return String(format: "%02d:%02d:%02d", hour, min, sec)
        } else {
            return String(format: "%02d:%02d", min, sec)
        }
    }
    
    /// 获取格式化字符串
    /// - Parameters:
    ///   - date: date实例
    ///   - formatter: 如 yyyy-MM-dd HH:mm:ss.SSS
    /// - Returns: 格式化字符串
    public static func formattedWith(_ date: Date, formatter: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = formatter
        return dateFormatter.string(from: date)
    }
    
    /// 获取格式化字符串
    /// formatter: 如 yyyy-MM-dd HH:mm:ss.SSS
    public func formattedString(with formatter: String) -> String {
        return Date.formattedWith(self, formatter: formatter)
    }
    
    /// 是否是今天
    public var isToday: Bool {
        return Calendar.current.isDateInToday(self)
    }
    
    /// 是否是昨天
    public var isYesterday: Bool {
        return Calendar.current.isDateInYesterday(self)
    }
    
    /// 是否是前天
    public var isTheDayBeforeYesterday: Bool {
        return isTheDayOf(-2)
    }
    
    /// 是否是明天
    public var isTomorrow: Bool {
        return Calendar.current.isDateInTomorrow(self)
    }
    
    /// 是否是后天
    public var isTheDayAfterTomorrow: Bool {
        return isTheDayOf(2)
    }
    
    /// 是否是周末
    public var isWeekend: Bool {
        return Calendar.current.isDateInWeekend(self)
    }
    
    /// 判断是否在时间段内
    public func isBetween(_ startTime: String,
                          and endTime: String) -> Bool {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm:ss"
        let nowTime =  dateFormatter.string(from: self)
        let nowDate =  dateFormatter.date(from: nowTime)
        guard let startDate = dateFormatter.date(from: startTime) else {
            return false
        }
        guard let endDate = dateFormatter.date(from: endTime) else {
            return false
        }
        if nowDate?.compare(startDate) == ComparisonResult.orderedDescending && nowDate?.compare(endDate) == ComparisonResult.orderedAscending {
            return true
        }
        return false
    }
    
    /// 判断date处于哪一天
    /// - Parameter count: 0是今天，-1是昨天，1是明天，以此类推。。。
    /// - Returns: <#description#>
    public func isTheDayOf(_ count: Int) -> Bool {
        let targetDate = Date(timeIntervalSinceNow: TimeInterval(count * 24 * 60 * 60))
        let targetComponents = Calendar.current.dateComponents([.year, .month, .day],
                                                               from: targetDate)
        let components = Calendar.current.dateComponents([.year, .month, .day],
                                                         from: self)
        return components.year == targetComponents.year &&
            components.month == targetComponents.month &&
            components.day == targetComponents.day
    }
    
}

extension CGFloat {
    public var float: Float {
        return Float(self)
    }
}

extension Int {
    public var cgFloat: CGFloat {
    CGFloat(self)
  }
}
