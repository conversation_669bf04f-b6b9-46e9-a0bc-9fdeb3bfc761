//
//  Dictionary+WHDictionary.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2023/5/13.
//

import Foundation

extension Dictionary {
    
    public func wh_toJsonData() -> Data? {
        var jsonData: Data?
        do {
            jsonData = try JSONSerialization.data(withJSONObject: self)
        } catch {
            WHPrint(self ,#function, error)
        }
        return jsonData
    }
    
    public func wh_toJsonString() -> String? {
        if let jsonData = wh_toJsonData(),
            let jsonString = String(data: jsonData, encoding: .utf8) {
            return jsonString
        }
        return nil
    }
    
    //用于字典的合并，接收的参数是一个键值对时，就可以添加到原有的字典中，并且对原有字典的重复值进行覆盖为新值，不重复则保留
    public mutating func merge<S>(_ other: S)
    where S: Sequence, S.Iterator.Element == (key: Key, value: Value) {
        for (k ,v) in other {
            self[k] = v
        }
    }
}
