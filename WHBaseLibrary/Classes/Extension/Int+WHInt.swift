//
//  Int+WHInt.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/4/30.
//

import Foundation

extension Int {
    
    /// 转换整形时间为mm:ss格式的字符串
    /// - Returns: String
    public func convertMillisecondToHHmmString() -> String {
        let date = Date(timeIntervalSince1970: (Double(self) / 1000.0).rounded())
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "mm:ss"
        return dateFormatter.string(from: date)
    }
    
}
