//
//  String+WHString.swift
//
//  Created by zjp on 2022/2/11.
//

import Foundation
import CommonCrypto

extension String {
    
    public var md5: String {
        guard let cChars = cString(using: .utf8) else {
            return self
        }
        var hash = [UInt8](repeating: 0, count: Int(CC_MD5_DIGEST_LENGTH))
        CC_MD5(cChars, CC_LONG(cChars.count - 1), &hash)
        return hash.reduce("") { $0 + String(format: "%02x", $1) }
    }
    
    public var date: Date? {
        guard let interval = TimeInterval(self),
            interval > 0 else {
            return nil
        }
        return Date(timeIntervalSince1970: interval / 1000)
    }
    
    public var urlEncodeString: String? {
        return addingPercentEncoding(withAllowedCharacters: .urlEncodeAllowed)
    }
    
    public var urlOnlyEncodeAdd: String? {
        return addingPercentEncoding(withAllowedCharacters: .urlEncodeAdd)
    }
    
    public var urlDecodeString: String? {
        return self.removingPercentEncoding
    }
    
    public func getStringHeight(with width:CGFloat,
                                limitHeight:CGFloat = CGFloat(MAXFLOAT),
                                attributes:[NSAttributedString.Key: Any] ) -> CGFloat {
        var contentHieght = self.boundingRect(
            with: CGSize(width:width, height: limitHeight),
            options: [.usesFontLeading, .usesLineFragmentOrigin, .truncatesLastVisibleLine],
            attributes: attributes, context: nil).height
        return contentHieght
    }
    
    public func getStringWidth(_ height : CGFloat,
                               font: UIFont) -> CGFloat {

        let maxWidth: CGFloat = .greatestFiniteMagnitude
        let size = CGSize(width: maxWidth, height: height)
        let options = NSStringDrawingOptions.usesFontLeading.union(.usesLineFragmentOrigin)
        let attributes = [NSAttributedString.Key.font: font]
        let rect = NSString(string: self).boundingRect(with: size,
                                                       options: options,
                                                       attributes: attributes,
                                                       context: nil)
        return ceil(rect.width)
    }
    
    
    
    public func getStringHeight(with width:CGFloat,
                                font: UIFont,
                                limitHeight: CGFloat = CGFloat(MAXFLOAT),
                                lineSpacing: CGFloat? = nil) -> CGFloat {
        var attributes: [NSAttributedString.Key: Any] = [NSAttributedString.Key.font: font]
        if let spacing = lineSpacing {
            var paragraph = NSMutableParagraphStyle()
            paragraph.lineSpacing = spacing;
            attributes[NSAttributedString.Key.paragraphStyle] = paragraph
        }
        return self.getStringHeight(with: width,
                                    limitHeight: limitHeight,
                                    attributes: attributes)
    }
    
    public func wh_substring(with range: NSRange) -> String {
        let text = NSString(string: self)
        return text.substring(with: range)
    }
    
    public func wh_range(of substring: String) -> NSRange {
        let text = NSString(string: self)
        return text.range(of: substring)
    }
    
    /// 是否为空格和换行符
    public var isBlank: Bool {
        let trimmedStr = self.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmedStr.isEmpty
    }
    
    /// 根据开始位置和长度截取字符串
    public func subString(start:Int, length:Int = -1) -> String {
        var len = length
        if len == -1 {
            len = self.count - start
        }
        
        let st = self.index(startIndex, offsetBy:start, limitedBy: endIndex) ?? endIndex
        let en = self.index(st, offsetBy:len, limitedBy: endIndex) ?? endIndex
        return st == en ? "" : String(self[st ..< en])
    }
    
    /// Range转换为NSRange
    public func toNSRange(_ range: Range<String.Index>?) -> NSRange {
        
        guard let range = range, let from = range.lowerBound.samePosition(in: utf16), let to = range.upperBound.samePosition(in: utf16) else {
            return NSMakeRange(0, 0)
        }
        return NSMakeRange(utf16.distance(from: utf16.startIndex, to: from), utf16.distance(from: from, to: to))
    }
    
    /// 图片缩略图
    /// width: 图片的宽度
    public func thumbCover(width: Int) -> String {
        if self.isEmpty {
            return ""
        }
        var cover: String = self
        if self.hasPrefix("https://") {
            if self.contains("?k=") {
                cover = self.replacingOccurrences(of: "k=", with: "imageView2/2/w/\(width)/interlace/1/format/webp&k=")
            } else {
                if self.contains("?") {
                    cover = self + "&imageView2/2/w/\(width)/interlace/1/format/webp"
                } else {
                    cover = self + "?imageView2/2/w/\(width)/interlace/1/format/webp"
                }
            }
        }
        return cover
    }
    
    public func fromBase64() -> String? {
        guard let data = Data(base64Encoded: self) else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }
    
    public func toBase64() -> String {
        return Data(self.utf8).base64EncodedString()
    }
    
    /// base64串转数组
    public func decodeToArray() -> [[String : Any]] {
        let arr = [Dictionary<String,String>()]
        if let decodedData = Data(base64Encoded: self) {
            do {
                // 将解码后的 Data 对象转换为字符串
                if let jsonString = String(data: decodedData, encoding: .utf8) {
                    // 将 JSON 字符串解析为字典数组
                    if let jsonArray = try JSONSerialization.jsonObject(with: jsonString.data(using: .utf8)!, options: []) as? [[String : Any]] {
                        return jsonArray
                    }
                }
            } catch {
                return arr
            }
        }
        return arr
    }
    
    /// json串转数组
    public func jsonToArrary() -> [[String : Any]] {
        let arr = [Dictionary<String,String>()]
        if let jsonData = self.data(using: .utf8) {
            do {
                // 将 JSON 数据解析为适当的 Swift 数据类型
                if let jsonArray = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [[String : Any]] {
                    // 现在 jsonArray 是一个包含整数的数组
                    return jsonArray
                }
            } catch {
                return arr
            }
        }
        return arr
    }
    /// json转字典
    public func stringValueDic() -> [String : Any]? {
        if let data = self.data(using: String.Encoding.utf8), let dict = try? JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? [String : Any] {
            return dict
        }
        return nil
    }
    ///字符串移除file:///
    public func removingLeadingFilePrefix() -> String {
        let prefix = "file:///"
        if self.hasPrefix(prefix) {
            return String(self.dropFirst(prefix.count))
        }
        return self
    }
}

let InfoDic = Bundle.main.infoDictionary!
let NameSpace = InfoDic["CFBundleExecutable"] as! String
// MARK: - 字符串转类
public extension String {
    /// String -> 类
    public func toClass() -> AnyClass? {
        if let cls = NSClassFromString(NameSpace + "." + self) {
            return cls
        } else if let cls = NSClassFromString(self) {
            return cls
        } else {
            print("未找到class")
            return nil
        }
    }
    /// String -> UIViewController类
    public func toControllerClass() -> UIViewController.Type? {
        guard let cls = self.toClass() else { return nil }
        guard let vc = cls as? UIViewController.Type else { return nil }
        return vc
    }
}

public extension CharacterSet {
    static var urlEncodeAllowed: CharacterSet {
        return CharacterSet(charactersIn: "!*'();:@&=+$,/?%#[]{}|^`\"<>\\").inverted
    }
    
    static var urlEncodeAdd: CharacterSet {
        return CharacterSet(charactersIn: "+").inverted
    }
    
}
