//
//  UIApplication+WHCurrentViewController.swift
//
//  Created by <PERSON> on 2022/2/9.
//

import Foundation

extension UIApplication {
    
    /// 当前窗口
    @objc
    public static var wh_currentWindow: UIWindow? {
        var window = UIApplication.shared.keyWindow
        if window == nil {
            window = UIApplication.shared.delegate?.window ?? nil
        }
        if window == nil,
           UIApplication.shared.windows.isEmpty == false {
            window = UIApplication.shared.windows.first
        }
        return window
    }
    
    /// 当前视图控制器
    @objc
    public static var wh_currentViewController: UIViewController? {
        return wh_currentWindow?.rootViewController?.wh_currentViewController
    }

}

extension UIViewController {
    
    @objc
    public var wh_currentViewController: UIViewController? {
        if let viewController = presentedViewController {
            let isAlert = viewController.isKind(of: UIAlertController.self)
            if isAlert {
                return self
            } else {
                return viewController.wh_currentViewController
            }
        }
        return self
    }
    
//    @objc
//    public class func currentVC(base: UIViewController? = (UIApplication.shared.delegate?.window as? UIWindow)?.rootViewController) -> UIViewController? {
//        if let nav = base as? UINavigationController {
//            return currentVC(base: nav.visibleViewController)
//        }
//        if let tab = base as? UITabBarController {
//            return currentVC(base: tab.selectedViewController)
//        }
//        if let presented = base?.presentedViewController {
//            return currentVC(base: presented)
//        }
//        return base
//    }
}

extension UITabBarController {
    
    @objc
    public override var wh_currentViewController: UIViewController? {
        return selectedViewController?.wh_currentViewController
    }
    
}

extension UINavigationController {
    
    @objc
    public override var wh_currentViewController: UIViewController? {
        return topViewController?.wh_currentViewController
    }
    
}

