//
//  UIButton+WHButton.swift
//
//  Created by <PERSON> on 2021/4/12.
//

import Foundation
import ObjectiveC.runtime

public enum ButtonEdgeInsetsStyle {
    // 图片相对于label的位置
    case Top
    case Left
    case Right
    case Bottom
}

fileprivate var rectNameKey:(Character?,Character?,Character?,Character?)

public extension UIButton {
    
    public func layoutButton(style: ButtonEdgeInsetsStyle, imageTitleSpace: CGFloat) {
        //得到imageView和titleLabel的宽高
        let imageWidth = self.imageView?.frame.size.width
        let imageHeight = self.imageView?.frame.size.height
        
        var labelWidth: CGFloat! = 0.0
        var labelHeight: CGFloat! = 0.0
        
        labelWidth = self.titleLabel?.intrinsicContentSize.width
        labelHeight = self.titleLabel?.intrinsicContentSize.height
        
        //初始化imageEdgeInsets和labelEdgeInsets
        var imageEdgeInsets = UIEdgeInsets.zero
        var labelEdgeInsets = UIEdgeInsets.zero
        
        //根据style和space得到imageEdgeInsets和labelEdgeInsets的值
        switch style {
        /**
         * titleEdgeInsets是titleLabel相对于其上下左右的inset，跟tableView的contentInset是类似的；
         * 如果只有title，那titleLabel的 上下左右 都是 相对于Button 的；
         * 如果只有image，那imageView的 上下左右 都是 相对于Button 的；
         * 如果同时有image和label，那image的 上下左 是 相对于Button 的，右 是 相对于label 的；
         * label的 上下右 是 相对于Button的， 左 是 相对于label 的。
         */
        case .Top:
            //上 左 下 右
            imageEdgeInsets = UIEdgeInsets(top: -labelHeight-imageTitleSpace/2, left: 0, bottom: 0, right: -labelWidth)
            labelEdgeInsets = UIEdgeInsets(top: 0, left: -imageWidth!, bottom: -imageHeight!-imageTitleSpace/2, right: 0)
            break;
            
        case .Left:
            imageEdgeInsets = UIEdgeInsets(top: 0, left: -imageTitleSpace/2, bottom: 0, right: imageTitleSpace)
            labelEdgeInsets = UIEdgeInsets(top: 0, left: imageTitleSpace/2, bottom: 0, right: -imageTitleSpace/2)
            break;
            
        case .Bottom:
            imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: -labelHeight!-imageTitleSpace/2, right: -labelWidth)
            labelEdgeInsets = UIEdgeInsets(top: -imageHeight!-imageTitleSpace/2, left: -imageWidth!, bottom: 0, right: 0)
            break;
            
        case .Right:
            imageEdgeInsets = UIEdgeInsets(top: 0, left: labelWidth+imageTitleSpace/2, bottom: 0, right: -labelWidth-imageTitleSpace/2)
            labelEdgeInsets = UIEdgeInsets(top: 0, left: -imageWidth!-imageTitleSpace/2, bottom: 0, right: imageWidth!+imageTitleSpace/2)
            break;
        }
        
        self.titleEdgeInsets = labelEdgeInsets
        self.imageEdgeInsets = imageEdgeInsets
        
    }
    
    public func wh_setEnlargeEdgeWith(top:CGFloat,right:CGFloat,bottom:CGFloat,left:CGFloat) {
        objc_setAssociatedObject(self, &rectNameKey.0, top, .OBJC_ASSOCIATION_COPY_NONATOMIC)
        objc_setAssociatedObject(self, &rectNameKey.1, right, .OBJC_ASSOCIATION_COPY_NONATOMIC)
        objc_setAssociatedObject(self, &rectNameKey.2, bottom, .OBJC_ASSOCIATION_COPY_NONATOMIC)
        objc_setAssociatedObject(self, &rectNameKey.3, left, .OBJC_ASSOCIATION_COPY_NONATOMIC)
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        
        if let topEdge = objc_getAssociatedObject(self, &rectNameKey.0) as? CGFloat,
           let rightEdge = objc_getAssociatedObject(self, &rectNameKey.1) as? CGFloat,
           let bottomEdge = objc_getAssociatedObject(self, &rectNameKey.2) as? CGFloat,
           let leftEdge = objc_getAssociatedObject(self, &rectNameKey.3) as? CGFloat,self.isHidden == false {
            return CGRect(x: bounds.origin.x - leftEdge, y: bounds.origin.y - topEdge, width: bounds.width + leftEdge + rightEdge, height: bounds.height + topEdge + bottomEdge).contains(point) ? self : nil
        }
        return super.hitTest(point, with: event)
    }
}
