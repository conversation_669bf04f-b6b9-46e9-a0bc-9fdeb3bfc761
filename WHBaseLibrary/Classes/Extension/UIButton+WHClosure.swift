//
//  UIButton+WHClosure.swift
//  WHBaseLibrary
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/9/23.
//

import Foundation


public typealias UIControlAction = (_ sender:UIControl) -> Void

class WHButtonClosureWrapper: NSObject {
    let controlEvents: UIControl.Event
    let action: UIControlAction
    
    init(_ controlEvents: UIControl.Event, _ action: @escaping UIControlAction) {
        self.controlEvents = controlEvents
        self.action = action
    }
    
    @objc func actionExcute(_ sender: UIControl) {
        action(sender)
    }
}

extension UIControl {
    static var WHControlClosureAssociatedKey = "WHControlClosureAssociatedKey"
    
    var closureWrapper: Dictionary<NSNumber, WHButtonClosureWrapper>? {
        get {
            return objc_getAssociatedObject(self, &UIControl.WHControlClosureAssociatedKey) as? Dictionary<NSNumber, WHButtonClosureWrapper>
        }
        set {
            objc_setAssociatedObject(self, &UIControl.WHControlClosureAssociatedKey, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    public func wh_addEventAction(for controlEvent: UIControl.Event, _ action: @escaping UIControlAction) {
        
        if closureWrapper == nil {
            closureWrapper = [NSNumber : WHButtonClosureWrapper]()
        }
        
        let wrapperValue = WHButtonClosureWrapper(controlEvent, action)
        let wrapperKey = NSNumber(value: controlEvent.rawValue)
        
        closureWrapper?[wrapperKey] = wrapperValue
        self.addTarget(wrapperValue, action: #selector(WHButtonClosureWrapper.actionExcute(_:)), for: controlEvent)
    }
    
    public func wh_removeEventAction(for controlEvent: UIControl.Event) {
        let wrapperKey = NSNumber(value: controlEvent.rawValue)
        closureWrapper?[wrapperKey] = nil
    }
    
    public func wh_addTouchupInsideAction(_ action: @escaping UIControlAction) {
        self.wh_addEventAction(for: .touchUpInside, action)
    }
    
    public func wh_removeTouchupInsideAction() {
        self.wh_removeEventAction(for: .touchUpInside)
    }
}
