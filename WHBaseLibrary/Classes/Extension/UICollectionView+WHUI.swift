//
//  UICollectionView+WHUI.swift
//  WHBaseLibrary
//
//  Created by zhanglifei on 2023/9/27.
//

import Foundation

public extension UICollectionView {
    
    func registerHeader<T: UICollectionReusableView>(_ type: T.Type) {
        let identifier = String(describing: type.self)
        register(type, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: identifier)
    }
    
    func dequeueReuseHeader<T: UICollectionReusableView>(_ type: T.Type, at indexPath: IndexPath) -> T {
        let identifier = String(describing: type.self)
        
        if let header = dequeueReusableSupplementaryView(ofKind: UICollectionView.elementKindSectionHeader,
                                                         withReuseIdentifier: identifier,
                                                         for: indexPath) as? T {
            return header
        } else {
            assertionFailure("\(type.self) was not registered")
            return UICollectionReusableView() as! T
        }
    }
    
    func registerCell<T: UICollectionViewCell>(_ type: T.Type) {
        let identifier = String(describing: type.self)
        register(type, forCellWithReuseIdentifier: identifier)
    }
    
    func dequeueReusableCell<T: UICollectionViewCell>(_ type: T.Type,
                                                      identifier: String? = nil,
                                                      for indexPath: IndexPath) -> T {
        
        guard let cell = dequeueReusableCell(withReuseIdentifier: identifier ?? String(describing: type.self),
                                             for: indexPath) as? T else {
            assertionFailure("\(type.self) was not registered")
            return UICollectionViewCell() as! T
        }
        return cell
    }
}
