//
//  UIColor+WHColor.m
//
//  Created by <PERSON> on 2021/6/22.
//

#import "UIColor+WHColor.h"

@implementation UIColor (WHColor)

+ (UIColor *)wh_colorWithRGB:(NSInteger)rgb {
    return [UIColor wh_colorWithRGB:rgb
                              alpha:1.0];
}

+ (UIColor *)wh_colorWithRGB:(NSInteger)rgb
                       alpha:(CGFloat)alpha {
    return [UIColor colorWithRed:((rgb & 0xff0000) >> 16) / 255.0
                           green:((rgb & 0xff00) >> 8) / 255.0
                            blue:(rgb & 0xff) / 255.0
                           alpha:alpha];
}

- (UIImage *)imageWithSize:(CGSize)size {
    UIGraphicsBeginImageContextWithOptions(size, NO, [UIScreen mainScreen].scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    
    CGContextSetFillColorWithColor(context, [self CGColor]);
    CGContextFillRect(context, (CGRect){.size=size});
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return image;
}

- (UIImage *)wh_image {
    return [self imageWithSize:(CGSize){.width=1.0, .height=1.0}];
}


@end
