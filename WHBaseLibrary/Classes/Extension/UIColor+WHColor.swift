//
//  UIColor+WHColor.swift
//
//  Created by <PERSON> on 2021/2/1.
//

import Foundation

extension UIColor {
    
    /// 获取rgb字符串。如：RRGGBB
    public var rgb: String {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var alpha: CGFloat = 0
        self.getRed(&r, green: &g, blue: &b, alpha: &alpha)
        return String(format: "%02X%02X%02X", Int(r * 255), Int(g * 255), Int(b * 255))
    }
    
    /// 获取rgba字符串。如：RRGGBBAA
    public var rgba: String {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var alpha: CGFloat = 0
        self.getRed(&r, green: &g, blue: &b, alpha: &alpha)
        return String(format: "%02X%02X%02X%02X", Int(r * 255), Int(g * 255), Int(b * 255), Int(alpha * 255))
    }
    
    public convenience init(rgb rgbHexValue: Int,
                            alpha: CGFloat = 1) {
        self.init(red: CGFloat((rgbHexValue & 0xff0000) >> 16) / 255,
                  green: CGFloat((rgbHexValue & 0xff00) >> 8) / 255,
                  blue: CGFloat(rgbHexValue & 0xff) / 255,
                  alpha: alpha)
    }
    
    public convenience init(wh_hexString: String) {
        let r, g, b, a: CGFloat
        if wh_hexString.hasPrefix("#") {
            let start = wh_hexString.index(wh_hexString.startIndex, offsetBy: 1)
            let hexColor = wh_hexString.substring(from: start)
            if hexColor.count == 8 {
                let scanner = Scanner(string: hexColor)
                var hexNumber: UInt64 = 0
                if scanner.scanHexInt64(&hexNumber) {
                    r = CGFloat((hexNumber & 0xff000000) >> 24) / 255
                    g = CGFloat((hexNumber & 0x00ff0000) >> 16) / 255
                    b = CGFloat((hexNumber & 0x0000ff00) >> 8) / 255
                    a = CGFloat(hexNumber & 0x000000ff) / 255
                    self.init(red: r, green: g, blue: b, alpha: a)
                    return
                }
            } else if hexColor.count == 6 {
                let scanner = Scanner(string: hexColor)
                var hexNumber: UInt64 = 0
                if scanner.scanHexInt64(&hexNumber) {
                    self.init(rgb: Int(hexNumber))
                    return
                }
            }
        }
        self.init(red: 1, green: 1, blue: 1, alpha: 1)
    }
    
    /// #RRGGBB 0xRRGGBB RRGGBBAA
    /// - Parameters:
    ///   - hexString:
    /// - alpha: 如果颜色值自带透明度即 8位就不要设置透明度，不然会失效
    convenience init(hexString: String, alpha: CGFloat = 1.0) {
          var hexString: String = hexString
          let index = hexString.index(hexString.endIndex, offsetBy: -8)
          let subString = hexString[index...]
        
          if hexString.hasPrefix("#") {
                hexString = String(subString)
          } else if hexString.hasPrefix("0x") {
              hexString = String(subString)
          }
          var rgbHexString: String = ""
          var alphaHexString: String = "FF"
          if hexString.count == 8 {
              let rgbEndIndex = hexString.index(hexString.endIndex, offsetBy: -2)
              rgbHexString = String(hexString[hexString.startIndex..<rgbEndIndex])
              
              let start = hexString.index(hexString.startIndex, offsetBy: 6)
              let end = hexString.index(hexString.startIndex, offsetBy: 7)
              alphaHexString = String(hexString[start...end])
          } else if hexString.count == 6 {
              rgbHexString = hexString
          }
          let rgbHex = Int(rgbHexString, radix: 16) ?? 0
          let alphaHex = Int(alphaHexString, radix: 16) ?? 0
          
          let colorAplha = alpha == 1.0 ? CGFloat(alphaHex) / 255.0 : alpha
          
          self.init(rgb: rgbHex, alpha: colorAplha)
      }
    
    /// 初始化, rbg
    public convenience init(r: CGFloat, g: CGFloat, b: CGFloat, a: CGFloat = 1) {
        self.init(red: r / 255.0, green: g / 255.0, blue: b / 255.0, alpha: a)
    }
    
    public func alpha(_ value: CGFloat) -> UIColor {
           var red: CGFloat = 0
           var green: CGFloat = 0
           var blue: CGFloat = 0
           var alpha: CGFloat = 0
           getRed(&red, green: &green, blue: &blue, alpha: &alpha)
           
           return UIColor(red: red, green: green, blue: blue, alpha: value)
       }
}

extension UIColor: MXBCompatible {}

public struct MXBWrapper<Base> {
    public var base: Base
    public init(_ base: Base) {
        self.base = base
    }
}

public protocol MXBCompatible: Any { }

extension MXBCompatible {
    public var mxb: MXBWrapper<Self> {
        get { return MXBWrapper(self) }
    }
    
    public static var mxb: MXBWrapper<Self>.Type {
        get { return MXBWrapper.self }
    }
}

public typealias MXBColorRGBA = (red: CGFloat, green: CGFloat, blue: CGFloat, alpha: CGFloat)

extension MXBWrapper where Base: UIColor {
    
    public var red: CGFloat {
        return rgbaValue().red
    }
    
    public var green: CGFloat {
        return rgbaValue().green
    }
    
    public var blue: CGFloat {
        return rgbaValue().blue
    }
    
    public var alpha: CGFloat {
        return rgbaValue().alpha
    }
    
    public func rgbaValue() -> MXBColorRGBA {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        base.getRed(&r, green: &g, blue: &b, alpha: &a)
        return (red: r, green: g, blue: b, alpha: a)
    }
}
