//
//  UIFont+WHFont.swift
//
//  Created by <PERSON> on 2021/4/2.
//

import Foundation

extension UIFont {
    
    /// 打印苹果全部字体
    public class func printAllFontName() {
        for familyName in UIFont.familyNames {
            WHPrint(familyName, ":")
            for name in UIFont.fontNames(forFamilyName: familyName) {
                WHPrint("----", name)
            }
        }
    }
    
    /// PingFangSC字体。支持：regular、ultraLight、thin、light、medium、semibold。
    public static func pingFangSCFont(ofSize size: CGFloat,
                               weight: UIFont.Weight = .regular) -> UIFont {
        var name: String?
        switch weight {
        case .regular:
            name = "PingFangSC-Regular"
        case .ultraLight:
            name = "PingFangSC-Ultralight"
        case .thin:
            name = "PingFangSC-Thin"
        case .light:
            name = "PingFangSC-Light"
        case .medium:
            name = "PingFangSC-Medium"
        case .semibold:
            name = "PingFangSC-Semibold"
        default:
            break
        }
        if let name = name,
           let font = UIFont(name: name, size: size) {
            return font
        } else {
            return UIFont.systemFont(ofSize: size, weight: weight)
        }
    }
    
    public static func montserratBoldFont(ofSize size: CGFloat) -> UIFont {
        let fontName = "Montserrat-Bold"
        if let font = UIFont(name: fontName, size: size) {
            return font
        }
        WHFontHelper.dynamicallyLoadFontNamed(fontName,
                                              in: Bundle.resources,
                                              path: "Fonts")
        if let font = UIFont(name: fontName, size: size) {
            return font
        }
        return UIFont.systemFont(ofSize: size)
    }
    
    public static func dinAlterNate(ofSize size: CGFloat) -> UIFont {
        return UIFont(name: "DIN Alternate", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
    public static func dinAlternateBold(ofSize size: CGFloat) -> UIFont {
        return UIFont(name: "DINAlternate-Bold", size: size) ?? UIFont.systemFont(ofSize: size)
    }
    
}
