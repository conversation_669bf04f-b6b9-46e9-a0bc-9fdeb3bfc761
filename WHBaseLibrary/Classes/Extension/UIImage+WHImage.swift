//
//  UIImage+WHImage.swift
//
//  Created by <PERSON> on 2021/2/1.
//

import Foundation

public enum WHGradientColorDirection {
    case leftToRight
    case topToBottom
    case upleftToLowright
    case uprightToLowleft
}



public
extension UIImage {
    
    convenience init?(wh_named name: String) {
        self.init(named: name,
                  in: Bundle.resources,
                  compatibleWith: nil)
    }
    
    @objc
    public static func wh_imageNamed(_ name: String) -> UIImage? {
        return UIImage(wh_named: name)
    }
    
    func compressImageQualityData(toByte maxLength: Int) -> Data? {
        var compression: CGFloat = 1
        guard var data = self.jpegData(compressionQuality: compression) else {
            return nil
        }
        
        if data.count < maxLength {
            return data
        }
        
        var max: CGFloat = 1
        var min: CGFloat = 0
        for _ in 0..<6 {
            compression = (max + min) / 2
            data = self.jpegData(compressionQuality: compression)!
            if CGFloat(data.count) < CGFloat(maxLength) * 0.9 {
                min = compression
            } else if data.count > maxLength {
                max = compression
            } else {
                break
            }
        }
        return data
    }
    
    static func compressImageQuality(_ image: UIImage, toByte maxLength: Int) -> UIImage {
        var compression: CGFloat = 1
        guard var data = image.jpegData(compressionQuality: compression),
            data.count > maxLength else { return image }
        
        var max: CGFloat = 1
        var min: CGFloat = 0
        for _ in 0..<6 {
            compression = (max + min) / 2
            data = image.jpegData(compressionQuality: compression)!
            if CGFloat(data.count) < CGFloat(maxLength) * 0.9 {
                min = compression
            } else if data.count > maxLength {
                max = compression
            } else {
                break
            }
        }
        return UIImage(data: data)!
    }
    
    /// 图像Mask切割
    /// - Parameters:
    ///   - color: 图片颜色
    ///   - size: 图片大小
    static public func maskImageForClip(_ originImage : UIImage, maskImage : UIImage) -> UIImage {
        let orginImage = originImage
        let size = orginImage.size
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        //画入内存
        orginImage.draw(in: CGRect(x: 0, y: 0, width: size.width, height: size.height))
        maskImage.draw(in: CGRect(x: 0, y: 0, width: size.width, height: size.height), blendMode: .destinationIn, alpha: 1)
        let endImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return endImage ?? UIImage()
    }
    
    /// 获取带指定透明度的UIImage
    /// - Parameter alpha: 透明度
    /// - Returns: UIImage?
    public func withAlphaComponent(_ alpha: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        draw(at: .zero, blendMode: .normal, alpha: alpha)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
    
}

extension UIImage {

    /// 生成渐变色的图片
    public convenience init?(gradientColors: [UIColor],
                             locations: [CGFloat]? = nil,
                             size: CGSize = CGSize(width: 10, height: 10),
                             direction: WHGradientColorDirection = .leftToRight) {
        UIGraphicsBeginImageContextWithOptions(size, true, UIScreen.main.scale)
        defer {
            UIGraphicsEndImageContext()
        }
        let context = UIGraphicsGetCurrentContext()
        let colorSpace = CGColorSpaceCreateDeviceRGB();
        let colors = gradientColors.map { $0.cgColor }
        if let gradient = CGGradient(colorsSpace: colorSpace, colors: colors as CFArray, locations: locations) {
            var startPoint, endPoint: CGPoint
            switch direction {
            case .leftToRight:
                startPoint = .zero
                endPoint = CGPoint(x: size.width, y: 0)
            case .topToBottom:
                startPoint = .zero
                endPoint = CGPoint(x: 0, y: size.height)
            case .upleftToLowright:
                startPoint = .zero
                endPoint = CGPoint(x: size.width, y: size.height)
            case .uprightToLowleft:
                startPoint = CGPoint(x: size.width, y: 0)
                endPoint = CGPoint(x: 0, y: size.height)
            }
            context?.drawLinearGradient(gradient,
                                        start: startPoint,
                                        end: endPoint,
                                        options: CGGradientDrawingOptions(rawValue: 0))
        }
        if let cgImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage {
            self.init(cgImage: cgImage)
        } else {
            return nil
        }
    }
    
    /// 将图片和背景色合成为一张图片，可以定制合成后图片的大小以及图片中包含的原始图片大小
    ///  scale 屏幕分辨率 可以UIScreen.main.scale
    public convenience init?(compose image: UIImage?,
                             imageSize: CGSize,
                             bgColor: UIColor,
                             bgSize: CGSize,
                             scale: CGFloat = 1) {
        
        guard let image = image else {
            return nil
        }
        
        UIGraphicsBeginImageContextWithOptions(bgSize, true, scale)
        defer {
            UIGraphicsEndImageContext()
        }
        let context = UIGraphicsGetCurrentContext()
        bgColor.setFill()
        context?.fill(CGRect(x: 0,
                             y: 0,
                             width: bgSize.width,
                             height: bgSize.height))
        let imageRect = CGRect(x: (bgSize.width - imageSize.width) / 2,
                               y: (bgSize.height - imageSize.height) / 2,
                               width: imageSize.width,
                               height: imageSize.height)
        image.draw(in: imageRect)
        if let cgImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage {
            self.init(cgImage: cgImage)
        } else {
            return nil
        }
    }
    
    
    /// 生成纯色图片
    /// - Parameters:
    ///   - color: 图片颜色
    ///   - size: 图片大小
    public convenience init?(color: UIColor,
                             size: CGSize = CGSize(width: 1, height: 1)) {
        UIGraphicsBeginImageContextWithOptions(size, true, UIScreen.main.scale)
        defer {
            UIGraphicsEndImageContext()
        }
        let context = UIGraphicsGetCurrentContext()
        color.setFill()
        context?.fill(CGRect(x: 0,
                             y: 0,
                             width: size.width,
                             height: size.height))
        if let cgImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage {
            self.init(cgImage: cgImage)
        } else {
            return nil
        }
    }

    
}

extension UIImage {
    
    /// 正方形占位图
    /// - Parameters:
    ///   - width: 占位图宽度
    ///   - bgColor: 背景色
    ///   - logoImage: 占位图中间的logo
    /// - Returns: 占位图
    public static func placeholderForSquare(_ width: CGFloat = 96,
                                            bgColor: UIColor = UIColor(rgb: 0xF7F7F8),
                                            logoImage: UIImage) -> UIImage? {
        let imgWidth = width * 13 / 32
        let imgHeight = imgWidth * logoImage.size.height / logoImage.size.width
        return UIImage(compose: logoImage,
                       imageSize: CGSize(width: imgWidth, height: imgHeight),
                       bgColor: bgColor,
                       bgSize: CGSize(width: width, height: width))
    }
    
    /// 长方形占位图
    /// - Parameters:
    ///   - size: 占位图大小
    ///   - heightRatio: logo高度 / 占位图高度
    ///   - bgColor: 背景色
    ///   - logoImage: 占位图中间的logo
    /// - Returns: 占位图
    public static func placeholderForRectangle(size: CGSize,
                                               heightRatio: CGFloat,
                                               bgColor: UIColor = UIColor(rgb: 0xF7F7F8),
                                               logoImage: UIImage) -> UIImage? {
        let imgHeight = size.height * heightRatio
        let imgWidth = imgHeight * logoImage.size.width / logoImage.size.height
        return UIImage(compose: logoImage,
                       imageSize: CGSize(width: imgWidth, height: imgHeight),
                       bgColor: bgColor,
                       bgSize: size)
    }
    
}


