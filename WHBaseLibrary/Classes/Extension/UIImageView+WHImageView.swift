//
//  UIImageView+WHImageView.swift
//
//  Created by <PERSON> on 2022/2/10.
//

import Foundation
import SDWebImage

public
extension UIImageView {
    
    /// 用于之前feed展示的图片作为占位图
    /// placeholderUrlKey: feed缓存图片的key
    /// finalUrl: 最终展示的高清图url
    /// placeholder: 占位图
    /// complete: 回调
    public func wh_setSdImageWithCache(
        placeholderUrlKey: URL?,
        finalUrl: URL?,
        placeholder: UIImage? = nil,
        complete: ((UIImage?, Error?, SDImageCacheType, URL?) -> ())? = nil
    ) {

        var newPlaceholder = placeholder
        if let key = placeholderUrlKey{
            SDWebImageManager.shared.loadImage(
                                           with: key,
                                           options: [],
                                           progress: nil) { image, data, error, cacheType, finished, imageURL in
                                           if let image = image {
                                               newPlaceholder = image
                                               self.image = newPlaceholder
                                           }
                                       }
        }
      
        if let originUrl = finalUrl {
            self.sd_setImage(with: originUrl, placeholderImage: newPlaceholder, completed: complete)
        }else{
            self.image = newPlaceholder
        }
    }
}
