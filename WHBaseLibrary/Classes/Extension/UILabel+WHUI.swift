//
//  UILabel+UI.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/7/19.
//

import Foundation

public enum WHFontType {
    case pingFangSCFontRegular(CGFloat)
    case pingFangSCFontUltralight(CGFloat)
    case pingFangSCFontThin(CGFloat)
    case pingFangSCFontLight(CGFloat)
    case pingFangSCFontMedium(CGFloat)
    case pingFangSCFontSemibold(CGFloat)
    case montserratBoldFont(CGFloat)
    case dinAlterNateFont(CGFloat)
}

extension UILabel {
    
    /// MARK : Label基础配置
    public func wh_configUI(text : String,
                            font : WHFontType,
                            textColor : UIColor = .white,
                            textAlignment : NSTextAlignment = .center,
                            numberOfLines : Int = 0 ) {
        self.text = text
        self.textColor = textColor
        self.textAlignment = textAlignment
        self.numberOfLines = numberOfLines
        switch font {
            case .pingFangSCFontRegular(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .regular)
            case .pingFangSCFontUltralight(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .ultraLight)
            case .pingFangSCFontThin(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .thin)
            case .pingFangSCFontLight(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .light)
            case .pingFangSCFontMedium(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .medium)
            case .pingFangSCFontSemibold(let size):
                self.font = UIFont.pingFangSCFont(ofSize: size, weight: .semibold)
            case .montserratBoldFont(let size):
                self.font = UIFont.montserratBoldFont(ofSize: size)
            case .dinAlterNateFont(let size):
                self.font = UIFont.dinAlterNate(ofSize: size)
        }
        
    }
}

/// 描边label
public class WHOutlineLabel: UILabel {
    
    @objc public var lineWidth: CGFloat = 1.5
    
    @objc public var sideLineAlpha: CGFloat = 0.16
    @objc public var blackSideLine: Bool = true
    /// 跟sideLineAlpha和blackSideLine互斥
    public var strokeColor: UIColor? = nil

    public override func drawText(in rect: CGRect) {
        let textColor = self.textColor
        if lineWidth > 0 && sideLineAlpha > 0, let c = UIGraphicsGetCurrentContext() {
            c.setLineWidth(lineWidth)
            c.setLineJoin(.round)
            c.setTextDrawingMode(.stroke)
            let sideLineColor = strokeColor ?? UIColor(white: blackSideLine ? 0 : 1, alpha: sideLineAlpha)
            self.textColor = sideLineColor
            
            super.drawText(in: rect)
            self.textColor = textColor
            c.setTextDrawingMode(.fill)
        }
        
        super.drawText(in: rect)
    }
}
