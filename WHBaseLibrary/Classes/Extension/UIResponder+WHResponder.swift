//
//  UIResponder+WHResponder.swift
//
//  Created by <PERSON> on 2022/2/10.
//

import Foundation

extension UIResponder {
    
    /// 通过事件响应链传递事件
    /// name: 事件名称
    /// params: 事件参数
    @objc
    public func wh_passEventWith(_ name: String,
                                 params: [String: Any]? = nil) {
        wh_handleEventWith(name, params: params)
        next?.wh_passEventWith(name, params: params)
    }
    
    /// 处理事件。子类可以重写该方法处理事件
    /// name: 事件名称
    /// params: 事件参数
    @objc
    open func wh_handleEventWith(_ name: String,
                                 params: [String: Any]? = nil) {
    }
    
}
