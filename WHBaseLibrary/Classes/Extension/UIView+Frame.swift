//
//  UIView+Frame.swift
//  WHBaseLibrary
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/9/13.
//

import Foundation

extension UIView {
    
    public var x : CGFloat {
        get {
            return frame.origin.x
        }
        set {
            var tempFrame : CGRect = frame
            tempFrame.origin.x = newValue
            frame = tempFrame
        }
    }
    
    public var y : CGFloat {
        get {
            return frame.origin.y
        }
        set {
            var tempFrame : CGRect = frame
            tempFrame.origin.y = newValue
            frame = tempFrame
        }
    }
    
    public var left : CGFloat {
        set {
            var frame = self.frame
            self.frame.origin.x = newValue
            self.frame = frame
        }
        get {
            return frame.origin.x
        }
    }
    
    public var top:CGFloat{
        set{
            var frame = self.frame
            frame.origin.y = newValue
            self.frame = frame
        }
        get{
            return self.frame.origin.y
        }
    }
    
    public var right:CGFloat{
        set{
            var frame = self.frame
            frame.origin.x = newValue - frame.size.width
            self.frame = frame
        }
        get{
            return self.frame.origin.x + self.frame.size.width
        }
    }
    
    public var bottom:CGFloat{
        set{
            var frame = self.frame
            frame.origin.y = newValue - frame.size.height
            self.frame = frame
        }
        get{
            return self.frame.origin.y + self.frame.size.height
        }
    }
    
    public var centerX:CGFloat{
        set{
            self.center = CGPoint(x:newValue, y:self.center.y)
        }
        get{
            return self.center.x
        }
    }
    
    public var centerY:CGFloat{
        set{
            self.center = CGPoint(x:self.center.x, y:newValue)
        }
        get{
            return self.center.y
        }
    }
    
    public var width:CGFloat{
        set{
            var frame = self.frame
            frame.size.width = newValue
            self.frame = frame
        }
        get{
            return self.frame.size.width
        }
    }
    
    public var height:CGFloat{
        set{
            var frame = self.frame
            frame.size.height = newValue
            self.frame = frame
        }
        get{
            return self.frame.size.height
        }
    }
    
    public var origin:CGPoint{
        set{
            var frame = self.frame
            frame.origin = newValue
            self.frame = frame
        }
        get{
            return self.frame.origin
        }
    }
    
    public var size:CGSize{
        set{
            var frame = self.frame
            frame.size = newValue
            self.frame = frame
        }
        get{
            return self.frame.size
        }
    }
    
}

public extension CGRect {
    func wh_center() -> CGPoint {
        let x = (self.maxX - self.minX) / 2 + self.minX
        let y = (self.maxY - self.minY) / 2 + self.minY
        return CGPoint(x: x, y: y)
    }
    
    init(center: CGPoint, size: CGSize) {
        self.init(x: center.x - size.width / 2.0, y: center.y - size.height / 2.0, width: size.width, height: size.height)
    }
    
    public var center: CGPoint {
        get { return CGPoint(x: centerX, y: centerY) }
        
        set {
            centerX = newValue.x
            centerY = newValue.y
        }
    }
    
    public var centerX: CGFloat {
        get { return midX }
        set { origin.x = newValue - width * 0.5 }
    }
    
    public var centerY: CGFloat {
        get { return midY }
        set { origin.y = newValue - height * 0.5 }
    }
}

infix operator ~~= : AssignmentPrecedence

public func ~~= <T: BinaryFloatingPoint>(left: inout T, right: T) {
    if fabsf(Float(left - right)) > 0.001 {
        left = right
    }
}

public func ~~=(left: inout CGRect, right: CGRect) {
    left.origin ~~= right.origin
    left.size ~~= right.size
}

public func ~~=(left: inout CGPoint, right: CGPoint) {
    left.x ~~= right.x
    left.y ~~= right.y
}

public func ~~=(left: inout CGSize, right: CGSize) {
    left.width ~~= right.width
    left.height ~~= right.height
}
