//
//  UIView+WHClipCorner.swift
//  WHBaseLibrary
//
//  Created by xiaoqi on 2022/12/13.
//

import Foundation

public struct WHCornerRadii {
    public let topLeft : CGFloat
    public let topRight : CGFloat
    public let bottomLeft : CGFloat
    public let bottomRight : CGFloat
    
    public init(topLeft: CGFloat, topRight: CGFloat, bottomLeft: CGFloat, bottomRight: CGFloat) {
        self.topLeft = topLeft
        self.topRight = topRight
        self.bottomLeft = bottomLeft
        self.bottomRight = bottomRight
    }
}

extension UIView {
    
    // 切圆角
    public func clipCorner(corners: UIRectCorner,
                           size: CGSize = .zero,
                           radius: CGFloat) {
        layoutIfNeeded()
        var rect = self.bounds
        if size != .zero {
            rect.size = size
        }
        
        let maskPath = UIBezierPath(roundedRect: rect,
                                    byRoundingCorners: corners,
                                    cornerRadii: CGSize(width: radius, height: radius))
        let maskLayer = CAShapeLayer()
        maskLayer.frame = rect
        maskLayer.path = maskPath.cgPath
        self.layer.mask = maskLayer
    }
    
    
    public func wh_applyMaskRound(withRect rect: CGRect, cornerRadii: WHCornerRadii) {
        let shapeLayer = CAShapeLayer()
        let path = wh_pathCreateWithRoundedRect(rect, cornerRadii)
        shapeLayer.path = path
        self.layer.mask = shapeLayer
    }
    
    
    func wh_pathCreateWithRoundedRect(_ bounds: CGRect, _ cornerRadii: WHCornerRadii) -> CGPath {
        let minX = bounds.minX
        let minY = bounds.minY
        let maxX = bounds.maxX
        let maxY = bounds.maxY
        
        let topLeftCenterX = minX + cornerRadii.topLeft
        let topLeftCenterY = minY + cornerRadii.topLeft
        
        let topRightCenterX = maxX - cornerRadii.topRight
        let topRightCenterY = minY + cornerRadii.topRight
        
        let bottomLeftCenterX = minX + cornerRadii.bottomLeft
        let bottomLeftCenterY = maxY - cornerRadii.bottomLeft
        
        let bottomRightCenterX = maxX - cornerRadii.bottomRight
        let bottomRightCenterY = maxY - cornerRadii.bottomRight
        
        let path = CGMutablePath()
        path.addArc(center: CGPoint(x: topLeftCenterX, y: topLeftCenterY),
                    radius: cornerRadii.topLeft,
                    startAngle: CGFloat.pi,
                    endAngle: 3 * CGFloat.pi / 2,
                    clockwise: false)
        
        path.addArc(center: CGPoint(x: topRightCenterX, y: topRightCenterY),
                    radius: cornerRadii.topRight,
                    startAngle: 3 * CGFloat.pi / 2,
                    endAngle: 0,
                    clockwise: false)
        
        path.addArc(center: CGPoint(x: bottomRightCenterX, y: bottomRightCenterY),
                    radius: cornerRadii.bottomRight,
                    startAngle: 0,
                    endAngle: CGFloat.pi / 2,
                    clockwise: false)
        
        path.addArc(center: CGPoint(x: bottomLeftCenterX, y: bottomLeftCenterY),
                    radius: cornerRadii.bottomLeft,
                    startAngle: CGFloat.pi / 2,
                    endAngle: CGFloat.pi,
                    clockwise: false)
        
        path.closeSubpath()
        
        return path
    }

    
}

public extension UIView {
    
    func addSubviews(_ subviews: [UIView]) {
        let processed = subviews.filterDuplicates({$0})
        processed.forEach { (sv) in
            self.addSubview(sv)
        }
    }
}


