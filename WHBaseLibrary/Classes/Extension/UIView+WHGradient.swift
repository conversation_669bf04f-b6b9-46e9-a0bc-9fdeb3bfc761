//
//  UIView+WHGradient.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2022/12/14.
//

import UIKit

//private var isExchaged = false
private var wh_gradientLayerKey: UInt8 = 0

extension UIView {
    
    // MARK: - Public
    public var wh_gradientLayer: CAGradientLayer? {
        set {
            objc_setAssociatedObject(self, &wh_gradientLayerKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            let layer = objc_getAssociatedObject(self, &wh_gradientLayerKey) as? CAGradientLayer
            return layer
        }
    }
    
    
    public func wh_enableGradient(points: (start: CGPoint, end: CGPoint),
                                  colors: [UIColor],
                                  locations: [NSNumber]) {
        DispatchQueue.main.async { [weak self] in
            self?.enableGradient(points: points,
                                 colors: colors,
                                 locations: locations)
        }
    }
    
    // MARK: - Private
    private func enableGradient(points: (start: CGPoint, end: CGPoint),
                                colors: [UIColor],
                                locations: [NSNumber]) {
        wh_gradientLayer?.removeFromSuperlayer()
        let gradientLayer = CAGradientLayer()
        gradientLayer.startPoint = points.start
        gradientLayer.endPoint = points.end
        var cgColors: [CGColor] = []
        for color in colors {
            cgColors.append(color.cgColor)
        }
        gradientLayer.colors = cgColors
        gradientLayer.locations = locations
        gradientLayer.frame = bounds
        layer.insertSublayer(gradientLayer, at: 0)
        wh_gradientLayer = gradientLayer
    }
    
//    private func exchangeLayoutSubviewsMethod() {
//        if !isExchaged {
//            let originalSEL: Selector = #selector(layoutSubviews)
//            let newSEL: Selector = #selector(WHlayoutSubviews)
//            if let originalMethod = class_getInstanceMethod(UIView.self, originalSEL),
//               let newMethod = class_getInstanceMethod(UIView.self, newSEL) {
//                let isAddMethod = class_addMethod(UIView.self, originalSEL, method_getImplementation(newMethod), method_getTypeEncoding(newMethod))
//                if isAddMethod {
//                    class_replaceMethod(UIView.self, newSEL, method_getImplementation(originalMethod), method_getTypeEncoding(originalMethod))
//                } else {
//                    method_exchangeImplementations(originalMethod, newMethod)
//                }
//                isExchaged = true
//            }
//        }
//    }
    
//    @objc func WHlayoutSubviews() {
//        WHlayoutSubviews()
//        if Thread.isMainThread, let gradientLayer = wh_gradientLayer {
//            CATransaction.begin()
//            CATransaction.setDisableActions(true)
//            gradientLayer.frame = bounds
//            CATransaction.commit()
//        }
//    }
    
}
