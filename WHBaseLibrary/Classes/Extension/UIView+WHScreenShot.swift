//
//  UIView+WHScreenShot.swift
//
//  Created by <PERSON> on 2022/3/9.
//

import Foundation

public extension UIView {
    
    func wh_snapshotImage(toAlbum save: Bool = false) -> UIImage {
        let format = UIGraphicsImageRendererFormat()
        format.scale = UIScreen.main.scale
        let renderer = UIGraphicsImageRenderer(bounds: bounds, format: format)
        let image = renderer.image { ctx in
            self.drawHierarchy(in: self.bounds,
                               afterScreenUpdates: true)
        }
        if save {
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        }
        return image
    }
    
    var currentFirstResponder: UIView? {
        guard !isFirstResponder else { return self }
        
        for subview in subviews {
            if let firstResponder = subview.currentFirstResponder {
                return firstResponder
            }
        }
        return nil
    }
}

extension UIScrollView {
    
    /// scrollView内容快照（支持scrollView用frame布局，layout布局截取不到视图以外的内容）
    /// - Parameter save: 是否保存到相册
    /// - Returns: image对象
    func wh_snapshotContentImage(toAlbum save: Bool = false) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.contentSize,
                                               false,
                                               UIScreen.main.scale)
        
        var savedFrame: CGRect?
        if self.frame.width < self.contentSize.width ||
            self.frame.height < self.contentSize.height {
            savedFrame = self.frame
            self.frame = CGRect(x: 0,
                                y: 0,
                                width: self.contentSize.width,
                                height: self.contentSize.height)
        }
        
        // 此方法截取的位置有问题，后期可研究一下
//        self.drawHierarchy(in: CGRect(origin: .zero, size: self.contentSize),
//                           afterScreenUpdates: true)
        if let context = UIGraphicsGetCurrentContext() {
            self.layer.render(in: context)
        }
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        if let frame = savedFrame {
            self.frame = frame
        }
        
        if save,
           let aImage = image {
            UIImageWriteToSavedPhotosAlbum(aImage, nil, nil, nil)
        }
        
        return image
    }
    
}

// MARK: - 获取 `instance` / `Class` 类名字符串
public extension NSObject {
    ///  类名字符串 for `instance`
    @objc var className: String {
        let name = type(of: self).description()
        return NSObject.absoluteClassName(name)
    }
    
    ///  类名字符串 for `Class`
    @objc static var className: String {
        let name = String(describing: self)
        return NSObject.absoluteClassName(name)
    }
    
    private static func absoluteClassName(_ full: String) -> String {
        return full.components(separatedBy: ".").last ?? ""
    }
    
}
