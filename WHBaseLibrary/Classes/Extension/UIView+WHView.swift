//
//  UIView+WHView.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/4/23.
//

import Foundation

extension UIView {
    
    /// 查找最近的满足条件的父视图
    public func closestAncestor(matching: (UIView) -> Bool) -> UIView? {
        var view: UIView? = self
        while let current = view {
            if matching(current) {
                return current
            }
            view = current.superview
        }
        return nil
    }
    
}

fileprivate let rotationKey = "rotationAnimation"

extension UIView {
    
    /// 开始旋转动画
    public func startRotating(duration: CFTimeInterval = 1.0) {
        if layer.animation(forKey: rotationKey) != nil { return }

        let rotation = CABasicAnimation(keyPath: "transform.rotation.z")
        rotation.fromValue = 0
        rotation.toValue = CGFloat.pi * 2
        rotation.duration = duration
        rotation.repeatCount = .infinity
        rotation.isRemovedOnCompletion = false

        layer.add(rotation, forKey: rotationKey)
    }

    /// 停止旋转动画
    public func stopRotating() {
        if layer.animation(forKey: rotationKey) == nil {
            return
        }
        layer.removeAnimation(forKey: rotationKey)
    }
    
}
