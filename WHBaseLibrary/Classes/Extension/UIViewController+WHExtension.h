

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIViewController (WHExtension)

/// 获取window上的顶层控制器
/// @note 参数如为nil，依次获取UIApplication的delegate.window->keyWindow
+ (UIViewController *)wh_topViewControllerOnWindow:(UIWindow * _Nullable)window;

/// 获取App当前顶层控制器，内部调用wh_topViewControllerOnWindow:方法，参数为nil
+ (UIViewController *)wh_topViewController;

/// 获取controller之上的顶层控制器，一般controller为容器对象
+ (UIViewController *)wh_topViewControllerWithViewController:(UIViewController *)controller;

/**
 关闭当前控制器之上present出来的所有页面。注意此方法并不关闭当前控制器
 @param flag 是否动画
 @param completion 完成后的回调
 */
- (void)wh_dismissAllPresentedControllerOnSelfWithAnimated:(BOOL)flag completion:(void(^ _Nullable)(void))completion;

/**
 在当前页面 push 或 present 一个页面，优先 push
 */
- (void)wh_showViewController:(UIViewController *)vc;

/**
 在当前页面 上一层VC
 */
- (nullable UIViewController *)previousViewController;

@end

NS_ASSUME_NONNULL_END


#if defined __cplusplus
extern "C" {
#endif
    
    /**
     获取当前显示的控制器
     
     @return 控制器
     */
    UIViewController *WHCurrentTopViewController();
    
#if defined __cplusplus
};
#endif
