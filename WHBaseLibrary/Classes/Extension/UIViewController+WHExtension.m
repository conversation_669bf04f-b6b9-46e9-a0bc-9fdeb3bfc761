

#import "UIViewController+WHExtension.h"

@implementation UIViewController (WHExtension)

+ (UIViewController *)wh_topViewControllerOnWindow:(UIWindow *)window
{
    if (!window) {
        if ([[UIApplication sharedApplication].delegate respondsToSelector:@selector(window)]) {
            window = [UIApplication sharedApplication].delegate.window;
        }
    }
    if (!window) {
        window = [UIApplication sharedApplication].keyWindow;
    }
    UIViewController *rootVC = [window rootViewController];
    return [self wh_topViewControllerWithViewController:rootVC];
}

+ (UIViewController *)wh_topViewController
{
    return [self wh_topViewControllerOnWindow:nil];
}

+ (UIViewController *)wh_topViewControllerWithViewController:(UIViewController *)controller
{
    if ([controller isKindOfClass:[UITabBarController class]]) {
        UITabBarController *tabBarController = (UITabBarController *)controller;
        return [self wh_topViewControllerWithViewController:tabBarController.selectedViewController];
    } else if ([controller isKindOfClass:[UINavigationController class]]) {
        UINavigationController *navController = (UINavigationController *)controller;
        return [self wh_topViewControllerWithViewController:navController.topViewController];
    } else if (controller.presentedViewController && !controller.presentedViewController.isBeingDismissed) {
        UIViewController *presentedController = controller.presentedViewController;
        return [self wh_topViewControllerWithViewController:presentedController];
    } else if (controller.childViewControllers.lastObject) {
        UIViewController *lastVC = controller.childViewControllers.lastObject;
        return [self wh_topViewControllerWithViewController:lastVC];
    } else {
        return controller;
    }
}

- (void)wh_dismissAllPresentedControllerOnSelfWithAnimated:(BOOL)flag completion:(void (^)(void))completion
{
    if (!self.presentedViewController) {
        if (completion) {
            completion();
        }
    } else {
        [self.presentedViewController wh_dismissAllPresentedControllerOnSelfWithAnimated:flag completion:^{
            [self.presentedViewController dismissViewControllerAnimated:flag completion:completion];
        }];
    }
}

- (void)wh_showViewController:(UIViewController *)vc
{
    if (![vc isKindOfClass:[UIViewController class]])
    {
        return;
    }
    if ([self isKindOfClass:[UINavigationController class]])
    {
        UINavigationController *nav = (UINavigationController *)self;
        vc.hidesBottomBarWhenPushed = YES;
        [nav pushViewController:vc animated:YES];
    } else
    {
        if (self.navigationController)
        {
            vc.hidesBottomBarWhenPushed = YES;
            [self.navigationController pushViewController:vc animated:YES];
        } else
        {
            [self presentViewController:vc animated:YES completion:nil];
        }
    }
}

- (nullable UIViewController *)previousViewController {
    // 1. push 情况
    if (self.navigationController) {
        NSArray<UIViewController *> *vcs = self.navigationController.viewControllers;
        NSUInteger index = [vcs indexOfObject:self];
        if (index != NSNotFound && index > 0) {
            return vcs[index - 1];
        }
    }
    
    // 2. present 情况
    UIViewController *presenting = self.presentingViewController;
    if (presenting) {
        if ([presenting isKindOfClass:[UINavigationController class]]) {
            UINavigationController *nav = (UINavigationController *)presenting;
            return nav.viewControllers.lastObject ?: nav;
        }
        return presenting;
    }
    
    // 3. 没有找到
    return nil;
}
@end


UIViewController *WHCurrentTopViewController() {
    UIViewController *topVC = [[[[UIApplication sharedApplication] delegate] window] rootViewController];
    while (topVC.presentedViewController) {
        topVC = topVC.presentedViewController;
    }
    return topVC;
}
