//
//  WHFontHelper.m
//
//  Created by <PERSON> on 2022/3/8.
//

#import "WHFontHelper.h"
#import <CoreText/CTFontManager.h>

@implementation WHFontHelper

+ (void)dynamicallyLoadFontNamed:(NSString *)name
                        inBundle:(NSBundle *)bundle
                            path:(NSString * __nullable)path {
    NSString *fileName = name;
    if (path.length > 0) {
        if ([path hasSuffix:@"/"]) {
            fileName = [NSString stringWithFormat:@"%@%@", path, fileName];
        } else {
            fileName = [NSString stringWithFormat:@"%@/%@", path, fileName];
        }
    }
    NSURL *url = [bundle URLForResource:fileName withExtension:@"ttf"];
    NSData *fontData = [NSData dataWithContentsOfURL:url];
    if (fontData) {
        CFErrorRef error;
        CGDataProviderRef provider = CGDataProviderCreateWithCFData((CFDataRef)fontData);
        CGFontRef font = CGFontCreateWithDataProvider(provider);
        if (CTFontManagerRegisterGraphicsFont(font, &error) == NO) {
            CFStringRef errorDescription = CFErrorCopyDescription(error);
            NSLog(@"WHBaseLibraryLog:: Failed to load font => %@", errorDescription);
            CFRelease(errorDescription);
        }
        CFRelease(font);
        CFRelease(provider);
    }
}

@end
