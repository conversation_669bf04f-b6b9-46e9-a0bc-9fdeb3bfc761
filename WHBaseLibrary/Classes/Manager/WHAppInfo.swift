//
//  WHAppInfo.swift
//
//  Created by <PERSON> on 2022/5/18.
//

import Foundation

@objcMembers
public class WHAppInfo: NSObject {

    /// app信息字典
    public static let appInfo: [String: Any]? = Bundle.main.infoDictionary
    
    /// app版本号
    public static var appVersion: String = {
        if let version = appInfo?["CFBundleShortVersionString"] as? String {
            return version
        }
        return ""
    } ()
    
    /// bundle版本号
    public static var bundleVersion: String = {
        if let version = appInfo?["CFBundleVersion"] as? String {
            return version
        }
        return ""
    } ()
    
    /// bundle id
    public static var bundleID: String = {
        if let id = appInfo?["CFBundleIdentifier"] as? String {
            return id
        }
        return ""
    } ()
    
    /// bundle name
    public static var bundleName: String = {
        if let name = appInfo?["CFBundleName"] as? String {
            return name
        }
        return ""
    } ()
    
    public static var channelID: String = {
        let channelId = "App Store"
        return channelId
    } ()
    
    /// 是否是安装后首次启动
    public static var isAppNewInstalled: Bool = false
    /// 是否覆盖安装上来的
    public static var isVersionCoverUpgrade: Bool = false
    
    public static func updateFlagAtDidFinishLaunching() {
        let currentAppVersion = appVersion
        let currentVersionKey: String = "kMTVersionStash"
        
        let previousVersion = UserDefaults.standard.string(forKey: currentVersionKey) ?? ""
        if previousVersion.count == 0 {
            isAppNewInstalled = true
            isVersionCoverUpgrade = false
            
            UserDefaults.standard.setValue(currentAppVersion, forKey: currentVersionKey)
            UserDefaults.standard.synchronize()
        } else if previousVersion == currentAppVersion {
            isAppNewInstalled = false
            isVersionCoverUpgrade = false
        } else {
            isAppNewInstalled = false
            isVersionCoverUpgrade = true
            
            UserDefaults.standard.setValue(currentAppVersion, forKey: currentVersionKey)
            UserDefaults.standard.synchronize()
        }
    }
        
    /// 比较版本号，如果传入的 version 更高，返回 true，否则 false
    /// 支持任意位数的版本号，例如 "1.2" 与 "1.2.0.1"
    public static func compareVersion(version: String) -> Bool {
        let currentVersion = WHAppInfo.appVersion
        let newArray = version.split(separator: ".").map { Int($0) ?? 0 }
        let nowArray = currentVersion.split(separator: ".").map { Int($0) ?? 0 }
        let maxCount = max(newArray.count, nowArray.count)

        for i in 0..<maxCount {
            let new = i < newArray.count ? newArray[i] : 0
            let now = i < nowArray.count ? nowArray[i] : 0
            if new > now {
                return true
            } else if new < now {
                return false
            }
        }
        return false // 相等时返回 false，表示不需要更新
    }
}
