//
//  WHFileManager.swift
//
//  Created by <PERSON> on 2021/5/12.
//

import Foundation

public class WHFileManager {
    
    /// 移动文件
    /// - Parameters:
    ///   - srcPath: 源路径
    ///   - dstPath: 目标路径
    ///   - overwrite: 目标路径存在同名文件时，是否覆盖
    /// - Returns: 是否移动成功
    public static func moveItem(atPath srcPath: String,
                                toPath dstPath: String,
                                overwrite: Bool) -> Bool {
        guard fileExists(atPath: srcPath) else {
            WHPrint("源文件不存在：\(srcPath)")
            return false
        }
        
        let toDirPath = directory(atPath: dstPath)
        if createDirectory(atPath: toDirPath) == false {
            return false
        }
        
        if fileExists(atPath: dstPath) {
            // 目标文件存在
            if overwrite {
                // 覆盖的话，删除原有的目标文件
                do {
                    try FileManager.default.removeItem(atPath: dstPath)
                } catch let error as NSError {
                    WHPrint("原有的目标文件删除失败：\(error.localizedDescription)")
                    return false
                }
            } else {
                // 不覆盖的话，停止移动
                return false
            }
        }
        
        do {
            try FileManager.default.moveItem(atPath: srcPath, toPath: dstPath)
            return true
        } catch let error as NSError {
            WHPrint("文件移动失败：\(error.localizedDescription)")
            return false
        }
    }
    
    public static func removeItem(atPath path: String) -> Bool {
        if fileExists(atPath: path) {
            do {
                try FileManager.default.removeItem(atPath: path)
            } catch let error as NSError {
                WHPrint("目标文件夹删除失败：\(error.localizedDescription)")
                return false
            }
        }
        return true
    }
    
    /// 创建文件夹
    /// - Parameter path: 路径
    /// - Returns: <#description#>
    public static func createDirectory(atPath path: String) -> Bool {
        guard fileExists(atPath: path) == false else {
            return true
        }
        do {
            try FileManager.default.createDirectory(atPath: path,
                                                    withIntermediateDirectories: true,
                                                    attributes: nil)
        } catch let error as NSError {
            WHPrint("目标文件夹创建失败：\(error.localizedDescription)")
            return false
        }
        WHPrint("目标文件夹创建成功：\(path)")
        return true
    }
    
    /// 设置目标目录不进行icloud备份
    /// - Parameter path: <#path description#>
    /// - Returns: <#description#>
    public static func excludedFromBackup(atPath path: String) -> Bool {
        guard fileExists(atPath: path) else {
            return false
        }
        var url = URL(fileURLWithPath: path)
        do {
            var resourceValues = URLResourceValues()
            resourceValues.isExcludedFromBackup = true
            try url.setResourceValues(resourceValues)
        } catch let error as NSError {
            WHPrint("设置目标目录不进行icloud备份失败：\(error.localizedDescription)")
            return false
        }
        WHPrint("设置目标目录不进行icloud备份成功：\(path)")
        return true
    }
    
    /// 文件所在的文件夹路径
    /// - Parameter path: 文件路径
    /// - Returns: 文件夹路径
    public static func directory(atPath path: String) -> String {
        var url = URL(fileURLWithPath: path)
        url.deleteLastPathComponent()
        return url.path
    }
    
    /// 判断文件是否存在
    /// - Parameter atPath: 文件路径
    /// - Returns: 是否存在
    public static func fileExists(atPath path: String) -> Bool {
        return FileManager.default.fileExists(atPath: path)
    }
    
}
