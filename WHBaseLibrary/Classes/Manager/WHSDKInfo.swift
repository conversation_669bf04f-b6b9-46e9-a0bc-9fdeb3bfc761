//
//  WHSDKInfo.swift
//
//  Created by <PERSON> on 2022/5/18.
//

import Foundation

@objcMembers
public class WHSDKInfo: NSObject {
    /// SDK信息字典
//    public static let sdkInfo: [String: Any]? = Bundle.resources.infoDictionary
//
//    /// SDK版本
//    public static var sdkVersion: String = {
//        if let version = sdkInfo?["CFBundleShortVersionString"] as? String {
//            return version
//        }
//        return ""
//    } ()
    
}
