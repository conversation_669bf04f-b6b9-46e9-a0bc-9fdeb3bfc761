//
//  WHStateHistoryManager.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/6/27.
//

import Foundation

public class WHStateHistoryManager<T> {
    
    private var undoStack = WHStack<T>()
    private var redoStack = WHStack<T>()
    
    /// 当前状态（栈顶）
    public var current: T? {
        return undoStack.top
    }
    
    /// 上一个状态
    public var previous: T? {
        return undoStack.subTop
    }
    
    /// 是否可以 undo
    public var canUndo: Bool {
        return undoStack.length > 1
    }
    
    /// 是否可以 redo
    public var canRedo: Bool {
        return !redoStack.isEmpty
    }
    
    public init() {}
    
    /// 添加一个新状态
    public func add(_ state: T) {
        undoStack.push(state)
        redoStack.clear()
    }
    
    /// 撤销上一个状态
    @discardableResult
    public func undo() -> T? {
        guard canUndo,
              let undoState = undoStack.pop() else {
            return current
        }
        redoStack.push(undoState)
        return current
    }
    
    /// 重做上一个撤销的状态
    @discardableResult
    public func redo() -> T? {
        guard canRedo,
              let redoState = redoStack.pop() else {
            return current
        }
        undoStack.push(redoState)
        return current
    }
    
}
