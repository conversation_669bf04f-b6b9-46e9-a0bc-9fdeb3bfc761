//
//  WHBaseRequest.swift
//
//  Created by <PERSON> on 2022/2/7.
//

import Foundation
import Alamofire
import ObjectMapper

open class WHBaseRequest: RequestInterceptor {
    
    private(set) var request: Request?
    
    public required init() {}
    
    /// 网络请求baseUrl
    open func baseURL() -> String {
        return ""
    }
    
    /// 网络请求通用参数
    open func commonParams() -> [String: Any] {
        return [:]
    }
    
    /// 请求头
    open func requestHeaders() -> [String: String] {
        return [:]
    }
    
    /// 网络请求签名ID
    open func sigAppId() -> String {
        return ""
    }
    /// 数据返回Response的回调
    open var receiveResponseBlock: ((WHOriginalResponse) -> Void)?
    
    /// get请求，获取原始的Json数据
    /// dataParsingQueue: 数据解析的队列
    open func GET(_ urlString: String,
                  params: [String: Any]? = nil,
                  dataParsingQueue: DispatchQueue = .main,
                  completionHandler: @escaping (WHOriginalResponse) -> Void) {
        AF.request(baseURL() + urlString,
                   method: .get,
                   parameters: configParameters(urlString, parameters: params),
                   interceptor: self)
        .responseJSON { (response) in
            dataParsingQueue.async {
                self.parseResponse(response,
                                   completionHandler: completionHandler)
            }
        }
    }
    
    /// get请求，同时解析成目标数据模型
    /// dataParsingQueue: 数据解析的队列
    open func GET<T: Mappable>(_ urlString: String,
                               params: [String: Any]? = nil,
                               dataParsingQueue: DispatchQueue = .main,
                               parseKeyPath: WHKeyPath = WHKeyPath("data"),
                               completionHandler: @escaping (WHResponse<T>) -> Void) {
        self.GET(urlString,
                 params: params,
                 dataParsingQueue: dataParsingQueue) { (response) in
            dataParsingQueue.async {
                self.parseResponse(response,
                                   parseKeyPath: parseKeyPath,
                                   completionHandler: completionHandler)
            }
        }
    }
    
    /// post请求，获取原始的Json数据
    /// dataParsingQueue: 数据解析的队列
    open func POST(_ urlString: String,
                   params: [String: Any]? = nil,
                   headers: HTTPHeaders? = nil,
                   dataParsingQueue: DispatchQueue = .main,
                   completionHandler: @escaping (WHOriginalResponse) -> Void) {
        AF.request(baseURL() + urlString,
                   method: .post,
                   parameters: configParameters(urlString, parameters: params),
                   headers: headers,
                   interceptor: self)
        .responseJSON { (response) in
            dataParsingQueue.async {
                self.parseResponse(response,
                                   completionHandler: completionHandler)
            }
        }
    }
    
    /// post请求，同时解析成目标数据模型
    /// dataParsingQueue: 数据解析的队列
    open func POSTT<T: Mappable>(_ urlString: String,
                                params: [String: Any]? = nil,
                                dataParsingQueue: DispatchQueue = .main,
                                parseKeyPath: WHKeyPath = WHKeyPath("data"),
                                completionHandler: @escaping (WHResponse<T>) -> Void) {
        self.POST(urlString,
                  params: params,
                  dataParsingQueue: dataParsingQueue) { (response) in
            dataParsingQueue.async {
                self.parseResponse(response,
                                   parseKeyPath: parseKeyPath,
                                   completionHandler: completionHandler)
            }
        }
    }
    
    /// put请求，获取原始的Json数据
    /// dataParsingQueue: 数据解析的队列
    open func PUT(_ urlString: String,
                  params: [String: Any]? = nil,
                  dataParsingQueue: DispatchQueue = .main,
                  completionHandler: @escaping (WHOriginalResponse) -> Void) {
        AF.request(baseURL() + urlString,
                   method: .post,
                   parameters: configParameters(urlString, parameters: params),
                   interceptor: self)
        .responseJSON { (response: AFDataResponse<Any>) in
            dataParsingQueue.async {
                self.parseResponse(response,
                                   completionHandler: completionHandler)
            }
        }
    }
    
    /// 下载数据
    /// - Parameters:
    ///   - url: 下载URL
    ///   - path: 保存路径
    ///   - progress: 下载进度
    ///   - completion: 完成回调
    /// - Returns: 请求实例
    @discardableResult
    public static func downloadData(with url: String,
                                    saveTo path: String,
                                    progress: ((Int64, Int64) -> ())? = nil,
                                    completion: @escaping (Bool) -> ()) -> Self {
        let request = Self.init()
        request.request = AF.download(url) { (url: URL, response: HTTPURLResponse) -> (destinationURL: URL, options: DownloadRequest.Options) in
            return (URL(fileURLWithPath: path), [.createIntermediateDirectories, .removePreviousFile])
        }.downloadProgress { (p: Progress) in
            WHPrint("propress: \(Double(p.completedUnitCount) / Double(p.totalUnitCount))")
            progress?(p.completedUnitCount, p.totalUnitCount)
        }.response { (response: AFDownloadResponse<URL?>) in
            completion(response.error == nil)
        }
        return request
    }
    
    @discardableResult
    public static func downloadData(with url: String,
                                    saveTo path: String,
                                    progress: @escaping (Int64, Int64) -> (),
                                    cancel: @escaping () -> (),
                                    success: @escaping () -> (),
                                    failure: @escaping (Error) -> ()) -> Self {
        let request = Self.init()
        request.request = AF.download(url) { (url: URL, response: HTTPURLResponse) -> (destinationURL: URL, options: DownloadRequest.Options) in
            return (URL(fileURLWithPath: path), [.createIntermediateDirectories, .removePreviousFile])
        }.downloadProgress { (p: Progress) in
            progress(p.completedUnitCount, p.totalUnitCount)
        }.response { (response: AFDownloadResponse<URL?>) in
            if let error = response.error {
                if let afError = error.asAFError,
                   afError.isExplicitlyCancelledError {
                    WHPrint(#function, "cancel")
                    cancel()
                } else {
                    failure(error)
                }
            } else {
                success()
            }
        }
        return request
    }
    
    @discardableResult
    public static func downloadResuming(with data: Data,
                                        saveTo path: String,
                                        progress: @escaping (Int64, Int64) -> (),
                                        cancel: @escaping () -> (),
                                        success: @escaping () -> (),
                                        failure: @escaping (Error) -> ()) -> Self {
        let request = Self.init()
        request.request = AF.download(resumingWith: data) { (url: URL, response: HTTPURLResponse) -> (destinationURL: URL, options: DownloadRequest.Options) in
            return (URL(fileURLWithPath: path), [.createIntermediateDirectories, .removePreviousFile])
        }.downloadProgress { (p: Progress) in
            progress(p.completedUnitCount, p.totalUnitCount)
        }.response { (response: AFDownloadResponse<URL?>) in
            if let error = response.error {
                if let afError = error.asAFError,
                   afError.isExplicitlyCancelledError {
                    WHPrint(#function, "cancel")
                    cancel()
                } else {
                    failure(error)
                }
            } else {
                success()
            }
        }
        return request
    }
    
    /// 取消网络请求
    public func cancel() {
        request?.cancel()
    }
    
    public func downloadCancel(byProducingResumeData: @escaping (Data?) -> ()) {
        guard let request = request as? DownloadRequest else {
            byProducingResumeData(nil)
            return
        }
        request.cancel(byProducingResumeData: byProducingResumeData)
    }
    
    // MARK: - private methods
    /// 配置请求参数，设置通参
    private func configParameters(_ urlPath: String,
                                  parameters: [String: Any]? = nil) -> [String: Any]? {
        
        // 通用参数
        var params = commonParams()
        // 合并通用参数
        if let dict = parameters {
            params.merge(dict) { (current, new) in new }
        }
        // 合并加签参数
        if let dict = WHSignParamter.signParams(withUrlPath: urlPath, params: params, appId: sigAppId()) as? [String: Any] {
            params.merge(dict) { (current, new) in new }
            params.removeValue(forKey: "Access-Token")
        }
        return params
    }
    
    /// 解析response原始数据
    private func parseResponse(_ response: AFDataResponse<Any>,
                               completionHandler: @escaping (WHOriginalResponse) -> Void) {
        var resultResponse: WHOriginalResponse

        if let error = response.error as? AFError {
            resultResponse = WHOriginalResponse(response: response.response,
                                                error: error)
        } else {
            resultResponse = WHOriginalResponse(response: response.response,
                                                value: response.value)
        }
        
        if Thread.current == Thread.main {
            completionHandler(resultResponse)
            receiveResponseBlock?(resultResponse)
        } else {
            DispatchQueue.main.async {
                completionHandler(resultResponse)
                self.receiveResponseBlock?(resultResponse)
            }
        }
    }
    
    private func parseResponse<T: Mappable>(_ response: WHOriginalResponse,
                                            parseKeyPath: WHKeyPath,
                                            completionHandler: @escaping (WHResponse<T>) -> Void) {
        
        let resultResponse = WHResponse<T>(response: response.response,
                                           error: response.error,
                                           value: response.value,
                                           designatedPath: parseKeyPath)
        if Thread.current == Thread.main {
            completionHandler(resultResponse)
            receiveResponseBlock?(response)
        } else {
            DispatchQueue.main.async {
                completionHandler(resultResponse)
                self.receiveResponseBlock?(response)
            }
        }
    }
    
    // MARK: - RequestInterceptor
    public func adapt(_ urlRequest: URLRequest, for session: Session, completion: @escaping (Result<URLRequest, Error>) -> Void) {
        
        var urlRequest = urlRequest
        
        self.requestHeaders().forEach { (arg0) in
            urlRequest.headers.add(name: arg0.key, value: arg0.value)
        }

        completion(.success(urlRequest))
    }
    
}
