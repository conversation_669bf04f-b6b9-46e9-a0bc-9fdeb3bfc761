//
//  WHKeyPath.swift
//
//  Created by <PERSON> on 2022/2/8.
//

import Foundation

public struct WHKeyPath {

    public var segments: [String]

    public var isEmpty: Bool {
        segments.isEmpty
    }

    public var path: String {
        segments.joined(separator: ".")
    }

    public func headAndTailKeyPath() -> (head: String, tailKeyPath: WHKeyPath)? {
        guard isEmpty == false else {
            return nil
        }

        var tail = segments
        let head = tail.removeFirst()
        return (head, WHKeyPath(segments: tail))
    }
    
    public func headKeyPathAndTail() -> (headKeyPath: WHKeyPath, tail: String)? {
        guard isEmpty == false else {
            return nil
        }

        var head = segments
        let tail = head.removeLast()
        return (WHKeyPath(segments: head), tail)
    }

}

extension WHKeyPath {
    
    public init(_ keyPath: String) {
        segments = keyPath.components(separatedBy: ".")
    }
    
}

extension Dictionary where Key == String {
    
    public func keyPath(_ keyPath: WHKeyPath) -> Any? {
        switch keyPath.headAndTailKeyPath() {
        case nil:
            return nil
        case let (head, remainingKeyPath)? where remainingKeyPath.isEmpty:
            // 路径尾部
            return self[head]
        case let (head, remainingKeyPath)?:
            switch self[head] {
            case let nestedDict as [String: Any]:
                return nestedDict.keyPath(remainingKeyPath)
            default:
                // 嵌套的不是字典，路径无效，终止。
                return nil
            }
        }
    }
    
}
