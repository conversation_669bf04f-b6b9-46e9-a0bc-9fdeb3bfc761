//
//  MBNetworkChangeMonitor.swift
//  MedicalBeautySDK
//
//  Created by <PERSON><PERSON><PERSON>qi on 2020/12/29.
//

import Foundation
import Alamofire

public enum MBNetworkReachabilityStatus {
    ///未知
    case unknown
    ///无网络
    case notReachable
    ///wifi
    case ethernetOrWiFi
    ///移动网络
    case cellular
}

public let WHNetworkChangeMonitorDefault = WHNetworkChangeMonitor.shared

public class WHNetworkChangeMonitor{
    
    private let net = NetworkReachabilityManager()
    
    public var currentNetwork: MBNetworkReachabilityStatus = .ethernetOrWiFi
    
    public static var shared: WHNetworkChangeMonitor = {
        let instance = WHNetworkChangeMonitor()
        return instance
    } ()
    
    public func startListener() {
        net?.startListening(onUpdatePerforming: { [weak self](status) in
            var currentNetwork: MBNetworkReachabilityStatus = .notReachable
            switch status {
            case .notReachable:
                currentNetwork = .notReachable
            case .unknown:
                currentNetwork = .unknown
            case .reachable(.ethernetOrWiFi):
                currentNetwork = .ethernetOrWiFi
            case .reachable(.cellular):
                currentNetwork = .cellular
            }
            
            if currentNetwork != self?.currentNetwork {
                self?.currentNetwork = currentNetwork
                NotificationCenter.default.post(name: .KMINetworkDidChange,
                                                object: nil)
            }
        })
    }
    
    private func stopListening() {
        net?.stopListening()
    }
    
    deinit {
        stopListening()
    }
}



extension Notification.Name {
    ///网络发生变化通知
    public static let KMINetworkDidChange = Notification.Name("networkDidChange")
}
