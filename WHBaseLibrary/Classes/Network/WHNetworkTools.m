//
//  WHNetworkTools.m
//  WHBaseLibrary
//
//  Created by <PERSON> on 2023/1/6.
//

#import "WHNetworkTools.h"
#include <ifaddrs.h>
#include <arpa/inet.h>
#include <net/if.h>

@implementation WHNetworkTools

+ (WHNetworkSpeed)currentSpeed {
    struct ifaddrs *addrs = 0, *cursor;
    uint32_t iBytes = 0;
    uint32_t oBytes = 0;
    if (getifaddrs(&addrs) == 0) {
        for (cursor = addrs; cursor; cursor = cursor->ifa_next) {
            if (AF_LINK != cursor->ifa_addr->sa_family) {
                continue;
            }
            if (!(cursor->ifa_flags & IFF_UP) && !(cursor->ifa_flags & IFF_RUNNING)) {
                continue;
            }
            if (cursor->ifa_data == 0) {
                continue;
            }
            
            /* Not a loopback device. */
            if (strncmp(cursor->ifa_name, "lo", 2)) {
                struct if_data *if_data = (struct if_data *)cursor->ifa_data;
                // 下行
                iBytes += if_data->ifi_ibytes;
                // 上行
                oBytes += if_data->ifi_obytes;
            }
        }
    }
    freeifaddrs(addrs);
    
    WHNetworkSpeed speed;
    speed.iBytes = iBytes;
    speed.oBytes = oBytes;
    
    return speed;
}

@end
