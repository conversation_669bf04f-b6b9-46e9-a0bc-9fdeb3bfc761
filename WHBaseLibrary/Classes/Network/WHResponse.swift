//
//  WHResponse.swift
//
//  Created by <PERSON> on 2022/2/8.
//

import Foundation
import Alamofire
import ObjectMapper
import ObjectMapperAdditions

public struct WHOriginalResponse {
    
    /// 网络请求响应数据
    public let response: HTTPURLResponse?
    /// 接口错误信息
    public let error: AFError?
    /// 网络层错误信息
    public let httpError: Bool?
    /// 接口返回的所有数据
    public let value: Any?
    
    init(response: HTTPURLResponse? = nil,
         error: AFError? = nil,
         value: Any? = nil) {
        self.response = response
        self.error = error
        self.httpError = (response?.statusCode != 200)
        self.value = value
    }
    
    /// 网络请求是否成功
    public func isSuccess() -> Bool {
        if let httpError = httpError, httpError {
            return false
        }else if let error = error {
            return false
        } else {
            if let dict = value as? [String: Any],
               let code = dict["code"] as? Int,
               code == 0 {
                return true
            } else {
                return false
            }
        }
    }
    
    /// 网络提示
    public func message() -> String? {
        if let httpError = httpError, httpError {
            if self.response?.statusCode == 400, error == nil {
                if let dict = value as? [String: Any],
                   let message = dict["message"] as? String {
                    return message
                } else {
                    return nil
                }
            }
            return "网络好像出问题了，请重试。"
        }else if let error = error {
            return error.localizedDescription
        } else {
            if let dict = value as? [String: Any],
               let message = dict["message"] as? String {
                return message
            } else {
                return nil
            }
        }
    }
    
    public func data() -> Any? {
        if let httpError = httpError, httpError {
            return nil
        }else if let error = error {
            return nil
        } else {
            if let dict = value as? [String: Any] {
                return dict["data"]
            } else {
                return nil
            }
        }
    }
    
}

public struct WHResponse<T: Mappable> {
    
    /// 网络请求响应数据
    public let response: HTTPURLResponse?
    /// 接口错误信息
    public let error: AFError?
    /// 网络层错误信息
    public let httpError: Bool?
    /// 接口返回的所有数据
    public let value: Any?

    /// 接口数据状态信息
    public private(set) var statusInfo: WHResponseStatusInfo?
    /// 解析得到的数据列表
    public private(set) var result: [T] = []
    /// 未解析的额外数据
    public private(set) var extraInfo: [String: Any]?
    
    init(response: HTTPURLResponse? = nil,
         error: AFError? = nil,
         value: Any? = nil,
         designatedPath: WHKeyPath? = nil) {
        self.response = response
        self.error = error
        self.httpError = (response?.statusCode != 200)
        self.value = value
        constructStatusInfo()
        constructResult(designatedPath)
        constructExtraInfo(designatedPath)
    }
    
    /// 网络请求是否成功
    public func isSuccess() -> Bool {
        if let httpError = httpError, httpError {
            return false
        }else if let error = error {
            return false
        } else {
            if let code = statusInfo?.code,
               code == 0 {
                return true
            } else {
                return false
            }
        }
    }
    
    /// 网络提示
    public func message() -> String? {
        if let httpError = httpError, httpError {
            return "您的网络不佳，请调整网络重试"
        }else if let error = error {
            return error.localizedDescription
        } else {
            return statusInfo?.message
        }
    }
    
    private mutating func constructStatusInfo() {
        guard let value = value as? [String: Any] else {
            return
        }
        self.statusInfo = WHResponseStatusInfo(JSON: value)
    }
    
    private mutating func constructResult(_ keyPath: WHKeyPath?) {
        guard let value = value as? [String: Any],
              let keyPath = keyPath else {
            return
        }
        if let obj = value.keyPath(keyPath) {
            
            if let dict = obj as? [String: Any],
               let model = Mapper<T>().map(JSON: dict) {
                self.result.append(model)
            } else if let array = obj as? [[String: Any]] {
                self.result.append(contentsOf: Mapper<T>().mapArray(JSONArray: array))
            }
        }
    }
    
    private mutating func constructExtraInfo(_ keyPath: WHKeyPath?) {
        guard let value = value as? [String: Any],
              let keyPath = keyPath,
              let (headKeyPath, tail) = keyPath.headKeyPathAndTail(),
              headKeyPath.isEmpty == false else {
            return
        }
        if let dict = value.keyPath(headKeyPath) as? [String: Any] {
            self.extraInfo = dict
            self.extraInfo?.removeValue(forKey: tail)
        }
    }

}

public let WHResponseCodeDefault: Int = -1000

public struct WHResponseStatusInfo: Mappable {
    
    public var code: Int = WHResponseCodeDefault
    public var errorCode: String = ""
    public var message: String = ""
    
    public init?(map: Map) {}
    
    mutating public func mapping(map: Map) {
        code        <- (map["code"], IntTransform.shared)
        errorCode   <- (map["errorCode"], StringTransform.shared)
        message     <- (map["message"], StringTransform.shared)
    }
    
}
