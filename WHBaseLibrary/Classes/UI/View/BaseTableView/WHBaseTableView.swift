//
//  WHBaseTableView.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/7/18.
//

import Foundation
import UIKit
import SnapKit

public protocol WHBaseTableViewDelegate: NSObjectProtocol {
    func tableViewDidScroll(_ view: WHBaseTableView)
    func willDisplay(_ view: WHBaseTableView, indexPath: IndexPath)
}

public extension WHBaseTableViewDelegate {
   public func tableViewDidScroll(_ view: WHBaseTableView) {
       
   }
    
   public func willDisplay(_ view: WHBaseTableView, indexPath: IndexPath) {
       
   }
}

open class WHBaseTableView : UIView {
    
    public var sourceData : [WHBaseTableViewItem]? {
        didSet {
            if let source = sourceData {
                tableView.reloadData()
            }
        }
    }
    
    public var bgColor : UIColor? {
        didSet {
            if let bgColor = bgColor {
                tableView.backgroundColor = bgColor
            }
        }
    }
    
    open var bundleName : String?
    public weak var delegate: WHBaseTableViewDelegate?
    
    public var tableView : UITableView = UITableView.init(frame: .zero, style: .plain).then { (v) in
        v.scrollsToTop = true
        v.separatorStyle = .none
        v.showsVerticalScrollIndicator = false
        v.showsHorizontalScrollIndicator = false
        v.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    public var isCanScrolled : Bool? {
        didSet {
            if let isCanScrolled = isCanScrolled {
                tableView.isScrollEnabled = isCanScrolled
            }
        }
    }
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        configUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func configUI() {
        if #available(iOS 11.0, *) {
            tableView.contentInsetAdjustmentBehavior = .never
        }
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0
        }
        addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        tableView.delegate = self
        tableView.dataSource = self
    }
    
    public func refreshData() {
        if sourceData?.count ?? 0 > 0 {
            tableView.reloadData()
        }
    }
    
    open func willDisplay(_ indexPath: IndexPath) {
        delegate?.willDisplay(self, indexPath: indexPath)
    }
    
    open func scrollViewDidScroll(_ scrollView: UIScrollView) {
        delegate?.tableViewDidScroll(self)
    }
}

extension WHBaseTableView : UITableViewDelegate {
    
    public func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let baseItem = sourceData?.object(at: indexPath.row)
        return baseItem?.cellHeight() ?? 0
    }
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
        
        let baseItem = sourceData?.object(at: indexPath.row)
        baseItem?.cellClicked()
    }
    
    public func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        willDisplay(indexPath)
    }
    
    
}

extension WHBaseTableView : UITableViewDataSource {
    
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sourceData?.count ?? 0
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let baseItem = sourceData?.object(at: indexPath.row)
        guard let classNameId = baseItem?.cellClassName().replacingOccurrences(of: "Item", with: "Cell")
        else {
            return UITableViewCell()
        }
        var cell : WHBaseTableViewCell? = tableView.dequeueReusableCell(withIdentifier: classNameId) as? WHBaseTableViewCell
        if (cell == nil) {
            var name = WHAppInfo.bundleName
            if let bundleName = bundleName, bundleName.count > 0 {
                name = bundleName
            }
            var cls : AnyClass?  = NSClassFromString(name + "." + classNameId)
            guard let concreteCls = cls as? WHBaseTableViewCell.Type else {
                return UITableViewCell()
            }
            cell = concreteCls.init(style: .default, reuseIdentifier: classNameId)
        }
        
        if let item = baseItem {
            item.currentIndex = indexPath
            cell?.setData(data: item)
        }
        
        return cell ?? UITableViewCell()
        
    }
    
}
 
