//
//  WHBaseTableViewCell.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/7/18.
//

import Foundation
import UIKit

open class WHBaseTableViewCell: UITableViewCell {
    
    required public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        self.selectionStyle = .none
        configUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open func configUI() {
        
    }
    
    open func setData(data: WHBaseTableViewItem) {
        
    }
}

