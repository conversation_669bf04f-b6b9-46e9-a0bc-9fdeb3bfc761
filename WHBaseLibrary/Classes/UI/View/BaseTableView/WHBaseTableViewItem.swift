//
//  WHBaseTableViewItem.swift
//  WHBaseLibrary
//
//  Created by mt_zl on 2023/7/18.
//

import Foundation

open class WHBaseTableViewItem : NSObject {
    
    public var currentIndex: IndexPath?
    
    public var clickedBlock : (() -> Void)?
    
    public override init() {
        super.init()
    }
    
    open func cellHeight() -> CGFloat {
        return 0.00
    }
    
    open func cellClicked() {
        if let block = clickedBlock {
            block()
        }
    }
    
    open func cellClassName() -> String {
        let className = String(describing: Self.self)
        return className
    }
    
}
