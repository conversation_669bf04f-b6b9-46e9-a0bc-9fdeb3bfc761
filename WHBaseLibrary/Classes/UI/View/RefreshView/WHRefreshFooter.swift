//
//  WHRefreshFooter.swift
//
//  Created by <PERSON> on 2022/5/23.
//

import MJRefresh

/// 内容底部显示
public class WHRefreshAutoNormalFooter: MJRefreshAutoNormalFooter {
    
    override init(frame: CGRect) {
        super.init(frame: frame)
//        self.stateLabel?.font = UIFont.systemFont(ofSize: 12)
//        self.stateLabel?.textColor = UIColor(rgb: 0xAEAFB7)
//        self.setTitle("到底了哦～", for: .noMoreData)
        self.ignoredScrollViewContentInsetBottom = WH_SCREEN_BOTTOM_SPACE
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}

/// 屏幕底部显示
public class WHRefreshBackNormalFooter: MJRefreshBackNormalFooter {
    
    override init(frame: CGRect) {
        super.init(frame: frame)
//        self.stateLabel?.font = UIFont.systemFont(ofSize: 12)
//        self.stateLabel?.textColor = UIColor(rgb: 0xAEAFB7)
//        self.setTitle("到底了哦～", for: .noMoreData)
        self.ignoredScrollViewContentInsetBottom = WH_SCREEN_BOTTOM_SPACE
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
