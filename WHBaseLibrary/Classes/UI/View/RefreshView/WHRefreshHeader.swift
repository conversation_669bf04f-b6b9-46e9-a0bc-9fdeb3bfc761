//
//  WHRefreshHeader.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2023/8/3.
//

import MJRefresh

public class WHRefreshNormalHeader: MJRefreshNormalHeader {
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
//        self.stateLabel?.font = UIFont.systemFont(ofSize: 12)
//        self.stateLabel?.textColor = UIColor(rgb: 0xAEAFB7)
//
//        self.lastUpdatedTimeLabel?.font = UIFont.systemFont(ofSize: 12)
//        self.lastUpdatedTimeLabel?.textColor = UIColor(rgb: 0xAEAFB7)
        
        self.isCollectionViewAnimationBug = true
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
