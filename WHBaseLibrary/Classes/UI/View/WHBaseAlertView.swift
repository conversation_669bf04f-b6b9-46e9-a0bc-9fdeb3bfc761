//
//  WHBaseAlertView.swift
//
//  Created by <PERSON> on 2020/12/15.
//

import UIKit

public enum WHAlertBgViewStyle {
    case normal
    case clear
    case none
}

open class WHBaseAlertView: UIView {
    
    public lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(rgb: 0x000000, alpha: 0.7)
        return view
    } ()
    
    /// 弹窗在Y轴偏移量，默认是屏幕中心展示。往下为正数，往上为负数
    public var contentOffsetY: CGFloat = 0
    
    var bgViewStyle: WHAlertBgViewStyle = .normal {
        didSet {
            bgView.backgroundColor = bgViewStyle == .clear ? .clear : UIColor(rgb: 0x000000, alpha: 0.7)
        }
    }
    
    open func show(in view: UIView? = UIApplication.wh_currentViewController?.view) {
        view?.addSubview(self)
    }
    
    open func hide(_ completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: 0.25) {
            self.bgView.alpha = 0
            self.alpha = 0
        } completion: { (finished) in
            self.bgView.removeFromSuperview()
            self.removeFromSuperview()
            completion?()
        }
    }
    
    open override func willMove(toSuperview newSuperview: UIView?) {
        guard let newSuperview = newSuperview else {
            return
        }
        
        if bgViewStyle != .none {
            self.bgView.frame = CGRect(x: 0,
                                       y: 0,
                                       width: WH_SCREEN_WIDTH,
                                       height: WH_SCREEN_HEIGHT)
            newSuperview.addSubview(bgView)
        }
        
        self.frame = CGRect(x: (WH_SCREEN_WIDTH - width()) / 2.0,
                            y: (WH_SCREEN_HEIGHT - height()) / 2.0 + contentOffsetY,
                            width: width(),
                            height: height())
        
        let animation = CAKeyframeAnimation(keyPath: "transform")
        animation.duration = 0.25
        animation.isRemovedOnCompletion = true
        animation.fillMode = .forwards
        animation.values = [
            CATransform3DMakeScale(0.9, 0.9, 1.0),
            CATransform3DMakeScale(1.1, 1.1, 1.0),
            CATransform3DMakeScale(1.0, 1.0, 1.0),
        ]
        self.layer.add(animation, forKey: nil)
        super.willMove(toSuperview: newSuperview)
    }
    
    open func width() -> CGFloat {
        return 0
    }
    
    open func height() -> CGFloat {
        return 0
    }
    
}
