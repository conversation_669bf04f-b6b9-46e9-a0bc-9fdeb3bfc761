//
//  WHBaseHUD.swift
//
//  Created by Devin on 2022/3/16.
//

import Foundation

public enum WHHUDAnimation {
    case fade
    case zoom
    case zoomOut
    case zoomIn
}

open class WHBaseHUD: UIView {
    
    // MARK: - Public Properties
    /// 隐藏时是否移除
    public var removeFromSuperViewOnHide: Bool = false
    /// 显示隐藏的动画类型
    public var animationType: WHHUDAnimation = .fade
    /// 结束回调Block
    public var completionBlock: (() -> ())?
    /// 弹窗在Y轴偏移量，默认是屏幕中心展示。往下为正数，往上为负数
    public var contentOffsetY: CGFloat = 0
    /// 延迟多久显示（单位：秒。默认：0）
    public var graceTime: TimeInterval = 0
    /// 最小显示时长（单位：秒。默认：0）
    public var minShowTime: TimeInterval = 0
    /// 是否支持运动视差效果（类似墙纸透视效果）
    public var motionEffectsEnabled: Bool = false
    
    public var bgViewColor: UIColor? {
        didSet {
            bgView.backgroundColor = bgViewColor
        }
    }
    
    // MARK: - Private Properties
    private var finished: Bool = false
    private var useAnimation: Bool = false

    private var showStarted: Date?
    
    private var graceTimer: Timer?
    private var minShowTimer: Timer?
    private var hideDelayTimer: Timer?
    
    private var hudMotionEffects: UIMotionEffectGroup?
    
    private lazy var bgView: UIView = {
        let view = UIView()
        view.alpha = 0
        return view
    }()
    
    // MARK: - Class methods
    public static func show(_ hud: WHBaseHUD,
                            in view: UIView,
                            animated: Bool) {
        hud.removeFromSuperViewOnHide = true
        view.addSubview(hud)
        hud.show(animated: animated)
    }
    
    public static func hide(for view: UIView,
                            animated: Bool) -> Bool {
        if let hud = HUD(for: view) {
            hud.removeFromSuperViewOnHide = true
            hud.hide(animated: animated)
            return true
        }
        return false
    }
    
    public static func HUD(for view: UIView) -> Self? {
        for subview in view.subviews.reversed() {
            if let hud = subview as? Self,
               hud.finished == false {
                return hud
            }
        }
        return nil
    }
    
    // MARK: - Lifecycle
    deinit {
        WHPrint(self, "释放了")
    }
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        self.alpha = 0
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open func width() -> CGFloat {
        return 0
    }
    
    open func height() -> CGFloat {
        return 0
    }
    
    // MARK: - Show & hide
    public func show(animated: Bool) {
        assert(Thread.isMainThread, "MGHUDView needs to be accessed on the main thread.")
        
        minShowTimer?.invalidate()
        useAnimation = animated
        finished = false
        
        if graceTime > 0 {
            let timer = Timer(timeInterval: graceTime,
                              target: self,
                              selector: #selector(handleGraceTimer(_:)),
                              userInfo: nil,
                              repeats: false)
            RunLoop.current.add(timer, forMode: .common)
            graceTimer = timer
        } else {
            showUsingAnimation(useAnimation)
        }
    }
    
    public func hide(animated: Bool) {
        assert(Thread.isMainThread, "MGHUDView needs to be accessed on the main thread.")

        graceTimer?.invalidate()
        useAnimation = animated
        finished = true
        
        if minShowTime > 0,
           let started = showStarted {
            let interval = Date().timeIntervalSince(started)
            if interval < minShowTime {
                let timer = Timer(timeInterval: minShowTime - interval,
                                  target: self,
                                  selector: #selector(handleMinShowTimer(_:)),
                                  userInfo: nil,
                                  repeats: false)
                RunLoop.current.add(timer, forMode: .common)
                minShowTimer = timer
                return
            }
        }
        
        hideUsingAnimation(useAnimation)
    }
    
    public func hide(animated: Bool,
                     after delay: TimeInterval) {
        hideDelayTimer?.invalidate()
        
        let timer = Timer(timeInterval: delay,
                          target: self,
                          selector: #selector(handleHideTimer(_:)),
                          userInfo: animated,
                          repeats: false)
        RunLoop.current.add(timer, forMode: .common)
        hideDelayTimer = timer
    }
    
    // MARK: - Timer callbacks
    @objc
    private func handleGraceTimer(_ timer: Timer) {
        if finished == false {
            showUsingAnimation(useAnimation)
        }
    }
    
    @objc
    private func handleMinShowTimer(_ timer: Timer) {
        hideUsingAnimation(useAnimation)
    }
    
    @objc
    private func handleHideTimer(_ timer: Timer) {
        if let animated = timer.userInfo as? Bool {
            hide(animated: animated)
        } else {
            hide(animated: false)
        }
    }
    
    // MARK: - Internal show & hide operations
    private func showUsingAnimation(_ animated: Bool) {
        layer.removeAllAnimations()
        bgView.layer.removeAllAnimations()
        
        hideDelayTimer?.invalidate()
        
        showStarted = Date()
        
        updateMotionEffects()
        
        if animated {
            animateIn(true, type: animationType, completion: nil)
        } else {
            self.alpha = 1.0
            bgView.alpha = 1.0
        }
    }
    
    private func hideUsingAnimation(_ animated: Bool) {
        hideDelayTimer?.invalidate()
        
        if animated && showStarted != nil {
            showStarted = nil
            animateIn(false, type: animationType) { [weak self] (finished: Bool) in
                self?.done()
            }
        } else {
            showStarted = nil
            self.alpha = 0
            bgView.alpha = 0
            done()
        }
    }
    
    private func animateIn(_ animateIn: Bool,
                           type: WHHUDAnimation,
                           completion: ((Bool) -> ())?) {
        var animationType = type
        if type == .zoom {
            animationType = animateIn ? .zoomIn : .zoomOut
        }
        
        let small = CGAffineTransform(scaleX: 0.5, y: 0.5)
        let large = CGAffineTransform(scaleX: 1.5, y: 1.5)

        // 设置初始状态
        if animateIn && self.alpha == 0 {
            if animationType == .zoomIn {
                self.transform = small
            } else if animationType == .zoomOut {
                self.transform = large
            }
        }
        
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 1.0, initialSpringVelocity: 0, options: .beginFromCurrentState, animations: {
            if animateIn {
                self.transform = .identity
            } else {
                if animationType == .zoomIn {
                    self.transform = large
                } else if animationType == .zoomOut {
                    self.transform = small
                }
            }
            
            let alpha = animateIn ? 1.0 : 0
            self.alpha = alpha
            self.bgView.alpha = alpha
        }, completion: completion)
    }
    
    private func done() {
        if finished && removeFromSuperViewOnHide {
            bgView.removeFromSuperview()
            self.removeFromSuperview()
        }
        completionBlock?()
    }
    
    // MARK: -
    private func updateMotionEffects() {
        if motionEffectsEnabled {
            if hudMotionEffects == nil {
                let effectOffset: CGFloat = 10
                
                let effectX = UIInterpolatingMotionEffect(keyPath: "center.x",
                                                          type: .tiltAlongHorizontalAxis)
                effectX.maximumRelativeValue = effectOffset
                effectX.minimumRelativeValue = -effectOffset
                
                let effectY = UIInterpolatingMotionEffect(keyPath: "center.y",
                                                          type: .tiltAlongVerticalAxis)
                effectY.maximumRelativeValue = effectOffset
                effectY.minimumRelativeValue = -effectOffset
                
                let group = UIMotionEffectGroup()
                group.motionEffects = [effectX, effectY]
                
                hudMotionEffects = group
                self.addMotionEffect(group)
            }
        } else {
            if let effects = hudMotionEffects {
                self.removeMotionEffect(effects)
                hudMotionEffects = nil
            }
        }
    }
    
    // MARK: -
    open override func willMove(toSuperview newSuperview: UIView?) {
        guard let newSuperview = newSuperview else {
            return
        }
        bgView.frame = newSuperview.bounds
        newSuperview.addSubview(bgView)
        
        self.frame = CGRect(x: (newSuperview.bounds.width - width()) / 2.0,
                            y: (newSuperview.bounds.height - height()) / 2.0 + contentOffsetY,
                            width: width(),
                            height: height())
        
        super.willMove(toSuperview: newSuperview)
    }
    
}
