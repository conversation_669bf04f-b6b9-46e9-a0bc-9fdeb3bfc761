//
//  WHBaseSheetView.swift
//
//  Created by <PERSON> on 2022/2/17.
//

import Foundation

open class WHBaseSheetView: UIView,
                            CAAnimationDelegate {
    
    public lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(rgb: 0x000000, alpha: 0.7)
        view.frame = CGRect(x: 0,
                            y: 0,
                            width: WH_SCREEN_WIDTH,
                            height: WH_SCREEN_HEIGHT)
//        let tap = UITapGestureRecognizer(target: self,
//                                         action: #selector(tapBgViewAction))
//        view.addGestureRecognizer(tap)
        return view
    } ()
    
    lazy var showFrame: CGRect = {
        return CGRect(x: 0,
                      y: WH_SCREEN_HEIGHT - height(),
                      width: WH_SCREEN_WIDTH,
                      height: height())
    } ()
    
    public var bgTapEnabled: Bool = true
    public var hasShowAnimation: Bool = true
    
    public var bgViewStyle: WHAlertBgViewStyle = .normal {
        didSet {
            bgView.backgroundColor = bgViewStyle == .clear ? .clear : UIColor(rgb: 0x000000, alpha: 0.7)
        }
    }
    
    /// 关闭回调
    public var closeBlock: (() -> Void)?
    
    deinit {
        WHPrint(self, "释放。。。")
    }
    
    // MARK: - Events
    @objc
    private func tapBgViewAction() {
        if bgTapEnabled {
            hide()
        }
    }
    
    // MARK: - Public Methods
    open func show(in view: UIView? = UIApplication.wh_currentViewController?.view) {
        view?.addSubview(self)
    }
    
    open func hide(_ completion: (() -> Void)? = nil) {
        guard let superview = superview else {
            completion?()
            return
        }
        let position = self.layer.position
        UIView.animate(withDuration: 0.25, delay: 0, usingSpringWithDamping: 1.0, initialSpringVelocity: 1.0, options: .curveEaseOut) {
            self.layer.position = CGPoint(x: position.x,
                                          y: self.bounds.height * 0.5 + superview.frame.height)
            if self.bgView.superview != nil {
                self.bgView.alpha = 0
            }
        } completion: { (finished) in
            self.removeView()
            self.closeBlock?()
            completion?()
        }

    }
    
    // MARK: - CAAnimationDelegate
    public func animationDidStart(_ anim: CAAnimation) {
    }
    
    public func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
    }
    
    // MARK: - Private Methods
    private func removeView() {
        if bgView.superview != nil {
            bgView.removeFromSuperview()
        }
        self.removeFromSuperview()
    }
    
    // MARK: - Override
    open override func willMove(toSuperview newSuperview: UIView?) {
        guard let newSuperview = newSuperview else {
            return
        }
        
        if bgViewStyle != .none {
            bgView.frame = newSuperview.bounds
            newSuperview.addSubview(bgView)
        }
                
        self.frame = CGRect(x: 0,
                            y: newSuperview.frame.height - height(),
                            width: newSuperview.frame.width,
                            height: height())

        if hasShowAnimation {
            let position = self.layer.position
            
            let animation = CAKeyframeAnimation(keyPath: "position")
            animation.delegate = self
            animation.duration = 0.25
            animation.isRemovedOnCompletion = true
            animation.fillMode = .forwards
            animation.values = [
                CGPoint(x: position.x,
                        y: self.bounds.height * 0.5 + newSuperview.frame.height),
                position,
            ]
            animation.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.layer.add(animation, forKey: nil)
        }
        
        super.willMove(toSuperview: newSuperview)
    }
    
    /// sheet高度
    open func height() -> CGFloat {
        return 0
    }
    
}
