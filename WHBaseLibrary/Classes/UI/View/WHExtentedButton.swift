//
//  WHExtentedButton.swift
//  WHBaseLibrary
//
//  Created by linda_zl on 2024/10/17.
//

import Foundation


/// 扩展响应区域
public class WHExtentedButton: UIButton {

    public var expandInset: UIEdgeInsets = UIEdgeInsets(top: -20, left: -20, bottom: -20, right: -20)
    
    public override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        let expandedBounds = bounds.inset(by: expandInset)
        return expandedBounds.contains(point) ? self : nil
    }
}
