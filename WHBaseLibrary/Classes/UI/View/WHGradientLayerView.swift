//
//  WHGradientLayerView.swift
//  WHBusinessService
//
// CAGradientLayer 的一层 UIView 的包装，方便使用自动布局。默认 userInteractionEnabled 为 NO。

import UIKit

open class WHGradientLayerView: UIView {
    
    public var gradientLayer: CAGradientLayer {
        return layer as! CAGradientLayer
    }
    
    public override class var layerClass: AnyClass {
        return CAGradientLayer.self
    }
    
    public var colors: [UIColor] {
        get {
            guard let cgColors = self.gradientLayer.colors as? [CGColor] else { return [] }
            return cgColors.map { UIColor(cgColor: $0 as! CGColor) }
        }
        set {
            gradientLayer.colors = newValue.map { $0.cgColor }
        }
    }

    public var locations: [NSNumber] {
        get { gradientLayer.locations ?? [] }
        set {
            gradientLayer.locations = newValue
        }
    }
    
    public var startPoint: CGPoint {
        get { gradientLayer.startPoint }
        set {
            gradientLayer.startPoint = newValue
        }
    }
    
    public var endPoint: CGPoint {
        get { gradientLayer.endPoint }
        set {
            gradientLayer.endPoint = newValue
        }
    }
    
    public init() {
        super.init(frame: .zero)
        self.isUserInteractionEnabled = false
    }
    
    public override init(frame: CGRect) {
        super.init(frame: .zero)
        self.isUserInteractionEnabled = false
    }
    
    public required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        self.isUserInteractionEnabled = false
    }

}
