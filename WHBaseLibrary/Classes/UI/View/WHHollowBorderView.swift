//
//  WHHollowBorderView.swift
//  WHBaseLibrary
//
//  Created by Devin on 2025/7/21.
//

import Foundation
import UIKit

/// 可配置的“回字形”线框视图
public class WHHollowBorderView: UIView {

    /// 每层线框的颜色（从外向内）
    public var borderColors: [UIColor] = [] {
        didSet { setNeedsDisplay() }
    }

    /// 每层之间的间隙
    public var gapBetweenBorders: CGFloat = 0 {
        didSet { setNeedsDisplay() }
    }

    /// 每层线框的宽度
    public var borderWidths: [CGFloat] = [] {
        didSet { setNeedsDisplay() }
    }

    /// 圆角半径
    public var cornerRadius: CGFloat = 0 {
        didSet { setNeedsDisplay() }
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        isOpaque = false
        backgroundColor = .clear
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        isOpaque = false
        backgroundColor = .clear
    }

    public override func draw(_ rect: CGRect) {
        // 确保颜色数组与宽度数组数量一致
        guard borderColors.count == borderWidths.count else {
            WHPrint("borderColors 和 borderWidths 数量不一致")
            return
        }

        // 获取当前图形上下文（Core Graphics 环境）
        guard let context = UIGraphicsGetCurrentContext() else { return }

        // 清空当前区域（防止重绘时叠加）
        context.clear(rect)

        // 初始绘制区域为整个视图
        var currentRect = bounds
        // 当前的圆角半径（随着层数递减）
        var currentCornerRadius = cornerRadius

        // 遍历每一层边框（从外向内）
        for (index, color) in borderColors.enumerated() {
            let width = borderWidths[index] // 当前边框宽度
            let totalInset = width + gapBetweenBorders // 本层总缩进（宽度 + 层间距）

            // ==== 构建外层路径 ====
            let outerPath = UIBezierPath(roundedRect: currentRect, cornerRadius: currentCornerRadius)

            // ==== 构建内层路径 ====
            let innerRect = currentRect.insetBy(dx: width, dy: width)
            let innerRadius = max(0, currentCornerRadius - width) // 圆角递减，避免负值
            let innerPath = UIBezierPath(roundedRect: innerRect, cornerRadius: innerRadius)

            // ==== 将两路径组合，构成“中空”的边框区域 ====
            // `innerPath.reversing()` 表示从外往内的路径反向组合，以便形成中间镂空
            outerPath.append(innerPath.reversing())

            // 绘制组合路径（只填充边框区域）
            context.addPath(outerPath.cgPath)
            context.setFillColor(color.cgColor)
            context.fillPath()

            // ==== 准备下一层绘制 ====
            // 进一步缩小绘制区域（为下一层留出位置）
            currentRect = currentRect.insetBy(dx: totalInset, dy: totalInset)
            // 递减圆角，保证下一层与当前形状相似
            currentCornerRadius = max(0, currentCornerRadius - totalInset)
        }

        // ==== 最内层镂空（清除中间区域）====
        let innerPath = UIBezierPath(roundedRect: currentRect, cornerRadius: currentCornerRadius)
        context.addPath(innerPath.cgPath)
        context.setBlendMode(.clear) // 设置为“清除”混合模式
        context.fillPath() // 透明填充，实现镂空
    }
    
}
