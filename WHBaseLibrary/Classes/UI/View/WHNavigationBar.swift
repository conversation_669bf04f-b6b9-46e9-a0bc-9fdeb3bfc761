//
//  WHNavigationBar.swift
//
//
//  Created by <PERSON> on 2020/11/17.
//

import UIKit

public enum WHNavigationBarBackItemStyle {
    case white
    case black
    case gray
    case whiteArrow
}

public class WHNavigationBar: UIView {
    
    public let WH_NAVIGATION_NO_STATUS_BAR_HEIGHT: CGFloat = 48.0
    
    public var titleTapBlock: (() -> Void)?
    
    public var title: String? {
        didSet {
            self.titleLabel?.text = title
        }
    }
    
    public var titleColor: UIColor = .white {
        didSet {
            self.titleLabel?.textColor = titleColor
        }
    }
    
    public var titleFont: UIFont = UIFont.pingFangSCFont(ofSize: 17, weight: .medium) {
        didSet {
            self.titleLabel?.font = titleFont
        }
    }
    
    public var backItemStyle: WHNavigationBarBackItemStyle = .white {
        didSet {
            switch backItemStyle {
            case .white:
                self.backItemImage = UIImage(wh_named: "wh_navigation_bar_back_white")
            case .black:
                self.backItemImage = UIImage(wh_named: "wh_navigation_bar_back_black")
            case .gray:
                self.backItemImage = UIImage(wh_named: "wh_navigation_bar_back_gray")
            case .whiteArrow:
                self.backItemImage = UIImage(wh_named: "wh_navigation_bar_back_whiteArrow")
            }
        }
    }
    
    public var rightItemTitle: String = "" {
        didSet {
            self.rightItem?.setTitle(rightItemTitle, for: .normal)
        }
    }
    
    public var rightItemTitleColor: UIColor = .white {
        didSet {
            self.rightItem?.setTitleColor(rightItemTitleColor,
                                          for: .normal)
        }
    }
    
    public var rightItemBlock: (() -> Void)?
    public var rightItems: [MBNavigationBarButtonItem] = [] {
        didSet {
            var right: MBNavigationBarButtonItem? = nil
            oldValue.forEach { (item: MBNavigationBarButtonItem) in
                item.customView.removeFromSuperview()
            }
            rightItems.forEach { (item: MBNavigationBarButtonItem) in
                self.addSubview(item.customView)
                item.customView.snp.makeConstraints { (make) in
                    make.height.equalTo(item.height)
                    make.bottom.equalTo(-item.leading)
                    make.height.equalTo(item.height)
                    make.width.equalTo(item.width)
                    if let rightItem = right {
                        make.right.equalTo(rightItem.customView.snp.left).offset(-item.trailing)
                    } else {
                        make.right.equalToSuperview().offset(-item.trailing)
                    }
                }
                right = item
            }
            if let rightItem = right {
                self.titleLabel?.snp.remakeConstraints { (make) in
                    make.centerX.bottom.equalToSuperview()
                    make.height.equalTo(WH_NAVIGATION_NO_STATUS_BAR_HEIGHT)
                    make.right.equalTo(rightItem.customView.snp.left)
                }
            }else{
                self.titleLabel?.snp.remakeConstraints { (make) in
                    make.centerX.bottom.equalToSuperview()
                    make.height.equalTo(WH_NAVIGATION_NO_STATUS_BAR_HEIGHT)
                    make.right.equalToSuperview().offset(-80)
                }
            }
            
        }
    }
    
    public var backItemImage: UIImage? = UIImage(wh_named: "wh_navigation_bar_back_white") {
        didSet {
            self.backItem?.setImage(backItemImage,
                                    for: .normal)
        }
    }
    
    private(set) var titleLabel: UILabel?
    private(set) var backItem: UIButton?
    private(set) var rightItem: UIButton?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        let titleLabel = UILabel()
        titleLabel.textColor = titleColor
        titleLabel.textAlignment = .center
        titleLabel.font = titleFont
        let tap = UITapGestureRecognizer(target: self, action: #selector(titleTap))
        titleLabel.addGestureRecognizer(tap)
        titleLabel.isUserInteractionEnabled = true
        self.addSubview(titleLabel)
        self.titleLabel = titleLabel
        
        let backBtn = UIButton(type: .custom)
        backBtn.setImage(backItemImage,
                         for: .normal)
        backBtn.addTarget(self,
                          action: #selector(backAction),
                          for: .touchUpInside)
        self.addSubview(backBtn)
        self.backItem = backBtn
        
        let rightBtn = UIButton(type: .custom)
        rightBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        rightBtn.addTarget(self,
                           action: #selector(rightBtnAction),
                           for: .touchUpInside)
        self.addSubview(rightBtn)
        self.rightItem = rightBtn
        
        titleLabel.snp.makeConstraints { (make) in
            make.centerX.bottom.equalToSuperview()
            make.height.equalTo(WH_NAVIGATION_NO_STATUS_BAR_HEIGHT)
            make.left.equalTo(backBtn.snp.right)
        }
        
        backBtn.snp.makeConstraints { (make) in
            make.left.equalTo(12)
            make.bottom.equalTo(-12)
            make.size.equalTo(CGSize(width: 24, height: 24))
        }
        
        rightBtn.snp.makeConstraints { (make) in
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview()
            make.height.equalTo(WH_NAVIGATION_NO_STATUS_BAR_HEIGHT)
        }
        
    }
    
    @objc
    private func backAction() {
        wh_passEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
    }
    
    @objc
    private func rightBtnAction() {
        self.rightItemBlock?()
    }
    
    @objc
    private func titleTap() {
        self.titleTapBlock?()
    }
    
}

public class MBNavigationBarButtonItem {
    
    public let customView: UIView
    public let width: CGFloat
    public let leading: CGFloat
    public let trailing: CGFloat
    public let height: CGFloat
    
    public init(customView: UIView,
                width: CGFloat = 0,
                height: CGFloat = WH_NAVIGATION_NO_STATUS_BAR_HEIGHT,
                leading: CGFloat = 0,
                trailing: CGFloat = 0) {
        self.customView = customView
        self.width = width
        self.leading = leading
        self.trailing = trailing
        self.height = height
    }
}
