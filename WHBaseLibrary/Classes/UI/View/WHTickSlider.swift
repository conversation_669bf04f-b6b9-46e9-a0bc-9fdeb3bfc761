//
//  WHTickSlider.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/4/18.
//

import Foundation
import UIKit

// TODO: 刻度样式、气泡位置等功能，可后续扩展。。。
/// 支持刻度、气泡提示、震动反馈
public class WHTickSlider: UISlider {

    // MARK: - 可配置属性
    public weak var delegate: WHTickSliderDelegate?
    
    public var minimumTrackColor: UIColor = .white
    public var maximumTrackColor: UIColor = .systemGray3
    
    /// 自定义轨道高度（默认4）
    public var trackHeight: CGFloat = 4 {
        didSet {
            setNeedsDisplay()
        }
    }

    /// 是否启用震动反馈
    public var feedbackType: WHTickSliderFeedbackType = .step
    /// 是否启用吸附效果
    public var snapType: WHTickSliderSnapType = .step

    /// 刻度列表
    public var tickItems: [WHTickItem] = [] {
        didSet {
            setNeedsDisplay()
            initLastTickValue()
        }
    }
    
    public override var value: Float {
        didSet {
            initLastTickValue()
            snapFeedback()
            setNeedsDisplay()
        }
    }
    
    /// 精度
    public var step: Float = 0.1
    
    /// 四舍五入到 step 精度
    public var roundedValue: Float {
        return (value / step).rounded() * step
    }
    

    // MARK: - 私有属性
    
    /// 上方显示数值的气泡标签
    private lazy var bubbleView: WHTickSliderBubble? = {
        return delegate?.tickSliderBubbleView()
    }()

    /// 上一次震动的值，用于避免重复触发震动
    private var lastFeedbackValue: Float? = Float.nan
    /// 气泡显示的值
    private var displayValue: Float?
    
    /// 吸附阈值
    private var snapThreshold: Float {
        if let usableTrackWidth = usableTrackWidth {
            // 设置最小吸附宽度为2，防止step值太大时，离刻度很远就吸附过去
            let value = (2 / Float(usableTrackWidth)) * (maximumValue - minimumValue)
            return min(value, step / 3)
        }
        return step / 3
    }
    
    /// 有效轨道宽度
    private var usableTrackWidth: CGFloat?

    /// 震动反馈生成器
    private let feedbackGenerator = UIImpactFeedbackGenerator(style: .light)
    

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    /// 公共初始化方法，配置默认属性
    private func commonInit() {
        super.minimumTrackTintColor = .clear
        super.maximumTrackTintColor = .clear
        
        feedbackGenerator.prepare()

        // 添加滑动相关事件
        super.addTarget(self, action: #selector(sliderDidBegin), for: .touchDown)
        super.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        super.addTarget(self, action: #selector(sliderDidEnd), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    private func initLastTickValue() {
        if lastFeedbackValue?.isNaN == true,
           tickItems.map({ $0.value }).contains(value) {
            lastFeedbackValue = value
        }
    }

    // MARK: - 滑动事件处理
    /// 滑动开始：显示气泡
    @objc
    private func sliderDidBegin() {
        updateBubble()
        delegate?.tickSliderDidBegin(self)
    }

    /// 滑动中：更新气泡位置与内容，处理震动反馈
    @objc
    private func sliderValueChanged() {
        snapFeedback()
        updateBubble()
        // 重绘刻度颜色
        setNeedsDisplay()
        delegate?.tickSliderValueChanged(self)
    }

    /// 滑动结束：隐藏气泡
    @objc
    private func sliderDidEnd() {
        UIView.animate(withDuration: 0.2) {
            self.bubbleView?.alpha = 0
        }
        delegate?.tickSliderDidEnd(self)
    }
    
    // MARK: - 吸附、震动反馈
    /// 吸附、反馈逻辑
    private func snapFeedback() {
        let nearestTick = currentNearestTick()
        let nearestStep = currentNearestStep()

        // 震动逻辑
        switch feedbackType {
        case .none:
            displayValue = nil
        case .step:
            if let nearestStep = nearestStep {
                displayValue = nearestStep
                if lastFeedbackValue != nearestStep {
                    lastFeedbackValue = nearestStep
                    feedbackGenerator.impactOccurred()
                }
            } else {
                // 作用是：当滑到1.5震动一下后，滑向1.6，但还没触发1.6震动，又滑回1.5，能够触发1.5震动
                lastFeedbackValue = nil
            }
        case .tick:
            if let nearestTick = nearestTick {
                if lastFeedbackValue != nearestTick {
                    lastFeedbackValue = nearestTick
                    feedbackGenerator.impactOccurred()
                }
            } else {
                lastFeedbackValue = nil
            }
            displayValue = roundedValue
        }
        
        // 吸附逻辑
        switch snapType {
        case .none:
            displayValue = nil
        case .step:
            if let nearestStep = nearestStep {
                super.value = nearestStep
                displayValue = nearestStep
            }
        case .tick:
            if let nearestTick = nearestTick {
                super.value = nearestTick
            }
            displayValue = roundedValue
        }
        
    }
    
    /// 找出离当前值最近的刻度
    private func currentNearestTick() -> Float? {
        guard tickItems.count > 0,
              feedbackType == .tick || snapType == .tick else {
            return nil
        }
        let tickValues = tickItems.map { $0.value }
        if let nearest = tickValues.min(by: { abs($0 - super.value) < abs($1 - super.value) }),
           abs(nearest - super.value) < snapThreshold {
            return nearest
        }
        return nil
    }
    
    private func currentNearestStep() -> Float? {
        guard feedbackType == .step || snapType == .step else {
            return nil
        }
        let ratio = value / step
        let lower = floor(ratio) * step
        let upper = ceil(ratio) * step
        let nearest = abs(value - lower) < abs(value - upper) ? lower : upper
        if abs(value - nearest) < snapThreshold {
            return nearest
        }
        return nil
    }

    // MARK: - 气泡逻辑
    /// 更新气泡位置与内容，并将其添加到 window 顶层
    private func updateBubble() {
        guard let bubbleView = bubbleView,
              let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else {
            return
        }

        // 获取滑块 thumb 的全局坐标
        let trackRect = self.trackRect(forBounds: bounds)
        let thumbRect = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: self.value)
        let globalThumbRect = self.convert(thumbRect, to: window)

        // ----- 设置气泡显示内容 -----
        
        // 根据 step 的小数位数决定格式
        let decimalPlaces: Int = {
            let string = String(step)
            if let dot = string.firstIndex(of: ".") {
                return string[dot...].dropFirst().count
            }
            return 0
        }()
        
        let size = bubbleView.setText(String(format: "%.\(decimalPlaces)f", displayValue ?? roundedValue))

        // 设置气泡位置（滑块上方偏移）
        let bubbleX = globalThumbRect.midX - size.width / 2
        let bubbleY = globalThumbRect.minY - size.height - 20
        bubbleView.frame = CGRect(origin: CGPoint(x: bubbleX, y: bubbleY), size: size)

        // 添加到 window 顶层
        if bubbleView.superview !== window {
            bubbleView.removeFromSuperview()
            window.addSubview(bubbleView)
        }

        bubbleView.alpha = 1
        window.bringSubviewToFront(bubbleView)
    }

    // MARK: - 绘制轨道、刻度与标签
    public override func draw(_ rect: CGRect) {
        super.draw(rect)
        guard let context = UIGraphicsGetCurrentContext() else {
            return
        }

        let trackRect = self.trackRect(forBounds: rect)
        let minThumbRect = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: minimumValue)
        // 有效轨道宽度
        usableTrackWidth = trackRect.width - 2 * (ceil(minThumbRect.width / 2) + minThumbRect.minX - trackRect.minX)
        // 起始X坐标
        let startX = ceil(minThumbRect.width / 2)
        
        drawTrackWith(context: context,
                      trackCenterY: trackRect.midY,
                      startX: startX)
        drawTicksWith(context: context,
                      trackCenterY: trackRect.midY,
                      startX: startX)
    }
    
    private func drawTrackWith(context: CGContext,
                               trackCenterY: CGFloat,
                               startX: CGFloat) {
        guard let usableTrackWidth = usableTrackWidth else {
            return
        }
        
        let trackY = trackCenterY - trackHeight / 2
        let startTarackWidth = usableTrackWidth * CGFloat((value - minimumValue) / (maximumValue - minimumValue))
        
        // 起始段（左边）
        let startRect = CGRect(x: startX,
                               y: trackY,
                               width: startTarackWidth,
                               height: trackHeight)
        let startPath = UIBezierPath(roundedRect: startRect, cornerRadius: 0)
        context.setFillColor(minimumTrackColor.cgColor)
        context.addPath(startPath.cgPath)
        context.fillPath()
        
        // 结尾段（右边）
        let endRect = CGRect(x: startX + startTarackWidth,
                             y: trackY,
                             width: usableTrackWidth - startTarackWidth,
                             height: trackHeight)
        let endPath = UIBezierPath(roundedRect: endRect, cornerRadius: 0)
        context.setFillColor(maximumTrackColor.cgColor)
        context.addPath(endPath.cgPath)
        context.fillPath()
    }

    /// 自定义绘制刻度线和文字
    private func drawTicksWith(context: CGContext,
                               trackCenterY: CGFloat,
                               startX: CGFloat) {
        guard tickItems.count > 0,
              let usableTrackWidth = usableTrackWidth else {
            return
        }
        
        for item in tickItems.filter({ $0.value >= minimumValue && $0.value <= maximumValue}) {
            let x = startX + usableTrackWidth * CGFloat((item.value - minimumValue) / (maximumValue - minimumValue))
            
            // 被划过的显示白色，未划过显示灰色
            let color: UIColor = item.value <= value ? minimumTrackColor : maximumTrackColor
            context.setFillColor(color.cgColor)
            
            // 刻度绘制为柱体 + 上下半圆
            let tickWidth: CGFloat = 3
            let tickHeight: CGFloat = 10

            let rect = CGRect(x: x - tickWidth / 2, y: trackCenterY - tickHeight / 2, width: tickWidth, height: tickHeight)
            let path = UIBezierPath(roundedRect: rect, cornerRadius: tickWidth / 2)
            context.addPath(path.cgPath)
            context.fillPath()
            
            // 标签文字绘制
            if let label = item.label {
                let textSize = label.size(withAttributes: item.labelAttributes)
                var textX = x - textSize.width / 2
                if textX < 0 {
                    textX = 0
                }
                if textX + textSize.width > bounds.width {
                    textX = bounds.width - textSize.width
                }
                let textY = centerY + 18
                label.draw(at: CGPoint(x: textX, y: textY),
                           withAttributes: item.labelAttributes)
            }
        }
    }

    // MARK: - 内存清理
    deinit {
        bubbleView?.removeFromSuperview()
    }
    
    // MARK: - 禁用属性
    @available(*, unavailable, message: "请使用 minimumTrackColor 代替")
    public override var minimumTrackTintColor: UIColor? {
        didSet {}
    }
    
    @available(*, unavailable, message: "请使用 maximumTrackColor 代替")
    public override var maximumTrackTintColor: UIColor? {
        didSet {}
    }
    
    @available(*, unavailable, message: "请使用 maximumTrackColor 代替")
    public override func addTarget(_ target: Any?, action: Selector, for controlEvents: UIControl.Event) {
    }
    
}


/// 吸附类型
public enum WHTickSliderSnapType {
    /// 无效果
    case none
    /// 精度吸附
    case step
    /// 刻度吸附
    case tick
}

/// 震动类型
public enum WHTickSliderFeedbackType {
    /// 无效果
    case none
    /// 精度震动
    case step
    /// 刻度震动
    case tick
}


public struct WHTickItem {
    /// 刻度值
    public let value: Float
    /// 标签（可选）
    private(set) var label: String? = nil
    /// 标签属性
    private(set) var labelAttributes: [NSAttributedString.Key: Any] = [
        .font: UIFont.systemFont(ofSize: 10),
        .foregroundColor: UIColor.systemGray3,
    ]
    
    public init(value: Float) {
        self.value = value
    }
    
    public init(value: Float, label: String?) {
        self.value = value
        self.label = label
    }
    
    public init(value: Float, label: String?, labelAttributes: [NSAttributedString.Key: Any]) {
        self.value = value
        self.label = label
        self.labelAttributes = labelAttributes
    }
}


public protocol WHTickSliderDelegate: NSObjectProtocol {
    
    /// 调用方不需要持有Bubble
    func tickSliderBubbleView() -> WHTickSliderBubble?
    
    func tickSliderDidBegin(_ slider: WHTickSlider)
    func tickSliderValueChanged(_ slider: WHTickSlider)
    func tickSliderDidEnd(_ slider: WHTickSlider)

}

public protocol WHTickSliderBubble where Self: UIView {
    
    /// 设置气泡文案，并返回气泡的整体大小
    func setText(_ text: String?) -> CGSize
    
}
