//
//  WHBaseViewController.swift
//
//  Created by <PERSON> on 2022/2/7.
//

import Foundation

public enum MBNavigationBarStyle {
    case none
    case system
    case custom
}

open class WHBaseViewController: UIViewController,
                                 UIGestureRecognizerDelegate {
    
    /// 导航条风格
    public var navigationBarStyle: MBNavigationBarStyle = .custom

    /// 导航控制器滑动返回，默认为true
    public var panBackEnabled = true
    /// 页面是否已加载
    public private(set) var viewIsLoaded = false
    
    public lazy var myNavigationBar: WHNavigationBar = {
        let navBar = WHNavigationBar()
        navBar.titleColor = .white
        ///根据设计需求统一改成灰色
        navBar.backItemStyle = .white
        navBar.backgroundColor = .black
        if let count = self.navigationController?.viewControllers.count,
           count > 1 {
            /// 导航控制器大于1个页面时
            navBar.backItem?.isHidden = false
        } else if self.presentingViewController != nil {
            /// 模态显示出来的页面
            navBar.backItem?.isHidden = false
        } else {
            navBar.backItem?.isHidden = true
        }
        return navBar
    }()
    
    deinit {
        WHPrint(self, "释放了")
    }
    
    open override func viewDidLoad() {
        super.viewDidLoad()

        setupSubviews()
    }
    
    open override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        if navigationBarStyle == .system {
            self.navigationController?.setNavigationBarHidden(false,
                                                              animated: true)
        } else {
            self.navigationController?.setNavigationBarHidden(true,
                                                              animated: true)
        }
        
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = true
        self.navigationController?.interactivePopGestureRecognizer?.delegate = self
    }
    
    open override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        viewIsLoaded = true
    }
    
    open func setupSubviews() {
        if navigationBarStyle == .custom {
            view.addSubview(self.myNavigationBar)
            myNavigationBar.snp.makeConstraints { (make) in
                make.left.top.right.equalToSuperview()
                make.height.equalTo(WH_NAVIGATION_BAR_HEIGHT)
            }
        }
    }
    
    open override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        if name == WH_NAVIGATION_BACK_EVENT_NAME {
            // 返回事件
            if let navigationController = self.navigationController,
               navigationController.viewControllers.count > 1 {
                navigationController.popViewController(animated: true)
            } else if self.presentingViewController != nil {
                self.dismiss(animated: true) {
                }
            }
        }
    }
    
    open override var preferredStatusBarStyle: UIStatusBarStyle {
        if #available(iOS 13.0, *) {
            return .darkContent
        } else {
            return .default
        }
    }
    
    /// view 是否阻止右滑返回手势。默认读取 blockingNavigationPopGesture 属性
    ///
    /// 无法设置 blockingNavigationPopGesture 的视图，可以重写下边的方法，自定义判断条件。如下：
    ///
    /// override func blockingNavigationPopGesture(_ view: UIView, matching: (UIView) -> Bool) -> Bool {
    ///     return super.blockingNavigationPopGesture(view) { view in
    ///         if view.isKind(of: XXX.self) { // 自定义判断条件
    ///             return true
    ///         }
    ///         return matching(view)
    ///     }
    /// }
    open func blockingNavigationPopGesture(_ view: UIView, matching: (UIView) -> Bool) -> Bool {
        if let closestView = view.closestAncestor(matching: matching) {
            return true
        }
        return false
    }
    
    // MARK: - UIGestureRecognizerDelegate
    open func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        if gestureRecognizer == navigationController?.interactivePopGestureRecognizer {
            guard let count = navigationController?.viewControllers.count,
                  count > 1 else {
                return false
            }
            if panBackEnabled {
                // 获取触摸点在 self.view 上的位置
                let point = gestureRecognizer.location(in: self.view)
                // 递归查找触摸点是否落在指定视图内
                if let hitView = self.view.hitTest(point, with: nil),
                   blockingNavigationPopGesture(hitView, matching: { $0.blockingNavigationPopGesture }) {
                    return false
                }
            }
            return panBackEnabled
        }
        return true
    }
    
    public func showBackButton(isShow: Bool) {
        myNavigationBar.backItem?.isHidden = !isShow
    }
}


private var blockingNavigationPopGestureKey: UInt8 = 0

extension UIView {
    
    /// 是否拦截导航控制器返回手势。默认：false
    public var blockingNavigationPopGesture: Bool {
        set {
            objc_setAssociatedObject(self,
                                     &blockingNavigationPopGestureKey,
                                     NSNumber(value: newValue),
                                     .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            (objc_getAssociatedObject(self, &blockingNavigationPopGestureKey) as? NSNumber)?.boolValue ?? false
        }
    }
    
}
