//
//  WHDeviceSupport.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/4/15.
//

import Foundation

public enum WHDeviceModel_iPhone: String, CaseIterable {
    case iPhone4, iPhone4S, iPhone5, iPhone5<PERSON>, iPhone5S, iPhone6, iPhone6Plus
    case iPhone6S, iPhone6SPlus, iPhone7, iPhone7Plus, iPhoneSE, iPhone8
    case iPhone8Plus, iPhoneX, iPhoneXR, iPhoneXS, iPhoneXSMax, iPhone11
    case iPhone11Pro, iPhone11ProMax, iPhoneSE2, iPhone12Mini, iPhone12
    case iPhone12Pro, iPhone12ProMax, iPhone13Mini, iPhone13, iPhone13Pro
    case iPhone13ProMax, iPhoneSE3, iPhone14, iPhone14Plus, iPhone14Pro
    case iPhone14ProMax, iPhone15, iPhone15Plus, iPhone15Pro, iPhone15ProMax
    case iPhone16, iPhone16Plus, iPhone16Pro, iPhone16ProMax
    
    var identifiers: [String] {
        Self.identifierMap[self] ?? []
    }
    
    private static let identifierMap: [WHDeviceModel_iPhone: [String]] = [
        .iPhone4        : ["iPhone3,1", "iPhone3,2", "iPhone3,3"],
        .iPhone4S       : ["iPhone4,1", "iPhone4,2", "iPhone4,3"],
        .iPhone5        : ["iPhone5,1", "iPhone5,2"],
        .iPhone5C       : ["iPhone5,3", "iPhone5,4"],
        .iPhone5S       : ["iPhone6,1", "iPhone6,2"],
        .iPhone6        : ["iPhone7,2"],
        .iPhone6Plus    : ["iPhone7,1"],
        .iPhone6S       : ["iPhone8,1"],
        .iPhone6SPlus   : ["iPhone8,2"],
        .iPhoneSE       : ["iPhone8,4"],
        .iPhone7        : ["iPhone9,1", "iPhone9,3"],
        .iPhone7Plus    : ["iPhone9,2", "iPhone9,4"],
        .iPhone8        : ["iPhone10,1", "iPhone10,4"],
        .iPhone8Plus    : ["iPhone10,2", "iPhone10,5"],
        .iPhoneX        : ["iPhone10,3", "iPhone10,6"],
        .iPhoneXR       : ["iPhone11,8"],
        .iPhoneXS       : ["iPhone11,2"],
        .iPhoneXSMax    : ["iPhone11,4", "iPhone11,6"],
        .iPhone11       : ["iPhone12,1"],
        .iPhone11Pro    : ["iPhone12,3"],
        .iPhone11ProMax : ["iPhone12,5"],
        .iPhoneSE2      : ["iPhone12,8"],
        .iPhone12Mini   : ["iPhone13,1"],
        .iPhone12       : ["iPhone13,2"],
        .iPhone12Pro    : ["iPhone13,3"],
        .iPhone12ProMax : ["iPhone13,4"],
        .iPhone13Mini   : ["iPhone14,4"],
        .iPhone13       : ["iPhone14,5"],
        .iPhone13Pro    : ["iPhone14,2"],
        .iPhone13ProMax : ["iPhone14,3"],
        .iPhoneSE3      : ["iPhone14,6"],
        .iPhone14       : ["iPhone14,7"],
        .iPhone14Plus   : ["iPhone14,8"],
        .iPhone14Pro    : ["iPhone15,2"],
        .iPhone14ProMax : ["iPhone15,3"],
        .iPhone15       : ["iPhone15,4"],
        .iPhone15Plus   : ["iPhone15,5"],
        .iPhone15Pro    : ["iPhone16,1"],
        .iPhone15ProMax : ["iPhone16,2"],
        .iPhone16       : ["iPhone17,3"],
        .iPhone16Plus   : ["iPhone17,4"],
        .iPhone16Pro    : ["iPhone17,1"],
        .iPhone16ProMax : ["iPhone17,2"]
    ]
}

public typealias WHDeviceCompareBlock = (_ current: Int, _ target: Int) -> Bool

public class WHDeviceSupport {
    
    /// 获取当前设备的型号标识符。eg:  "iPhone11,8"
    public static func getDeviceModelIdentifier() -> String {
        // 创建 utsname 结构体用于接收系统信息
        var systemInfo = utsname()
        // 调用 uname 函数获取系统信息，填充 systemInfo
        uname(&systemInfo)
        // 使用 Swift 的指针语法读取 systemInfo.machine（这是一个 C 字符数组）
        return withUnsafePointer(to: &systemInfo.machine) {
            // 将 machine 字段的指针类型从固定大小数组重绑定为 CChar 指针（C 字符串）
            $0.withMemoryRebound(to: CChar.self, capacity: 1) {
                // 将 C 字符串转换为 Swift 字符串（验证其为合法 UTF-8）
                String(validatingUTF8: $0) ?? "unknown"
            }
        }
    }
    
    /// 获取当前设备的型号
    public static func currentDeviceModel() -> WHDeviceModel_iPhone? {
        let identifier = getDeviceModelIdentifier()
        return WHDeviceModel_iPhone.allCases.first { $0.identifiers.contains(identifier) }
    }
    
    /// 判断当前设备与目标设备的关系。eg: 是否是 iPhone XR 或更早的机型
    public static func compare(_ targetModel: WHDeviceModel_iPhone,
                               compareBlock: WHDeviceCompareBlock) -> Bool {
        guard let model = currentDeviceModel(),
              let current = WHDeviceModel_iPhone.allCases.firstIndex(of: model),
              let target = WHDeviceModel_iPhone.allCases.firstIndex(of: targetModel) else {
            return false
        }
        return compareBlock(current, target)
    }
    
}
