//
//  WHPhotoLibraryUtils.swift
//  WHBaseLibrary
//
//  Created by 王耀 on 2025/8/1.
//

import Foundation
import Photos

public class WHPhotoLibraryUtils {
    
    /// 获取相册中视频资源文件大小（字节数）
    public static func getVideoFileSize(from asset: PHAsset, completion: @escaping (Int64?) -> Void) {
        // 视频资源类型是 `.video` 或 `.pairedVideo`（Live Photo）
        let videoResources = PHAssetResource.assetResources(for: asset).filter {
            $0.type == .video || $0.type == .pairedVideo
        }
        guard let videoResource = videoResources.first else {
            completion(nil)
            return
        }
        // 创建一个空目标，拿到 fileSize
        var fileSize: Int64 = 0

        let options = PHAssetResourceRequestOptions()
        options.isNetworkAccessAllowed = true

        PHAssetResourceManager.default().requestData(for: videoResource, options: options, dataReceivedHandler: { data in
            fileSize += Int64(data.count)
        }, completionHandler: { error in
            if let error = error {
                WHPrint("获取视频大小失败: \(error.localizedDescription)")
                completion(nil)
            } else {
                completion(fileSize)
            }
        })
    }
}
