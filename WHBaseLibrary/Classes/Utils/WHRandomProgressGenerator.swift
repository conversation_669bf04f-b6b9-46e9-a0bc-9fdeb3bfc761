//
//  WHRandomProgressGenerator.swift
//  WHBaseLibrary
//
//  Created by 王耀 on 2025/9/16.
//

import Foundation

public class WHRandomProgressGenerator {
    
    public static let shared = WHRandomProgressGenerator()
    
    private var timer: DispatchSourceTimer?
    private var duration: Int = 0
    private var currentSecond: Int = 0

    public let startProgress: Double = 0.50
    public let endProgress: Double = 0.99

    private var power: Double = 2.5                      // 每次调用随机化
    private let jitterRange: ClosedRange<Double> = -0.015...0.015
    private var lastProgress: Double = 0.50

    /// 开始生成进度（每秒回调一次）
    /// - Parameters:
    ///   - duration: 持续秒数（>=1）
    ///   - onUpdate: 每秒回调（在主线程）
    public func start(duration: Int, onUpdate: @escaping (Double) -> Void) {
        stop() // 先停止已有任务

        guard duration >= 1 else {
            onUpdate(endProgress)
            return
        }

        self.duration = duration
        self.currentSecond = 0
        self.lastProgress = startProgress

        // 每次调用随机化 power（控制曲线形状），从而每次曲线不同
        self.power = Double.random(in: 1.5...4.0)

        let queue = DispatchQueue(label: "com.example.progressgenerator", qos: .userInitiated)
        let timer = DispatchSource.makeTimerSource(queue: queue)
        // 第一次在 1 秒后触发（每秒一次）
        timer.schedule(deadline: .now() + 1.0, repeating: 1.0, leeway: .milliseconds(100))

        timer.setEventHandler { [weak self] in
            guard let self = self else { return }

            self.currentSecond += 1
            let sec = self.currentSecond

            // 最后一秒：强制返回 99% 并结束
            if sec >= self.duration {
                DispatchQueue.main.async {
                    onUpdate(self.endProgress)
                }
                self.stop()
                return
            }

            // 计算当前进度（非匀速，带轻微抖动），并保证单调不减
            let progress = self.progressAt(second: sec)
            let clamped = min(max(progress, self.lastProgress), self.endProgress)
            self.lastProgress = clamped

            DispatchQueue.main.async {
                onUpdate(clamped)
            }
        }

        self.timer = timer
        timer.resume()
    }

    /// 停止计时器（主动取消）
    public func stop() {
        if let t = timer {
            t.setEventHandler {}
            t.cancel()
            // 如果是已 resume 的 DispatchSourceTimer，cancel 后仍要置 nil
            timer = nil
        }
    }

    private func progressAt(second: Int) -> Double {
        // 归一化时间 x in (0,1]
        let x = Double(second) / Double(max(1, duration))
        // easing: factor = 1 - (1 - x)^power
        var factor = 1.0 - pow(1.0 - x, power)

        // 加一些小抖动以便每次调用结果不同（但以后有单调保护）
        factor += Double.random(in: jitterRange)

        factor = min(max(factor, 0.0), 1.0)
        return startProgress + (endProgress - startProgress) * factor
    }

    deinit {
        stop()
    }
}
