//
//  WHRect.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/7/8.
//

import Foundation

public struct WHRect {
    
    public let center: CGPoint
    public let size: CGSize
    
    public init(center: CGPoint,
                size: CGSize) {
        self.center = center
        self.size = size
    }
    
    public func toCGRect() -> CGRect {
        return CGRect(origin: CGPoint(x: center.x - size.width / 2,
                                      y: center.y - size.height / 2),
                      size: size)
    }
}
