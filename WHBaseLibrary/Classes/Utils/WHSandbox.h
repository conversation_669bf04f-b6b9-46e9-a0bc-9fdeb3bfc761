//
//  QdSandbox.h
//  QdIOSSDK
//
//  Created by 王耀 on 2022/5/27.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface WHSandbox : NSObject

//以下均为系统文件夹
+ (NSString *)homePath;
+ (NSString *)documentPath;
+ (NSString *)libraryPath;
+ (NSString *)tempPath;
+ (NSString *)cachePath;

/// 在folderPath创建一个文件夹
/// @param folderPath 文件夹路径
+ (BOOL)createFolderAtPath:(NSString *)folderPath;

/// 将指定路径的文件设置为跳过iCloud云备份
/// @param fileURL 文件路径URL
+ (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)fileURL;

/// 对指定路径的文件设置自定义的文件属性
/// @param filePath 文件路径
/// @param key 键值名称
/// @param value 值
/// @return 设置成功返回YES，否则返回NO
+ (BOOL)setCustomAttributeForFileAtPath:(NSString *)filePath key:(NSString *)key value:(NSString *)value;

/// 获取自定义的文件属性
/// @param filePath 文件路径
/// @param key 键值名称
/// @return 如果指定的key名不存在，返回nil，否则返回其值
+ (nullable NSString *)customAttributeForItemAtPath:(NSString *)filePath key:(NSString *)key;

/// 获取文件夹大小
/// @param folderPath 文件夹路径
/// @attention if the 'folderPath' is not a directory,this method return 0.
+ (NSUInteger)folderSizeAtPath:(NSString *)folderPath;

/// 获取文件大小（支持文件或文件夹）
/// @param filePath 文件路径
+ (NSUInteger)fileSizeAtPath:(NSString *)filePath;

@end

NS_ASSUME_NONNULL_END
