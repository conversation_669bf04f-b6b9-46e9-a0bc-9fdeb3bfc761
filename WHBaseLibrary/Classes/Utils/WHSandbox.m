//
//  QdSandbox.m
//  QdIOSSDK
//
//  Created by 王耀 on 2022/5/27.
//

#import "WHSandbox.h"
#import <sys/xattr.h>
#import <dirent.h>
#import <sys/stat.h>

static NSString *kWHFileExtendedAttributesKey = @"kWHFileExtendedAttributesKey";

@implementation WHSandbox

+ (NSString *)homePath
{
    return NSHomeDirectory();
}

+ (NSString *)libraryPath
{
    NSArray *array = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    return [array firstObject];
}

+ (NSString *)documentPath
{
    NSArray *array = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return [array firstObject];
}

+ (NSString *)tempPath
{
    return NSTemporaryDirectory();
}

+ (NSString *)cachePath
{
    NSArray *array = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    return [array firstObject];
}

+ (BOOL)createFolderAtPath:(NSString *)path
{
    BOOL isDir;
    if (![[NSFileManager defaultManager] fileExistsAtPath:path isDirectory:&isDir]) {
        NSError *error = nil;
        BOOL isCreate = [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:YES attributes:@{} error:&error];
        if (isCreate) {
            [self addSkipBackupAttributeToItemAtURL:[NSURL fileURLWithPath:path]];
        }
        return isCreate;
    }
    
    return isDir;
}

+ (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)fileURL
{
    if (![[NSFileManager defaultManager] fileExistsAtPath:[fileURL path]]) {
        return NO;
    }
    NSError *error = nil;
    BOOL success = [fileURL setResourceValue:@(YES) forKey:NSURLIsExcludedFromBackupKey error:&error];
#if DEBUG
    if (!success) {
        NSLog(@"QDBase", @"Exclude %@ from backup error:%@", fileURL, error);
    }
#endif
    return success;
}

+ (BOOL)setCustomAttributeForFileAtPath:(NSString *)filePath key:(NSString *)key value:(NSString *)value
{
    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return NO;
    }
//    ssize_t writeLen = setxattr([filePath UTF8String], [key UTF8String], [value UTF8String], value.length, 0, 0);
//    return (writeLen == 0);
    
    NSError *error = nil;
    NSData *data = [value dataUsingEncoding:NSUTF8StringEncoding];
    //以下写法，并不会每次都覆盖kMTCPFileExtendedAttributesKey对应的值，如果key不同，为增加，如果相同，仅覆盖key的值。
    BOOL success = [[NSFileManager defaultManager] setAttributes:@{
        kWHFileExtendedAttributesKey : @{
                key : data
        }
    } ofItemAtPath:filePath error:&error];
    if (!success) {
        NSLog(@"[WHBaseLibrary], set attributes failed, Error->%@, file->%@", error, filePath);
    }
    return success;
}

+ (NSString *)customAttributeForItemAtPath:(NSString *)filePath key:(NSString *)key
{
    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return nil;
    }
//    ssize_t valueLength = getxattr([filePath UTF8String], [key UTF8String], NULL, 0, 0, 0);
//    if (valueLength < 0) {
//        return nil;
//    }
//    unsigned char buffer[valueLength];
//    getxattr([filePath fileSystemRepresentation], [key UTF8String], buffer, valueLength, 0, 0);
//    NSData *data = [NSData dataWithBytes:buffer length:valueLength];
//    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
//    return string;
    
    NSError *error = nil;
    NSDictionary *attribtues = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:&error];
    if (!attribtues) {
        NSLog(@"[WHBaseLibrary], read file attributes failed, error->%@", error);
        return nil;
    }
    NSDictionary *extendedAttributes = attribtues[kWHFileExtendedAttributesKey];
    if (!extendedAttributes) {
        return nil;
    }
    NSData *data = extendedAttributes[key];
    if (!data || ![data isKindOfClass:[NSData class]]) {
        return nil;
    }
    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    return string;
}

+ (NSUInteger)folderSizeAtPath:(NSString *)folderPath
{
    BOOL isDir = NO;
    if (![[NSFileManager defaultManager] fileExistsAtPath:folderPath isDirectory:&isDir] || !isDir) {
        return 0;
    }
    NSError *error = nil;
    NSArray *contents = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:folderPath error:&error];
    if (!contents) {
        NSLog(@"[WHBaseLibrary], read content of directory failed, error->%@", error);
        return 0;
    }
    NSUInteger totalSize = 0;
    for (NSString *subPath in contents) {
        NSString *fullPath = [folderPath stringByAppendingPathComponent:subPath];
        NSError *error = nil;
        NSDictionary *attribbutes = [[NSFileManager defaultManager] attributesOfItemAtPath:fullPath error:&error];
        if (!attribbutes) {
            NSLog(@"[WHBaseLibrary], read attributes for file failed, file->%@, error->%@", fullPath, error);
            continue;
        }
        NSString *fileType = attribbutes[NSFileType];
        if ([fileType isEqualToString:NSFileTypeRegular]) {
            totalSize += [attribbutes[NSFileSize] unsignedIntegerValue];
        } else if ([fileType isEqualToString:NSFileTypeDirectory]) {
            totalSize += [attribbutes[NSFileSize] unsignedIntegerValue]; //加当前文件夹占用大小
            totalSize += [self folderSizeAtPath:fullPath];
        }
    }
    return totalSize;
}

+ (NSUInteger)fileSizeAtPath:(NSString *)filePath
{
    BOOL isDir = YES;
    if (![[NSFileManager defaultManager] fileExistsAtPath:filePath isDirectory:&isDir]) {
        return 0;
    }
    if (isDir) {
        return [self folderSizeAtPath:filePath];
    }
    NSError *error = nil;
    NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:filePath error:&error];
    if (!attributes) {
        NSLog(@"[WHBaseLibrary], read attribute for file failed. file->%@, error->%@", filePath, error);
        return 0;
    }
    return [attributes[NSFileSize] unsignedIntegerValue];
}
@end
