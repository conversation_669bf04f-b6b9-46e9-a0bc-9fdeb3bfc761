//
//  WHSandboxFileManager.swift
//  WHBaseLibrary
//
//  Created by <PERSON> on 2025/7/3.
//

import Foundation
import Photos

public let WHSandboxFileManagerer = WHSandboxFileManager.shared
public class WHSandboxFileManager {
    public static let shared = WHSandboxFileManager()
    /// 用于文件操作的自定义串行队列，确保线程安全
    public let fileOperationQueue = DispatchQueue(label: "com.whee.fileoperations", qos: .utility)
    
    /// 删除沙盒中的文件或目录
    /// - Parameters:
    ///   - path: 文件/目录路径
    ///   - completion: 完成回调 (success: 是否成功, error: 错误信息)
    public func deleteItem(atPath path: String, completion: @escaping (Bool, Error?) -> Void) {
        // 1. 检查路径是否为空
        guard !path.isEmpty else {
            DispatchQueue.main.async {
                let error = NSError(
                    domain: "SandboxError",
                    code: 1001,
                    userInfo: [NSLocalizedDescriptionKey: "路径不能为空"]
                )
                completion(false, error)
            }
            return
        }
        
        // 2. 转换为URL并验证路径格式
        guard let url = validSandboxURL(from: path) else {
            DispatchQueue.main.async {
                let error = NSError(
                    domain: "SandboxError",
                    code: 1002,
                    userInfo: [NSLocalizedDescriptionKey: "无效的路径格式: \(path)"]
                )
                completion(false, error)
            }
            return
        }
        
        // 3. 在专用队列中执行文件操作
        fileOperationQueue.async {
            do {
                // 4. 检查文件是否存在
                guard FileManager.default.fileExists(atPath: url.path) else {
                    DispatchQueue.main.async {
                        completion(true, nil) // 文件不存在视为删除成功
                    }
                    return
                }
                
                // 5. 验证路径是否在沙盒内
                guard self.isPathInSandbox(url.path) else {
                    DispatchQueue.main.async {
                        let error = NSError(
                            domain: "SandboxError",
                            code: 1003,
                            userInfo: [NSLocalizedDescriptionKey: "路径不在沙盒范围内: \(url.path)"]
                        )
                        completion(false, error)
                    }
                    return
                }
                
                // 6. 执行删除操作
                try FileManager.default.removeItem(at: url)
                
                // 7. 验证删除结果
                if FileManager.default.fileExists(atPath: url.path) {
                    throw NSError(
                        domain: "SandboxError",
                        code: 1005,
                        userInfo: [NSLocalizedDescriptionKey: "删除操作未完成，文件仍然存在"]
                    )
                }
                
                DispatchQueue.main.async {
                    completion(true, nil)
                }
            } catch {
                DispatchQueue.main.async {
                    completion(false, error)
                }
            }
        }
    }
    
    /// 验证路径是否在应用沙盒内
    public func isPathInSandbox(_ path: String) -> Bool {
        let sandboxRoot = NSHomeDirectory()
        let standardizedPath = (path as NSString).standardizingPath
        
        // 确保路径在沙盒内且不是根目录
        return standardizedPath.hasPrefix(sandboxRoot) &&
               standardizedPath.count > sandboxRoot.count
    }
    
    /// 从路径字符串创建有效的沙盒URL
    public func validSandboxURL(from path: String) -> URL? {
        // 处理可能的文件URL格式
        let cleanPath: String
        if path.hasPrefix("file://") {
            cleanPath = String(path.dropFirst(7))
        } else {
            cleanPath = path
        }
        
        // 创建URL并标准化
        let url = URL(fileURLWithPath: cleanPath).standardized
        
        // 验证路径格式
        guard !url.path.isEmpty, url.isFileURL else {
            return nil
        }
        
        return url
    }
    /// 获取设备有价值的剩余空间
    public static func getAvailableSpaceInMB() -> Int64? {
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory() as String),
              var space = (systemAttributes[FileAttributeKey.systemFreeSize] as? NSNumber)?.int64Value else { return nil }
        space = space / (1024 * 1024)
        return space
    }
    /// 计算本地路径组总文件大小
    public static func allFilesSize(videoPaths: [String]) -> Int64 {
        var clipSize:Int64 = 0
        videoPaths.forEach { path in
            clipSize += self.fileSizeInMB(path: path)
        }
        return clipSize
    }
    /// 计算路径对应文件大小
    private static func fileSizeInMB(path: String) -> Int64 {
        let url = NSURL(fileURLWithPath: path)
        guard let values = try? url.resourceValues(forKeys: [.fileSizeKey]), var space = (values[.fileSizeKey] as? NSNumber)?.int64Value else {
            return 0
        }
        space = space / (1024 * 1024)
        return space
    }
    /// 获取本地视频路径（授权的）
    public static func getOriginalVideoFromPhotoLibrary(asset: PHAsset,
                                           completion: @escaping (URL?) -> Void) {
        let options = PHVideoRequestOptions()
        options.version = .current
        options.isNetworkAccessAllowed = true
        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
            guard let urlAsset = avAsset as? AVURLAsset else {
                WHPrint("获取 AVURLAsset 失败")
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }
            let sourceURL = urlAsset.url
            DispatchQueue.main.async {
                completion(sourceURL)
            }
        }
    }
    
    /// 将 UIImage 保存到指定目录
    /// - Parameters:
    ///   - image: 要保存的图片
    ///   - directoryPath: 目标文件夹路径（如果不存在会自动创建）
    ///   - fileName: 可选的文件名（不传则自动生成带时间戳的文件名）
    ///   - compressionQuality: JPEG 压缩率（仅 JPEG 格式时有效）
    ///   - completion: 完成回调，返回最终路径或错误
    public static func saveUIImageToDirectory(image: UIImage,
                                directoryPath: String?,
                                fileName: String? = nil,
                                compressionQuality: CGFloat = 0.99,
                                completion: @escaping (String?, Error?) -> Void) {
        
        // 判断目标目录
        var targetDirectory: URL
        if let dir = directoryPath, !dir.trimmingCharacters(in: .whitespaces).isEmpty {
            targetDirectory = URL(fileURLWithPath: dir)
        } else {
            // 如果没传，默认存到临时目录
            targetDirectory = URL(fileURLWithPath: NSTemporaryDirectory())
        }
        
        // 检查目录是否可用，否则回退到临时目录
        do {
            try FileManager.default.createDirectory(at: targetDirectory, withIntermediateDirectories: true, attributes: nil)
        } catch {
            print("⚠️ 无法创建目录，回退到临时目录: \(error.localizedDescription)")
            targetDirectory = URL(fileURLWithPath: NSTemporaryDirectory())
        }
        
        // 自动生成文件名（如果没有传入）
        let defaultName = "image_\(Int(Date().timeIntervalSince1970))"
        let providedName = fileName?.trimmingCharacters(in: .whitespacesAndNewlines)
        var finalFileName = providedName?.isEmpty == false ? providedName! : defaultName
        
        // 获取扩展名并自动修正
        let hasAlpha = imageHasAlpha(image)
        let lowerExt = finalFileName.split(separator: ".").last?.lowercased()
        let isPNGFromName = lowerExt == "png"
        let shouldUsePNG = isPNGFromName || (lowerExt == nil && hasAlpha)
        
        // 如果没带扩展名，自动补上
        if lowerExt == nil {
            finalFileName += shouldUsePNG ? ".png" : ".jpg"
        } else {
            // 如果扩展名和图片类型不匹配，自动修正
            if shouldUsePNG && lowerExt != "png" {
                finalFileName = finalFileName.replacingOccurrences(of: ".\(lowerExt!)", with: ".png")
            } else if !shouldUsePNG && lowerExt != "jpg" && lowerExt != "jpeg" {
                finalFileName = finalFileName.replacingOccurrences(of: ".\(lowerExt!)", with: ".jpg")
            }
        }
        
        // 生成最终路径
        let targetURL = targetDirectory.appendingPathComponent(finalFileName)
        
        // 生成图片数据
        let imageData = shouldUsePNG ? image.pngData() : image.jpegData(compressionQuality: compressionQuality)
        
        guard let data = imageData else {
            let error = NSError(domain: "ImageSaveError", code: -1,
                                userInfo: [NSLocalizedDescriptionKey: "无法生成图片数据"])
            completion(nil, error)
            return
        }
        
        // 异步写入
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 如果文件已存在，先删除
                if FileManager.default.fileExists(atPath: targetURL.path) {
                    try FileManager.default.removeItem(at: targetURL)
                }
                
                try data.write(to: targetURL, options: .atomic)
                
                DispatchQueue.main.async {
                    WHPrint("WHSandboxFileManager:图片保存成功: \(targetURL.path)")
                    completion(targetURL.path, nil)
                }
            } catch {
                DispatchQueue.main.async {
                    WHPrint("WHSandboxFileManager:图片保存失败: \(error.localizedDescription)")
                    completion(nil, error)
                }
            }
        }
    }

    /// 判断图片是否含有透明通道
    public static func imageHasAlpha(_ image: UIImage) -> Bool {
        guard let alpha = image.cgImage?.alphaInfo else { return false }
        return alpha == .first || alpha == .last || alpha == .premultipliedFirst || alpha == .premultipliedLast
    }

    /// 清理文件名非法字符
    private static func sanitizeFileName(_ name: String) -> String {
        let invalidChars = CharacterSet(charactersIn: "\\/:*?\"<>|")
        let cleanName = name.components(separatedBy: invalidChars).joined()
        return cleanName.isEmpty ? "image.jpg" : cleanName
    }
}

