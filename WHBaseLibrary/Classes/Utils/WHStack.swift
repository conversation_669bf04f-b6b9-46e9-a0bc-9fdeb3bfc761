//
//  MIStack.swift
//  MIBaseLibrary
//
//  Created by <PERSON> on 2023/2/15.
//

import Foundation

public struct WHStack<T>: CustomStringConvertible {
    
    private var elements: [T] = []
    
    public var description: String {
        return "TOP->\(elements.reversed())<-BOTTOM"
    }
    
    /// 栈顶
    public var top: T? {
        elements.last
    }
    
    /// 次顶
    public var subTop: T? {
        guard elements.count - 2 >= 0 else {
            return nil
        }
        return elements[elements.count - 2]
    }
    
    /// 栈底
    public var bottom: T? {
        elements.first
    }
    
    /// 长度
    public var length: Int {
        elements.count
    }
    
    /// 是否为空
    public var isEmpty: Bool {
        elements.isEmpty
    }
    
    public init() {}
    
    /// 入栈
    public mutating func push(_ element: T) {
        elements.append(element)
    }
    
    /// 出栈
    public mutating func pop() -> T? {
        elements.popLast()
    }
    
    /// 清空栈
    public mutating func clear() {
        elements.removeAll()
    }
    
}
