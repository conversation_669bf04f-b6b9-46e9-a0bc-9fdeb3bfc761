//
//  WHTimerProxy.swift
//  WHBaseLibrary
//
//  Created by Devin on 2025/7/14.
//

import Foundation

/// 定时器代理类。用于打破使用定时器时的循环引用。
public class WHTimerProxy: NSObject {
    
    private weak var target: AnyObject?
    
    deinit {
        WHPrint(self, "释放了...")
    }

    public init(target: AnyObject) {
        self.target = target
    }

    public override func responds(to aSelector: Selector!) -> <PERSON><PERSON> {
        return target?.responds(to: aSelector) ?? false
    }

    public override func forwardingTarget(for aSelector: Selector!) -> Any? {
        return target
    }
    
}
