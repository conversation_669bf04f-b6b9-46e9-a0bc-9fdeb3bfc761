#
# Be sure to run `pod lib lint WHBusinessCommon.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'WHBusinessCommon'
  s.version          = '2.7.2'
  s.summary          = 'A short description of WHBusinessCommon.'

  s.description      = <<-DESC
TODO: Add long description of the pod here.
                       DESC

  s.homepage         = '*********************:miraclevision-whee/WHBusinessCommon.git'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'xiaoqi' => '<EMAIL>' }
  s.source           = { :git => '*********************:miraclevision-whee/WHBusinessCommon.git', :tag => s.version.to_s }

  s.ios.deployment_target = '12.0'
  
  s.swift_versions = '5.3'
  s.requires_arc = true
  s.static_framework = true
  
  s.resource_bundle = {
    'WHBusinessCommon' => ['WHBusinessCommon/Assets/**/*',
                           'WHBusinessCommon/Resource/*.{png,jpg,gif,otf,json,webp}',
    ]
  }
  
  s.pod_target_xcconfig = {
    'SWIFT_COMPILATION_MODE' => 'wholemodule',
    'OTHER_SWIFT_FLAGS' => '-Xcc -Wno-error=non-modular-include-in-framework-module',
    'CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES' => 'YES'
  }

  s.source_files = 'WHBusinessCommon/Classes/**/*'
  
  s.subspec 'Base' do |base|
    base.source_files = 'WHBusinessCommon/Classes/Base/**/*'
  end
  
  s.subspec 'Module' do |m|
    m.source_files = 'WHBusinessCommon/Classes/Module/**/*'
  end
  
  s.dependency 'WHBaseLibrary'
  s.dependency 'ObjectMapperAdditions/Core'
  s.dependency 'MTAnalytics'
  s.dependency 'MTAccount'
  s.dependency 'MTPullToRefresh'
  s.dependency 'lottie-ios'
  s.dependency 'YYModel'
  s.dependency 'YYCache'
  s.dependency 'YYText'
  s.dependency 'MTIAPSDK'
  s.dependency 'MTAPM'
  s.dependency 'MTSSOShareKit'
  s.dependency 'MTPushNotification'
  s.dependency 'MTPhotoLibrary'
  s.dependency 'MTWebKit/Core'
  s.dependency 'MTWebKit/Upload'
  s.dependency 'MTWebKit/Request'
  s.dependency 'MTMFFPlayer'
  
end
