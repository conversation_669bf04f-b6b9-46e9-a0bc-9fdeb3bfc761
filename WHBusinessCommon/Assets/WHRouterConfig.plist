<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>app</key>
	<dict>
		<key>url</key>
		<string>mine</string>
		<key>controllerName</key>
		<string></string>
		<key>responderName</key>
		<string></string>
		<key>openMode</key>
		<string>push</string>
		<key>subResponder</key>
		<dict>
			<key>live_photo_detail</key>
			<dict>
				<key>url</key>
				<string>app/live_photo_detail</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAILiveDetailResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>live_photo</key>
			<dict>
				<key>url</key>
				<string>app/live_photo </string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAILiveResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>about_us</key>
			<dict>
				<key>url</key>
				<string>app/about_us</string>
				<key>controllerName</key>
				<string>MeituWhee.WHSettingAboutUsViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>limit_manage</key>
			<dict>
				<key>url</key>
				<string>app/limit_manage</string>
				<key>controllerName</key>
				<string>MeituWhee.WHAuthorityManageViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>home</key>
			<dict>
				<key>url</key>
				<string>app/home</string>
				<key>controllerName</key>
				<string>MeituWhee.WHHomeFlowViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterTabResponder</string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>detail</key>
			<dict>
				<key>url</key>
				<string>app/detail</string>
				<key>controllerName</key>
				<string>MeituWhee.WHHomeFlowViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterAPPDetailResponder</string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>inspiration</key>
			<dict>
				<key>url</key>
				<string>app/inspiration</string>
				<key>controllerName</key>
				<string>MeituWhee.WHInspirationViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterTabResponder</string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>mine</key>
			<dict>
				<key>url</key>
				<string>app/mine</string>
				<key>controllerName</key>
				<string>MeituWhee.WHMineViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterTabResponder</string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>setting</key>
			<dict>
				<key>url</key>
				<string>app/setting</string>
				<key>controllerName</key>
				<string>MeituWhee.WHSettingViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>textimageDetail</key>
			<dict>
				<key>url</key>
				<string>app/textimageDetail</string>
				<key>controllerName</key>
				<string>MeituWhee.WHToImageDetailViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>modelDetail</key>
			<dict>
				<key>url</key>
				<string>app/modelDetail</string>
				<key>controllerName</key>
				<string>MeituWhee.WHModelDetailViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
			</dict>
			<key>account_security</key>
			<dict>
				<key>url</key>
				<string>app/account_security</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterComResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>good_review</key>
			<dict>
				<key>url</key>
				<string>app/good_review</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterComResponder</string>
				<key>openMode</key>
				<string></string>
			</dict>
			<key>album_miniapp</key>
			<dict>
				<key>url</key>
				<string>app/album_miniapp</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterAlbumMiniAppResponder</string>
				<key>openMode</key>
				<string></string>
			</dict>
			<key>image_part</key>
			<dict>
				<key>url</key>
				<string>app/image_part</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterInpaintingResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_erase</key>
			<dict>
				<key>url</key>
				<string>app/ai_erase</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAIClearResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_video</key>
			<dict>
				<key>url</key>
				<string>app/ai_video</string>
				<key>controllerName</key>
				<string>MeituWhee.WHAICreateVideoViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>mediaEditEntrance</key>
			<dict>
				<key>url</key>
				<string>app/mediaEditEntrance</string>
				<key>controllerName</key>
				<string>MeituWhee.WHMediaEditEntranceViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>text2img</key>
			<dict>
				<key>url</key>
				<string>app/text2img</string>
				<key>controllerName</key>
				<string>MeituWhee.WHTextToImageViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string>push</string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>img2img</key>
			<dict>
				<key>url</key>
				<string>app/img2img</string>
				<key>controllerName</key>
				<string>MeituWhee.WHImageToImageViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>task_center</key>
			<dict>
				<key>url</key>
				<string>app/task_center</string>
				<key>controllerName</key>
				<string>MeituWhee.WHRewardsViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_history</key>
			<dict>
				<key>url</key>
				<string>app/ai_history</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterHistoryResponder</string>
				<key>openMode</key>
				<string>push</string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_editor</key>
			<dict>
				<key>url</key>
				<string>app/ai_editor</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHEditorModule.WHRouterEditorResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_introduce</key>
			<dict>
				<key>url</key>
				<string>app/ai_introduce</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHAIIntroduceResponder</string>
				<key>openMode</key>
				<string></string>
			</dict>
			<key>album_web</key>
			<dict>
				<key>url</key>
				<string>app/album_web</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterAlbumWebResponder</string>
				<key>openMode</key>
				<string></string>
			</dict>
			<key>ai_upscaler</key>
			<dict>
				<key>url</key>
				<string>app/ai_upscaler</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAIUpScalerResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_expand</key>
			<dict>
				<key>url</key>
				<string>app/ai_expand</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAIExpandResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>video_hd_detail</key>
			<dict>
				<key>url</key>
				<string>app/video_hd_detail</string>
				<key>controllerName</key>
				<string>MeituWhee.WHVideoHudDetailViewController</string>
				<key>responderName</key>
				<string></string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>one_sentence</key>
			<dict>
				<key>url</key>
				<string>app/one_sentence</string>
				<key>controllerName</key>
				<string>MeituWhee.WHOneSentenceViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterOneSentenceResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>ai_upscalerDetail</key>
			<dict>
				<key>url</key>
				<string>ai_upscalerDetail</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHRouterAIUpScalerDetailResponder</string>
				<key>openMode</key>
				<string></string>
				<key>needsLogin</key>
				<string>1</string>
			</dict>
			<key>toast</key>
			<dict>
				<key>url</key>
				<string>toast</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHCommonToastAlertResponder</string>
			</dict>
			<key>ai_cutout</key>
			<dict>
				<key>url</key>
				<string>ai_cutout</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHSmartCutoutRouterResponder</string>
			</dict>
			<key>sop_pre</key>
			<dict>
				<key>url</key>
				<string>app/sop_pre</string>
				<key>controllerName</key>
				<string>WHBusinessCommon.WHSOPPreViewController</string>
				<key>responderName</key>
				<string></string>
			</dict>
			<key>ai_flow</key>
			<dict>
				<key>url</key>
				<string>app/ai_flow</string>
				<key>controllerName</key>
				<string>WHViewController</string>
				<key>responderName</key>
				<string>WHBusinessCommon.WHRouterSopResponder</string>
			</dict>
			<key>feed_detail</key>
			<dict>
				<key>url</key>
				<string>feed_detail</string>
				<key>controllerName</key>
				<string>MeituWhee.WHHomeFullScreenViewController</string>
				<key>responderName</key>
				<string>MeituWhee.WHHomeFullScreenResponder</string>
				<key>openMode</key>
				<string>push</string>
			</dict>
		</dict>
	</dict>
	<key>miniapp</key>
	<dict>
		<key>url</key>
		<string>miniapp</string>
		<key>controllerName</key>
		<string></string>
		<key>responderName</key>
		<string>WHBusinessCommon.WHRouterMiniResponder</string>
		<key>openMode</key>
		<string>push</string>
	</dict>
	<key>openMiniApp</key>
	<dict>
		<key>url</key>
		<string>openMiniApp</string>
		<key>controllerName</key>
		<string></string>
		<key>responderName</key>
		<string>WHBusinessCommon.WHRouterMiniResponder</string>
		<key>openMode</key>
		<string>push</string>
	</dict>
	<key>web</key>
	<dict>
		<key>url</key>
		<string>web</string>
		<key>controllerName</key>
		<string>MeituWhee.WHWebViewController</string>
		<key>responderName</key>
		<string></string>
		<key>openMode</key>
		<string>push</string>
	</dict>
</dict>
</plist>
