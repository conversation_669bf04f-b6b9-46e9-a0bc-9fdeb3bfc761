//
//  WHConfigItem.swift
//  MTXX
//
//  Copyright © 2020 Meitu. All rights reserved.
//

import Foundation
import ObjectMapper
import ObjectMapperAdditions

public struct WHConfigCenterItem<T>: Mappable {
    public init?(map: Map) {
        
    }
    
    public mutating func mapping(map: Map) {
        self.key <- map["key"]
        self.group <- map["group"]
        self.value <- (map["value"], TypeCastTransform<T>())
    }
    
    public init() {
        
    }
    
    init(key: String, group: WHConfigCenterGroup = .defaultName) {
        self.key = key
        self.group = group
    }
    public var key: String = ""
    public var group: WHConfigCenterGroup = .defaultName
    public var value: T?
    
    public static func item(for key: String, group: WHConfigCenterGroup = .defaultName, value: Any? = nil) -> Self? {
        let dict = self.dictForValue(key: key, group: group, value: value)
        
        guard let config = self.init(JSON: dict) else {
            return nil
        }
        return config
    }
    
    public static func dictForValue(key: String, group: WHConfigCenterGroup = .defaultName, value: Any? = nil) -> [String: Any] {
        var dict: [String: Any] = ["key": key, "group": group.rawValue]
        if let v = value {
            dict["value"] = v
        }
        return dict
    }
}

