//
//  WHConfigCenterManager.swift
//  MTXX
//
//  Copyright © 2020 Meitu. All rights reserved.
//

import Foundation
import ObjectMapper
import WHBaseLibrary

public extension NSNotification.Name {
    static let WHConfigCenterDataDidChange = Notification.Name("WHConfigCenterDataDidChange")
}

private let kWHConfigCenterCacheKey = "kWHConfigCenterCacheKey"

public enum WHConfigCenterGroup: String {
    case defaultName
}

//private let configAPI: MTNetworkRequestAPI = MTNetworkRequestAPI(path: "v1/common/config.json", method: .get, parameters: ["random": Int(arc4random() % 100 + 1)], callInMainQueue: false)

/// group, key, value
public typealias WHConfigCenterDidChangeBlock = (String, WHConfigCenterGroup, AnyHashable?) -> Void

@objc
final public class WHConfigCenterManager: NSObject {
    
    @objc public static let share = WHConfigCenterManager()
    
    private var registerConfigs: [String: WHConfigCenterDidChangeBlock] = [:]
    
    typealias MTConfigDicType = [AnyHashable: AnyHashable]
    private var configDic: MTConfigDicType = [:]
    
    static var hasRequestData = false
    //    配置中心随机值参数
    private let configCenterRandom = Int(arc4random() % 100 + 1)
    
    private var lock = DispatchSemaphore(value: 1)
    // 查询类型：1启动；2后台唤醒到前台；3正常轮询；0表示其它原因查询，配合fields使用。
    private var status: Int = 1

    required override init() {
        super.init()
        self.loadCacheConfig()
        self.requestData()
        NotificationCenter.default.addObserver(forName: UIApplication.willEnterForegroundNotification, object: nil, queue: nil) { [weak self](noti) in
            self?.status = 2
            self?.requestData()
        }
        NotificationCenter.default.addObserver(self, selector: #selector(networkDidChange), name: .KMINetworkDidChange, object: nil)

        WHNetworkChangeMonitorDefault.startListener()
    }
    
    @objc static public func setup() {
        _ = WHConfigCenterManager.share
    }
    
    @objc func networkDidChange() {
        if WHNetworkChangeMonitorDefault.currentNetwork != .notReachable && WHAppInfo.isAppNewInstalled {
            self.reload()
        }
    }
    
    @objc public func reload() {
        WHConfigCenterManager.hasRequestData = true
        requestData()
    }
    
    @objc
    private func requestData() {
        var params: [String: Any] = [:]
        // 打开类型 1 安装后第一次运行，2 更新后第一次运行， 0 同一个版本非第一次运行
        params["open_type"] = WHAppInfo.isAppNewInstalled ? 1 : 0
        params["status"] = status
        params["fields"] = "switch,version_upgrade"
        WHSharedRequest.GET("/common/interact.json", params: params) { response in
            if let dic = response.data() as? [String: AnyHashable] {
                self.updateRegisterConfigs(dic)
                self.configDic = dic
                self.saveConfig()
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .WHConfigCenterDataDidChange, object: nil)
                }
            }
        }
        
    }
    
    private func loadCacheConfig() {
        if let dic = UserDefaults.standard.dictionary(forKey: kWHConfigCenterCacheKey) as? MTConfigDicType {
            self.configDic = dic
        } else {
            if let bundleURL = Bundle.main.url(forResource: "WHBusinessCommonConfigCenter", withExtension: "bundle"),
               let bundle = Bundle(url: bundleURL),
               let path = bundle.path(forResource: "WHConfigCenterDefaultData", ofType: "json") {
                let url = URL(fileURLWithPath: path)
                do {
                    let data = try Data(contentsOf: url)
                    let jsonData: MTConfigDicType? = try JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? MTConfigDicType
                    self.configDic = jsonData ?? [:]
                } catch _ as Error? {
                }
            }
            if let path = Bundle.main.path(forResource: "WHConfigCenterDefaultData", ofType: "json") {
                let url = URL(fileURLWithPath: path)
                do {
                    let data = try Data(contentsOf: url)
                    let jsonData: MTConfigDicType? = try JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? MTConfigDicType
                    self.configDic = jsonData ?? [:]
                } catch _ as Error? {
                }
            }
        }
    }
    
    private func saveConfig() {
        UserDefaults.standard.setValue(self.configDic, forKey: kWHConfigCenterCacheKey)
        UserDefaults.standard.synchronize()
    }
    
    private func registerConfig(for key: String, group: WHConfigCenterGroup = .defaultName, didChangeBlock:@escaping WHConfigCenterDidChangeBlock) {
        self.lock.wait()
        let currentKey = "\(key)_||_\(group.rawValue)"
        self.registerConfigs[currentKey] = didChangeBlock
        self.lock.signal()
    }
    
    @objc
    private func updateRegisterConfigs(_ configs: MTConfigDicType?) {
        self.lock.wait()
        for (registerKey, didChangeBlock) in registerConfigs {
            let keyList = registerKey.components(separatedBy: "_||_")
            if keyList.count == 2 {
                let key = keyList[0]
                let group = WHConfigCenterGroup.init(rawValue: keyList[1]) ?? .defaultName
                let oldValue = self.config(for: key, group: group)
                let newValue = self.config(for: key, group: group, fromConfigs: configs)
                if oldValue != newValue {
                    DispatchQueue.main.async {
                        didChangeBlock(key, group, newValue)
                    }
                }
            }
        }
        self.lock.signal()
    }
    
    private func config(for key: String, group: WHConfigCenterGroup = .defaultName, fromConfigs: [AnyHashable: AnyHashable]? = nil) -> AnyHashable? {
        var config: MTConfigDicType? = fromConfigs ?? self.configDic
        var value: AnyHashable?
        let keys = key.components(separatedBy: ".")
        for (index, key) in keys.enumerated() {
            if index == keys.count - 1 {
                value = config?[key]
            } else {
                config = config?[key] as? MTConfigDicType
            }
        }
        return value
    }
}

// MARK: 注册配置
extension WHConfigCenterManager {
    
    /// 获取对应key的config值
    /// - Parameters:
    ///   - key: 键值，支持 . 方法获取下一级value，比如  appAreaType.type
    ///   - group: group分组，无分组不用传
    ///   - didChangeBlock: 用于监听config修改的block，只有当对应键值的value真的修改了才会调用， 返回对应类型的 可选value
    /// - Returns: 可选类型value
    public static func config<T>(for key: String,
                                 group: WHConfigCenterGroup = .defaultName,
                                 didChangeBlock: ((T?) -> ())? = nil) -> T? {
        if !WHUserPrivacyManager.isAgreedUserPrivacy() {return nil}
        if let callBack = didChangeBlock {
            WHConfigCenterManager.share.registerConfig(for: key, group: group) { (changedKey, changedGroup, changedValue) in
                guard key == changedKey, changedGroup == group else { return }
                
                guard let config = WHConfigCenterItem<T>.item(for: changedKey, group: changedGroup, value: changedValue) else {
                    callBack(nil)
                    return
                }
                callBack(config.value)
            }
        }
        
        let value = WHConfigCenterManager.share.config(for: key, group: group)
        guard let config = WHConfigCenterItem<T>.item(for: key, group: group, value: value) else {
            return nil
        }
        
        return config.value
    }
    
    public class func registerConfig(for key: String, group: WHConfigCenterGroup = .defaultName, didChangeBlock:@escaping WHConfigCenterDidChangeBlock) {
        WHConfigCenterManager.share.registerConfig(for: key, group: group, didChangeBlock: didChangeBlock)
    }
    
    @objc public static func getConfig(for key: String,
                                       didChangeBlock: ((Any?) -> ())? = nil) -> Any? {
        return WHConfigCenterManager.config(for: key, didChangeBlock: didChangeBlock)
    }
    
    public static func cacheConfig<T>(for key: String,
                                 group: WHConfigCenterGroup = .defaultName,
                                 didChangeBlock: ((T?) -> ())? = nil) -> T? {
        var configDic: MTConfigDicType? = [:]
        if let dic = UserDefaults.standard.dictionary(forKey: kWHConfigCenterCacheKey) as? MTConfigDicType {
            configDic = dic
        } else {
            if let bundleURL = Bundle.main.url(forResource: "WHBusinessCommonConfigCenter", withExtension: "bundle"),
               let bundle = Bundle(url: bundleURL),
               let path = bundle.path(forResource: "WHConfigCenterDefaultData", ofType: "json") {
                let url = URL(fileURLWithPath: path)
                do {
                    let data = try Data(contentsOf: url)
                    let jsonData: MTConfigDicType? = try JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? MTConfigDicType
                    configDic = jsonData ?? [:]
                } catch _ as Error? {
                }
            }
            if let path = Bundle.main.path(forResource: "WHConfigCenterDefaultData", ofType: "json") {
                let url = URL(fileURLWithPath: path)
                do {
                    let data = try Data(contentsOf: url)
                    let jsonData: MTConfigDicType? = try JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? MTConfigDicType
                    configDic = jsonData ?? [:]
                } catch _ as Error? {
                }
            }
        }
        
        var value: AnyHashable?
        let keys = key.components(separatedBy: ".")
        for (index, key) in keys.enumerated() {
            if index == keys.count - 1 {
                value = configDic?[key]
            } else {
                configDic = configDic?[key] as? MTConfigDicType
            }
        }
        guard let config = WHConfigCenterItem<T>.item(for: key, group: group, value: value) else {
            return nil
        }
        return config.value
    }
}

// MARK: 开关获取
extension WHConfigCenterManager {
    public static func getWheekk() -> Bool {
        var isKK = false
        if let iosExamined:String = WHConfigCenterManager.config(for: "switch.whee_kk.switch") {
            if iosExamined == "1" {
                isKK = true
            }
        }
        return isKK
    }
    //超清详情页，图生视频
    public static func getHdToVideo() -> ([String: Any]) {
        var info : [String: Any] = [:]
        if let s: String = WHConfigCenterManager.config(for: "switch.hd_extra_btn.switch") {
            info["switch"] = (s == "1")
        }
        
        if let img: String = WHConfigCenterManager.config(for: "switch.hd_extra_btn.img") {
            info["img"] = img
        }
        if let scheme: String = WHConfigCenterManager.config(for: "switch.hd_extra_btn.scheme") {
            info["scheme"] = scheme
        }
        
        if let title: String = WHConfigCenterManager.config(for: "switch.hd_extra_btn.title") {
            info["title"] = title
        }
        return info
    }
    
    public static func useSystemAlbum() -> (Bool, String) {
        var result = (false, "")
        if let s: String = WHConfigCenterManager.config(for: "switch.use_s_album.switch") {
            result.0 = s == "1"
        }
        if let device: String = WHConfigCenterManager.config(for: "switch.use_s_album.device_limit") {
            result.1 = device
        }
        return result
    }
    //AI超清相册选择是否默认选中全部
    public static func getAIUpScalerAlbumIsSelectAll() -> Bool {
        var isKK = false
        if let albumSelectAll:String = WHConfigCenterManager.config(for: "switch.album_select_all.switch") {
            if albumSelectAll == "1" {
                isKK = true
            }
        }
        return isKK
    }
    //低端机过渡页是否播放视频
    public static func introducePlayLimit() -> (Bool, String) {
        var result = (false, "")
        if let s: String = WHConfigCenterManager.config(for: "switch.introduce_play_limit.switch") {
            result.0 = s == "1"
        }
        if let device: String = WHConfigCenterManager.config(for: "switch.introduce_play_limit.device_limit") {
            result.1 = device
        }
        return result
    }
    
}
