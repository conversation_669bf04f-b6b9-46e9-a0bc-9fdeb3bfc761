//
//  WHAnalyticsConstants.swift
//  WHBusinessCommon
//
//  Created by Devin on 2023/8/3.
//

import Foundation
import WHBaseLibrary

/// 埋点事件ID
public enum WHEEAnalyticsEventId: String,
                         WHAnalyticsEventId {
    ///我的页面，tab和下拉按钮点击
    case wheeMineTabDropdownClick = "whee_mine_tab_dropdown_click"
    ///我的页面 点击
    case wheeMineClick = "whee_mine_click"
    /// 我的页面VIP banner点击
    case wheemineVipBannerClick = "whee_mine_vip_banner_click"
    /// 美豆点击
    case wheeBeautyCoinClick = "whee_beauty_coin_click"
    /// 首页Banner点击
    case wheeFirstPageBannerClick = "whee_first_page_banner_click"
    /// 首页Banner曝光
    case wheeFirstPageBannerExpo = "whee_first_page_banner_expo"
    /// 首页金刚位点击
    case wheeFirstPageFuncClick = "whee_first_page_click"
    /// APP底部Tab点击
    case wheeTabClick = "whee_tab_click"
    /// 灵感页顶部tab名称点击
    case wheeGalleryTabClick = "whee_gallery_tab_click"
    /// 灵感页搜索点击
    case wheeSearchClick = "whee_search_click"
    /// 灵感页搜索页面，清除历史搜索记录点击（垃圾桶按钮
    case wheeSearchClearClick = "whee_search_clear_click"
    ///弹窗或半弹层点击
    case wheePopupClick = "whee_popup_click"
    // MARK: - MIAnalyticsEventId
    public func analyticsEventId() -> String {
        return self.rawValue
    }
}

/// 埋点Key
extension WHAnalyticsKey {
    ///下拉按钮名称
    public static let dropdownName = WHAnalyticsKey(rawValue: "dropdown_name")
    ///tab名称
    public static let tabName = WHAnalyticsKey(rawValue: "tab_name")
    ///tabID
    public static let tabID = WHAnalyticsKey(rawValue: "tab_id")
    ///点击类型
    public static let clickType = WHAnalyticsKey(rawValue: "click_type")
    ///来源
    public static let whsource = WHAnalyticsKey(rawValue: "source")
    /// 点击位置
    public static let location = WHAnalyticsKey(rawValue: "location")
    /// 点击时是否是会员
    public static let isVip = WHAnalyticsKey(rawValue: "is_vip")
    /// 点击时是否登录
    public static let isLogin = WHAnalyticsKey(rawValue: "is_login")
    /// 首页BannerID
    public static let bannerId = WHAnalyticsKey(rawValue: "banner_id")
    /// App底部tab点击前是否选中
    public static let whetherChoose = WHAnalyticsKey(rawValue: "whether_choose")
    
}
