//
//  BAGuideViewType1.swift
//  WHBusinessCommon-WHBusinessCommon
//
//  Created by CzJ on 2024/5/22.
//

import UIKit
import WHBaseLibrary
import WHBusinessCommon
import MTPhotoLibrary
let picWdith = WH_SCREEN_WIDTH * 240 / 375
let picHeight = picWdith * 160 / 240
let btnWidth = 102
public enum tipViewLoactionType {
    case left
    case right
    case center
}

public enum arrowDirectionType {
    case top
    case bottom
    case left
    case right
}

public typealias  guideClickCompletionBlock = (_ success:Bool) -> Void

public class BAGuideViewType1: UIView {
    
    override init(frame: CGRect) {
        super.init(frame: frame)
//        initTipView(hollow: CGRect(x: 0, y:200, width: WH_SCREEN_WIDTH, height: 400),
//                    arrowPoint: CGPoint(x: 20, y: WH_SCREEN_HEIGHT-250),
//                    arrowType: .top,
//                    tipViewType: .right,
//                    tipViewMargin: 10,
//                    titleStr: "标题", picUrl: "123", desStr: "描述", sureBtnStr: "确定", cancelBtnStr: nil) { success in
//            if success {
//                
//            }
//        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 引导样式1
    /// - Parameters:
    ///   - hollow: 镂空区域
    ///   - arrowPoint: 箭头位置
    ///   - arrowType: 箭头方向
    ///   - tipViewType: tipView 位置， Y默认在箭头上方16
    ///   - tipViewMargin: 距离左右的边距 剧中传0
    ///   - titleStr: 标题
    ///   - picUrl: 图片地址 可为nil
    ///   - desStr: 描述
    ///   - sureBtnStr: 确定按钮
    ///   - cancelBtnStr: 取消按钮 可为nil
    public func initTipView(hollow:CGRect, arrowPoint:CGPoint,
                            hollowCornerRadius: CGFloat,
                            arrowType: arrowDirectionType,
                            tipViewType:tipViewLoactionType,
                            tipViewMargin:Int,
                            titleStr: String,
                            picUrl: String?,
                            desStr: String,
                            sureBtnStr: String,
                            cancelBtnStr: String?,
                            completionBlock: @escaping guideClickCompletionBlock) {
        
        // 镂空区域
        let maskView = BAGuideViewType1.drawHollow(size: hollow, cornerRadius: hollowCornerRadius)
        self.addSubview(maskView)
        
        // 箭头
        let arrowImage = UIImageView()
        switch arrowType {
        case .top:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-top")
        case .bottom:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-bottom")
        default:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-top")
        }
        
        self.addSubview(arrowImage)
        arrowImage.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(arrowPoint.y)
            make.left.equalToSuperview().offset(arrowPoint.x)
            make.width.equalTo(12)
            make.height.equalTo(48)
        }
        
        // 提示view
        let tipView = BATipView()
        tipView.initTipView(titleStr: titleStr, 
                            picUrl: picUrl,
                            desStr: desStr,
                            sureBtnStr: sureBtnStr,
                            cancelBtnStr: cancelBtnStr,
                            tipViewType: tipViewType,
                            completionBlock: completionBlock)
        self.addSubview(tipView)
        tipView.snp.makeConstraints { make in
            make.bottom.equalTo(arrowImage.snp.top).offset(-16)
            make.width.equalTo(picWdith)
            switch tipViewType {
            case .left:
                make.left.equalToSuperview().offset(tipViewMargin)
            case .right:
                make.right.equalToSuperview().offset(-tipViewMargin)
            case .center:
                make.centerX.equalToSuperview()
            }
        }
       
        
    }
}
extension BAGuideViewType1 {
    public static func drawHollow(size:CGRect, cornerRadius: CGFloat) -> UIView {
        // 创建一个View
        let maskView = UIView(frame: CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: WH_SCREEN_HEIGHT))
        maskView.backgroundColor = .black.alpha(0.75)
        
        let rectPath = UIBezierPath(roundedRect: maskView.bounds, cornerRadius: 0)
        let circlePath =  UIBezierPath(roundedRect: size, cornerRadius: cornerRadius)
        
        // 合并路径
        rectPath.append(circlePath)
        rectPath.usesEvenOddFillRule = true
        
        // 创建一个CAShapeLayer 图层
        let shapeLayer = CAShapeLayer()
        shapeLayer.path = rectPath.cgPath
        shapeLayer.fillRule = .evenOdd
        
        // 添加图层蒙板
        maskView.layer.mask = shapeLayer
        
        return maskView
    }

}

class BATipView: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private var completionBlock: guideClickCompletionBlock?

    
    func initTipView(titleStr: String, 
                     picUrl: String?,
                     desStr: String,
                     sureBtnStr: String,
                     cancelBtnStr: String?,
                     tipViewType:tipViewLoactionType,
                     completionBlock: @escaping guideClickCompletionBlock) {
        self.completionBlock = completionBlock
        let title = UILabel()
        title.text = titleStr
        title.textColor = UIColor(red: 1, green: 1, blue: 1, alpha: 1)
        title.font = UIFont(name: "PingFangSC-Semibold", size: 28)
        self.addSubview(title)
        
        title.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        
        let picImage = UIImageView()
        picImage.layer.cornerRadius = 24
        picImage.layer.masksToBounds = true
        picImage.backgroundColor = UIColor(red: 0.672, green: 0.684, blue: 0.708, alpha: 1)
        picImage.sd_setImage(with: URL(string: picUrl ?? ""))
        self.addSubview(picImage)
        picImage.snp.makeConstraints { make in
            make.top.equalTo(title.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(picHeight)
        }
        
        let des = UILabel()
        des.text = desStr
        des.numberOfLines = 0
        des.textColor = UIColor(red: 1, green: 1, blue: 1, alpha: 1)
        des.font = UIFont(name: "PingFangSC-Medium", size: 14)
        self.addSubview(des)
        des.snp.makeConstraints { make in
            if (picUrl == nil) {
                make.top.equalTo(title.snp.bottom).offset(8)
            } else {
                make.top.equalTo(picImage.snp.bottom).offset(12)
            }
            make.left.right.equalToSuperview()
        }
        
        let cancelBtn = createBtn(title: cancelBtnStr)
        cancelBtn.layer.borderColor = UIColor.white.withAlphaComponent(0.2).cgColor
        cancelBtn.layer.borderWidth = 2
        cancelBtn.tag = 1
        cancelBtn.addTarget(self, action: #selector(createButtonClickAction(_:)), for: .touchUpInside)

        self.addSubview(cancelBtn)
        cancelBtn.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(des.snp.bottom).offset(24)
            make.width.equalTo(btnWidth)
            make.height.equalTo(44)
        }
        
        let sureBtn = createBtn(title: sureBtnStr)
        sureBtn.backgroundColor = UIColor(red: 0.208, green: 0.286, blue: 1, alpha: 1)
        sureBtn.tag = 2
        sureBtn.addTarget(self, action: #selector(createButtonClickAction(_:)), for: .touchUpInside)
        self.addSubview(sureBtn)
        sureBtn.snp.makeConstraints { make in
            if (cancelBtnStr == nil) {
                if (tipViewType == .left) {
                    make.left.equalToSuperview()
                } else {
                    make.right.equalToSuperview()
                }
            } else {
                make.left.equalTo(cancelBtn.snp.right).offset(12)
            }
            make.top.equalTo(des.snp.bottom).offset(24)
            make.width.equalTo(btnWidth)
            make.height.equalTo(44)
            make.bottom.equalToSuperview()
        }
        
        if (picUrl == nil) {
            picImage.isHidden = true
        }
        
        if (cancelBtnStr == nil) {
            cancelBtn.isHidden = true
        }
    }
  
    func createBtn(title:String?) -> UIButton {
        let createButton = UIButton(type: .custom)
        createButton.setTitle(title, for: .normal)
        createButton.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 14,weight: .semibold)
        createButton.setTitleColor(.white, for: .normal)
        createButton.addTarget(self, action: #selector(createButtonClickAction(_:)), for: .touchUpInside)
        ///圆角
        createButton.layer.cornerRadius = 22.0
        createButton.layer.masksToBounds = true
        return createButton
    }
    
    @objc func createButtonClickAction(_ btn: UIButton){
        if btn.tag == 1 {
            self.completionBlock?(false)
        } else {
            self.completionBlock?(true)
        }
        self.superview?.removeFromSuperview()
    }
}
// Auto layout, variables, and unit scale are not yet supported

