//
//  BAGuideViewType2.swift
//  WHBusinessCommon
//
//  Created by CzJ on 2024/5/24.
//

import UIKit
import WHBaseLibrary
import WHBusinessCommon
import MTPhotoLibrary

public class BAGuideViewType2: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
//        initTipView(hollow: CGRect(x: Int(WH_SCREEN_WIDTH)  - 50, y: 400, width: 50, height: 100),
//                    arrowPoint: CGPoint(x: WH_SCREEN_WIDTH - 50 - 48, y: 350),
//                    arrowType: .top,
//                    titleStr: "标题") { success in
//            
//        }
    }
    private var completionBlock: guideClickCompletionBlock?
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    var hollowLocation: CGRect = CGRectZero

    
    /// 引导样式2
    /// - Parameters:
    ///   - hollow: 镂空区域
    ///   - arrowPoint: 箭头位置
    ///   - arrowType: 箭头方向
    ///   - titleStr: 标题
    public func initTipView(hollow:CGRect,
                            hollowCornerRadius: CGFloat,
                            arrowPoint:CGPoint,
                            arrowType: arrowDirectionType,
                            titleStr: String,
                            completionBlock: @escaping guideClickCompletionBlock) {
        hollowLocation = hollow
        self.completionBlock = completionBlock

        
        // 镂空区域
        let maskView = BAGuideViewType1.drawHollow(size: hollow, cornerRadius: hollowCornerRadius)
        maskView.isUserInteractionEnabled = true
        self.addSubview(maskView)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        maskView.addGestureRecognizer(tapGesture)
        
        // 箭头
        let arrowImage = UIImageView()
        switch arrowType {
            
        case .top:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-top")
        case .left:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-left")
        default:
            arrowImage.image = UIImage(cm_named: "guidanceArrow-left")

        }

        self.addSubview(arrowImage)
        arrowImage.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(arrowPoint.y)
            make.left.equalToSuperview().offset(arrowPoint.x)
            if arrowType == .left {
                make.width.equalTo(48)
                make.height.equalTo(12)
            } else if (arrowType == . top){
                make.width.equalTo(12)
                make.height.equalTo(48)
            }
        }
        
        let tipView = BATipView2()
        tipView.initTipView(titleStr: titleStr)
        self.addSubview(tipView)
        tipView.snp.makeConstraints { make in
            make.height.equalTo(36)
            if (arrowType == .top) {
                make.centerX.equalTo(arrowImage)
                make.top.equalTo(arrowImage.snp.bottom)
            } else if (arrowType == .left) {
                make.centerY.equalTo(arrowImage)
                make.right.equalTo(arrowImage.snp.left).offset(-16)
            }
        }
        
    }
    
    @objc func handleTap(_ sender: UITapGestureRecognizer) {
        let location = sender.location(in: self)
        // 检查手势是否在镂空区域内
        if hollowLocation.contains(location) {
            self.removeFromSuperview()
            self.completionBlock?(true)
        }
        
    }
    
}

class BATipView2: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func initTipView(titleStr: String) {
        
        let imageView = UIImageView()
        imageView.image = UIImage(cm_named: "guideImage")
        self.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        let title = UILabel()
        title.text = titleStr
        title.textColor = UIColor(red: 1, green: 1, blue: 1, alpha: 1)
        title.font = UIFont(name: "PingFangSC-Medium", size: 14)
        self.addSubview(title)
        title.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(imageView.snp.right).offset(8)
            make.right.equalToSuperview().offset(-8)
        }
        
    }
    
}
