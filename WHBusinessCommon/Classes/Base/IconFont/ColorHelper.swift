////
////  ColorHelp.swift
////  WHBusinessCommon
////
////  Created by mt_zl on 2023/8/14.
////
//
//import Foundation
//
//
class ColorHelper {
    /// MARK : 基础色
    static func color(forKey : String) -> UIColor {
        let colorDic = WHFigmaManager.getColorData()
        return UIColor(wh_hexString: colorDic[forKey] ?? "")
    }
    
    /// MARK : 渐变色
    static func gradientColor(forKey : String) -> GradientColor {
        return WHFigmaManager.gradualColors[forKey] ?? defaultGradual
    }
    
    /// - 渐变色默认色
    fileprivate static var defaultGradual: GradientColor = GradientColor(
        colors: [UIColor(wh_hexString: "FF3545"),
                 UIColor(wh_hexString: "F22BBA"),
                 UIColor(wh_hexString: "9923FF")],
        locations: [0, 0.5, 1],
        startPoint: CGPoint(x: 0.75, y: 0.5),
        endPoint: CGPoint(x: 0.25, y: 0.5),
        type: CAGradientLayerType.axial
    )
    
}
//
class RadiusHelper {
    /// MARK : 圆角
    static func radius(forKey : String) -> WHFigmaRadius {
        let radiusData = WHFigmaManager.getRadiusData()
        if let value = radiusData[forKey] as? AnyObject {
            return convertToRadius(value) ?? WHFigmaRadius.init(radius: 0)
        }
        return WHFigmaRadius.init(radius: 0)
    }
    
    private static func convertToRadius(_ object: AnyObject) -> WHFigmaRadius? {
        if var array = object as? [CGFloat] {
            while array.count < 4 {
                array.append(0)
            }
            // [左上，右上，右下，左下]
            return WHFigmaRadius(topL: array[0], topR: array[1], bottomL: array[3], bottomR: array[2])
        } else if let number = object as? CGFloat {
            return WHFigmaRadius(radius: number)
        } else {
            debugPrint("unknown format")
        }
        
        return nil
    }
    
}
