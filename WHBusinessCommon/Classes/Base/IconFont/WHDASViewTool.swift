//
//  DASViewTool.swift
//  DASonic
//
//  Created by <PERSON> on 2023/7/28.
//

import Foundation

private var das_gradientBorderLayerKey: UInt8 = 0

extension UIView {
    private var das_gradientLayer: CAGradientLayer? {
        set {
            objc_setAssociatedObject(self, &das_gradientBorderLayerKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            let layer = objc_getAssociatedObject(self, &das_gradientBorderLayerKey) as? CAGradientLayer
            return layer
        }
    }
    
    public func wh_addGradientBorder(_ gradientColor : GradientColor,
                                                frame: CGRect,
                                         cornerRadius: CGFloat = 1,
                                                width: CGFloat = 1) {
        self.das_addGradientBorder(colors: gradientColor.colors,
                                   points: (start: gradientColor.startPoint, end: gradientColor.endPoint),
                                   locations: gradientColor.locations,
                                   frame: frame,
                                   cornerRadius:cornerRadius,
                                   width:width)
    }
    
    func das_addGradientBorder(colors: [UIColor],
                               points: (CGPoint, CGPoint),
                               locations: [NSNumber],
                               frame: CGRect,
                               cornerRadius: CGFloat = 1,
                               width: CGFloat = 1)
    {
        das_removeGradientBorder()
        // 形状layer
        let shapeLayer = CAShapeLayer()
        shapeLayer.lineWidth = width
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.strokeColor = UIColor.black.cgColor
        shapeLayer.path = UIBezierPath(roundedRect: frame, cornerRadius: cornerRadius).cgPath
        // 渐变色layer
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = colors.map({ $0.cgColor })
        gradientLayer.startPoint = points.0
        gradientLayer.endPoint = points.1
        gradientLayer.locations = locations
        gradientLayer.frame = frame
        gradientLayer.cornerRadius = cornerRadius
        // 设置遮罩
        gradientLayer.mask = shapeLayer
        
        self.layer.addSublayer(gradientLayer)
        
        das_gradientLayer = gradientLayer
    }
    
    func das_removeGradientBorder() {
        if das_gradientLayer?.superlayer != nil {
            das_gradientLayer?.removeFromSuperlayer()
        }
    }
}
