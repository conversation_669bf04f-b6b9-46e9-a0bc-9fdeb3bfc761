//
//  WHFigmaColor.swift
//  WHBusinessService
//
//  Created by mt_zl on 2022/12/12.
//

import UIKit
import WHBaseLibrary

public class WHFigmaColor: UIColor {
        public static var contentBrandText: UIColor { ColorHelper.color(forKey: "contentBrandText") }
        public static var backgroundButtonMain: UIColor { ColorHelper.color(forKey: "backgroundButtonMain") }
        public static var contentBrandIconBackground: UIColor { ColorHelper.color(forKey: "contentBrandIconBackground") }
        public static var contentBottomSheetTitleBar: UIColor { ColorHelper.color(forKey: "contentBottomSheetTitleBar") }
        public static var contentBottomSheetStatement: UIColor { ColorHelper.color(forKey: "contentBottomSheetStatement") }
        public static var contentBottomSheetTitle: UIColor { ColorHelper.color(forKey: "contentBottomSheetTitle") }
        public static var contentBottomSheetBodyInactive: UIColor { ColorHelper.color(forKey: "contentBottomSheetBodyInactive") }
        public static var contentBottomSheetBodyActive: UIColor { ColorHelper.color(forKey: "contentBottomSheetBodyActive") }
        public static var contentBottomSheetName: UIColor { ColorHelper.color(forKey: "contentBottomSheetName") }
        public static var contentBottomSheetTag: UIColor { ColorHelper.color(forKey: "contentBottomSheetTag") }
        public static var contentBottomSheetInactive: UIColor { ColorHelper.color(forKey: "contentBottomSheetInactive") }
        public static var contentBottomSheetMoreIcon: UIColor { ColorHelper.color(forKey: "contentBottomSheetMoreIcon") }
        public static var contentBottomSheetActive: UIColor { ColorHelper.color(forKey: "contentBottomSheetActive") }
        public static var contentBottomSheetLabel: UIColor { ColorHelper.color(forKey: "contentBottomSheetLabel") }
        public static var contentButtonOnMain: UIColor { ColorHelper.color(forKey: "contentButtonOnMain") }
        public static var backgroundButtonCancel: UIColor { ColorHelper.color(forKey: "backgroundButtonCancel") }
        public static var backgroundButtonSave: UIColor { ColorHelper.color(forKey: "backgroundButtonSave") }
        public static var contentButtonShareSocialname: UIColor { ColorHelper.color(forKey: "contentButtonShareSocialname") }
        public static var backgroundButtonRetry: UIColor { ColorHelper.color(forKey: "backgroundButtonRetry") }
        public static var contentButtonShareNotification: UIColor { ColorHelper.color(forKey: "contentButtonShareNotification") }
        public static var backgroundButtonVipCard: UIColor { ColorHelper.color(forKey: "backgroundButtonVipCard") }
        public static var contentButtonExport: UIColor { ColorHelper.color(forKey: "contentButtonExport") }
        public static var contentButtonShareOnNotification: UIColor { ColorHelper.color(forKey: "contentButtonShareOnNotification") }
        public static var contentLoadingImageFeedImage: UIColor { ColorHelper.color(forKey: "contentLoadingImageFeedImage") }
        public static var contentStateDiagramImage: UIColor { ColorHelper.color(forKey: "contentStateDiagramImage") }
        public static var contentSearchSearchBarSearchIcon: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarSearchIcon") }
        public static var contentBottomSheetMoreActive: UIColor { ColorHelper.color(forKey: "contentBottomSheetMoreActive") }
        public static var contentModelHyperlink: UIColor { ColorHelper.color(forKey: "contentModelHyperlink") }
        public static var contentHudIcon: UIColor { ColorHelper.color(forKey: "contentHudIcon") }
        public static var contentGlobalHomeIdicator: UIColor { ColorHelper.color(forKey: "contentGlobalHomeIdicator") }
        public static var contentButtonShareToolIcon: UIColor { ColorHelper.color(forKey: "contentButtonShareToolIcon") }
        public static var backgroundHomeSubButton: UIColor { ColorHelper.color(forKey: "backgroundHomeSubButton") }
        public static var contentHudText: UIColor { ColorHelper.color(forKey: "contentHudText") }
        public static var contentLoadingImageBanner: UIColor { ColorHelper.color(forKey: "contentLoadingImageBanner") }
        public static var contentStateDiagramTitle: UIColor { ColorHelper.color(forKey: "contentStateDiagramTitle") }
        public static var contentGlobalContainerIcon: UIColor { ColorHelper.color(forKey: "contentGlobalContainerIcon") }
        public static var contentGlobalOnContainerIcon: UIColor { ColorHelper.color(forKey: "contentGlobalOnContainerIcon") }
        public static var strokeHomeSubButton: UIColor { ColorHelper.color(forKey: "strokeHomeSubButton") }
        public static var contentModelOnButton: UIColor { ColorHelper.color(forKey: "contentModelOnButton") }
        public static var contentStateDiagramBody: UIColor { ColorHelper.color(forKey: "contentStateDiagramBody") }
        public static var contentSearchSearchBarCancle: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarCancle") }
        public static var contentButtonShareSocialIcon: UIColor { ColorHelper.color(forKey: "contentButtonShareSocialIcon") }
        public static var contentGlobalOnClose: UIColor { ColorHelper.color(forKey: "contentGlobalOnClose") }
        public static var contentSearchSearchBarClearIcon: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarClearIcon") }
        public static var contentModelTitle: UIColor { ColorHelper.color(forKey: "contentModelTitle") }
        public static var contentSearchSearchBarPlaceholder: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarPlaceholder") }
        public static var contentModelBody: UIColor { ColorHelper.color(forKey: "contentModelBody") }
        public static var contentSearchSearchBarCursor: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarCursor") }
        public static var contentSearchSearchBarGuidedText: UIColor { ColorHelper.color(forKey: "contentSearchSearchBarGuidedText") }
        public static var contentSearchHistoryTag: UIColor { ColorHelper.color(forKey: "contentSearchHistoryTag") }
        public static var contentSearchHistoryOnTag: UIColor { ColorHelper.color(forKey: "contentSearchHistoryOnTag") }
        public static var contentSearchHistoryUnfold: UIColor { ColorHelper.color(forKey: "contentSearchHistoryUnfold") }
        public static var contentSearchHistoryOnUnfold: UIColor { ColorHelper.color(forKey: "contentSearchHistoryOnUnfold") }
        public static var contentSearchHistoryTitle: UIColor { ColorHelper.color(forKey: "contentSearchHistoryTitle") }
        public static var contentButtonHomeOnSubButton: UIColor { ColorHelper.color(forKey: "contentButtonHomeOnSubButton") }
        public static var strokeHudLoadingIconActive: UIColor { ColorHelper.color(forKey: "strokeHudLoadingIconActive") }
        public static var strokeHudLoadingIconInactive: UIColor { ColorHelper.color(forKey: "strokeHudLoadingIconInactive") }
        public static var contentButtonHomeLabel: UIColor { ColorHelper.color(forKey: "contentButtonHomeLabel") }
        public static var contentButtonBottomNearButtonIcon: UIColor { ColorHelper.color(forKey: "contentButtonBottomNearButtonIcon") }
        public static var contentButtonBottomNearButtonText: UIColor { ColorHelper.color(forKey: "contentButtonBottomNearButtonText") }
        public static var backgroundGlobalPage: UIColor { ColorHelper.color(forKey: "backgroundGlobalPage") }
        public static var contentOnTagContentType: UIColor { ColorHelper.color(forKey: "contentOnTagContentType") }
        public static var contentMiracleVisionText: UIColor { ColorHelper.color(forKey: "contentMiracleVisionText") }
        public static var contentStyleText: UIColor { ColorHelper.color(forKey: "contentStyleText") }
        public static var contentImageControl: UIColor { ColorHelper.color(forKey: "contentImageControl") }
        public static var contentAvatarGraphic: UIColor { ColorHelper.color(forKey: "contentAvatarGraphic") }
        public static var contentMaskImageControl: UIColor { ColorHelper.color(forKey: "contentMaskImageControl") }
        public static var contentListSettingManageUserName: UIColor { ColorHelper.color(forKey: "contentListSettingManageUserName") }
        public static var contentSignAboutContact: UIColor { ColorHelper.color(forKey: "contentSignAboutContact") }
        public static var contentInfoTextDropDown: UIColor { ColorHelper.color(forKey: "contentInfoTextDropDown") }
        public static var contentListTimeStamp: UIColor { ColorHelper.color(forKey: "contentListTimeStamp") }
        public static var contentTextThesaurusTitle: UIColor { ColorHelper.color(forKey: "contentTextThesaurusTitle") }
        public static var contentNullContainerErrorIcon: UIColor { ColorHelper.color(forKey: "contentNullContainerErrorIcon") }
        public static var contentNullContainerErrorText: UIColor { ColorHelper.color(forKey: "contentNullContainerErrorText") }
        public static var contentListSettingManageUserID: UIColor { ColorHelper.color(forKey: "contentListSettingManageUserID") }
        public static var contentMaskStyle: UIColor { ColorHelper.color(forKey: "contentMaskStyle") }
        public static var contentMaskSaveLoading: UIColor { ColorHelper.color(forKey: "contentMaskSaveLoading") }
        public static var backgroundBottomTabBar: UIColor { ColorHelper.color(forKey: "backgroundBottomTabBar") }
        public static var contentSignAboutVision: UIColor { ColorHelper.color(forKey: "contentSignAboutVision") }
        public static var backgroundGlobalClose: UIColor { ColorHelper.color(forKey: "backgroundGlobalClose") }
        public static var contentSignSplashScreenCopyRight: UIColor { ColorHelper.color(forKey: "contentSignSplashScreenCopyRight") }
        public static var contentProfileSegmentDivider: UIColor { ColorHelper.color(forKey: "contentProfileSegmentDivider") }
        public static var contentProfileSegmentActive: UIColor { ColorHelper.color(forKey: "contentProfileSegmentActive") }
        public static var backgroundFeedMainButton: UIColor { ColorHelper.color(forKey: "backgroundFeedMainButton") }
        public static var contentProfileSegmentInactive: UIColor { ColorHelper.color(forKey: "contentProfileSegmentInactive") }
        public static var contentFeedOnMainButton: UIColor { ColorHelper.color(forKey: "contentFeedOnMainButton") }
        public static var strokeFeedMainButton: UIColor { ColorHelper.color(forKey: "strokeFeedMainButton") }
        public static var backgroundFeedFavButton: UIColor { ColorHelper.color(forKey: "backgroundFeedFavButton") }
        public static var strokeFeedFavButton: UIColor { ColorHelper.color(forKey: "strokeFeedFavButton") }
        public static var contentFeedFavButtonInactive: UIColor { ColorHelper.color(forKey: "contentFeedFavButtonInactive") }
        public static var contentFeedCompareButtonOnCompare: UIColor { ColorHelper.color(forKey: "contentFeedCompareButtonOnCompare") }
        public static var contentFeedFavButtonActive: UIColor { ColorHelper.color(forKey: "contentFeedFavButtonActive") }
        public static var contentFeedDescription: UIColor { ColorHelper.color(forKey: "contentFeedDescription") }
        public static var contentFeedTag: UIColor { ColorHelper.color(forKey: "contentFeedTag") }
        public static var contentFeedOnTag: UIColor { ColorHelper.color(forKey: "contentFeedOnTag") }
        public static var contentNavbarIcon: UIColor { ColorHelper.color(forKey: "contentNavbarIcon") }
        public static var contentFeedCompareButtonCompare: UIColor { ColorHelper.color(forKey: "contentFeedCompareButtonCompare") }
        public static var contentNavbarSegmentDivider: UIColor { ColorHelper.color(forKey: "contentNavbarSegmentDivider") }
        public static var contentNavbarSegmentActive: UIColor { ColorHelper.color(forKey: "contentNavbarSegmentActive") }
        public static var contentNavbarSegmentInactive: UIColor { ColorHelper.color(forKey: "contentNavbarSegmentInactive") }
        public static var backgroundNavbar: UIColor { ColorHelper.color(forKey: "backgroundNavbar") }
        public static var backgroundNavbarMTBean: UIColor { ColorHelper.color(forKey: "backgroundNavbarMTBean") }
        public static var strokeNavbarMTBean: UIColor { ColorHelper.color(forKey: "strokeNavbarMTBean") }
        public static var contentNavbarMTBean: UIColor { ColorHelper.color(forKey: "contentNavbarMTBean") }
        public static var contentNavbarTitle: UIColor { ColorHelper.color(forKey: "contentNavbarTitle") }
        public static var backgroundInput: UIColor { ColorHelper.color(forKey: "backgroundInput") }
        public static var strokeNavbarTransparentIcon: UIColor { ColorHelper.color(forKey: "strokeNavbarTransparentIcon") }
        public static var strokeNavbarTransparentTitle: UIColor { ColorHelper.color(forKey: "strokeNavbarTransparentTitle") }
        public static var backgroundActionBarActionBar: UIColor { ColorHelper.color(forKey: "backgroundActionBarActionBar") }
        public static var backgroundBottomBar: UIColor { ColorHelper.color(forKey: "backgroundBottomBar") }
        public static var backgroundHud: UIColor { ColorHelper.color(forKey: "backgroundHud") }
        public static var backgroundModelModel: UIColor { ColorHelper.color(forKey: "backgroundModelModel") }
        public static var backgroundSearchBarDefault: UIColor { ColorHelper.color(forKey: "backgroundSearchBarDefault") }
        public static var backgroundShareTool: UIColor { ColorHelper.color(forKey: "backgroundShareTool") }
        public static var backgroundSearchBarActive: UIColor { ColorHelper.color(forKey: "backgroundSearchBarActive") }
        public static var backgroundSearchBarFinish: UIColor { ColorHelper.color(forKey: "backgroundSearchBarFinish") }
        public static var contentInputPlaceholder: UIColor { ColorHelper.color(forKey: "contentInputPlaceholder") }
        public static var contentInputLabel: UIColor { ColorHelper.color(forKey: "contentInputLabel") }
        public static var backgroundModelButton: UIColor { ColorHelper.color(forKey: "backgroundModelButton") }
        public static var contentInputDelete: UIColor { ColorHelper.color(forKey: "contentInputDelete") }
        public static var strokeImageSelected: UIColor { ColorHelper.color(forKey: "strokeImageSelected") }
        public static var backgroundEditInfoCard: UIColor { ColorHelper.color(forKey: "backgroundEditInfoCard") }
        public static var contentEditTitle: UIColor { ColorHelper.color(forKey: "contentEditTitle") }
        public static var contentEditIconUnfold: UIColor { ColorHelper.color(forKey: "contentEditIconUnfold") }
        public static var contentEditIconFold: UIColor { ColorHelper.color(forKey: "contentEditIconFold") }
        public static var contentEditIconNext: UIColor { ColorHelper.color(forKey: "contentEditIconNext") }
        public static var strokeEditCardDivider: UIColor { ColorHelper.color(forKey: "strokeEditCardDivider") }
        public static var backgroundEditButton: UIColor { ColorHelper.color(forKey: "backgroundEditButton") }
        public static var contentEditOnButton: UIColor { ColorHelper.color(forKey: "contentEditOnButton") }
        public static var contentEditCardBody: UIColor { ColorHelper.color(forKey: "contentEditCardBody") }
        public static var contentEditCardTitle: UIColor { ColorHelper.color(forKey: "contentEditCardTitle") }
        public static var backgroundEditRemove: UIColor { ColorHelper.color(forKey: "backgroundEditRemove") }
        public static var contentEditRemove: UIColor { ColorHelper.color(forKey: "contentEditRemove") }
        public static var strokeSliderInactive: UIColor { ColorHelper.color(forKey: "strokeSliderInactive") }
        public static var strokeSliderActive: UIColor { ColorHelper.color(forKey: "strokeSliderActive") }
        public static var backgroundSliderThumb: UIColor { ColorHelper.color(forKey: "backgroundSliderThumb") }
        public static var backgroundEditPlaceholder: UIColor { ColorHelper.color(forKey: "backgroundEditPlaceholder") }
        public static var contentLoadingImageDetailList: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailList") }
        public static var contentTabActive: UIColor { ColorHelper.color(forKey: "contentTabActive") }
        public static var contentLoadingImageDetailListTitle: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailListTitle") }
        public static var backgroundTabActive: UIColor { ColorHelper.color(forKey: "backgroundTabActive") }
        public static var contentTabInactive: UIColor { ColorHelper.color(forKey: "contentTabInactive") }
        public static var contentLoadingImageDetailListLabel: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailListLabel") }
        public static var contentLoadingImageDetailListIconWithLabel: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailListIconWithLabel") }
        public static var contentLoadingImageDetailContentLoading: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailContentLoading") }
        public static var backgroundEditButtonPrimary: UIColor { ColorHelper.color(forKey: "backgroundEditButtonPrimary") }
        public static var contentLoadingImageDetailTitle: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailTitle") }
        public static var contentLoadingImageDetailBody: UIColor { ColorHelper.color(forKey: "contentLoadingImageDetailBody") }
        public static var contentEditButtonOnPrimary: UIColor { ColorHelper.color(forKey: "contentEditButtonOnPrimary") }
        public static var contentEditOnPlaceholder: UIColor { ColorHelper.color(forKey: "contentEditOnPlaceholder") }
        public static var contentProfileUserName: UIColor { ColorHelper.color(forKey: "contentProfileUserName") }
        public static var backgroundProfileUserTag: UIColor { ColorHelper.color(forKey: "backgroundProfileUserTag") }
        public static var contentProfileUserTag: UIColor { ColorHelper.color(forKey: "contentProfileUserTag") }
        public static var contentProfileIcon: UIColor { ColorHelper.color(forKey: "contentProfileIcon") }
        public static var backgroundList: UIColor { ColorHelper.color(forKey: "backgroundList") }
        public static var backgroundNullContainerRecord: UIColor { ColorHelper.color(forKey: "backgroundNullContainerRecord") }
        public static var backgroundProfileFilter: UIColor { ColorHelper.color(forKey: "backgroundProfileFilter") }
        public static var contentProfileFilterText: UIColor { ColorHelper.color(forKey: "contentProfileFilterText") }
        public static var contentProfileFilterIcon: UIColor { ColorHelper.color(forKey: "contentProfileFilterIcon") }
        public static var backgroundSwitchActive: UIColor { ColorHelper.color(forKey: "backgroundSwitchActive") }
        public static var contentSwitchActive: UIColor { ColorHelper.color(forKey: "contentSwitchActive") }
        public static var backgroundSwitchInactive: UIColor { ColorHelper.color(forKey: "backgroundSwitchInactive") }
        public static var contentSwitchInactive: UIColor { ColorHelper.color(forKey: "contentSwitchInactive") }
        public static var backgroundAvatar: UIColor { ColorHelper.color(forKey: "backgroundAvatar") }
        public static var contentInputActive: UIColor { ColorHelper.color(forKey: "contentInputActive") }
        public static var contentInputError: UIColor { ColorHelper.color(forKey: "contentInputError") }
        public static var contentInputCursor: UIColor { ColorHelper.color(forKey: "contentInputCursor") }
        public static var contentSearchTitleIcon: UIColor { ColorHelper.color(forKey: "contentSearchTitleIcon") }
        public static var strokeSearchActive: UIColor { ColorHelper.color(forKey: "strokeSearchActive") }
        public static var strokeProfileFilter: UIColor { ColorHelper.color(forKey: "strokeProfileFilter") }
        public static var strokeProfileDesk: UIColor { ColorHelper.color(forKey: "strokeProfileDesk") }
        public static var backgroundModelBackdropblanket: UIColor { ColorHelper.color(forKey: "backgroundModelBackdropblanket") }
        public static var strokeTagContentType: UIColor { ColorHelper.color(forKey: "strokeTagContentType") }
        public static var strokeMiracleVisionText: UIColor { ColorHelper.color(forKey: "strokeMiracleVisionText") }
        public static var strokeImageControltext: UIColor { ColorHelper.color(forKey: "strokeImageControltext") }
        public static var strokeStyle: UIColor { ColorHelper.color(forKey: "strokeStyle") }
        public static var strokeTextThesaurus: UIColor { ColorHelper.color(forKey: "strokeTextThesaurus") }
        public static var strokeContainerActiveGap: UIColor { ColorHelper.color(forKey: "strokeContainerActiveGap") }
        public static var strokeContainerErrorIcon: UIColor { ColorHelper.color(forKey: "strokeContainerErrorIcon") }
        public static var contentModelPlayingMethodTitle: UIColor { ColorHelper.color(forKey: "contentModelPlayingMethodTitle") }
        public static var contentModelPlayingMethodBody: UIColor { ColorHelper.color(forKey: "contentModelPlayingMethodBody") }
        public static var contentButtonOnRetryIcon: UIColor { ColorHelper.color(forKey: "contentButtonOnRetryIcon") }
        public static var contentButtonOnRetryText: UIColor { ColorHelper.color(forKey: "contentButtonOnRetryText") }
        public static var contentModelOnSharePic: UIColor { ColorHelper.color(forKey: "contentModelOnSharePic") }
        public static var backgroundModelSharePic: UIColor { ColorHelper.color(forKey: "backgroundModelSharePic") }
        public static var contentButtonOnvipCard: UIColor { ColorHelper.color(forKey: "contentButtonOnvipCard") }
        public static var contentButtonOnSave: UIColor { ColorHelper.color(forKey: "contentButtonOnSave") }
        public static var contentListTitle: UIColor { ColorHelper.color(forKey: "contentListTitle") }
        public static var contentListAnnotations: UIColor { ColorHelper.color(forKey: "contentListAnnotations") }
        public static var contentLoadingImageCreatingLoadingIcon: UIColor { ColorHelper.color(forKey: "contentLoadingImageCreatingLoadingIcon") }
        public static var contentLoadingImageCreatingLoadingText: UIColor { ColorHelper.color(forKey: "contentLoadingImageCreatingLoadingText") }
        public static var contentLoadingImageCreatingFailIcon: UIColor { ColorHelper.color(forKey: "contentLoadingImageCreatingFailIcon") }
        public static var contentLoadingImageCreatingFailText: UIColor { ColorHelper.color(forKey: "contentLoadingImageCreatingFailText") }
        public static var contentStateTextResultPhoto: UIColor { ColorHelper.color(forKey: "contentStateTextResultPhoto") }
        public static var contentLoadingImageModelFeeImage: UIColor { ColorHelper.color(forKey: "contentLoadingImageModelFeeImage") }
        public static var contentOnTagMain: UIColor { ColorHelper.color(forKey: "contentOnTagMain") }
        public static var contentOnMTBean: UIColor { ColorHelper.color(forKey: "contentOnMTBean") }
        public static var neutral2: UIColor { ColorHelper.color(forKey: "neutral2") }
        public static var neutral5: UIColor { ColorHelper.color(forKey: "neutral5") }
        public static var neutral10: UIColor { ColorHelper.color(forKey: "neutral10") }
        public static var neutral20: UIColor { ColorHelper.color(forKey: "neutral20") }
        public static var neutral30: UIColor { ColorHelper.color(forKey: "neutral30") }
        public static var neutral40: UIColor { ColorHelper.color(forKey: "neutral40") }
        public static var neutral50: UIColor { ColorHelper.color(forKey: "neutral50") }
        public static var neutral60: UIColor { ColorHelper.color(forKey: "neutral60") }
        public static var neutral70: UIColor { ColorHelper.color(forKey: "neutral70") }
        public static var neutral80: UIColor { ColorHelper.color(forKey: "neutral80") }
        public static var neutral90: UIColor { ColorHelper.color(forKey: "neutral90") }
        public static var neutral95: UIColor { ColorHelper.color(forKey: "neutral95") }
        public static var neutral100: UIColor { ColorHelper.color(forKey: "neutral100") }
        public static var blackOpacity6: UIColor { ColorHelper.color(forKey: "blackOpacity6") }
        public static var blackOpacity8: UIColor { ColorHelper.color(forKey: "blackOpacity8") }
        public static var blackOpacity10: UIColor { ColorHelper.color(forKey: "blackOpacity10") }
        public static var blackOpacity20: UIColor { ColorHelper.color(forKey: "blackOpacity20") }
        public static var blackOpacity30: UIColor { ColorHelper.color(forKey: "blackOpacity30") }
        public static var blackOpacity40: UIColor { ColorHelper.color(forKey: "blackOpacity40") }
        public static var blackOpacity50: UIColor { ColorHelper.color(forKey: "blackOpacity50") }
        public static var blackOpacity60: UIColor { ColorHelper.color(forKey: "blackOpacity60") }
        public static var blackOpacity70: UIColor { ColorHelper.color(forKey: "blackOpacity70") }
        public static var blackOpacity80: UIColor { ColorHelper.color(forKey: "blackOpacity80") }
        public static var blackOpacity90: UIColor { ColorHelper.color(forKey: "blackOpacity90") }
        public static var blackOpacity100: UIColor { ColorHelper.color(forKey: "blackOpacity100") }
        public static var whiteOpacity4: UIColor { ColorHelper.color(forKey: "whiteOpacity4") }
        public static var whiteOpacity6: UIColor { ColorHelper.color(forKey: "whiteOpacity6") }
        public static var whiteOpacity8: UIColor { ColorHelper.color(forKey: "whiteOpacity8") }
        public static var whiteOpacity10: UIColor { ColorHelper.color(forKey: "whiteOpacity10") }
        public static var whiteOpacity20: UIColor { ColorHelper.color(forKey: "whiteOpacity20") }
        public static var whiteOpacity30: UIColor { ColorHelper.color(forKey: "whiteOpacity30") }
        public static var whiteOpacity40: UIColor { ColorHelper.color(forKey: "whiteOpacity40") }
        public static var whiteOpacity50: UIColor { ColorHelper.color(forKey: "whiteOpacity50") }
        public static var whiteOpacity60: UIColor { ColorHelper.color(forKey: "whiteOpacity60") }
        public static var whiteOpacity70: UIColor { ColorHelper.color(forKey: "whiteOpacity70") }
        public static var whiteOpacity80: UIColor { ColorHelper.color(forKey: "whiteOpacity80") }
        public static var whiteOpacity90: UIColor { ColorHelper.color(forKey: "whiteOpacity90") }
        public static var whiteOpacity100: UIColor { ColorHelper.color(forKey: "whiteOpacity100") }
        public static var brandPrimary10: UIColor { ColorHelper.color(forKey: "brandPrimary10") }
        public static var brandPrimary20: UIColor { ColorHelper.color(forKey: "brandPrimary20") }
        public static var brandPrimary30: UIColor { ColorHelper.color(forKey: "brandPrimary30") }
        public static var brandPrimary40: UIColor { ColorHelper.color(forKey: "brandPrimary40") }
        public static var brandPrimary50: UIColor { ColorHelper.color(forKey: "brandPrimary50") }
        public static var brandPrimary60: UIColor { ColorHelper.color(forKey: "brandPrimary60") }
        public static var brandPrimary70: UIColor { ColorHelper.color(forKey: "brandPrimary70") }
        public static var brandPrimary80: UIColor { ColorHelper.color(forKey: "brandPrimary80") }
        public static var brandPrimary90: UIColor { ColorHelper.color(forKey: "brandPrimary90") }
        public static var brandSecondary10: UIColor { ColorHelper.color(forKey: "brandSecondary10") }
        public static var brandSecondary20: UIColor { ColorHelper.color(forKey: "brandSecondary20") }
        public static var brandSecondary30: UIColor { ColorHelper.color(forKey: "brandSecondary30") }
        public static var brandSecondary40: UIColor { ColorHelper.color(forKey: "brandSecondary40") }
        public static var brandSecondary50: UIColor { ColorHelper.color(forKey: "brandSecondary50") }
        public static var brandSecondary60: UIColor { ColorHelper.color(forKey: "brandSecondary60") }
        public static var brandSecondary70: UIColor { ColorHelper.color(forKey: "brandSecondary70") }
        public static var brandSecondary80: UIColor { ColorHelper.color(forKey: "brandSecondary80") }
        public static var brandSecondary90: UIColor { ColorHelper.color(forKey: "brandSecondary90") }
        public static var brandVip10: UIColor { ColorHelper.color(forKey: "brandVip10") }
        public static var brandVip20: UIColor { ColorHelper.color(forKey: "brandVip20") }
        public static var brandVip30: UIColor { ColorHelper.color(forKey: "brandVip30") }
        public static var brandVip40: UIColor { ColorHelper.color(forKey: "brandVip40") }
        public static var brandVip50: UIColor { ColorHelper.color(forKey: "brandVip50") }
        public static var brandVip60: UIColor { ColorHelper.color(forKey: "brandVip60") }
        public static var brandVip70: UIColor { ColorHelper.color(forKey: "brandVip70") }
        public static var brandVip80: UIColor { ColorHelper.color(forKey: "brandVip80") }
        public static var brandVip90: UIColor { ColorHelper.color(forKey: "brandVip90") }
        public static var blue10: UIColor { ColorHelper.color(forKey: "blue10") }
        public static var blue20: UIColor { ColorHelper.color(forKey: "blue20") }
        public static var blue30: UIColor { ColorHelper.color(forKey: "blue30") }
        public static var blue40: UIColor { ColorHelper.color(forKey: "blue40") }
        public static var blue50: UIColor { ColorHelper.color(forKey: "blue50") }
        public static var blue60: UIColor { ColorHelper.color(forKey: "blue60") }
        public static var blue70: UIColor { ColorHelper.color(forKey: "blue70") }
        public static var blue80: UIColor { ColorHelper.color(forKey: "blue80") }
        public static var blue90: UIColor { ColorHelper.color(forKey: "blue90") }
        public static var blue100: UIColor { ColorHelper.color(forKey: "blue100") }
        public static var purple10: UIColor { ColorHelper.color(forKey: "purple10") }
        public static var purple20: UIColor { ColorHelper.color(forKey: "purple20") }
        public static var purple30: UIColor { ColorHelper.color(forKey: "purple30") }
        public static var purple40: UIColor { ColorHelper.color(forKey: "purple40") }
        public static var purple50: UIColor { ColorHelper.color(forKey: "purple50") }
        public static var purple60: UIColor { ColorHelper.color(forKey: "purple60") }
        public static var purple70: UIColor { ColorHelper.color(forKey: "purple70") }
        public static var purple80: UIColor { ColorHelper.color(forKey: "purple80") }
        public static var purple90: UIColor { ColorHelper.color(forKey: "purple90") }
        public static var red10: UIColor { ColorHelper.color(forKey: "red10") }
        public static var red20: UIColor { ColorHelper.color(forKey: "red20") }
        public static var red30: UIColor { ColorHelper.color(forKey: "red30") }
        public static var red40: UIColor { ColorHelper.color(forKey: "red40") }
        public static var red50: UIColor { ColorHelper.color(forKey: "red50") }
        public static var red60: UIColor { ColorHelper.color(forKey: "red60") }
        public static var red70: UIColor { ColorHelper.color(forKey: "red70") }
        public static var red80: UIColor { ColorHelper.color(forKey: "red80") }
        public static var red90: UIColor { ColorHelper.color(forKey: "red90") }
        public static var yellow10: UIColor { ColorHelper.color(forKey: "yellow10") }
        public static var yellow20: UIColor { ColorHelper.color(forKey: "yellow20") }
        public static var yellow30: UIColor { ColorHelper.color(forKey: "yellow30") }
        public static var yellow40: UIColor { ColorHelper.color(forKey: "yellow40") }
        public static var yellow50: UIColor { ColorHelper.color(forKey: "yellow50") }
        public static var yellow60: UIColor { ColorHelper.color(forKey: "yellow60") }
        public static var yellow70: UIColor { ColorHelper.color(forKey: "yellow70") }
        public static var yellow80: UIColor { ColorHelper.color(forKey: "yellow80") }
        public static var yellow90: UIColor { ColorHelper.color(forKey: "yellow90") }
        public static var green10: UIColor { ColorHelper.color(forKey: "green10") }
        public static var green20: UIColor { ColorHelper.color(forKey: "green20") }
        public static var green30: UIColor { ColorHelper.color(forKey: "green30") }
        public static var green40: UIColor { ColorHelper.color(forKey: "green40") }
        public static var green50: UIColor { ColorHelper.color(forKey: "green50") }
        public static var green60: UIColor { ColorHelper.color(forKey: "green60") }
        public static var green70: UIColor { ColorHelper.color(forKey: "green70") }
        public static var green80: UIColor { ColorHelper.color(forKey: "green80") }
        public static var green90: UIColor { ColorHelper.color(forKey: "green90") }
        public static var grayBlue: UIColor { ColorHelper.color(forKey: "grayBlue") }
        public static var color2: UIColor { ColorHelper.color(forKey: "color2") }
}


public class WHFigmaGradientColor{
    public static var backgroundMTBean: GradientColor { ColorHelper.gradientColor(forKey: "backgroundMTBean") }
    public static var backgroundTagMain: GradientColor { ColorHelper.gradientColor(forKey: "backgroundTagMain") }
    public static var backgroundBottomBarHover: GradientColor { ColorHelper.gradientColor(forKey: "backgroundBottomBarHover") }
    public static var strokeInputActive: GradientColor { ColorHelper.gradientColor(forKey: "strokeInputActive") }
    public static var strokeButtonMain: GradientColor { ColorHelper.gradientColor(forKey: "strokeButtonMain") }
    public static var leftButtonMain: GradientColor { ColorHelper.gradientColor(forKey: "leftButtonMain") }
    public static var rightButtonMain: GradientColor { ColorHelper.gradientColor(forKey: "rightButtonMain") }

}

public extension WHFigmaRadius {
    public static var testRadius: WHFigmaRadius { RadiusHelper.radius(forKey: "testRadius") }
    public static var radiusCapsule: WHFigmaRadius { RadiusHelper.radius(forKey: "radiusCapsule") }
    public static var radius0: WHFigmaRadius  { RadiusHelper.radius(forKey: "radius0") }
    public static var radius40: WHFigmaRadius { RadiusHelper.radius(forKey: "radius40") }
    public static var radius32: WHFigmaRadius { RadiusHelper.radius(forKey: "radius32") }
    public static var radius24: WHFigmaRadius { RadiusHelper.radius(forKey: "radius24") }
    public static var radius20: WHFigmaRadius { RadiusHelper.radius(forKey: "radius20") }
    public static var radius12: WHFigmaRadius { RadiusHelper.radius(forKey: "radius12") }
    public static var radius4:  WHFigmaRadius  { RadiusHelper.radius(forKey: "radius4") }
    public static var radius56: WHFigmaRadius { RadiusHelper.radius(forKey: "radius56") }
    public static var radius64: WHFigmaRadius { RadiusHelper.radius(forKey: "radius64") }
    public static var radius8:  WHFigmaRadius { RadiusHelper.radius(forKey: "radius8") }
    public static var radius16: WHFigmaRadius { RadiusHelper.radius(forKey: "radius16") }
    public static var radius48: WHFigmaRadius  { RadiusHelper.radius(forKey: "radius48") }
    public static var radius2:  WHFigmaRadius { RadiusHelper.radius(forKey: "radius2") }
    public static var radius14: WHFigmaRadius { RadiusHelper.radius(forKey: "radius14") }
    public static var radius26: WHFigmaRadius { RadiusHelper.radius(forKey: "radius26") }
}

