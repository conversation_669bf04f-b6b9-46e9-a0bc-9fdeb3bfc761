//
//  IconFontInfo.swift
//
//  ⚠️本文件由UI同学通过脚本直接创建，不要在此文件内新增代码

import UIKit

public enum WHFigmaIconName: String {
    case arrowRightLine = "\u{e000}";
    case arrowRightBold = "\u{e001}";
    case arrowDownBold = "\u{e002}";
    case arrowRightFill = "\u{e003}";
    case arrowDownFill = "\u{e004}";
    case arrowLeftFill = "\u{e005}";
    case arrowUpFill = "\u{e006}";
    case arrowLeftBold = "\u{e007}";
    case arrowUpBold = "\u{e008}";
    case checkedFill = "\u{e009}";
    case chevronRightBold = "\u{e00a}";
    case chevronDownBold = "\u{e00b}";
    case chevronUpBold = "\u{e00c}";
    case shoppingBagBold = "\u{e00d}";
    case chevronRightFill = "\u{e00e}";
    case chevronDownFill = "\u{e00f}";
    case chevronUpFill = "\u{e010}";
    case addFill = "\u{e011}";
    case minusFill = "\u{e012}";
    case addBold = "\u{e013}";
    case minusBold = "\u{e014}";
    case addLine = "\u{e015}";
    case minusLine = "\u{e016}";
    case circleLine = "\u{e017}";
    case checkCircleLine = "\u{e018}";
    case checkMarkBold = "\u{e019}";
    case checkMarkFill = "\u{e01a}";
    case checkMarkBlack = "\u{e01b}";
    case chevronRightLine = "\u{e01c}";
    case chevronDownLine = "\u{e01d}";
    case chevronUpLine = "\u{e01e}";
    case headPhoneLine = "\u{e01f}";
    case locationLine = "\u{e020}";
    case locationFill = "\u{e021}";
    case policyLine = "\u{e022}";
    case agreementLine = "\u{e023}";
    case settingLine = "\u{e024}";
    case reservationLine = "\u{e025}";
    case shoppingBagLine = "\u{e026}";
    case chevronLeftLine = "\u{e027}";
    case chevronLeftBold = "\u{e028}";
    case chevronLeftFill = "\u{e029}";
    case crossFill = "\u{e02a}";
    case crossBold = "\u{e02b}";
    case crossLine = "\u{e02c}";
    case cameraFill = "\u{e02d}";
    case codeBold = "\u{e02e}";
    case logoLine = "\u{e02f}";
    case logoHorizantal = "\u{e030}";
    case homeBold = "\u{e031}";
    case planBold = "\u{e032}";
    case meBold = "\u{e033}";
    case editLine = "\u{e034}";
    case editFill = "\u{e035}";
    case logoWechatFill = "\u{e036}";
    case searchLine = "\u{e037}";
    case crossCircleFill = "\u{e038}";
    case playFill = "\u{e039}";
    case pauseFill = "\u{e03a}";
    case fullScreenBold = "\u{e03b}";
    case exitFullScreenBold = "\u{e03c}";
    case infoBold = "\u{e03d}";
    case infoLine = "\u{e03e}";
    case femaleLine = "\u{e03f}";
    case femaleBold = "\u{e040}";
    case maleLine = "\u{e041}";
    case maleBold = "\u{e042}";
    case questionmarkCircleLine = "\u{e043}";
    case quitBold = "\u{e044}";
    case sweepBold = "\u{e045}";
    case bonusFill = "\u{e046}";
    case bonusLine = "\u{e047}";
    case timeFill = "\u{e048}";
    case timeLine = "\u{e049}";
    case timeBold = "\u{e04a}";
    case questionniareFill = "\u{e04b}";
    case phoneLine = "\u{e04c}";
    case unlinkLine = "\u{e04d}";
    case unlinkBold = "\u{e04e}";
    case linkBold = "\u{e04f}";
    case dialogArrowLeftFill = "\u{e050}";
}
