//
//  WHFigmaManager.swift
//  WHBusinessService
//
//  Created by mt_zl on 2023/2/21.
//

import Foundation
import WHBaseLibrary


@objcMembers
public class WHFigmaManager {
    
    ///TODO: 需要添加个获取色值文件的方法
    /// MARK：- 动态注入色值库路径
    public static func loadCustomizedGradualConfig(_ path: String) {
        gradualColors = loadGradualConfig(from: URL(fileURLWithPath: path))
    }
    
    /// MARK: - 纯色色值信息
    /// MARK：- 动态注入色值库路径
    /// json文件读出一般色值信息数据
    public static func loadCustomizedConfig(_ path: String) {
        if let colorData = URL(fileURLWithPath: path).decodeToJson() {
            colors = colorData["color"] as? [String: String] ?? [:]
        }
    }
    
    /// 主题色文件名
    public static var defaultColorJsonName : String = "dasonic_theme"
    ///图标名称
    public static var defaultIconFontName : String = "candy"
    class func getColorData() -> [String: String] {
        return colors
    }
    
    /// MARK: - 纯色色值信息
    /// json文件读出一般色值信息数据
    private static var colors: [String: String] = {
        guard let url: URL = WHCMBundle.main.url(forResource: defaultColorJsonName, withExtension: "json") else { return [:] }
        var colorData = url.decodeToJson()
        return colorData?["color"] as? [String: String] ?? [:]
    }()
    
  
    /// MARK: - 渐变色色值信息
    public static var gradualColors: [String: GradientColor] = {
        guard let url: URL = WHCMBundle.main.url(forResource: defaultColorJsonName, withExtension: "json") else { return [:] }
        return loadGradualConfig(from: url)
    }()

    
    private static func loadGradualConfig(from url: URL) -> [String: GradientColor] {
        var colorGraduals: [String: GradientColor] = [String: GradientColor]()
        var colorData = url.decodeToJson()
        var graudal = colorData?["colorGradual"] as? [String : Any]
        if let gradualColors = graudal {
            for (key, value) in gradualColors {
                if let colorInfo = value as? [String: Any],
                   let gradientColor = GradientColor.decode(from: colorInfo) {
                    colorGraduals.updateValue(gradientColor, forKey: key)
                }
            }
        }
        return colorGraduals
    }
    
    class func getRadiusData() -> [String: AnyObject] {
        return daRadius
    }
    
    private static var daRadius: [String: AnyObject] = {
        guard let url: URL = WHCMBundle.main.url(forResource: defaultColorJsonName, withExtension: "json") else { return [:] }
        var radiusData = url.decodeToJson()
        return radiusData?["radius"] as? [String: AnyObject] ?? [:]
    }()
    
}



public struct WHFigmaRadius {
    public let topL: CGFloat
    public let topR: CGFloat
    public let bottomL: CGFloat
    public let bottomR: CGFloat
    
    public init(topL: CGFloat,
                topR: CGFloat,
                bottomL: CGFloat,
                bottomR: CGFloat) {
        self.topL = topL
        self.topR = topR
        self.bottomL = bottomL
        self.bottomR = bottomR
    }

    public init(radius: CGFloat) {
        self.init(topL: radius, topR: radius, bottomL: radius, bottomR: radius)
    }
    
    public func allEqual() -> Bool {
        return self.topL == self.topR && self.topL == self.bottomL && self.topL == self.bottomR
    }
    
}

public struct GradientColor {
    public var colors: [UIColor]
    public var locations: [NSNumber]
    public var startPoint: CGPoint
    public var endPoint: CGPoint
    public var type: CAGradientLayerType = .axial

    public init(colors: [UIColor], locations: [NSNumber], startPoint: CGPoint, endPoint: CGPoint, type: CAGradientLayerType) {
        self.colors = colors
        self.locations = locations
        self.startPoint = startPoint
        self.endPoint = endPoint
        self.type = type
    }
    
    struct ColorMapping {
        static let colorsKey: String = "colors"
        static let locationsKey: String = "locations"
        static let startPointKey: String = "startPoint"
        static let endPointKey: String = "endPoint"
    }
    
    static func decode(from info: [String: Any]) -> GradientColor? {
        if let colorsString: [String] = info[ColorMapping.colorsKey] as? [String],
           let locationsString: [NSNumber] = info[ColorMapping.locationsKey] as? [NSNumber],
           let startPointString: String = info[ColorMapping.startPointKey] as? String,
           let endPointString: String = info[ColorMapping.endPointKey] as? String {
            
            var colors: [UIColor] = [UIColor]()
            for colorStr in colorsString {
                colors.append(UIColor(wh_hexString: colorStr))
            }
            var locations: [NSNumber] = [NSNumber]()
            for number in locationsString {
                locations.append(number)
            }
            let startPoint = NSCoder.cgPoint(for: startPointString)
            let endPoint = NSCoder.cgPoint(for: endPointString)
            
            return GradientColor(colors: colors,
                                 locations: locations,
                                 startPoint: startPoint,
                                 endPoint: endPoint,
                                 type: .axial)
        }
        
        return nil
    }
}


extension UIView {
    public func wh_addGradient(_ gradientColor : GradientColor) {
        self.wh_enableGradient(points: (start: gradientColor.startPoint,
                                        end: gradientColor.endPoint),
                               colors: gradientColor.colors,
                               locations: gradientColor.locations)
    }
    
    public func wh_addCorner(_ corners: WHFigmaRadius) {
        
        if corners.allEqual() {
            layer.cornerRadius = corners.topL
        }else {
            self.layoutIfNeeded()
            let miCornerRadii = WHCornerRadii(topLeft: corners.topL,
                                              topRight: corners.topR,
                                              bottomLeft: corners.bottomL,
                                              bottomRight: corners.bottomR)
            self.wh_applyMaskRound(withRect: self.bounds, cornerRadii: miCornerRadii)
        }
    }
}

extension String {
    
    /// 字符串转化为图片
    /// - Parameters:
    ///   - fontName: 字体名称
    ///   - fontSize: 字体大小
    ///   - fontColor: 颜色
    ///   - fontInsets: 图片周围偏移多少，留出多少空白
    public func iconfontImage(name: String = WHFigmaManager.defaultIconFontName,
                              size: CGFloat,
                              isRound: Bool = false,
                              color: UIColor = .black,
                              backgroundColor: UIColor = .clear,
                              insets: UIEdgeInsets = .zero) -> UIImage? {
        let iconString = self as NSString
        return iconString.iconfontImage(name: name, size: size, isRound: isRound, color: color, backgroundColor: backgroundColor, insets: insets)
    }
}

