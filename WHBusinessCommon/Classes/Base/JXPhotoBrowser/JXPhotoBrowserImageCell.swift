//
//  JXPhotoBrowserImageCell.swift
//  JXPhotoBrowser
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/12.
//  Copyright © 2019 JiongXing. All rights reserved.
//

import UIKit
import WHBaseLibrary
import WHBusinessCommon
import SDWebImage

open class JXPhotoBrowserImageCell: UIView, UIScrollViewDelegate, UIGestureRecognizerDelegate, JXPhotoBrowserCell, JXPhotoBrowserZoomSupportedCell {
    
    /// 弱引用PhotoBrowser
    open weak var photoBrowser: JXPhotoBrowser?
    
    open var index: Int = 0
    
    open var scrollDirection: JXPhotoBrowser.ScrollDirection = .horizontal {
        didSet {
            if scrollDirection == .horizontal {
                addPanGesture()
            } else if let existed = existedPan {
                scrollView.removeGestureRecognizer(existed)
            }
        }
    }
    
    /// 进度环
    public let progressView = JXPhotoBrowserProgressView()
    
    ///水印相关
    private let logo: UIImageView = {
        return UIImageView(image: UIImage(named:"ic_fullScreen_waterMark"))
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(white: 1, alpha: 0.7)
        label.font = UIFont.systemFont(ofSize: 12.0)
        label.textAlignment = .left
        label.adjustsFontSizeToFitWidth = true
        label.layer.shadowColor = UIColor(white: 0, alpha: 0.5).cgColor
        label.layer.shadowOffset = CGSize(width: 0, height: 0.5)
        label.layer.shadowOpacity = 0.5
        label.layer.shadowRadius = 0.5
        label.isUserInteractionEnabled = false
        return label
    }()
    
    /// 水印上的用户名
    open var username: String? {
        didSet {
            nameLabel.text = username
        }
    }
    ///是否显示水印
    open var showWatermark: Bool = false {
        didSet {
            logo.isHidden = !showWatermark
            nameLabel.isHidden = !showWatermark
        }
    }
    
    open var imageView: UIImageView = {
        let view = UIImageView()
        view.clipsToBounds = true
        view.contentMode = .scaleAspectFill
        return view
    }()
    
    open var scrollView: UIScrollView = {
        let view = UIScrollView()
        view.maximumZoomScale = 2.0
        view.showsVerticalScrollIndicator = false
        view.showsHorizontalScrollIndicator = false
        if #available(iOS 11.0, *) {
            view.contentInsetAdjustmentBehavior = .never
        }
        return view
    }()
    
    /// 原图逻辑
    private var originUrl: URL?
    
    private lazy var originBtn: UIButton = {
        let originBtn = UIButton()
        originBtn.backgroundColor = UIColor(r: 161, g: 161, b: 161, a: 0.3)
        originBtn.setTitle(WHLocalizedString("查看原图", comment: "私信-大图页"), for: .normal)
        originBtn.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        originBtn.layer.cornerRadius = 5
        originBtn.clipsToBounds = true
        originBtn.contentEdgeInsets = UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 5)
        originBtn.addTarget(self, action: #selector(originBtnClick), for: .touchUpInside)
        return originBtn
    }()
    
    deinit {
        JXPhotoBrowserLog.low("deinit - \(self.classForCoder)")
    }
    
    public required override init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }
    
    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }
    
    /// 生成实例
    public static func generate(with browser: JXPhotoBrowser) -> Self {
        let cell = Self.init(frame: .zero)
        cell.photoBrowser = browser
        cell.scrollDirection = browser.scrollDirection
        return cell
    }
    
    /// 子类可重写，创建子视图。完全自定义时不必调super。
    open func constructSubviews() {
        scrollView.delegate = self
        addSubview(scrollView)
        scrollView.addSubview(imageView)
        imageView.addSubview(logo)
        imageView.addSubview(nameLabel)
        logo.frame = .zero
        nameLabel.frame = .zero
    }
    
    open func setup() {
        backgroundColor = .clear
        constructSubviews()
        
        /// 拖动手势
        addPanGesture()
        
        // 双击手势
        let doubleTap = UITapGestureRecognizer(target: self, action: #selector(onDoubleTap(_:)))
        doubleTap.numberOfTapsRequired = 2
        addGestureRecognizer(doubleTap)
        
        // 单击手势
        let singleTap = UITapGestureRecognizer(target: self, action: #selector(onSingleTap(_:)))
        singleTap.require(toFail: doubleTap)
        addGestureRecognizer(singleTap)
        
        addSubview(progressView)
    }
    
    // 长按事件
    public typealias LongPressAction = (JXPhotoBrowserImageCell, UILongPressGestureRecognizer) -> Void
    
    /// 长按时回调。赋值时自动添加手势，赋值为nil时移除手势
    open var longPressedAction: LongPressAction? {
        didSet {
            if oldValue != nil && longPressedAction == nil {
                removeGestureRecognizer(longPress)
            } else if oldValue == nil && longPressedAction != nil {
                addGestureRecognizer(longPress)
            }
        }
    }
    
    /// 已添加的长按手势
    private lazy var longPress: UILongPressGestureRecognizer = {
        return UILongPressGestureRecognizer(target: self, action: #selector(onLongPress(_:)))
    }()
    
    private var perOffset: CGPoint = .zero
    
    private weak var existedPan: UIPanGestureRecognizer?
    
    /// 添加拖动手势
    open func addPanGesture() {
        guard existedPan == nil else {
            return
        }
        let pan = UIPanGestureRecognizer(target: self, action: #selector(onPan(_:)))
        pan.delegate = self
        // 必须加在图片容器上，否则长图下拉不能触发
        scrollView.addGestureRecognizer(pan)
        existedPan = pan
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        scrollView.frame = bounds
        scrollView.setZoomScale(1.0, animated: false)
        let size = computeImageLayoutSize(for: imageView.image, in: scrollView)
        let origin = computeImageLayoutOrigin(for: size, in: scrollView)
        updateImageFrame(CGRect(origin: origin, size: size))
        scrollView.setZoomScale(1.0, animated: false)
        
        progressView.center = CGPoint(x: bounds.width / 2, y: bounds.height / 2)
    }
    

    private func reloadTheSubViews() {

        
    }
    
    public func reloadData(placeholder: UIImage?, url: URL?, originUrl: URL? = nil) {
        progressView.progress = placeholder == nil ? 0 : 1
        originBtn.isHidden = true
        guard let imageUrl = url else { return }
//        if MTCommunityImageDiskCache.mt_imageExists(withPath: imageUrl.absoluteString) {
//            MTCommunityImageDiskCache.mt_asyncImageLocalPath(imageUrl.absoluteString, completionHandler: { [weak self]  (image) in
//                guard let self = self else { return }
//                if image != nil {
//                    self.progressView.progress = 1
//                    self.imageView.image = image
//                    self.setNeedsLayout()
//                    self.layoutIfNeeded()
//                }
//            })
//        } else {
            self.reloadDataOnLineURL(placeholder: placeholder, url: imageUrl, originUrl: originUrl)
//        }
    }

    func reloadDataOnLineURL(placeholder: UIImage?, url: URL, originUrl: URL?) {
        imageView.sd_setImage(with: url, placeholderImage: placeholder, options: [], progress: { [weak self] (received, total, _) in
            if total > 0 , placeholder == nil {
                self?.progressView.progress = CGFloat(received) / CGFloat(total)
            }
        }) { [weak self] (image, error, _, _) in
            guard let self = self else { return }
            self.progressView.progress = error == nil ? 1.0 : 0
            self.setNeedsLayout()
            if originUrl != nil {
                self.originUrl = originUrl
                let cacheKey = SDWebImageManager.shared.cacheKey(for: originUrl)
                SDWebImageManager.shared.imageCache.containsImage?(forKey: cacheKey, cacheType: .all) {  (cache) in
                    if cache != .none {
                        self.imageView.sd_setImage(with: originUrl, placeholderImage: image, options: [], completed: nil)
                    } else {
                        self.originBtn.isHidden = false
                        self.addSubview(self.originBtn)
                        self.originBtn.snp.makeConstraints { (make) in
//                            make.width.equalTo(72)
                            make.height.equalTo(28)
                            make.centerX.equalToSuperview()
                            if #available(iOS 11.0, *) {
                                make.bottom.equalTo(-self.safeAreaInsets.bottom - 20)
                            } else {
                                make.bottom.equalTo(-20)
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func updateImageFrame(_ frame: CGRect) {
        imageView.frame = frame
        if frame.size == .zero {
            logo.frame = .zero
            nameLabel.frame = .zero
        } else {
            let y = frame.height - 8 - 20
            logo.frame = CGRect(x: 10, y: y, width: 43, height: 20)
            nameLabel.left = logo.right + 4
            nameLabel.height = 17
            nameLabel.centerY = logo.centerY
            nameLabel.width = frame.width - logo.right - 10 - 4
        }
    }
    
    open func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return imageView
    }
    
    open func scrollViewDidZoom(_ scrollView: UIScrollView) {
        imageView.center = computeImageLayoutCenter(in: scrollView)
    }
    
    open func computeImageLayoutSize(for image: UIImage?, in scrollView: UIScrollView) -> CGSize {
        guard let imageSize = image?.size, imageSize.width > 0 && imageSize.height > 0 else {
            return .zero
        }
        var width: CGFloat
        var height: CGFloat
        let containerSize = scrollView.bounds.size
        if scrollDirection == .horizontal {
            // 横竖屏判断
            if containerSize.width < containerSize.height {
                width = containerSize.width
                height = imageSize.height / imageSize.width * width
                if height > containerSize.height {
                    scrollView.contentInset = UIEdgeInsets.init(top:WH_STATUS_BAR_HEIGHT , left: 0, bottom: WH_SCREEN_BOTTOM_SPACE, right: 0)
                }
            } else {
                height = containerSize.height
                width = imageSize.width / imageSize.height * height
                if width > containerSize.width {
                    width = containerSize.width
                    height = imageSize.height / imageSize.width * width
                }
            }
        } else {
            width = containerSize.width
            height = imageSize.height / imageSize.width * width
            if height > containerSize.height {
                height = containerSize.height
                width = imageSize.width / imageSize.height * height
            }
        }
        
        return CGSize(width: width, height: height)
    }
    
    open func computeImageLayoutOrigin(for imageSize: CGSize, in scrollView: UIScrollView) -> CGPoint {
        let containerSize = scrollView.bounds.size
        var y = (containerSize.height - imageSize.height) * 0.5
        y = max(0, y)
        var x = (containerSize.width - imageSize.width) * 0.5
        x = max(0, x)
        return CGPoint(x: x, y: y)
    }
    
    open func computeImageLayoutCenter(in scrollView: UIScrollView) -> CGPoint {
        var x = scrollView.contentSize.width * 0.5
        var y = scrollView.contentSize.height * 0.5
        let offsetX = (bounds.width - scrollView.contentSize.width) * 0.5
        if offsetX > 0 {
            x += offsetX
        }
        let offsetY = (bounds.height - scrollView.contentSize.height) * 0.5
        if offsetY > 0 {
            y += offsetY
        }
        return CGPoint(x: x, y: y)
    }
    
    /// 单击
    @objc open func onSingleTap(_ tap: UITapGestureRecognizer) {
        photoBrowser?.dismiss()
    }
    
    /// 双击
    @objc open func onDoubleTap(_ tap: UITapGestureRecognizer) {
        // 如果当前没有任何缩放，则放大到目标比例，否则重置到原比例
        if scrollView.zoomScale < 1.1 {
            // 以点击的位置为中心，放大
            perOffset = scrollView.contentOffset
            let pointInView = tap.location(in: imageView)
            let width = scrollView.bounds.size.width / scrollView.maximumZoomScale
            let height = scrollView.bounds.size.height / scrollView.maximumZoomScale
            let x = pointInView.x - (width / 2.0)
            let y = pointInView.y - (height / 2.0)
            scrollView.zoom(to: CGRect(x: x, y: y, width: width, height: height), animated: true)
        } else {
            scrollView.setZoomScale(1.0, animated: true)
            if perOffset.y == -WH_STATUS_BAR_HEIGHT, scrollView.contentInset.top == WH_STATUS_BAR_HEIGHT {
                scrollView.setContentOffset(CGPoint(x: 0, y: -WH_STATUS_BAR_HEIGHT), animated: true)
            }
        }
    }
    
    /// 长按
    @objc open func onLongPress(_ press: UILongPressGestureRecognizer) {
        if press.state == .began {
            longPressedAction?(self, press)
        }
    }
    
    /// 记录pan手势开始时imageView的位置
    private var beganFrame = CGRect.zero
    
    /// 记录pan手势开始时，手势位置
    private var beganTouch = CGPoint.zero
    
    /// 响应拖动
    @objc open func onPan(_ pan: UIPanGestureRecognizer) {
        guard imageView.image != nil else {
            return
        }
        switch pan.state {
        case .began:
            beganFrame = imageView.frame
            beganTouch = pan.location(in: scrollView)
        case .changed:
            let result = panResult(pan)
            updateImageFrame(result.frame)
            photoBrowser?.maskView.alpha = result.scale * result.scale
            photoBrowser?.setStatusBar(hidden: result.scale > 0.99)
//            photoBrowser?.pageIndicator?.isHidden = result.scale < 0.99
            photoBrowser?.viewshouldHide()
        case .ended, .cancelled:
            updateImageFrame(panResult(pan).frame)
            let isDown = abs(pan.velocity(in: self).y) > 40
            if isDown {
                photoBrowser?.dismiss()
            } else {
                photoBrowser?.maskView.alpha = 1.0
                photoBrowser?.setStatusBar(hidden: true)
//                photoBrowser?.pageIndicator?.isHidden = false
                photoBrowser?.viewshouldShow()
                resetImageViewPosition()
            }
        default:
            resetImageViewPosition()
        }
    }
    
    /// 计算拖动时图片应调整的frame和scale值
    private func panResult(_ pan: UIPanGestureRecognizer) -> (frame: CGRect, scale: CGFloat) {
        // 拖动偏移量
        let translation = pan.translation(in: scrollView)
        let currentTouch = pan.location(in: scrollView)
        
        // 由下拉的偏移值决定缩放比例，越往下偏移，缩得越小。scale值区间[0.3, 1.0]
        let scale = min(1.0, max(0.3, 1 - abs(translation.y) / bounds.height))
        
        let width = beganFrame.size.width * scale
        let height = beganFrame.size.height * scale
        
        // 计算x和y。保持手指在图片上的相对位置不变。
        // 即如果手势开始时，手指在图片X轴三分之一处，那么在移动图片时，保持手指始终位于图片X轴的三分之一处
        let xRate = (beganTouch.x - beganFrame.origin.x) / beganFrame.size.width
        let currentTouchDeltaX = xRate * width
        let x = currentTouch.x - currentTouchDeltaX
        
        let yRate = (beganTouch.y - beganFrame.origin.y) / beganFrame.size.height
        let currentTouchDeltaY = yRate * height
        let y = currentTouch.y - currentTouchDeltaY
        
        return (CGRect(x: x.isNaN ? 0 : x, y: y.isNaN ? 0 : y, width: width, height: height), scale)
    }
    
    /// 复位ImageView
    private func resetImageViewPosition() {
        // 如果图片当前显示的size小于原size，则重置为原size
        let size = computeImageLayoutSize(for: imageView.image, in: scrollView)
        let needResetSize = imageView.bounds.size.width < size.width || imageView.bounds.size.height < size.height
        UIView.animate(withDuration: 0.25) {
            self.imageView.center = self.computeImageLayoutCenter(in: self.scrollView)
            if needResetSize {
                self.imageView.bounds.size = size
            }
            self.updateImageFrame(self.imageView.frame)
        }
    }
    
    open override func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // 只处理pan手势
        guard let pan = gestureRecognizer as? UIPanGestureRecognizer else {
            return true
        }
        //图片放大模式不进行取消手势操作
        if scrollView.zoomScale > 1 {
            return false
        }
        
        let velocity = pan.velocity(in: self)
        // 向上滑动时，不响应手势
        if velocity.y < 0 {
            if scrollView.contentSize.height > scrollView.height , scrollView.contentOffset.y < scrollView.contentSize.height - scrollView.height - 1 {
                return false
            } else {
//                return true
            }
        }
        // 横向滑动时，不响应pan手势
        if abs(Int(velocity.x)) > abs(Int(velocity.y)) {
            return false
        }
        // 向下滑动，如果图片顶部超出可视区域，不响应手势
        if velocity.y > 0,scrollView.contentOffset.y > 0 {
            return false
        }
        // 响应允许范围内的下滑手势
        return true
    }
    
    open var showContentView: UIView {
        return imageView
    }
    
    @objc func originBtnClick() {
        if let originUrl = originUrl {
            imageView.sd_setImage(with: originUrl, placeholderImage: imageView.image, options: [], progress: { [weak self] (received, total, _) in
                if total > 0 {
                    DispatchQueue.main.async {
                        self?.originBtn.setTitle("\(Int(CGFloat(received) / CGFloat(total) * 100))%", for: .normal)
                    }
                }
            }) { [weak self] (image, error, _, _) in
                self?.originBtn.isHidden = true
            }
        }
    }
}
