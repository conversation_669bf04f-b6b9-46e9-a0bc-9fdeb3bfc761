//
//  JXScrollView.swift
//  MTXX
//
//  Created by meitu on 2020/9/22.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
open 
class JXScrollView: UIScrollView {

    open override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        
        let view = super.hitTest(point, with: event)
        if view is UISlider {
            self.isScrollEnabled = false
        } else {
            self.isScrollEnabled = true
        }
        return view
    }

}
