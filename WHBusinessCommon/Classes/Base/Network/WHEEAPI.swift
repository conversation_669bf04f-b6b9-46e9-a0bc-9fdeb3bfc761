//
//  WHEEAPI.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/7/20.
//  存放API

import Foundation

public struct WHWebBaseURL {
    static public func domain() -> String{
        var urlString = ""
        switch WHEnvConfigShareManager.environment {
        case .pre:
            urlString = "http://pre-h5.whee.com/"
        case .beta:
            urlString = "http://beta-h5.whee.com/"
        case.release:
            urlString = "https://h5.whee.com/"
        }
        return urlString
    }
}


struct WH_API {
    /// 全局配置
    static let config = "/v1/api/2d/base/config"
    /// 首页feed
    static let homeFeed = "/v1/api/2d/home/<USER>"
    /// 主播列表
    static let anchorList = "/v1/api/2d/anchor/list"
    /// 删除主播
    static let anchorDelete = "/v1/api/2d/anchor/del"
    /// 人脸检测
    static let faceCheck = "/v1/api/2d/ai/face/check"
    /// 查询结果（轮询）
    static let queryResult = "/v1/api/2d/ai/job/progress"
    /// 视频合成
    static let generateVideo = "/v1/api/2d/ai/video/generate"
    ///素材配置项
    static let sourceList = "/v1/api/2d/source/list"
    /// 风格化列表
    static let formulaList = "/v1/api/2d/ai/img/formulas"
    /// 风格化提交
    static let formulaImg2Img = "/v1/api/2d/ai/img2img"
    /// 照片定制数字人
    static let generateDigitalMan = "/v1/api/2d/user/character/generate"
    /// 数字人生成结果查询（轮询）
    static let characterQuery = "/v1/api/2d/user/character/progress"
    ///根据人物获取音色列表
    static let voiceList = "/v1/api/2d/source/voice/list"
    ///文本转语音-异步
    static let aiText = "/v1/api/2d/ai/text/speech"
    ///订阅商品列表
    static let productQuery = "/v1/api/2d/product/query"
    ///视频合成预估
    static let videoPretreate = "/v1/api/2d/ai/video/pretreate"

}


