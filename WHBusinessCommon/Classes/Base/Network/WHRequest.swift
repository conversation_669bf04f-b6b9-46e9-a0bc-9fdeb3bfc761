//
//  WHBsetRequest.swift
//  WHBusinessCommon
//
//  Created by mt_zl on 2023/7/18.
//

import Foundation
import WHBaseLibrary
import MTAccount
import MTIAPSDK

public let WHSharedRequest = WHRequest.shared

public class WHRequest : WHBaseRequest {
    
    public static let shared = WHRequest()
    
    /// 大账号
    public let kAccountClientId = "**********"
    
    public override func baseURL() -> String {
        return WHEnvConfigShareManager.envNetBaseUrl
    }
    
    public override func commonParams() -> [String : Any] {
        var params: [String: Any] = [:]
        params["client_id"] = kAccountClientId
        params["gnum"] = MTAnalyticsGID.sharedInstance()?.gid ?? ""
        params["version"] = WHAppInfo.appVersion
        params["client_language"] = WHAppShareLanguage.wh_currentSystemLanguage()
        params["client_network"] = MTAnalyticsShareBasic.shared().network
        params["client_channel_id"] = WHAppInfo.channelID
        params["client_model"] = UIDevice.current.mtac_platform()
        params["client_operator"] = MTAnalyticsShareBasic.shared().carrier
        params["client_os"] = UIDevice.current.systemVersion
        params["os_type"] = "iOS"
        params["client_timezone"] = TimeZone.current.identifier
        if let token = MTACAccountManager.share().accessToken , token.count > 0 {
            params["Access-Token"] = token
        }
        if let sk_cc = MTIAPModule.shared().storefrontRegionCode(), sk_cc.count > 0 {
            params["sk_cc"] = sk_cc
        }
        if let simCountryCode = WHEditionKit.getCountryCodeWithSIM() { //如果能获取到sim_codeCode，则传
            params["country_code"] = simCountryCode
        }
        if let serviceCC = WHEditionKit.getCountryCodeWithNewCache() {
            params["service_cc"] = serviceCC
        }
        if let localCC = WHEditionKit.getCountryCodeWithLocal() {
            params["local_cc"] = localCC
        }
        return params
    }

    public override func requestHeaders() -> [String : String] {
        var header: [String: String] = [:]
        header["Access-Token"] = MTACAccountManager.share().accessToken
        return header
    }
    
    public override func sigAppId() -> String {
        return "6363893339699281920"
    }
    
    public func requestHeadersMTCC(position_level1:String?, function_name:String?) -> String {
        let countryCode = UserDefaults.standard.string(forKey: kMTCountryCode)
        let mtcc = ["app_id":"198",
                    "os_type":"iOS",
                    "biz_id":"",
                    "country_code":countryCode ?? "",
                    "function":["name":function_name ?? "other"],
                    "position":["level1":position_level1 ?? "Others"],
                    "gnum":MTAnalyticsGID.sharedInstance()?.gid ?? ""
        ] as [String : Any]
        let jsonString = mtcc.wh_toJsonString() ?? ""
        var base64String = jsonString.data(using: .utf8)?.base64EncodedString()
        return base64String ?? ""
    }
    
}
