//
//  WHRouteBaseFilter.swift
//  
//
//  Created by <PERSON> on 2022/12/9.
//

import Foundation

open class WHRouteBaseFilter {
    public required init() {}
    
    /// 创建目标视图控制器
    /// - Parameters:
    ///   - params: 路由传参
    ///   - originalVC: 原目标视图控制器
    /// - Returns: <#description#>
    open func targetViewControllerWith(_ params: [String: Any],
                                       originalVC: UIViewController?) -> UIViewController? {
        return nil
    }
    
    /// 页面跳转方式
    /// - Parameter params: 路由传参
    /// - Returns: <#description#>
    open func openModeWith(_ params: [String: Any]) -> WHRouterOpenMode? {
        return nil
    }
    
}
