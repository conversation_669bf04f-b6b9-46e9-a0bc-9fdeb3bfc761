//
//  WHRouter.swift
//
//
//  Created by <PERSON> on 2022/12/9.
//

/*
 * 打开页面需要先登录时，必须实现 WHRouterLoginDelegate 的代理方法！
 */

import Foundation
import WHBaseLibrary

public class WHRouter: NSObject {
    
    /// 注册路由
    /// - Parameter configFile: 配置文件路径
    /// - Parameter scheme: 路由scheme
    /// - Parameter loginDelegate: 登录代理对象
    @objc
    public static func register(with configFile: String,
                                scheme: String,
                                loginDelegate: WHRouterLoginDelegate? = nil) {
        WHRouterService.register(with: configFile,
                                 scheme: scheme,
                                 loginDelegate: loginDelegate)
    }
    
    public static func setDelegate(_ delegate: WHRouterDelegate) {
        WHRouterManager.shared.delegate = delegate
    }
    
    /// 是否注册路由
    @objc
    public static func hasRegistered() -> Bool {
        return WHRouterService.hasRegistered()
    }
    
    /// 路由跳转方法
    /// - Parameters:
    ///   - URLString: url字符串
    ///   - viewController: 当前viewController，可为空
    ///   - params: 参数字典，可为空
    ///   - application: application，可为空
    @objc
    public static func route(with URLString: String,
                             viewController: UIViewController? = nil,
                             params: [String: Any]? = nil,
                             application: UIApplication? = nil) {
        
        if !WHRouter.hasRegistered() {
            if let path = WHCMBundle.main.path(forResource: "WHRouterConfig", ofType: "plist") {
                WHRouter.register(with: path, scheme: "wheeai", loginDelegate: WHShareManager)
            }
        }
        
        var temp = URLString
        guard let url = URL(string: temp) else {
            WHPrint("create url error: \(URLString)!!!")
            return
        }
        
        Self.route(with: url,
                   viewController: viewController,
                   params: params,
                   application: application)
    }
    
    /// 路由跳转方法
    /// - Parameters:
    ///   - URL: url，
    ///   - viewController: 当前viewController，可为空
    ///   - params: 参数字典，可为空
    ///   - application: application，可为空
    public static func route(with url: URL,
                             viewController: UIViewController? = nil,
                             params: [String: Any]? = nil,
                             application: UIApplication? = nil) {
        var jumpUrl = url
        if !WHRouter.hasRegistered() {
            if let path = WHCMBundle.main.path(forResource: "WHRouterConfig", ofType: "plist") {
                WHRouter.register(with: path, scheme: "wheeai", loginDelegate: WHShareManager)
            }
        }
        if jumpUrl.scheme?.hasPrefix("http") == true { //如果是HTTP，兜底使用内部浏览器打开
            let webEncoding = jumpUrl.absoluteString.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let urlScheme = "wheeai://web?url="+webEncoding
            jumpUrl = URL(string: urlScheme) ?? URL(fileURLWithPath: "")
        }
        
        if WHRouterManager.shared.delegate?.route(with: url,
                                                  viewController: viewController,
                                                  params: params,
                                                  application: application) == true {
            return
        }
        
        WHRouterService.route(with: jumpUrl,
                              viewController: viewController,
                              params: params,
                              application: application)
    }
    
    /// 当前显示的viewController
    @objc
    public static var topViewController: UIViewController? {
        return UIApplication.wh_currentViewController
    }
    
}

/// 路由协议
@objc
public protocol WHRouteProtocol: NSObjectProtocol {
    /// 路由传递的参数
    func loadRouteParams(_ params: [String: Any])
}

/// 跳转方式
public enum WHRouterOpenMode: String {
    case push
    case present
    
    /// 预留模式，暂未实现
    case tabSwitch
}

/// 路由参数的Key
public struct WHRouterParamsKey {
    
    public let value: String
    
    // MARK: - 配置文件中的字段
    /// url
    public static let url = WHRouterParamsKey(value: "url")
    
    /// 控制器名称
    public static let controllerName = WHRouterParamsKey(value: "controllerName")
    
    /// 处理器名称
    public static let responderName = WHRouterParamsKey(value: "responderName")
    
    /// 打开页面方式
    public static let openMode = WHRouterParamsKey(value: "openMode")
    
    /// 子处理器
    public static let subResponder = WHRouterParamsKey(value: "subResponder")
    
    /// Filter
    public static let filter = WHRouterParamsKey(value: "filter")
    
    // MARK: -
    /// 路由跳转的url
    public static let routeUrl = WHRouterParamsKey(value: "routeUrl")
    
    /// 是否需要登录
    public static let needsLogin = WHRouterParamsKey(value: "needsLogin")
    
    /// 打开页面是否有动画
    public static let openAnimated = WHRouterParamsKey(value: "openAnimated")
    
    // MARK: - push跳转相关设置
    
    /// 移除导航控制器VC列表的最后一个VC
    public static let removeLastOne = WHRouterParamsKey(value: "removeLastOne")

    /// 将当前要push的VC作为rootVC
    public static let beRootVC = WHRouterParamsKey(value: "beRootVC")
    
    /// 导航控制器VC列表中只保留一个即将push的VC类型（比如，即将push XXXViewController，会把页面堆栈中其他XXXViewController都移除调）
    public static let beSingleVC = WHRouterParamsKey(value: "beSingleVC")
    
    /// 移除中间所有VC，只留下rootVC和即将push的VC
    public static let removeBetweenVCs = WHRouterParamsKey(value: "removeBetweenVCs")

}

@objc
public protocol WHRouterLoginDelegate: NSObjectProtocol {
    
    /// 是否登录
    /// - Returns: <#description#>
    func hasLogined() -> Bool
    
    /// 需要登录
    /// - Parameters:
    ///   - currentViewController: 当前页面
    ///   - completion: 登录完成回调，成功传true，失败传false
    func needsToLogin(_ currentViewController: UIViewController,
                      completion: @escaping (Bool) -> ())
}

class WHManager:NSObject, WHRouterLoginDelegate {
    
    static let shared = WHManager()
    
    public func hasLogined() -> Bool {
        return WHAccountShareManager.isLogin() ? true : false
    }

    public func needsToLogin(_ currentViewController: UIViewController, completion: @escaping (Bool) -> ()) {
        WHLoginViewsharedManager.showAccountLoginWith(hostVC: currentViewController, loginType: .half) { success, account in
            completion(success)
        } failure: {
            completion(false)
        }
    }
}
let WHShareManager = WHManager.shared

public protocol WHRouterDelegate: NSObjectProtocol {
    
    func route(with url: URL,
               viewController: UIViewController?,
               params: [String: Any]?,
               application: UIApplication?) -> Bool
    
}

class WHRouterManager {
    
    weak var delegate: WHRouterDelegate?
    
    private init() {}
    
    static let shared = WHRouterManager()
    
}
