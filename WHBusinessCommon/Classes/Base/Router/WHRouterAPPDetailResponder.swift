//
//  WHRouterAPPDetailResponder.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2024/7/1.
//

import Foundation
//详情页路由
class WHRouterAPPDetailResponder: WHRouterBaseResponder{
    override func setConfig(with config: [String: Any]) {
        configDict = config
    }
    
    override func respond(with URL: URL,
                 viewController: UIViewController? = nil,
                 params: [String: Any]? = nil,
                 application: UIApplication? = nil) {
        mergeRouteParams(with: URL, params: params)
        if let cate =  self.params["task_category"] as? String, cate == "model"{//模型详情页
            WHRouter.route(with: "wheeai://app/modelDetail",params: self.params)
        } else {
            WHRouter.route(with: "wheeai://app/textimageDetail",params: self.params)
        }
    }
}
