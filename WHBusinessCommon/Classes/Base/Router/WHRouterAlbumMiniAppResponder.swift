//
//  WHRouterAlbumMiniAppResponder.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/11/16.
//

import Foundation
import PhotosUI
import MobileCoreServices
import MTResourceUpload
import SDWebImage
import MTPhotoLibrary

class WHRouterAlbumMiniAppResponder: WHRouterBaseResponder {
    override func setConfig(with config: [String: Any]) {
        configDict = config
    }
    
    override func respond(with URL: URL,
                 viewController: UIViewController? = nil,
                 params: [String: Any]? = nil,
                 application: UIApplication? = nil) {
        mergeRouteParams(with: URL, params: params)
        prepareRoute(with: viewController,
                          application: application)
    
        let schemeModel = WHMiniAppSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
        let miniAppScheme = schemeModel?.queryDic?["url"]
        if (miniAppScheme == nil) { //没有小程序链接则不需要继续处理了
            return
        }
        var source = "image_extension" //默认是图片扩展
        if let s = schemeModel?.queryDic?["source"] as? String,s.count > 0 {
            source = s
        }
        WHAlbumMiniAppManager.shared.startAlumbTask(schemeUrl: miniAppScheme as? String ?? "",source: source)
    }
}

public class WHAlbumMiniAppManager : NSObject, UINavigationControllerDelegate {
    public static let shared = WHAlbumMiniAppManager()
    
    var maskView:UIView?
    var miniAppScheme = ""
    var imageUrlString = ""
    var mtaSource = "image_extension"
    weak var pickerVC:UIViewController?
    
    public func startAlumbTask(schemeUrl:String?,source:String? = "image_extension") {
        //初始化数据
        self.miniAppScheme = schemeUrl ?? ""
        self.removeMaskView()
        self.pickerVC = nil
        self.mtaSource = source ?? "image_extension"
        //检测登录
        if WHAccountShareManager.isLogin() {
            self.formalTask()
        } else {
            WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self?.formalTask()
                }
            } failure: {
            }
        }
        
    }
    func formalTask() {
        //检测权限--用户是否操作过
        let photoStatus = PHPhotoLibrary.authorizationStatus()
        if photoStatus == .notDetermined {
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                guard let self = self else { return }
                if self.photoOpenStatus() == true {
                    //打开相册
                    DispatchQueue.main.async {
                        self.openPhotoAlubm()
                    }
                }
            }
            return
        }
        //检测权限--用户是否拒绝了
        let isAuthority = self.photoOpenStatus()
        if isAuthority == false {
            //打开提示弹窗
            self.showAlbumAuthorizationAlert()
            return
        }
        //打开相册
        self.openPhotoAlubm()
    }
    
    func showAlbumAuthorizationAlert() {
        let alertController = UIAlertController(title: WHLocalizedString("开启照片权限"), message: WHLocalizedString("你还没有开启照片权限，开启之后WHEE才能访问你的照片"), preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: WHLocalizedString("取消"), style: .cancel, handler: nil)
        let settingsAction = UIAlertAction(title: WHLocalizedString("去设置"), style: .default, handler: { (action) in
            self.openSettings()
        })
        alertController.addAction(cancelAction)
        alertController.addAction(settingsAction)
        WHRouter.topViewController?.present(alertController, animated: true, completion: nil)
    }
    
    func openPhotoAlubm() {
        // 配置相册参数并打开
//        if #available(iOS 14, *) {
//            let filter = PHPickerFilter.any(of: [.images])
//            var config = PHPickerConfiguration(photoLibrary: PHPhotoLibrary.shared())
//            config.selectionLimit = 1
//            config.filter = filter
//            
//            let phPickerVC = PHPickerViewController(configuration: config)
//            phPickerVC.modalPresentationStyle = .fullScreen
//            phPickerVC.delegate = self
//            WHRouter.topViewController?.present(phPickerVC, animated: true, completion: nil)
//        } else {
//            let pickerVC = UIImagePickerController()
//            pickerVC.delegate = self
//            pickerVC.sourceType = .photoLibrary
//            pickerVC.mediaTypes = [kUTTypeImage as String]
//            WHRouter.topViewController?.present(pickerVC, animated: true, completion: nil)
//        }
        WHPhotoAlbumSharedManager.loadPhotosAndShowVCWithDelegate(delegate: self)
    }
    //上传图片
    func uploadImgWithData(mtAsset:MTPhotoAsset?) {
        if mtAsset == nil || mtAsset?.assetFileURL() == nil {
            self.showUnPicAlert()
            return
        }
        //检测图片是否合规
        let imgFormat = mtAsset?.mediaFormat()
        if imgFormat == "gif" { //不支持gif
            self.showUnPicAlert()
            return
        }
        //检测大小
        if self.isBeyondLimitImageSize(mtAsset: mtAsset) == true {
            self.showUnPicAlert()
            return
        }
        var uploadImg = mtAsset?.fetchImage(with: .imagefullResolutionImage)
        var uploadResource = MTResource()
        let imageSuffix = self.getSuffixNameWithImageType(imgFormat: imgFormat ?? "")
        if imageSuffix == ".jpg" { //其它格式的图片需要转成jpg
            let jpgData = uploadImg?.jpegData(compressionQuality: 1.0)
            uploadImg = UIImage(data: jpgData ?? Data())
            uploadResource = MTResource(resource: NSData(data: jpgData ?? Data()), uploadType: .resourcePhoto)
        } else {
            uploadResource = MTResource(resource: mtAsset?.asset ?? PHAsset(), uploadType: .resourcePhoto)
        }
        //开始上传
        self.showMaskView() //添加蒙层，防止用户其他点击
        uploadResource.suffix = imageSuffix
        WHResourceUploaderManager.manager.addTask(with: uploadResource) { resourceKey, percent in
            
        } completion: { resourceKey, encryptResult, error in
            let urlStr = encryptResult?["url"]
            DispatchQueue.main.async {
                if error != nil || urlStr == nil {
                    self.removeMaskView()//移除蒙层
                    WHRouter.topViewController?.showToast(title: error?.localizedDescription ?? "上传失败")
                } else {
                    self.checkPicIsSafe(urlStr: urlStr as! String)
                }
            }
        }
    }
    //展示图片不符合要求
    func showUnPicAlert() {
        let alertView = WHCommonAlertView(title: WHLocalizedString("图片不符合要求"), desStr: WHLocalizedString("要求格式: jpg、jpeg、bmp、png \n大小: 30M以内"), alertViewType: .sureStyle)
        alertView.show(in: UIApplication.wh_currentWindow)
    }
    //鉴黄
    func checkPicIsSafe(urlStr:String) {
        if urlStr == nil {
            self.removeMaskView()//移除蒙层
            WHRouter.topViewController?.showToast(title: "上传失败,图片链接无效")
            return
        }
        self.imageUrlString = urlStr
        var params:[String:Any] = [:]
        params["url"] = urlStr
        params["task_category"] = self.mtaSource == "image_extension" ? "extend" : self.mtaSource
        WHSharedRequest.POST("/image/monitor.json",params: params) { response in
            DispatchQueue.main.async {
                self.removeMaskView()//移除蒙层
                if response.error != nil {
                    WHRouter.topViewController?.showToast(title: "网络连接失败")
                } else {
                    let result = response.data() as? [String: Any]
                    self.openMiniAppWithSafeResult(result: result ?? [:])
                }
            }
        }
    }
    
    func openMiniAppWithSafeResult(result:[String: Any]) {
        let isSafe = result["result"]
        let message = result["message"] ?? "安全检测失败"
        if isSafe as! Bool == true {
            WHAnalyticsManager.otherTrackEvent("upload_image_success", params: ["function": self.mtaSource])
            let paramsStr = "image_url="+(self.imageUrlString.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? "")
            let encodingParams = paramsStr.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let jumpSchema = self.miniAppScheme+"&params="+encodingParams
            WHRouter.route(with: jumpSchema)
        } else {
            WHRouter.topViewController?.showToast(title: message as! String)
        }
    }
    
    private func photoOpenStatus() -> Bool {
        var photoAuthorityOpenStatus = false
        if #available(iOS 14.0, *) {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized || PHPhotoLibrary.authorizationStatus() == .limited
        } else {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized
        }
        return photoAuthorityOpenStatus
    }
    
    ///UIImagePickerControllerDelegate
//    public func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
//        
//        if picker.sourceType == .photoLibrary {
//            let url = info[.imageURL]
//            let imgData = NSData(contentsOf: url as! URL)
//            self.uploadImgWithData(imgData: imgData)
//        }
//        picker.dismiss(animated: true, completion: nil)
//    }
//    public func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
//        
//        picker.dismiss(animated: true, completion: nil)
//    }
    
//    @available(iOS 14.0, *)
//    public func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
//        var array = NSMutableArray()
//        for result in results {
//            array.add(result.assetIdentifier)
//        }
//        
//        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: array as! [String], options: nil)
//        var imgDataArray = [NSData]()
//        var isHaveUnLegalData = false
//        let group = DispatchGroup()
//        
//        for i in 0..<fetchResult.count {
//            let result = results[i]
//            let itemProvider = result.itemProvider
//            let asset = fetchResult.object(at: i)
//            
//            autoreleasepool {
//                if itemProvider.hasItemConformingToTypeIdentifier(kUTTypeImage as String) {
//                    group.enter()
//                    PHImageManager.default().requestImageData(for: asset, options: nil) { (imageData, dataUTI, orientation, info) in
//                        if imageData != nil {
//                            let imgData = NSData(data: imageData!)
//                            imgDataArray.append(imgData)
//                        } else {
//                            isHaveUnLegalData = true //存在不合法的图片
//                        }
//                        group.leave()
//                    }
//                } else if itemProvider.hasItemConformingToTypeIdentifier(kUTTypeMovie as String) || itemProvider.hasItemConformingToTypeIdentifier(kUTTypeVideo as String) {
//                    group.enter()
//                    let options = PHVideoRequestOptions()
//                    options.deliveryMode = .automatic
//                    options.version = .original
//                    options.isNetworkAccessAllowed = true
//                    
//                    PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { (avAsset, audioMix, info) in
//                        if let urlAsset = avAsset as? AVURLAsset {
//                            let url = urlAsset.url
//                            if let track = urlAsset.tracks(withMediaType: .video).first {
//                                let fileManager1 = FileManager.default
//                                if fileManager1.fileExists(atPath: urlAsset.url.path) {
//                                    // 获取文件的属性
//                                    let fileDic = try? fileManager1.attributesOfItem(atPath: urlAsset.url.path)
//                                    if let size = fileDic?[FileAttributeKey.size] as? Int64 {
//                                    }
//                                }
//                            }
//                            
////                            assetsArray.append(videoInfo) //暂时不添加视频
//                        }
//                        group.leave()
//                    }
//                }
//            }
//        }
//        
//        group.notify(queue: DispatchQueue.main) {
//            if imgDataArray.count > 0 {
//                self.uploadImgWithData(imgData: imgDataArray[0] ?? nil)
//            } else if isHaveUnLegalData == true {
//                self.uploadImgWithData(imgData: nil)
//            }
//        }
//        picker.dismiss(animated: true, completion: nil)
//    }
    
    private func openSettings() {
        DispatchQueue.main.async {
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        }
    }
    //展示遮罩view
    func showMaskView() {
        if ((self.maskView?.superview) != nil) {
            return
        }
        self.maskView = UIView(frame: CGRectMake(0, 0, UIScreen.main.bounds.size.width, UIScreen.main.bounds.size.height))
        UIApplication.wh_currentWindow?.addSubview(self.maskView!)
        UIApplication.wh_currentWindow?.showLoading(title: WHLocalizedString("图片安全检测中..."))
    }
    //移除遮罩view
    func removeMaskView() {
        if self.maskView?.superview == nil {
            return
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.maskView?.removeFromSuperview()
            self.maskView = nil
        }
        UIApplication.wh_currentWindow?.hiddenToast()
    }
    //检测图片格式，返回应使用的后缀名
    func getSuffixNameWithImageType(imgFormat: String) -> String {
        var suffix = ".jpg"
        switch imgFormat {
        case "jpeg":
            suffix = ".jpeg"
        case "png":
            suffix = ".png"
        case "bmp":
            suffix = ".bmp"
        default:
            suffix = ".jpg"
        }
        return suffix
    }
    //检测图片文件大小
    func isBeyondLimitImageSize(mtAsset:MTPhotoAsset?) -> Bool {
        let length = mtAsset?.fileSize() ?? 0
        let MB = Double(length) / 1024.0 / 1024.0
        return MB >= 30
    }
}

extension WHAlbumMiniAppManager:WHPhotosViewControllerDelegate {
    public func selectPhotoWithMtasset(mtAssets: [MTPhotoAsset], vc: UIViewController) {
        self.pickerVC = vc
        let mtAsset = mtAssets.object(at: 0)
        if let asset = mtAsset {
            self.uploadImgWithData(mtAsset: mtAsset)
        }
    }
}
