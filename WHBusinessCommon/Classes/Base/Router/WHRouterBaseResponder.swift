//
//  WHRouterBaseResponder.swift
//
//
//  Created by <PERSON> on 2022/12/9.
//

import Foundation
import WHBaseLibrary

public typealias WHRouteCompletion = () -> Void

open class WHRouterBaseResponder: NSObject {
    
    var configDict: [String: Any] = [:]
    public private(set) var params: [String: Any] = [:]
    
    public var sourceViewController: UIViewController?
    /// 目标控制器；如果不遵循 WHRouteProtocol 协议，则无法通过路由传参！
    public var targetViewController: UIViewController?
    /// 跳转方式
    var openMode: WHRouterOpenMode = .push
    var openAnimated = true
    /// 是否需要登录
    var needsLogin = false
    ///跳转的URL
    var routerURL: URL?
    
    var beSingleVC = false
    
    var filter: WHRouteBaseFilter?
    
    required public override init() {
        super.init()
    }
    
    deinit {
        WHPrint(self, #function)
    }
    
    func setConfig(with config: [String: Any]) {
        configDict = config
        
        refreshConfig(with: config)
        
        if let name = config.routeValue(for: .controllerName) as? String,
           let cls = NSClassFromString(name),
           let clsType = cls as? UIViewController.Type {
            targetViewController = clsType.init()
        }
        
        if let name = config.routeValue(for: .filter) as? String,
           let cls = NSClassFromString(name),
           let clsType = cls as? WHRouteBaseFilter.Type {
            filter = clsType.init()
        }
    }
    
    open func respond(with URL: URL,
                 viewController: UIViewController? = nil,
                 params: [String: Any]? = nil,
                 application: UIApplication? = nil) {
        self.routerURL = URL
        // 1. 合并参数
        self.mergeRouteParams(with: URL,
                              params: params)
        
        // 2. 处理filter
        self.filterHandle()
        
        // 3. 准备跳转
        self.prepareRoute(with: viewController,
                          application: application)
        
        // 4. 目标控制器加载路由参数
        if let targetVC = targetViewController as? WHRouteProtocol {
            targetVC.loadRouteParams(self.params)
        }
        
        // 5. 跳转逻辑
        self.routeWithOrWithoutLogin()
    }
    
    /// 合并路由参数：url中的参数、params字典中的参数，以params参数为主
    /// - Parameters:
    ///   - URL: 路由url
    ///   - params: 参数字典
    public func mergeRouteParams(with URL: URL,
                                 params: [String: Any]?) {
        var aParams = URL.params
        if let params = params {
            aParams.merge(params) { (current, new) in new }
        }
        self.params = aParams
    }
    
    /// 处理filter：filter中可根据不同参数，做出不同的处理
    public func filterHandle() {
        guard let filter = self.filter else {
            return
        }
        if let vc = filter.targetViewControllerWith(params,
                                                    originalVC: targetViewController) {
            self.targetViewController = vc
        }
        if let mode = filter.openModeWith(params) {
            self.openMode = mode
        }
    }
    
    /// 跳转前准备工作：设置跳转模式、是否动画、是否需要登录。。。
    /// - Parameters:
    ///   - viewController: 当前控制器
    ///   - application: 应用实例
    public func prepareRoute(with viewController: UIViewController?,
                             application: UIApplication?) {
        
        refreshConfig(with: params)
        
        if let viewController = viewController {
            self.sourceViewController = viewController
        } else {
            self.sourceViewController = WHRouter.topViewController
        }
    }
    
    /// 跳转逻辑（需要登录的页面包括登录逻辑）
    public func routeWithOrWithoutLogin() {
        guard let currentViewController = self.sourceViewController else {
            return
        }
        
        if self.needsLogin,
           let hasLogined = WHRouterLoginSharedManager.delegate?.hasLogined(),
           hasLogined == false {
            WHRouterLoginSharedManager.delegate?.needsToLogin(currentViewController) { (success: Bool) in
                if success {
                    WHRouterLoginSharedManager.didLogined()
                } else {
                    WHRouterLoginSharedManager.responder = nil
                }
            }
            WHRouterLoginSharedManager.responder = self
        } else {
            self.route()
        }
    }
    
    /// 跳转逻辑
    /// - Parameter completion: 完成回调
    open func route(_ completion: WHRouteCompletion? = nil) {
        var routeUrl: String? = nil
        if let url = self.params.routeValue(for: .routeUrl) as? String {
            routeUrl = url
        }
        
        switch self.openMode {
        case .push:
            self.push() {
                completion?()
                self.showDebugRouteAlert(routeUrl)
            }
        case .present:
            self.present() {
                completion?()
                self.showDebugRouteAlert(routeUrl)
            }
        case .tabSwitch:
            WHPrint("tabSwitch...")
        }
    }
    
    func refreshConfig(with config: [String: Any]) {
        if let str = config.routeValue(for: .openMode) as? String,
           let mode = WHRouterOpenMode(rawValue: str) {
            openMode = mode
        }
        
        if let animated = config.routeValue(for: .openAnimated) as? Bool {
            openAnimated = animated
        }
        
        if let needs = config.routeValue(for: .needsLogin) as? String, needs == "1" {
            needsLogin = true
        } 
        
        if let single = config.routeValue(for: .beSingleVC) as? Bool {
            beSingleVC = single
        }
    }
    
    private func push(_ completion: WHRouteCompletion? = nil) {
        guard let currentViewController = self.sourceViewController,
              let navigationController = currentViewController.navigationController,
              let targetVC = self.targetViewController else {
            // 如果不存在导航控制器，就执行present跳转
            self.present(completion)
            return
        }
        
//        if navigationController.isKind(of: WHBaseNavigationController.self) == false &&
//           (currentViewController is WHRouteProtocol) == false {
//            // 如果源viewController和导航控制器都不是我们自己的类示例，说明此次跳转是宿主App触发，必须用present方式
//            self.present(completion)
//            return
//        }
        
        var temp = navigationController.viewControllers
        
        if let removeLastOne = self.params.routeValue(for: .removeLastOne) as? Bool,
           removeLastOne {
            temp.removeLast()
        }
        if self.beSingleVC {
            temp = temp.filter { $0.isMember(of: type(of: targetVC)) == false }
        }
        if let removeBetweenVCs = self.params.routeValue(for: .removeBetweenVCs) as? Bool,
           removeBetweenVCs,
           let first = temp.first {
            temp = [first]
        }
        if let beRootVC = self.params.routeValue(for: .beRootVC) as? Bool,
           beRootVC {
            temp.removeAll()
        }
        
        targetVC.hidesBottomBarWhenPushed = true
        temp.append(targetVC)
        
        navigationController.setViewControllers(temp,
                                                animated: self.openAnimated)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            completion?()
        }
    }
    
    private func present(_ completion: WHRouteCompletion? = nil) {
        guard let currentViewController = self.sourceViewController,
              let targetVC = self.targetViewController else {
            return
        }
        
        targetVC.modalPresentationStyle = .fullScreen
        currentViewController.present(targetVC,
                                      animated: self.openAnimated,
                                      completion: completion)
    }
    
    /// 路由弹窗
    private func showDebugRouteAlert(_ url: String?) {
//        guard MBMedicalSharedManager.config.environment != .release else {
//            return
//        }
//        guard let url = url else {
//            return
//        }
//        let alert = UIAlertController(title: "Route URL", message: url, preferredStyle: .alert)
//        alert.addAction(UIAlertAction(title: "OK", style: .cancel, handler: nil))
//        MBRouter.topViewController?.present(alert, animated: true, completion: nil)
    }
}
