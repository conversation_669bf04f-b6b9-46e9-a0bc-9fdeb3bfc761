//
//  WHRouterComResponder.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2023/10/7.
//

import Foundation
import StoreKit


class WHRouterComResponder: WHRouterBaseResponder{

    /// 跳转逻辑
    /// - Parameter completion: 完成回调
    override func route(_ completion: WHRouteCompletion? = nil) {
        guard let url = self.routerURL else {
            return
        }
        if let path = url.paths.last{
            switch WHOtherPath(rawValue: path) {
            case .accountSecurity:
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    let vc = WHAccountShareManager.getAccountManagerVC()
                    WHRouter.topViewController?.navigationController?.pushViewController(vc, animated: true)
                }
            case .review:
                let appId  = "**********"
                if let url = URL(string: "itms-apps://itunes.apple.com/app/id\(appId)?action=write-review") {
                    UIApplication.shared.openURL(url)
                }
            case .none:
                debugPrint("4444444")
            }
        }
        
    }
}

enum WHOtherPath: String {
    case accountSecurity = "account_security"//
    case review = "good_review"
}
