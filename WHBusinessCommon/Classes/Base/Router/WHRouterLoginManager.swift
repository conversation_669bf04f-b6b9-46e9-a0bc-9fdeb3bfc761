//
//  WHRouterLoginManager.swift
//  
//
//  Created by <PERSON> on 2022/12/12.
//

import Foundation

class WHRouterLoginManager {
    
    weak var delegate: WHRouterLoginDelegate?
    
    var responder: WHRouterBaseResponder?
    
    fileprivate static let `default` = WHRouterLoginManager()
    private init() {}
    
    func didLogined() {
        responder?.route()
        responder = nil
    }
    
}

let WHRouterLoginSharedManager = WHRouterLoginManager.default
