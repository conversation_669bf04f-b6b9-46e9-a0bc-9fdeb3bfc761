//
//  WHRouterMiniResponder.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2023/10/4.
//

import Foundation

class WHRouterMiniResponder: WHRouterBaseResponder {
    
//    override func respond(with URL: URL,
//                 viewController: UIViewController? = nil,
//                 params: [String: Any]? = nil,
//                 application: UIApplication? = nil) {
//        mergeRouteParams(with: URL, params: params)
//        prepareRoute(with: viewController,
//                          application: application)
//        routeWithOrWithoutLogin()
//    }
    
    override func route(_ completion: WHRouteCompletion? = nil) {
        guard let URL = self.routerURL else { return }
//        let schemeModel = WHMiniAppSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
//        if schemeModel?.host == "openMiniApp" { //openMiniApp
//            WHMiniAppManager.shared.wh_openMiniAppWithMTLink(link: URL.absoluteString, parentVC: UIViewController.wh_top())
//        } else { //miniapp
//            let openType: String = (schemeModel?.queryDic?["type"] ?? "") as! String
//            if openType == "2" { //通过链接方式打开小程序
//                let linkStr:String = (schemeModel?.queryDic?["url"] ?? "") as! String
//                if linkStr.isEmpty {
//                    return
//                }
//                WHMiniAppManager.shared.wh_openMiniAppWithLink(link: linkStr ?? "", parentVC: UIViewController.wh_top())
//            } else { //通过APPID方式打开小程序
//                let appidStr:String = (schemeModel?.queryDic?["appid"] ?? "") as! String
//                let pathStr:String = (schemeModel?.queryDic?["path"] ?? "") as! String
//                let paramsStr:String = (schemeModel?.queryDic?["params"] ?? "") as! String
//                if appidStr.isEmpty {
//                    return
//                }
//                let encodingParams = paramsStr.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
//                WHMiniAppManager.shared.wh_openMiniApp(appid: appidStr, path: pathStr, params: encodingParams, parentVC: UIViewController.wh_top())
//            }
//        }
    }
}

public class WHMiniAppSchemeModel: NSObject {
    
    public var originScheme: String?
    public var scheme: String?
    public var host: String?
    public var path: String?
    public var query: String?
    public var queryDic: Dictionary<String, Any>?
    
    public static func modelWithSchemeUrl(schemeUrl:URL) -> WHMiniAppSchemeModel? {
        if !(schemeUrl is URL) {
            return nil
        }
        let scheme = schemeUrl.scheme
        let host = schemeUrl.host
        let path = schemeUrl.path
        let query = schemeUrl.query
        
        let schemeModel = WHMiniAppSchemeModel()
        schemeModel.originScheme = schemeUrl.absoluteString
        schemeModel.scheme = scheme
        schemeModel.host = host
        schemeModel.path = path
        schemeModel.query = query
        schemeModel.queryDic = query?.wh_conventToDictionaryWithInnerString(innerStr: "=", outterStr: "&", isDecode: true)
        
        return schemeModel
    }
}

