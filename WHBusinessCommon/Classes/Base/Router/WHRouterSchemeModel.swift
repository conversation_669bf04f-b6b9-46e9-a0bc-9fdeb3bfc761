//
//  WHRouterSchemeModel.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/2/22.
//

import Foundation

open class WHRouterSchemeModel: NSObject {
    
    public var originScheme: String?
    public var scheme: String?
    public var host: String?
    public var path: String?
    public var query: String?
    public var queryDic: Dictionary<String, Any>?
    
    public static func modelWithSchemeUrl(schemeUrl:URL) -> WHRouterSchemeModel? {
        if !(schemeUrl is URL) {
            
            return nil
        }
        let scheme = schemeUrl.scheme
        let host = schemeUrl.host
        let path = schemeUrl.path
        let query = schemeUrl.query
        
        let schemeModel = WHRouterSchemeModel()
        schemeModel.originScheme = schemeUrl.absoluteString
        schemeModel.scheme = scheme
        schemeModel.host = host
        schemeModel.path = path
        schemeModel.query = query
        schemeModel.queryDic = query?.wh_conventToDictionaryWithInnerString(innerStr: "=", outterStr: "&", isDecode: true)
        
        return schemeModel
    }
    
    public static func getRouteParams(url: URL) -> [String: Any] {
        var params: [String: Any] = [:]
        if let urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false),
            let queryItems = urlComponents.queryItems {
            let urlParams = queryItems.reduce([String: Any](), {
                var resultParams: [String: Any] = $0
                resultParams[$1.name] = $1.value
                return resultParams
            })

            params.merge(urlParams, uniquingKeysWith: { (current, _) in return current })
        }
        return params
    }
}

extension String {
    public func wh_conventToDictionaryWithInnerString(innerStr: String, outterStr: String, isDecode:Bool) -> [String: Any] {
        let array = self.components(separatedBy: outterStr)
        var dic = [String: Any]()
        for str in array {
            let subArr = str.components(separatedBy: innerStr)
            if subArr.count == 2 {
                let obj = isDecode ? subArr[1].removingPercentEncoding : subArr[1]
                let key = subArr[0]
                if !key.isEmpty {
                    dic[key] = obj
                }
            }
        }
        return dic
    }
}
