//
//  WHRouterService.swift
//
//
//  Created by <PERSON> on 2022/12/9.
//

import Foundation
import WHBaseLibrary

class WHRouterService {

    static var configFile: String?

    static var responderConfig: [String: Any]?

    static var scheme: String?

    static func register(with configFile: String,
                         scheme: String,
                         loginDelegate: WHRouterLoginDelegate?) {
        Self.configFile = configFile
        if let dict = NSDictionary(contentsOfFile: configFile) as? [String: Any] {
            Self.responderConfig = dict
        }
        Self.scheme = scheme
        WHRouterLoginSharedManager.delegate = loginDelegate
    }
    
    static func hasRegistered() -> Bool {
        guard let configFile = configFile,
            let scheme = scheme else {
            return false
        }
        return configFile.count > 0 && scheme.count > 0
    }

    static func route(with URL: URL,
                      viewController: UIViewController? = nil,
                      params: [String: Any]? = nil,
                      application: UIApplication? = nil) {
        guard let scheme = Self.scheme else {
            WHPrint("unregister business scheme!!!")
            return
        }

        guard scheme == URL.scheme else {
            WHPrint("unsuport business scheme: \(URL.scheme ?? "")!!!")
            return
        }

        guard let configDict = Self.responderConfig else {
            WHPrint("responder config register failed!!!")
            return
        }

        guard let hostName = URL.host,
        let hostDict = configDict[hostName] as? [String: Any],
        let host = WHRouterURLHost.host(with: hostDict) else {
            WHPrint("unsuport host: \(URL.host ?? "")!!!")
            return
        }

        host.route(with: URL,
                   viewController: viewController,
                   params: params,
                   application: application)
    }

}

class WHRouterURLHost {

    let config: [String: Any]

    static func host(with config: [String: Any]?) -> WHRouterURLHost? {
        guard let dict = config else {
            return nil
        }
        return WHRouterURLHost(with: dict)
    }

    init(with config: [String: Any]) {
        self.config = config
    }

    func route(with URL: URL,
               viewController: UIViewController? = nil,
               params: [String: Any]? = nil,
               application: UIApplication? = nil) {
        // 保存路由url
        var aParams: [String: Any] = [WHRouterParamsKey.routeUrl.value: URL.absoluteString]
        if let routeUrl = URL.absoluteString.removingPercentEncoding {
            aParams[WHRouterParamsKey.routeUrl.value] = routeUrl
        }
        if let temp = params {
            aParams.merge(temp) { (current, new) in current }
        }
        
        let responder = routerResponder(with: URL)
        responder.respond(with: URL,
                          viewController: viewController,
                          params: aParams,
                          application: application)
    }

    func routerResponder(with URL: URL) -> WHRouterBaseResponder {

        var configDict = config
        if let subConfigs = config.routeValue(for: .subResponder) as? [String: Any],
           URL.paths.count > 0,
           let path = URL.paths.first,
           let subConfig = subConfigs[path] as? [String: Any] {
            if let controllerName = subConfig.routeValue(for: .controllerName) as? String,
               controllerName.isEmpty == false {
                configDict = subConfig
            } else if let filterName = subConfig.routeValue(for: .filter) as? String,
                      filterName.isEmpty == false {
                configDict = subConfig
            }
        }

        if let name = configDict.routeValue(for: .responderName) as? String,
           let cls = NSClassFromString(name),
           let clsType = cls as? WHRouterBaseResponder.Type,
           let responder = clsType.init() as? WHRouterBaseResponder {
            responder.setConfig(with: configDict)
            return responder
        } else {
            let responder = WHRouterBaseResponder()
            responder.setConfig(with: configDict)
            return responder
        }

    }

}

extension Dictionary where Key == String {

    func routeValue(for key: WHRouterParamsKey) -> Any? {
        return self[key.value]
    }

}

extension URL {

    var paths: [String] {
        return path.components(separatedBy: "/").filter { $0.isEmpty == false }
    }

    public var params: [String: Any] {
         guard var query = query else {
             return [:]
         }
         if let temp = query.removingPercentEncoding {
             query = temp
         }
         var result: [String: Any] = [:]
         if let host = host,
            host == "web" {
             if let range = query.range(of: "url=") {
                 let value = query[range.upperBound...]
                 result["url"] = String(value)
                 let newQuery = query[..<range.lowerBound]
                 query = String(newQuery)
                 if query.suffix(1) == "&" {
                     query.removeLast()
                 }
             }
         }
         query.components(separatedBy: "&").forEach { (aQuery: String) in
             if let range = aQuery.range(of: "=") {
                 let key = aQuery[..<range.lowerBound]
                 let value = aQuery[range.upperBound...]
                 result[String(key)] = String(value)
             }
         }
        result["ori_schema"] = self.absoluteString
         return result
     }

}
