//
//  WHRouterTabResponder.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2023/10/5.
//

import Foundation
import WHBaseLibrary

class WHRouterTabResponder: WHRouterBaseResponder{
    public var targetViewControllerClass: WHRouteProtocol.Type = WHViewController.self
    
    override func setConfig(with config: [String: Any]) {
        configDict = config
        if let name = config.routeValue(for: .controllerName) as? String,
           let cls = NSClassFromString(name),
           let clsType = cls as? WHRouteProtocol.Type {
            targetViewControllerClass = clsType
        }
    }
        
//    override func respond(with URL: URL,
//                          viewController: UIViewController? = nil,
//                          params: [String: Any]? = nil,
//                          application: UIApplication? = nil) {
//        // 合并参数
//        self.mergeRouteParams(with: URL,
//                              params: params)
//
//        // 准备跳转
//        self.prepareRoute(with: viewController,
//                          application: application)
//
//        self.routeWithOrWithoutLogin()
//    }
    
    /// 跳转逻辑
    /// - Parameter completion: 完成回调
    open override func route(_ completion: WHRouteCompletion? = nil) {
        guard let currentViewController = self.sourceViewController else {
            return
        }
        
        let targetVC = UIApplication.shared.keyWindow?.rootViewController ?? UIViewController()
        
        if let tabBarController = targetVC as? UITabBarController {
            selectTabBarControllerItem(tabBarController)
        } else if let nav = targetVC as? UINavigationController,
                  let tabBarController = nav.viewControllers.first as? UITabBarController {
            selectTabBarControllerItem(tabBarController)
        }
    }

    private func selectTabBarControllerItem(_ tabBarController: UITabBarController) {
        var selectIndex = 0
        if let viewControllers = tabBarController.viewControllers {
            for (index, vc) in viewControllers.enumerated() {
                if let nav = vc as? UINavigationController,
                   let rootVC = nav.viewControllers.first,
                   rootVC.isKind(of: targetViewControllerClass) {
                    selectIndex = index
                    if let targetVC = vc as? WHRouteProtocol {
                        targetVC.loadRouteParams(self.params)
                    }
                    break
                } else if vc.isKind(of: targetViewControllerClass) {
                    selectIndex = index
                    if let targetVC = vc as? WHRouteProtocol {
                        targetVC.loadRouteParams(self.params)
                    }
                    break
                }
            }
        }
        tabBarController.wh_dismissAllPresentedControllerOnSelfWith(animated: false)
        tabBarController.navigationController?.popToRootViewController(animated: false)
        tabBarController.selectedIndex = selectIndex
    }
}
