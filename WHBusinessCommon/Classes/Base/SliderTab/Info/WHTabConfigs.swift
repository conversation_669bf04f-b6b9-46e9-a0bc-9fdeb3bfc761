//
//  WHTabConfigs.swift
//  MeituWhee
//
//  Created by zhanglifei on 2023/9/20.
//

import Foundation

public class WHTabConfigs: Equatable {
  
    required public init() {
      
    }
    
    public init(_ tabInfos: [WHTabInfo]) {
        self.tabInfos = tabInfos
    }
    
    public var defaultIndex: Int = 0
    public var defaultTabInfo: WHTabInfo?
    public var tabInfos: [WHTabInfo] = []
    
    public static func == (lhs: WHTabConfigs, rhs: WHTabConfigs) -> Bool {
        return lhs.tabInfos == rhs.tabInfos
    }
}

