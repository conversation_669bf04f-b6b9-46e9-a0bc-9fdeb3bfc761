//
//  WHTabInfo.swift
//  MeituWhee
//
//  Created by z<PERSON>lifei on 2023/9/20.
//

import Foundation
import YYModel

public enum WHTaskType: Int {
    case textImg  = 0   // 文生图
    case imgImg   = 1   // 图生图
    case model    = 2   // 模型
    case extend   = 3   // 图像扩展
    case inpaint  = 4   // 局部重绘
    case aiVideo  = 5   // AI视频
    case aiEraser       // AI消除
    case aiMagicsr      // AI超清
    case aiTemplate     // AI创作
    case project        // 项目
    case aiLive         // AILive
    case aiVideoMagicsr // 视频超清
    case oneSentence    //一句话修图
}

public enum WHFeedFromType: Int {
    case unkown      = 0     // 未知
    case home        = 1     // 首页feed
    case inspiration = 2     // 灵感feed
    case search      = 3     // 搜索结果页feed
    case works       = 4     // 我的作品feed
    case model       = 5     // 我的模型feed
    case collects    = 6     // 我的收藏feed
    case history     = 8     // 历史作品feed
    case project     = 9     // 我的项目feed
    
    public func getLoaction()-> String {
        switch self {
        case .unkown:
            return ""
        case .home:
            return "home_page"
        case .inspiration:
            return "gallery_page"
        case .works:
            return "my_work_page"
        case .model:
            return "my_model_page"
        case .collects:
            return "my_favor_page"
        case .search:
            return "search_results_page"
        case .history:
            return "history_page"
        case .project:
            return ""
        }
        return ""
    }
    
}

@objcMembers public class WHTabInfo: NSObject, YYModel, NSCoding {
    
    public var tabID: String = ""
    // 类型 1文生图图生图内容 2模型内容
    public var tabType: Int = 1
    public var name: String = ""
    // 接口
    public var urlString: String = ""
    /// 未选中图标
    public var picture: String = ""
    /// 选中图标
    public var selePicture: String = ""
    /// tab对应的任务种类
    public var taskCategory: String = ""

    ///打点数据
    public var analyString: String = ""
    public var subTabs:[WHSubTabInfo] = []
    public var selectTab: WHSubTabInfo?
    // 哪个页面的feed
    public var feedFromType: WHFeedFromType = .unkown
    
    public lazy var taskType: WHTaskType = {
        var type:WHTaskType = .textImg
        switch taskCategory {
        case "txt2img":
            type = .textImg
        case "img2img":
            type = .imgImg
        case "model":
            type = .model
        case "extend":
            type = .extend
        case "inpaint":
            type = .inpaint
        case "ai_video":
            type = .aiVideo
        case "ai_eraser":
            type = .aiEraser
        case "magicsr":
            type = .aiMagicsr
        case "ai_template":
            type = .aiTemplate
        case "image_to_live":
            type = .aiLive
        case "text_image_editing":
            type = .oneSentence
        default:
            type = .textImg
        }
        return type
    }()
 
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "tabID"  : "id",
            "tabType": "type",
            "name"   : "name",
            "picture": "pic",
            "selePicture": "check_pic",
            "taskCategory": "task_category"
        ]
    }
}

public class WHSubTabInfo: NSObject{
    public var name: String = ""
    public var type: Int = 1
    public var analyString: String = ""
    
    public init(name: String, type: Int,analy: String) {
        self.name = name
        self.type = type
        self.analyString = analy
    }
}
