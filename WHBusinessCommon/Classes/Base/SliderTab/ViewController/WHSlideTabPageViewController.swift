//
//  WHSlideTabPageViewController.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/9/20.
//

import Foundation
import WHBusinessCommon
import WHBaseLibrary

public enum WHSlidePageViewLoadType: Int {
    case loadData = 1
    case scroll
    case select
}

public enum WHFeedListRefreshCause {
    
    public enum ClickCause {
        /// click title of current page
        case currentTitle
        /// click title of other pages
        case otherTitles
        /// click home tab
        case bottomTab
        /// click guides
        case guides
        /// click new guides
        case newGuides
    }
    
    public enum InteractiveCause {
        /// pull header
        case `default`
        /// swipe between tabs
        case horizontalSwipe
    }
    
    /// refresh for first time
    case initial
    /// trigerred automatically, eg. view controller appearance changes,
    /// `emphasized` means whether bubble should appear
    case automatically(emphasized: Bool)
    /// trigerred by button click
    case byClick(ClickCause)
    /// by interactive gestures
    case byGesture(InteractiveCause)
    /// from push notifications or other external routing
    case fromRouter
    /// 旧版本首页或无网切换等
    case ignored
    
    static let `default`: WHFeedListRefreshCause = .byGesture(.default)
}

public protocol WHSubPageViewProtocol {
    func connectScrollView() -> UIScrollView?
    func selectTabRefresh(_ refreshCause: WHFeedListRefreshCause)
    ///点击tab
    func didSelectTab()
    
    func mineTabClick(isShow: Bool)
}

open class WHSlideTabPageViewController: WHViewController {
    
    open var dataSoure: [String] = []
    
    open var tabConfigs: WHTabConfigs {
        didSet {
            self.defaultPage = tabConfigs.defaultIndex
        }
    }
    
    open var tabInfos: [WHTabInfo] = []
    
    open var pageIndex = -1 {
        didSet {
            currentViewController = childViewController(self.pageIndex)
        }
    }
    
    open var childviewControllers: [UIViewController] = []
    /// 当前正在显示的页面
    open var currentViewController: UIViewController?
    /// 进入哪个页面, 默认0，表示第一页
    @objc dynamic open var defaultPage: Int = 0
    
    open var scrollEnabled: Bool = true {
        didSet {
            self.pageContainerScrollView.isScrollEnabled = scrollEnabled
        }
    }
    
    //    更改为字典，只有VC显示时才创建，否则不创建对应VC, 无法直接调用，需要调用 childViewController(index）
    open var pageChildViewControllers: [String: UIViewController] = [:]
    
    open lazy var topSlideTabView: WHSlideTabBar = {
        let tabView = WHSlideTabBar(frame: CGRect(origin: .zero, size: CGSize(width: view.bounds.width, height: 32)))
        tabView.barColor = UIColor(wh_hexString: "#3549FF")
        tabView.delegate = self
        tabView.dataSource = self
        tabView.scaleRatio = 0
        tabView.flowLineSize = CGSize(width: tabView.flowLineSize.width, height: 3)
        tabView.isHiddenBottomShadowLine = true
        
        tabView.tabItemRGB = WHSlideRGBAColor(red: 122, green: 126, blue: 133, alpha: 1)
        tabView.tabItemSelectedRGB = WHSlideRGBAColor(red: 255, green: 255, blue: 255, alpha: 1)
        tabView.tabItemTitleColor = UIColor(wh_hexString: "#7A7E85")
        tabView.tabItemSelectedTitleColor = .white
        tabView.tabItemTitleFont = UIFont.systemFont(ofSize: 17)
        tabView.tabItemSelectTitleFont = UIFont.pingFangSCFont(ofSize: 18,weight: .semibold)
        tabView.backgroundColor = UIColor(wh_hexString: "#000000")
        
        return tabView
    }()
    
    open lazy var pageContainerScrollView: WHContainerScrollView = {
        let scrollView = WHContainerScrollView(frame: CGRect(origin: .zero, size: CGSize(width: view.bounds.width, height: 0)))
        scrollView.containerScrollDataSource = self
        scrollView.containerScrollDelegate = self
        return scrollView
    }()
   
    public init(tabConfigs: WHTabConfigs) {
        self.tabConfigs = tabConfigs
        tabInfos = tabConfigs.tabInfos
        super.init(nibName: nil, bundle: nil)
        defaultPage = tabConfigs.defaultIndex
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func viewDidLoad() {
        super.viewDidLoad()

        setupViews()
        reloadData()
    }

    open func setupViews() {
        view.addSubview(topSlideTabView)
        view.addSubview(pageContainerScrollView)
        pageContainerScrollView.frame = CGRect(
            x: 0,
            y: topSlideTabView.bottom,
            width: view.bounds.width,
            height: view.bounds.height - topSlideTabView.bounds.height)
    }
    
    open func reloadData() {
        guard defaultPage < tabInfos.count else { return }
        topSlideTabView.reloadData()
        topSlideTabView.scrollToTab(defaultPage, animated: false)
        pageContainerScrollView.reloadData(defaultPage)
        goToPage(defaultPage, loadType: .loadData)
    }
    
    open func goToPage(_ page: Int, loadType: WHSlidePageViewLoadType) {
        pageIndex = page
        currentViewController = self.childViewController(page)
    }
    
    @objc open func childViewController(_ index: Int) -> UIViewController {
        guard index < self.childviewControllers.count else {
            return UIViewController()
        }
        let vc = self.childviewControllers[index]
        return vc
    }
    
    /// 代码滑动到指定index
    open func slideTo(_ index: Int, animated: Bool = false) {
        if index == pageIndex { return }
        topSlideTabView.scrollToTab(index, sendAction: true, animated: animated)
    }
    
    open func didTapTabTitle(_ refreshCause: WHFeedListRefreshCause = .byClick(.currentTitle)) {

    }
    
    open func didTapTabAnalytics(_ title: String) {

    }
}


extension WHSlideTabPageViewController: WHSlideTabDataSource {
    public func slideTabTitles() -> [String] {
        return tabInfos.map { $0.name }
    }
}

extension WHSlideTabPageViewController: WHSlideTabDelegate {
    
    public func allowUserBehavior(_ enable: Bool) {
        view.isUserInteractionEnabled = enable
    }
    
    @objc open func didSelect(_ slideTab: WHSlideTabBar, at index: Int) {
        if pageIndex != index {
            goToPage(index, loadType: .select)
            pageContainerScrollView.scrollToPage(index, animated: false)
        } else {
            didTapTabTitle(.byClick(.currentTitle))
        }
        let tabInfo = tabInfos.object(at: index)
        didTapTabAnalytics(tabInfo?.tabID ?? "")
    }
    
    public func showRedPoint(index: Int) -> Bool {
        return false
    }
    
    public func didSelectLeftView(_ info: Any?) {
        
    }
    
    @objc open func slideTabBarWillBeginDragging(_ slideTabBar: WHSlideTabBar) {
        
    }
}

extension WHSlideTabPageViewController: MTContainerScrollViewDataSource {
    public func numberOfPages() -> Int {
        return tabInfos.count
    }
    
    public func viewForPage(_ index: Int) -> UIView {
        return self.childViewController(index).view
    }
}

extension WHSlideTabPageViewController: MTContainerScrollViewDelegate {
    open func containerScrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let page = pageContainerScrollView.getCurrentPageIndex()
        topSlideTabView.scrollToTab(page)
        self.goToPage(page, loadType: .scroll)
    }
    
    open func containerScrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        goToPage(pageContainerScrollView.getCurrentPageIndex(), loadType: .scroll)
    }
    
    open func containerScrollViewDidScroll(_ scrollView: UIScrollView) {
        
    }
    
    open func containerScrollViewDidScroll(at index: Int, scrollRatio: CGFloat) {
        topSlideTabView.adjustIndicator(at: index, ratio: scrollRatio)
    }
}
