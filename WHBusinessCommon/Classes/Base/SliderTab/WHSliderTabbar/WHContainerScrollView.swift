//
//  WHContainerScrollView.swift
//  MTXX
//
//  Created by 刘欢 on 2020/3/10.
//  Copyright © 2020 Meitu. All rights reserved.
//

import Foundation
import UIKit
import WHBaseLibrary

public enum MTScrollViewEventStrategy: String {
    case willBeginDragging
    case didEndDragging
}

@objc public protocol MTContainerScrollViewDelegate {
    @objc optional func containerScrollViewDidScroll(to page: Int)
    @objc optional func containerScrollViewDidEndDecelerating(_ scrollView: UIScrollView)
    @objc optional func containerScrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView)
    @objc optional func containerScrollViewDidScroll(_ scrollView: UIScrollView)
    @objc optional func containerScrollViewDidScroll(at index: Int, scrollRatio: CGFloat)
}

@objc public protocol MTContainerScrollViewDataSource {
    func numberOfPages() -> Int
    func viewForPage(_ index: Int) -> UIView
}


@objc(MTContainerScrollView)
public class WHContainerScrollView: UIScrollView, UIGestureRecognizerDelegate {
    public weak var containerScrollDataSource: MTContainerScrollViewDataSource?
    public weak var containerScrollDelegate: MTContainerScrollViewDelegate?
    
    var canScroll = false
    var currentPageIndex = 0
    var totalPageNumber = 0
    var changePageInner = false
    
    
    required public override init(frame: CGRect) {
        super.init(frame: frame)
        self.autoresizingMask = .flexibleHeight
        self.clipsToBounds = true
        self.isPagingEnabled = true
        self.isDirectionalLockEnabled = true
        self.showsHorizontalScrollIndicator = false
        self.showsVerticalScrollIndicator = false
        self.delegate = self
        self.backgroundColor = UIColor.clear
        self.delaysContentTouches = false
        self.canCancelContentTouches = false
        self.panGestureRecognizer.delegate = self
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc public func reloadData(_ defaultIndex: Int = 0) {
        if let dataSource = self.containerScrollDataSource {
            self.totalPageNumber = dataSource.numberOfPages()
            self.contentSize = CGSize(width: Int(self.bounds.width) * self.totalPageNumber, height: Int(self.bounds.height))
            
            let subviews = self.subviews
            for view in subviews {
                if let dataSource = self.containerScrollDataSource,
                   defaultIndex >= 0,
                   view != dataSource.viewForPage(defaultIndex) {
                    view.removeFromSuperview()
                }
            }
            
            self.scrollToPage(defaultIndex, animated: false)
        }
    }
    
    @objc public func getCurrentPageIndex() -> Int {
        let pageWidth = self.bounds.width
        let pageIndex = Int(floor((self.contentOffset.x - pageWidth / 2.0) / pageWidth)) + 1
        return pageIndex
    }
    
    @objc public func scrollToPage(_ index: Int, animated: Bool) {
        if index >= 0 {
            self.setContentOffset(CGPoint(x: CGFloat(index) * self.bounds.width, y: 0), animated: animated)
            self.loadPage(index)
        }
    }
    
    @objc public func loadPage(_ index: Int) {
        if let dataSource = self.containerScrollDataSource {
            let view = dataSource.viewForPage(index)
            view.frame = self.frameForPage(index, view: view)
            view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            self.addSubViewSafely(view)
        }
        self.currentPageIndex = index
    }
    
    func frameForPage(_ index: Int, view: UIView) -> CGRect {
        var frame = self.bounds
        frame.origin.x = frame.width * CGFloat(index)
        frame.size.width = frame.width
        return frame
    }
    
    func addSubViewSafely(_ view: UIView?) {
        if let v = view, !v.isDescendant(of: self) {
            self.addSubview(v)
        }
    }
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        self.contentSize = CGSize(width: Int(self.bounds.width) * self.totalPageNumber, height: Int(self.bounds.height))
    }
}

extension WHContainerScrollView: UIScrollViewDelegate {
    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
        containerScrollDelegate?.containerScrollViewDidScroll?(self)
        if (self.contentOffset.x > self.contentSize.width - self.bounds.width) || self.contentOffset.x < 0 {
            return
        }
        let offset = scrollView.contentOffset.x
        let index = Int(offset / scrollView.bounds.width)
        let scale = CGFloat(Int(offset) % Int(self.bounds.width)) / self.bounds.width
        containerScrollDelegate?.containerScrollViewDidScroll?(at: index, scrollRatio: scale)
    }
    
    public func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        wh_passEventWith(MTScrollViewEventStrategy.willBeginDragging.rawValue)
        self.changePageInner = true
    }
    
    public func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        wh_passEventWith(MTScrollViewEventStrategy.didEndDragging.rawValue)
    }
    
    public func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        self.changePageInner = false
        let index = self.getCurrentPageIndex()
        if index != self.currentPageIndex {
            self.scrollToPage(index, animated: true)
            containerScrollDelegate?.containerScrollViewDidEndDecelerating?(scrollView)
        }
    }
    
    public func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        let index = self.getCurrentPageIndex()
        if index != self.currentPageIndex, self.changePageInner {
            self.scrollToPage(index, animated: true)
            containerScrollDelegate?.containerScrollViewDidEndScrollingAnimation?(scrollView)
        }
        self.changePageInner = false
    }
}
