//
//  WHSlideFlowLine.swift
//  covert
//
//  Created by 刘欢 on 2020/3/11.
//  Copyright © 2020 MT. All rights reserved.
//

import Foundation
import UIKit

let kFlowLineWidth: CGFloat = 32.0
let kFlowLineHalfWidth: CGFloat = kFlowLineWidth/2.0
let kFlowLineHeight: CGFloat = 2.5

public enum WHSlideFlowDirection: Int {
    case left = 0
    case right = 1
}

public class WHSlideFlowLine: UIView {
    
    public var fixedLine: Bool = false
    public var baseLineWidth = kFlowLineWidth
    public override var frame: CGRect {
        didSet {
            
        }
    }
    public required override init(frame: CGRect) {
        super.init(frame: frame)
        self.layer.cornerRadius = 1.5
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func updateLine(_ percent: CGFloat, fromRect: CGRect, toRect: CGRect, flowDirection: WHSlideFlowDirection) {
        let lineWidth = CGFloat(self.baseLineWidth)
        let maxWidth = abs(toRect.minX-fromRect.minX) - lineWidth
        
        let a = 4 * (lineWidth - maxWidth)
        let b = 4 * (maxWidth - lineWidth)
        let c = lineWidth
        
        let width = a * percent * percent + b * percent + c  //honghao: 在percentage = 0.5时，width = maxWidth，利用0.5代入此式，c为定值，a与b为相反数，能推算出a与b的值
        
        var rect = fromRect
        rect.size.width = fixedLine ? c : width
        
        var baseMovespace = percent*(lineWidth/0.5)   //移动50%时，x移动距离为一倍的baselinewidth
        
        if (percent>0.5) {
            baseMovespace = (1-percent)*lineWidth*2
            rect.origin.x = ((flowDirection == WHSlideFlowDirection.right) ? toRect : fromRect).maxX - baseMovespace - width
        } else {
            rect.origin.x = ((flowDirection == WHSlideFlowDirection.right) ? fromRect : toRect).minX + baseMovespace
        }
        self.frame = rect
    }
    
}
