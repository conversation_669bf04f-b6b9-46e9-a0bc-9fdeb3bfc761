//
//  MTTopSlideBar.swift
//  MTXX
//
//  Created by 刘欢 on 2020/3/11.
//  Copyright © 2020 Meitu. All rights reserved.
//

import Foundation
import UIKit

let kFontSizeOfTab = 16
let kLeftMargin: CGFloat = 10.0
let kSelectBackgroundViewLeftMargin: CGFloat = 8.0
let kTabItemScalePercent: CGFloat = 0.105
let kTabItemStartPercent: CGFloat = 0.895
let kSlideTabSelectBackgroundViewColor = UIColor.init(r: 255, g: 234, b: 238)
let kSlideTabSelectBackgroundViewTopMargin: CGFloat = 2.0

public enum WHSlideTabSelectMode: Int {
    case flowLine = 1
    case selectBackgroundView
    case flowPic
}

public struct WHSlideRGBAColor {
    let red: CGFloat
    let green: CGFloat
    let blue: CGFloat
    let alpha: CGFloat
    public init(red: CGFloat, green: CGFloat, blue: CGFloat, alpha: CGFloat) {
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }
}

public enum WHSlideTabAlignment: Int {
    case left = 0
    case center
    case right
}

public enum WHSlideTabUIType: Int {
    case common = 0
    case border
}

let kBaseButtonTag: Int = 9991

//from 为初始正常值，to 为初始选中值
func calculateColorFloat(from: CGFloat, to:CGFloat, percent: CGFloat) -> CGFloat {
    return (from + (to - from) * percent)/255.0
}

//from 为初始正常值，to 为初始选中值
func calculateAlphaFloat(from: CGFloat, to:CGFloat, percent: CGFloat) -> CGFloat {
    return from + (to - from) * percent
}

func calculateColor(from: WHSlideRGBAColor, to:WHSlideRGBAColor, percent: CGFloat) -> UIColor {
    return UIColor(red: calculateColorFloat(from: from.red, to: to.red, percent: percent),
                   green: calculateColorFloat(from: from.green, to: to.green, percent: percent),
                   blue: calculateColorFloat(from: from.green, to: to.green, percent: percent),
                   alpha: calculateAlphaFloat(from: from.alpha, to: to.alpha, percent: percent))
}

func colorForSlideRABAColor(_ rgba: WHSlideRGBAColor) -> UIColor {
    return UIColor(red: rgba.red/255.0, green: rgba.green/255.0, blue: rgba.blue/255.0, alpha: rgba.alpha)
}


public protocol WHSlideTabDataSource: NSObjectProtocol {
    func slideTabTitles() -> [String]
}

@objc
public protocol WHSlideTabDelegate: NSObjectProtocol {
    
    @objc
    optional func allowUserBehavior(_ enable: Bool)
    
    func didSelect(_ slideTab: WHSlideTabBar, at index:Int)
    
    func showRedPoint(index: Int) -> Bool
    
    func didSelectLeftView(_ info: Any?)
    
    func slideTabBarWillBeginDragging(_ slideTabBar: WHSlideTabBar)
}


extension String {
    public func sizeWith(_ font : UIFont , _ maxSize : CGSize , _ lineMargin : CGFloat = 0) -> CGSize {
        let options = NSStringDrawingOptions.usesLineFragmentOrigin
        let paragraphStyle : NSMutableParagraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = lineMargin
        var attributes : [NSAttributedString.Key : Any] = [:]
        attributes[NSAttributedString.Key.font] = font
        attributes[NSAttributedString.Key.paragraphStyle] = paragraphStyle
        let textBouds = self.boundingRect(with: maxSize,
                                          options: options,
                                          attributes: attributes,
                                          context: nil)
        return textBouds.size
    }
}

open class WHSlideTabBar: UIView {
    
    /// tabitem是否可以放大，放大比例， 0默认不放大
    public var scaleRatio: CGFloat = 0.895
    
    public var selectedIndex: Int = 0
    
    public var barColor: UIColor = .black {
        didSet {
            self.flowLine.backgroundColor = barColor
        }
    }
    
    public var fixedLine: Bool = false {
        didSet {
            flowLine.fixedLine = fixedLine
        }
    }
    
    public var isBlurEnable = false
    
    public var flowLineSize: CGSize = CGSize(width: kFlowLineWidth, height: kFlowLineHeight)
    
    public var contentEdgeInsets: UIEdgeInsets = UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    
    public weak var delegate: WHSlideTabDelegate?
    public weak var dataSource: WHSlideTabDataSource?
    
    public var slideChangeTextColor = true
    
    public var hideFlowLineWhenSingleItem = true
    public var scrollViewOffSet: CGFloat = 0.0
    public var tabItemRGB: WHSlideRGBAColor = WHSlideRGBAColor(red: 174, green: 175, blue: 183, alpha: 1) {
        didSet {
            tabItemTitleColor = colorForSlideRABAColor(self.tabItemRGB)
        }
    }
    
    ///【Bugfix-MTXX-182876】【建议优化】排序页点击顶部tab栏切换时，无需有动画执行，直接切换即可
    public var needClickTabAnimate: Bool = true
    
    public var tabItemSelectedRGB: WHSlideRGBAColor = WHSlideRGBAColor(red: 44, green: 46, blue: 71, alpha: 1) {
        didSet {
            tabItemSelectedTitleColor = colorForSlideRABAColor(self.tabItemSelectedRGB)
        }
    }
    
    public var slideType: WHSlideTabUIType = .common {
        didSet {
            switch slideType {
            case .common:
                slideTypeCommonHelper()
            case .border:
                slideTypeBorderHelper()
            }
        }
    }
    
    public func slideTypeCommonHelper() {
        tabItemTitleMarginH = 2
        tabItemTitleMarginV = 2
        tabItemBorderColor = .clear
        tabItemBorderWidth = 0
        tabItemCornerRadius = 0
        tabItemSelectTitleFont = UIFont.boldSystemFont(ofSize: 11)
        tabItemTitleFont = UIFont.systemFont(ofSize: 11)
        tabScrollViewInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        tabItemTitleColor = UIColor(r: 68, g: 70, b: 113)
        tabItemSelectedTitleColor = UIColor.init(r: 253, g: 57, b: 96)
        barColor = UIColor(r: 255, g: 234, b: 238)
        tabSpace = 23
    }
    
    public func slideTypeBorderHelper() {
        tabItemTitleMarginH = 16
        tabItemTitleMarginV = 5
        tabItemBorderColor = UIColor(r: 230, g: 230, b: 233)
        tabItemBorderWidth = 1
        tabItemCornerRadius = 8
        tabItemSelectTitleFont = UIFont.boldSystemFont(ofSize: 12)
        tabItemTitleFont = UIFont.systemFont(ofSize: 12)
        tabScrollViewInset = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
        tabItemTitleColor = UIColor(r: 68, g: 70, b: 113)
        tabItemSelectedTitleColor = UIColor.init(r: 253, g: 57, b: 96)
        barColor = .clear
        tabSpace = 8
    }
    public var tabItemWidth: CGFloat = 0
    public var tabItemTitleMarginH: CGFloat = 2
    public var tabItemTitleMarginV: CGFloat = 2
    public var tabItemTitleColor: UIColor
    public var tabItemSelectedTitleColor: UIColor
    public var tabItemBorderColor: UIColor = .clear
    public var tabItemBorderWidth: CGFloat = 0
    public var tabItemCornerRadius: CGFloat = 8
    public var bgBackgroundColor: UIColor
    public var tabItemSelectTitleFont: UIFont = UIFont.boldSystemFont(ofSize: 16)
    public var tabItemTitleFont: UIFont = UIFont.systemFont(ofSize: 16)
    public var tabItemBgColor: UIColor = .clear
    public var tabItemSelectBgColor: UIColor = .clear
    
    public var tabScrollViewInset: UIEdgeInsets = .zero {
        didSet {
            scrollView.contentInset = tabScrollViewInset
            scrollView.contentOffset = CGPoint(x: -scrollView.contentInset.left, y: 0)
        }
    }
    
    private var tabList: [String] = []
    
    private lazy var tabTextSizeArray: [CGSize] = []
    
    private var barOrigin: CGPoint = CGPoint.zero
    
    public var tabSpace: CGFloat = 16.0
    
    /// 如果条数少支持自动更改间距
    public var autoAspectSpace: Bool = false
    
    public var leftSpace: CGFloat = kLeftMargin
    public var rightSpace: CGFloat = kLeftMargin
    //    tab所有文字的总宽度
    public var totalTabWidth: CGFloat = 0
    
    public var alignment: WHSlideTabAlignment = .center
    
    public var selectMode: WHSlideTabSelectMode = .flowLine
    
    public var canScroll = true
    
    public var isLeftMaskViewHidden = false {
        didSet {
            leftMask.isHidden = isLeftMaskViewHidden
        }
    }
    
    public lazy var scrollView :UIScrollView = {
        let view = UIScrollView(frame: .zero)
        view.backgroundColor = self.backgroundColor
        view.delegate = self
        view.showsHorizontalScrollIndicator = false
        view.showsVerticalScrollIndicator = false
        return view
    }()
        
    public lazy var bgView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var leftMask: UIImageView = {
        let maskImage = UIImage.init(named: "slidetab_left_mask_white.png")
        let imageView = UIImageView(image: maskImage)
        imageView.frame = CGRect(x: 0, y: 0, width: maskImage?.size.width ?? 0, height: scrollView.bounds.height)
        return imageView
    }()
    
    public lazy var flowLine: WHSlideFlowLine = {
        let line = WHSlideFlowLine(frame: CGRect(x: 0, y: scrollView.bounds.height - flowLineSize.height, width: flowLineSize.width, height: flowLineSize.height))
        line.layer.zPosition = 1
        return line
    }()
    
    public lazy var flowPicView: UIImageView = {
        let make = UIImageView.init(frame: CGRect(x: 0, y: scrollView.bounds.height - 16, width: 16, height: 16))
        make.contentMode = .scaleAspectFill
        make.image = UIImage.init(named: "icon_narTabUnder_image")
        return make
    }()
    
    public var leftView: UIView?
    public var rightView: UIView?
    public var bottomView: UIView?
    
    public lazy var bottomShadowLine: CALayer? = {
        let line = CALayer()
        line.frame = CGRect(x: 0.0, y: self.bounds.maxY - 0.5/UIScreen.main.scale, width: self.bounds.width, height:0.5/UIScreen.main.scale)
        line.backgroundColor = UIColor.init(white: 247.0/255.0, alpha: 1).cgColor
        self.layer.addSublayer(line)
        return line
    }()
    
    public var isHiddenBottomShadowLine = true {
        didSet {
            self.bottomShadowLine?.isHidden = self.isHiddenBottomShadowLine
        }
    }
    
    public override init(frame: CGRect) {
        self.tabItemTitleColor = colorForSlideRABAColor(self.tabItemRGB)
        self.tabItemSelectedTitleColor = colorForSlideRABAColor(self.tabItemSelectedRGB)
        self.bgBackgroundColor = UIColor.clear
        super.init(frame: frame)
        self.addSubview(bgView)
        self.addSubview(scrollView)
        self.addSubview(leftMask)
        setupView()
        scrollView.addSubview(flowLine)
        scrollView.addSubview(flowPicView)
    }
    
    required public init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        self.layoutScrollView()
    }
    
    func setupView() {
        if let left = self.leftCustomView() {
            self.leftView?.removeFromSuperview()
            self.leftView = left
            self.addSubview(left)
        }
        
        if let right = self.rightCustomView() {
            self.rightView?.removeFromSuperview()
            self.rightView = right
            self.addSubview(right)
        }
        
        if let bottom = self.bottomCustomView() {
            self.bottomView?.removeFromSuperview()
            self.bottomView = bottom
            self.insertSubview(bottom, belowSubview: bgView)
        }
        scrollView.frame = slideScrollViewFrame()
        leftMask.frame = CGRect(x: scrollView.frame.minX, y: scrollView.frame.minY, width: leftMask.frame.width, height: scrollView.bounds.height)
        
        bgView.frame = CGRect(x: 0, y: 0, width: self.width, height: scrollView.height)
        bgView.backgroundColor = bgBackgroundColor
    }
    
}

// MARK: - layout
extension WHSlideTabBar {
    
    func slideScrollViewFrame() -> CGRect {
        
        let height = customSlideScrollViewHeight() > 0 ? customSlideScrollViewHeight() : self.bounds.height
        var rect = CGRect(x: contentEdgeInsets.left,
                          y: contentEdgeInsets.top + self.scrollViewOffSet,
                          width: self.bounds.width - contentEdgeInsets.left - contentEdgeInsets.right,
                          height: height - contentEdgeInsets.bottom - contentEdgeInsets.top)
        
        if let left = self.leftCustomView() {
            rect.origin.x = left.frame.maxX + leftSpace
            rect.size.width = rect.width - left.frame.maxX - leftSpace - rightSpace
        }
        
        if let right = self.rightCustomView() {
            rect.size.width = rect.width - right.width - rightSpace - leftSpace
        }
        
        return rect
    }
    
    @objc func leftCustomView() -> UIView? {
        return nil
    }
    
    @objc func rightCustomView() -> UIView? {
        return nil
    }
    
    @objc func bottomCustomView() -> UIView? {
        return nil
    }
    
    @objc func customSlideScrollViewHeight() -> CGFloat {
        return 0.0
    }
}

// MARK: - func
extension WHSlideTabBar {
    
    public func reloadData(defaultSelect: Int = 0) {
        self.selectedIndex = defaultSelect
        if self.dataSource?.slideTabTitles() != self.tabList {
            self.tabList = self.dataSource?.slideTabTitles() ?? []
            self.totalTabWidth = 0
            self.tabTextSizeArray = self.calculateTabSizes(true)
            self.reloadUI()
        } else {
            self.reloadUI()
            self.selectTab(at: defaultSelect)
        }
        if hideFlowLineWhenSingleItem && self.tabList.count == 1 {
            flowLine.isHidden = true
        }
    }
    
    public func reloadScrollView() {
        self.tabTextSizeArray = self.calculateTabSizes(true)
        layoutSubviews()
    }
    
    private func layoutScrollView() {
        scrollView.frame = self.slideScrollViewFrame()
        
        var tabMargin = self.tabSpace
        var startMargin = ceil(self.tabSpace/2)
        let allSpace = scrollView.frame.width - (self.totalTabWidth + CGFloat(tabList.count) * tabMargin)
        if autoAspectSpace {
            if allSpace > 0 && self.alignment == .center {
                tabMargin = floor(self.tabSpace + allSpace/CGFloat(tabList.count + 1))
                startMargin = tabMargin
            }
        } else if allSpace > 0 {
            startMargin += allSpace/2
        }
        if self.alignment == .left {
            startMargin = 16
        }
        var xOffset: CGFloat = startMargin
        var currentOffsetX: CGFloat = 0
        var currentWidth: CGFloat = 0
        for (index, _) in tabList.enumerated() {
            let item = tabItem(at: index)
            let textSize = self.tabTextSizeArray[index]
            
            item.frame = CGRect(x: xOffset, y: (scrollView.frame.height - textSize.height)/2, width: textSize.width, height:textSize.height)
            
            
            if selectedIndex == index {
                currentOffsetX = xOffset
                currentWidth = textSize.width
                switch selectMode {
                case .flowLine:
                    let flowlineY = ((item.frame.maxY + 4) > (scrollView.bounds.height - flowLineSize.height)) ? (scrollView.bounds.height - flowLineSize.height) : (item.frame.maxY + 4)
                    flowLine.frame = CGRect(x: item.frame.midX - flowLineSize.width/2,
                                            y: flowlineY,
                                            width: flowLine.frame.width,
                                            height: flowLine.frame.height)
                case .selectBackgroundView:
                    flowLine.frame =  CGRect(x: item.frame.origin.x - kLeftMargin,
                                             y: item.frame.origin.y - kSlideTabSelectBackgroundViewTopMargin,
                                             width: item.frame.size.width + 2 * kLeftMargin,
                                             height: item.frame.size.height + 2 * kSlideTabSelectBackgroundViewTopMargin)
                case .flowPic:
                    flowPicView.frame = CGRect(x: item.frame.midX - 16/2,
                                               y: scrollView.bounds.height - 8 - 16,
                                               width: 16,
                                               height: 16)
                }
            }
            // 计算下一个tab的x偏移量
            xOffset += textSize.width + ((index == tabList.count - 1) ? startMargin : tabMargin)
        }
        
        let totalContentWidth: CGFloat = xOffset
        
        if (totalContentWidth + scrollView.contentInset.left + scrollView.contentInset.right <= scrollView.bounds.width) {
            var x: CGFloat = 0
            canScroll = false
            switch (self.alignment) {
            case .center:
                x = (scrollView.frame.width - totalContentWidth)/2.0
            case .left:
                x = leftSpace
            default:
                break
            }
            
            self.scrollView.frame = CGRect(x: scrollView.frame.origin.x + x, y:0.0 + self.scrollViewOffSet, width: totalContentWidth, height: self.scrollView.frame.height)
        }
        
        scrollView.contentSize = CGSize(width: xOffset, height: scrollView.frame.height)
        tabMargin = tabMargin == 0 ? self.rightSpace : tabMargin
        if currentOffsetX + currentWidth > scrollView.bounds.width {
            scrollView.contentOffset = CGPoint(x: currentOffsetX + currentWidth + tabMargin - scrollView.bounds.width, y: 0)
        } else {
            scrollView.contentOffset = CGPoint(x: -scrollView.contentInset.left, y: 0)
        }
    }
    
    private func reloadUI() {
        self.scrollView.subviews.forEach {
            $0.isHidden = true
        }
        scrollView.frame = self.slideScrollViewFrame()
        
        var tabMargin = self.tabSpace
        var startMargin = ceil(self.tabSpace/2)
        
        let allSpace = scrollView.frame.width - (self.totalTabWidth + CGFloat(tabList.count) * tabMargin)
        
        if autoAspectSpace {
            if allSpace > 0 && self.alignment == .center {
                tabMargin = floor(self.tabSpace + allSpace/CGFloat(tabList.count + 1))
                startMargin = tabMargin
            }
        } else if allSpace > 0 {
            startMargin += allSpace/2
        }
        if self.alignment == .left {
            startMargin = 16
        }
        var xOffset: CGFloat = startMargin
        for (index, tabString) in tabList.enumerated() {
            let item = tabItem(at: index)
            let textSize = self.tabTextSizeArray[index]
            
            item.frame = CGRect(x: xOffset, y: (scrollView.frame.height - textSize.height)/2, width: textSize.width, height:textSize.height)
            // 计算下一个tab的x偏移量
            xOffset += textSize.width + ((index == tabList.count - 1) ? startMargin : tabMargin)
            
            if tabItemWidth != 0 {
                item.titleLabel?.adjustsFontSizeToFitWidth = true
            }
            item.setTitle(tabString, for: .normal)
            item.tabItemTitleFont = tabItemTitleFont
            item.tabItemSelectTitleFont = tabItemSelectTitleFont
            item.textNormalColor = self.tabItemTitleColor
            item.textSelectedColor = self.tabItemSelectedTitleColor
            item.layer.borderWidth = tabItemBorderWidth
            item.layer.borderColor = tabItemBorderColor.cgColor
            item.layer.cornerRadius = tabItemCornerRadius
            item.tabItemBgColor = self.tabItemBgColor
            item.tabItemSelectBgColor = self.tabItemSelectBgColor
            
            item.isHiddenRedPoint = !(delegate?.showRedPoint(index: index) ?? false)
            
            if selectedIndex == index {
                switch selectMode {
                case .flowLine:
                    let flowlineY = ((item.frame.maxY + 4) > (scrollView.bounds.height - flowLineSize.height)) ? (scrollView.bounds.height - flowLineSize.height) : (item.frame.maxY + 4)
                    flowLine.frame = CGRect(x: item.frame.midX - flowLine.frame.width/2,
                                            y: flowlineY,
                                            width: flowLine.frame.width,
                                            height: flowLine.frame.height)
                case .selectBackgroundView:
                    flowLine.frame =  CGRect(x: item.frame.origin.x - kLeftMargin,
                                             y: item.frame.origin.y - kSlideTabSelectBackgroundViewTopMargin,
                                             width: item.frame.size.width + 2 * kLeftMargin,
                                             height: item.frame.size.height + 2 * kSlideTabSelectBackgroundViewTopMargin)
                case .flowPic:
                    flowPicView.frame = CGRect(x: item.frame.midX - 16/2,
                                               y: scrollView.bounds.height - 16 - 8,
                                               width: 16,
                                               height: 16)
                }
                
                if self.scaleRatio > 0 {
                    let scalePercent = 1 - self.scaleRatio
                    let startPercent = self.scaleRatio
                    item.transform = CGAffineTransform(scaleX: scalePercent + startPercent, y: scalePercent + startPercent)
                }
                
                item.isTabSelected = true
            } else {
                if self.scaleRatio > 0 {
                    let startPercent = self.scaleRatio
                    item.transform = CGAffineTransform(scaleX: startPercent, y: startPercent)
                }
                item.isTabSelected = false
            }
            
        }
        
        let totalContentWidth: CGFloat = xOffset
        
        if (totalContentWidth + scrollView.contentInset.left + scrollView.contentInset.right <= scrollView.bounds.width) {
            canScroll = false
            var x: Int = 0
            switch (self.alignment) {
            case .center:
                x = Int((scrollView.frame.width - totalContentWidth)/2.0)
            case .left:
                x = Int(leftSpace)
            default:
                break
            }
            
            self.scrollView.frame = CGRect(x: scrollView.frame.origin.x + CGFloat(x), y:0.0 + self.scrollViewOffSet, width: totalContentWidth, height: self.scrollView.frame.height)
            scrollView.contentOffset = CGPoint(x: -scrollView.contentInset.left, y: 0)
        } else {
            canScroll = true
        }
        
        self.flowLine.backgroundColor = self.barColor
        self.flowLine.isHidden = selectMode == .flowPic ? true : false
        self.flowLine.layer.zPosition = 0
        scrollView.sendSubviewToBack(self.flowLine)
        self.flowPicView.isHidden = selectMode == .flowPic ? false : true
        scrollView.sendSubviewToBack(self.flowPicView)
        scrollView.contentSize = CGSize(width: totalContentWidth, height: scrollView.frame.height)
    }
    
    func tag(at index: Int) -> Int {
        return kBaseButtonTag + index
    }
    
    func index(from tag: Int) -> Int {
        return tag - kBaseButtonTag
    }
    
    private func tabItem(at index: Int) -> WHSlideTabItem {
        let itemTag = tag(at: index)
        guard let item = self.scrollView.viewWithTag(itemTag) as? WHSlideTabItem else {
            let button = WHSlideTabItem.init(type: .custom)
            button.tag = itemTag
            button.addTarget(self, action:#selector(selectButton(button:)), for: .touchUpInside)
            button.backgroundColor = scrollView.backgroundColor
            button.titleLabel?.numberOfLines = 1
            button.titleLabel?.baselineAdjustment = .alignCenters
            button.titleLabel?.lineBreakMode = .byClipping
            self.scrollView.addSubview(button)
            return button
        }
        item.isHidden = false
        return item
    }
    
    private func calculateTabSizes(_ force: Bool = false) -> [CGSize] {
        var tabWidths: [CGSize] = []
        var width: CGFloat = 0
        for item in tabList {
            var size = item.sizeWith(tabItemSelectTitleFont, CGSize(width: self.frame.width, height: 1000), 0)
            if tabItemWidth == 0 {
                size.width += (tabItemTitleMarginH * 2)
                size.height = 18 + tabItemTitleMarginV * 2
            } else {
                let width = size.width > tabItemWidth ? size.width + 15 : tabItemWidth
                size.width = force ? width : min((self.bounds.width-kLeftMargin*4-8)/CGFloat(tabList.count), tabItemWidth)
                size.height = 18 + tabItemTitleMarginV * 2
            }
            width += size.width
            tabWidths.append(size)
        }
        self.totalTabWidth = width
        return tabWidths
    }
    
    
}

// MARK: - 红点逻辑

extension WHSlideTabBar {
    
    public func showRedDot(show: Bool, at index: Int) {
        if index < self.tabList.count {
            tabItem(at: index).isHiddenRedPoint = !show
        }
    }
    
    public func isRedDotShown(at index: Int) -> Bool {
        if index < self.tabList.count {
            return !tabItem(at: index).isHiddenRedPoint
        }
        return false
    }
    
}

// MARK: - 滚动tab及选择tab

extension WHSlideTabBar {
    public func scrollToTab(_ index: Int, sendAction: Bool = false, animated: Bool = false) {
        if index < self.tabList.count {
            self.selectTab(at: index, sendAction: sendAction, animated: animated)
        }
    }
    
    @objc func selectButton(button: WHSlideTabItem) {
        self.selectTab(at: index(from: button.tag), sendAction: true, animated: needClickTabAnimate)
    }
    
    func selectTab(at index: Int, sendAction: Bool = false, animated: Bool = false) {
        // 如果点击的tab文字显示不全，调整滚动视图x坐标使用使tab文字显示全
        let selectedItem = tabItem(at: index)
        self.adjustScrollViewContentX(selectedItem, index: index)
        
        if self.selectedIndex != index {
            let oldItem = tabItem(at: self.selectedIndex)
            
            selectedIndex = index
            
            if animated {
                delegate?.allowUserBehavior?(false)
                UIView.animate(withDuration: 0.25, animations: {
                    self.setButtonSelected(oldItem, selected: false)
                    self.setButtonSelected(selectedItem, selected: true)
                    oldItem.isHighlighted = false
                    selectedItem.isHighlighted = true
                }) { _ in
                    if sendAction {
                        self.delegate?.didSelect(self, at: index)
                    }
                    self.delegate?.allowUserBehavior?(true)
                }
            } else {
                self.setButtonSelected(oldItem, selected: false)
                self.setButtonSelected(selectedItem, selected: true)
                if sendAction {
                    self.delegate?.didSelect(self, at: index)
                }
            }
        } else {
            if sendAction {
                self.delegate?.didSelect(self, at: index)
            }
        }
        
    }
    
    private func setButtonSelected(_ button: WHSlideTabItem, selected: Bool) {
        button.isHiddenRedPoint = true
        button.isTabSelected = selected
        if self.scaleRatio > 0 {
            let scalePercent = 1 - self.scaleRatio
            let startPercent = self.scaleRatio
            button.transform = CGAffineTransform(scaleX: startPercent + (selected ? scalePercent : 0.0),
                                                 y: startPercent + (selected ? scalePercent : 0.0))
        }
        if selected {
            switch self.selectMode {
            case .flowLine:
                self.flowLine.frame = CGRect(x: button.frame.midX - self.flowLineSize.width/2, y: self.flowLine.frame.minY, width: self.flowLineSize.width, height: self.flowLine.frame.size.height)
            case .selectBackgroundView:
                flowLine.frame =  CGRect(x: button.frame.origin.x - kLeftMargin,
                                         y: button.frame.origin.y - kSlideTabSelectBackgroundViewTopMargin,
                                         width: button.frame.size.width + 2 * kLeftMargin,
                                         height: button.frame.size.height + 2 * kSlideTabSelectBackgroundViewTopMargin)
            case .flowPic:
                self.flowPicView.frame = CGRect(x: button.frame.midX - 16/2, y: self.flowPicView.frame.minY, width: 16, height: 16)
            }
        }
    }
    
    public func adjustIndicator(at index: Int, ratio: CGFloat) {
        guard self.selectMode != .selectBackgroundView else { return }
        if ratio == 0 {
            self.isUserInteractionEnabled = true
            return
        }
        
        self.isUserInteractionEnabled = false
        let oldTabItem = tabItem(at: index)
        let nextTabItem = tabItem(at: index + 1)
        
        let rectCurrent = oldTabItem.frame
        let rectNext = nextTabItem.frame
        
        
        var fromLineRect = flowLine.frame
        fromLineRect.size.width = flowLine.baseLineWidth
        
        var toLineRect = flowLine.frame
        toLineRect.size.width = flowLine.baseLineWidth
        
        
        // 计算右边按钮偏移量
        let rightScale = ratio
        let leftScale = 1 - rightScale
        
        // 形变按钮
        // scale 0 ~ 1 => 1 ~ 1.3
        
        if self.scaleRatio > 0 {
            
            let scalePercent = 1 - self.scaleRatio
            let startPercent = self.scaleRatio
            oldTabItem.transform = CGAffineTransform(scaleX: leftScale * scalePercent + startPercent, y: leftScale * scalePercent + startPercent)
            nextTabItem.transform = CGAffineTransform(scaleX: rightScale * kTabItemScalePercent + startPercent, y: rightScale * scalePercent + startPercent)
        }
        
        if (self.slideChangeTextColor) {
            oldTabItem.setTitleColorAllState(calculateColor(from: self.tabItemRGB, to: self.tabItemSelectedRGB, percent: leftScale))
            nextTabItem.setTitleColorAllState(calculateColor(from: self.tabItemRGB, to: self.tabItemSelectedRGB, percent: rightScale))        }
        
        let direction: WHSlideFlowDirection = (selectedIndex > index) ? WHSlideFlowDirection.left : WHSlideFlowDirection.right
        
        switch direction {
        case .right:
            fromLineRect.origin.x = rectCurrent.minX + (rectCurrent.width - flowLine.baseLineWidth)/2.0
            toLineRect.origin.x = rectNext.minX + (rectNext.width - flowLine.baseLineWidth) / 2.0
        case .left:
            toLineRect.origin.x = rectCurrent.minX + (rectCurrent.width - flowLine.baseLineWidth)/2.0
            fromLineRect.origin.x = rectNext.minX + (rectNext.width - flowLine.baseLineWidth) / 2.0
        }
        
        flowLine.updateLine(ratio, fromRect: fromLineRect, toRect: toLineRect, flowDirection: direction)
    }
    
    private func adjustScrollViewContentX(_ sender:WHSlideTabItem, index:Int, animated: Bool = true) {
        if !canScroll {
            scrollView.setContentOffset(CGPoint(x:-scrollView.contentInset.left, y:0), animated: animated)
            return
        }
        
        let hasNext = (self.tabList.count > (index + 1))
        if hasNext {
            let nextItem = tabItem(at: index + 1)
            //            如果 当前显示的最后一个tab文字超出右边界
            if (nextItem.frame.origin.x - scrollView.contentOffset.x > scrollView.frame.width - nextItem.bounds.size.width) {
                // 向左滚动视图，显示完整tab文字
                var xOffset = nextItem.frame.origin.x - (scrollView.bounds.size.width - nextItem.bounds.size.width)
                xOffset = hasNext ? xOffset : (xOffset + scrollView.contentInset.right)
                scrollView.setContentOffset(CGPoint(x:xOffset, y:0), animated: animated)
                return
            }
        } else {
            let xOffset = scrollView.contentSize.width - scrollView.bounds.size.width + scrollView.contentInset.right
            scrollView.setContentOffset(CGPoint(x:xOffset, y:0), animated: animated)
        }
        
        let hasPre = (index > 0)
        let preItem = hasPre ? tabItem(at: index - 1) : sender
        // 如果 （tab的文字坐标 - 当前滚动视图左边界所在整个视图的x坐标） < 按钮的隔间 ，代表tab文字已超出边界
        if (preItem.frame.origin.x - scrollView.contentOffset.x < scrollView.contentInset.left) {
            // 向右滚动视图（tab文字的x坐标 - 按钮间隔 = 新的滚动视图左边界在整个视图的x坐标），使文字显示完整
            var xOffset = preItem.frame.origin.x
            xOffset = hasPre ? xOffset : (xOffset - scrollView.contentInset.left)
            scrollView.setContentOffset(CGPoint(x:xOffset - scrollView.contentInset.left, y: 0.0), animated:animated)
        }
        
        if !hasPre {
            scrollView.setContentOffset(CGPoint(x: -scrollView.contentInset.left, y: 0.0), animated:animated)
        }
    }
    
}

extension WHSlideTabBar: UIScrollViewDelegate, UIGestureRecognizerDelegate {
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return false
    }
    
    public func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        delegate?.slideTabBarWillBeginDragging(self)
    }
}

// MARK: - 对外公开的方法，根据index获取对应的MTSlideTabItem位置
extension WHSlideTabBar {
    public func tabItemRect(at index: Int) -> CGRect? {
        let itemTag = tag(at: index)
        guard let item = self.scrollView.viewWithTag(itemTag) as? WHSlideTabItem else {
            return nil
        }
        return self.convert(item.frame, from: self.scrollView)
    }
}
