//
//  WHSlideTabItem.swift
//  covert
//
//  Created by 刘欢 on 2020/3/11.
//  Copyright © 2020 MT. All rights reserved.
//

import Foundation
import UIKit


let kSlideTabItemIdentifier = "kSlideTabItemIdentifier"

public class WHSlideTabItem: UIButton {
    
    public var redPointWidth: CGFloat = 4
    
    public var isHiddenRedPoint: Bool = false {
        didSet {
            self.redPoint.isHidden = self.isHiddenRedPoint
        }
    }
    
    public lazy var redPoint: UIView = {
        let view = UIView()
        view.backgroundColor = self.redPointColor
        view.isHidden = true
        view.bounds = CGRect(x: 0, y: 0, width: self.redPointWidth, height: self.redPointWidth)
        view.layer.cornerRadius = CGFloat(self.redPointWidth / 2)
        self.addSubview(view)
        return view
    }()
    
    public var redPointColor: UIColor = UIColor(r: 253, g: 73, b: 101) {
        didSet {
            self.redPoint.backgroundColor = self.redPointColor
        }
    }
    
    public var textSelectedColor: UIColor = .black {
        didSet {
            self.setTitleColorAllState(self.textSelectedColor)
        }
    }
    
    public var textNormalColor: UIColor = .black {
        didSet {
            self.setTitleColorAllState(self.textNormalColor)
        }
    }
    
    public var tabItemBgColor: UIColor = .clear
    public var tabItemSelectBgColor: UIColor = .clear
    
    public var tabItemSelectTitleFont: UIFont = UIFont.boldSystemFont(ofSize: 16)
    public var tabItemTitleFont: UIFont = UIFont.systemFont(ofSize: 14)
    
    public var isTabSelected: Bool  = false {
        didSet {
            self.titleLabel?.font = self.isTabSelected ? tabItemSelectTitleFont : tabItemTitleFont
            let color = self.isTabSelected ? self.textSelectedColor : self.textNormalColor
            self.setTitleColorAllState(color)
            self.backgroundColor = self.isTabSelected ? tabItemSelectBgColor : tabItemBgColor
            if self.tabItemSelectBgColor != UIColor.clear {
                self.layer.borderWidth = self.isTabSelected ? 0 : 1
            }
        }
    }
    
    public func setTitleColorAllState(_ color: UIColor) {
        self.setTitleColor(color, for: UIControl.State.normal)
        self.setTitleColor(color, for: UIControl.State.selected)
        self.setTitleColor(color, for: UIControl.State.highlighted)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.addSubview(self.redPoint)
        backgroundColor = .clear
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        let text = self.titleLabel?.text
        let size = text?.boundingRect(with: self.size, options: NSStringDrawingOptions.usesLineFragmentOrigin, attributes: nil, context: nil)
        var redPointX = self.bounds.width - redPointWidth/2.0
        // 0 - redPointWidth/2.0
        let redPointY: CGFloat = 4
        if let textWidth = size?.width {
            let textW = min(textWidth, self.width-self.layer.cornerRadius)
            redPointX = (self.width - textW)/2 + textW + 4
        }
        
        self.redPoint.frame = CGRect(x: redPointX, y: redPointY, width: self.redPointWidth, height: self.redPointWidth)
    }
    
    public override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        let hotX: CGFloat = 5.0
        let hotY: CGFloat = 12.0
        let newPoint = self.convert(point, to: UIApplication.shared.keyWindow)
        let rect = CGRect(x:-hotX, y: -hotY, width: self.bounds.width + 2.0 * hotX, height: self.bounds.height + 2.0 * hotY)
        let newRect = self.convert(rect, to: UIApplication.shared.keyWindow)
        
        if newRect.contains(newPoint) {
            return true
        }
        return false
    }
}

