//
//  PanDirectionGestureRecognizer.swift
//  MTXX
//
//  Created by yumeng tang on 2018/11/17.
//  Copyright © 2018 Meitu. All rights reserved.
//

//https://stackoverflow.com/questions/7100884/uipangesturerecognizer-only-vertical-or-horizontal
import UIKit

public enum PanAxis {
    case vertical
    case horizontal
}

public enum PanDirection {
    case up
    case down
    case left
    case right
}

public class PanDirectionGestureRecognizer: UIPanGestureRecognizer {
    
    public let axis: PanAxis
    public var direction: PanDirection?
    public var touchBeganPoint: CGPoint = .zero
    public var currentTouchStartPoint: CGPoint?
    
    public init(axis: PanAxis, target: AnyObject, action: Selector) {
        self.axis = axis
        super.init(target: target, action: action)
    }
    
    public init(axis: PanAxis) {
        self.axis = axis
        super.init(target: nil, action: nil)
    }
    
    public override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent) {
        super.touchesBegan(touches, with: event)
        touchBeganPoint = self.location(in: view)
        currentTouchStartPoint = touchBeganPoint
    }
    public override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent) {
        super.touchesMoved(touches, with: event)

        if state == .began {
            let vel = velocity(in: view)
            switch axis {
            case .horizontal where abs(vel.y) > abs(vel.x):
                state = .cancelled
            case .vertical where abs(vel.x) > abs(vel.y):
                state = .cancelled
            default:
                break
            }
        }
    }
    
    public override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent) {
        super.touchesEnded(touches, with: event)
        currentTouchStartPoint = nil
    }
    
    public override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent) {
        super.touchesCancelled(touches, with: event)
        currentTouchStartPoint = nil
    }
}

//扩展Block
public typealias GestureRecognizerHandle = (_ recognizer: UIGestureRecognizer) -> Void

public class GesHandleWrapper: NSObject {
    public var handle: GestureRecognizerHandle
    
    public init(_ handle: @escaping GestureRecognizerHandle) {
        self.handle = handle
    }
    
    @objc public func handleExcute(_ recognizer: UIGestureRecognizer) {
        handle(recognizer)
    }
}

extension UIGestureRecognizer {
    public static var UIGestureRecognizerAssociatedKey = "UIGestureRecognizerAssociatedKey"
    
    public var handleWrapper: GesHandleWrapper? {
        get {
            return objc_getAssociatedObject(self, &UIGestureRecognizer.UIGestureRecognizerAssociatedKey) as? GesHandleWrapper
        }
        set {
            objc_setAssociatedObject(self, &UIGestureRecognizer.UIGestureRecognizerAssociatedKey, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    public func addHandle(_ handle: @escaping GestureRecognizerHandle) {
        if handleWrapper == nil {
            handleWrapper = GesHandleWrapper.init({ _ in })
        }
        handleWrapper?.handle = handle
        if let handleWrapper = handleWrapper {
            self.addTarget(handleWrapper, action: #selector(GesHandleWrapper.handleExcute(_:)))
        }
    }
    
    public func removeHandle(_ handle: GestureRecognizerHandle) {
        handleWrapper = nil
    }
    
}
