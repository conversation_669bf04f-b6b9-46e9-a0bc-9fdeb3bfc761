//
//  WHCMBundle.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/7/20.
//  

import Foundation

@objc public class WHCMBundle: NSObject {
    private static let bundleName = "WHBusinessCommon"
    
    @objc static var bundlePath: String? = Bundle.main.path(forResource: bundleName, ofType: "bundle")
    
    @objc public static var main: Bundle {
        guard let bundlePath = bundlePath, !bundlePath.isEmpty else { return Bundle.main }
        return Bundle(path: bundlePath) ?? Bundle.main
    }
    
//    @objc public static var localizationBundle: Bundle {
//        let bundlePath = DASBundle.main.path(forResource: MTAppLanguage.appLanguage(), ofType: "lproj", inDirectory: "Localizable") ?? ""
//        return Bundle(path: bundlePath) ?? MTXXShareBundle.main
//    }
//
//    @objc class func localizedString(_ key: String, comment: String? = "") -> String {
//        return NSLocalizedString(key, tableName: nil, bundle: MTXXShareBundle.localizationBundle, value: "", comment: comment ?? "")
//    }
}

extension Bundle {

    static let resources: Bundle = {
        let bundle = Bundle(for: BundleToken.self)
        let path = bundle.path(forResource: "WHBusinessCommon", ofType: "bundle")
        return Bundle(path: path!)!
    }()

}
private class BundleToken {}


public extension UIImage {
    public convenience init?(cm_named name: String) {
        self.init(named: name,
                  in: Bundle.resources,
                  compatibleWith: nil)
    }
    
    public convenience init?(WithCommont name: String) {
        self.init(cm_named: name)
    }
}

// 国际化全局函数
func WHSonicLocalizedString(_ key: String, comment: String? = "") -> String {
    return key
//    return NSLocalizedString(key, tableName: nil, bundle: MTXXShareBundle.localizationBundle, value: "", comment: comment ?? "")
}
