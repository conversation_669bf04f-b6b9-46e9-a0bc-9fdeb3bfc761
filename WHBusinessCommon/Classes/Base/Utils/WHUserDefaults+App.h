

#import "WHUserDefaults.h"

NS_ASSUME_NONNULL_BEGIN

@interface WHUserDefaults (App)

/// 获取APP版本是否更新
/// - Parameter isReset: 是否重置版本状态（记录下当前最新的版本号）
+ (BOOL)getAppVersionIsUpgrade:(BOOL)isReset;
///局部修改玩法是否需要展示，默认获取一次后就不会再展示了
+ (BOOL)getInpaintingNeedShow;
///设置首页配置缓存
+ (void)setHomeConfigCache:(NSDictionary *)dic;
///获取首页配置缓存
+ (NSDictionary *)getHomeConfigCache;
/// AI生视频是否展示玩法介绍
+ (BOOL)getAICreateVideoNeedShow;
/// 文生图玩法介绍
+ (BOOL)getText2ImageNeedShow;
/// 图生图玩法介绍
+ (BOOL)getImage2ImageNeedShow;
/// AI扩图玩法介绍
+ (BOOL)getAIExpandNeedShow;
/// AI超清玩法介绍
+ (BOOL)getAIMagicsrNeedShow;
/// 【添加】AI超清失败的弹窗，是否被关闭过（AI超清失败，美豆已返还您的账户）
+ (void)addAIMagicsrFailAlertIsClosed:(NSString *)urlString;
/// 【查询】AI超清失败的弹窗，是否被关闭过（AI超清失败，美豆已返还您的账户）
+ (BOOL)getAIMagicsrFailAlertIsClosed:(NSString *)urlString;
/// 【移除】移除记录的AI超清失败的弹窗URL
+ (void)removeRecordAIMaigicsrFailAlert:(NSString *)urlString;
/// AI超清详情页温馨提示
+ (BOOL)getAIMagicsrToastNeedShow;

+ (BOOL)getAIGuaidLiveAlertShow;

+ (BOOL)getAIHomeADAlertShow: (NSString *)idstr;

///推送提醒【设置时间&次数】
+ (void)setAPNSAuthorityAlertCache:(NSDictionary *)dic;
///推送提醒【获取时间&次数】
+ (NSDictionary *)getAPNSAuthorityAlertCache;

@end

NS_ASSUME_NONNULL_END
