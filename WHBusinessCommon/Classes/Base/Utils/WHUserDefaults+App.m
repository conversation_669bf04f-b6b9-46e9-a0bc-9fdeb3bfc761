

#import "WHUserDefaults+App.h"

static NSString *const WHAPPVersion                      = @"WHAPPVersion";
static NSString *const WHInpaintingPlayIntroduce         = @"WHInpaintingPlayIntroduce";
static NSString *const WHHomeConfigCache                 = @"WHHomeConfigCache";
static NSString *const WHAICreateVideoPlayIntroduce      = @"WHAICreateVideoPlayIntroduce";
static NSString *const WHAPNSAuthorityAlertCache         = @"WHAPNSAuthorityAlertCache";
static NSString *const WHText2ImagePlayIntroduce         = @"WHText2ImagePlayIntroduce";
static NSString *const WHImage2ImagePlayIntroduce        = @"WHImage2ImagePlayIntroduce";
static NSString *const WHAIExpandPlayIntroduce           = @"WHAIExpandPlayIntroduce";
static NSString *const WHAIMagicsrdPlayIntroduce         = @"WHAIMagicsrdPlayIntroduce";
static NSString *const WHAIMagicsrdFailAlertCloses       = @"WHAIMagicsrdFailAlertCloses";
static NSString *const WHAIMagicsrdDetailToast           = @"WHAIMagicsrdDetailToast";
static NSString *const WHAILiveAlertShow                 = @"WHAILiveAlertShow";
static NSString *WHAIHomeADAlert = @"WHAIHomeADAlert";


@implementation WHUserDefaults (App)

+ (BOOL)getAppVersionIsUpgrade:(BOOL)isReset {
    NSString *historyVersion = [WHUserDefaults objectForKey:WHAPPVersion];
    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if (isReset) { //重置版本
        [WHUserDefaults setWhObject:appVersion forKey:WHAPPVersion];
    }
    if (!historyVersion || ![historyVersion isEqualToString:appVersion]) {
        return YES;
    } else {
        return NO;
    }
}

+ (BOOL)getInpaintingNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHInpaintingPlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHInpaintingPlayIntroduce];
        return YES;
    }
}

+ (BOOL)getAICreateVideoNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAICreateVideoPlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAICreateVideoPlayIntroduce];
        return YES;
    }
}

+ (BOOL)getText2ImageNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHText2ImagePlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHText2ImagePlayIntroduce];
        return YES;
    }
}

+ (BOOL)getImage2ImageNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHImage2ImagePlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHImage2ImagePlayIntroduce];
        return YES;
    }
}

/// AI扩图玩法介绍
+ (BOOL)getAIExpandNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAIExpandPlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAIExpandPlayIntroduce];
        return YES;
    }
}

/// AI超清玩法介绍
+ (BOOL)getAIMagicsrNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAIMagicsrdPlayIntroduce];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAIMagicsrdPlayIntroduce];
        return YES;
    }
}

/// AI超清详情页温馨提示
+ (BOOL)getAIMagicsrToastNeedShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAIMagicsrdDetailToast];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAIMagicsrdDetailToast];
        return YES;
    }
}

/// AI超清是否显示引导live弹窗
+ (BOOL)getAIGuaidLiveAlertShow {
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAILiveAlertShow];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAILiveAlertShow];
        return YES;
    }
}

/// 首页广告引导弹窗
+ (BOOL)getAIHomeADAlertShow: (NSString *)idstr {
    WHAIHomeADAlert = [NSString stringWithFormat:@"WHAIHomeADAlert_%@", idstr];
    NSString *isNeedShow = [WHUserDefaults objectForKey:WHAIHomeADAlert];
    if (isNeedShow) {
        return NO;
    } else {
        [WHUserDefaults setWhObject:@"showed" forKey:WHAIHomeADAlert];
        return YES;
    }
}

/// 【添加】AI超清失败的弹窗，是否被关闭过（AI超清失败，美豆已返还您的账户）
+ (void)addAIMagicsrFailAlertIsClosed:(NSString *)urlString {
    if (!urlString) {
        return;
    }
    NSDictionary *urlDic = [WHUserDefaults objectForKey:WHAIMagicsrdFailAlertCloses];
    NSMutableDictionary *muDic;
    if (urlDic && urlDic.allKeys.count > 0) {
        muDic = [NSMutableDictionary dictionaryWithDictionary:urlDic];
    } else {
        muDic = [NSMutableDictionary dictionary];
    }
    [muDic setObject:@"1" forKey:urlString];
    [WHUserDefaults setWhObject:muDic forKey:WHAIMagicsrdFailAlertCloses];
}
/// AI超清失败的弹窗，是否被关闭过（AI超清失败，美豆已返还您的账户）
+ (BOOL)getAIMagicsrFailAlertIsClosed:(NSString *)urlString {
    if (!urlString) {
        return NO;
    }
    NSDictionary *urlDic = [WHUserDefaults objectForKey:WHAIMagicsrdFailAlertCloses];
    if (urlDic && urlDic.allKeys.count > 0) {
        NSString *obj = [urlDic objectForKey:urlString];
        if (obj) {
            return YES;
        }
    }
    return NO;
}

/// 移除记录的AI超清失败的弹窗URL
+ (void)removeRecordAIMaigicsrFailAlert:(NSString *)urlString {
    if (!urlString) {
        return;
    }
    NSDictionary *urlDic = [WHUserDefaults objectForKey:WHAIMagicsrdFailAlertCloses];
    if (urlDic && urlDic.allKeys.count > 0) {
        NSMutableDictionary *muDic = [NSMutableDictionary dictionaryWithDictionary:urlDic];
        [muDic removeObjectForKey:urlString];
        [WHUserDefaults setWhObject:muDic forKey:WHAIMagicsrdFailAlertCloses];
    }
}

///设置首页配置缓存
+ (void)setHomeConfigCache:(NSDictionary *)dic {
    if (dic) {
        [WHUserDefaults setWhObject:dic forKey:WHHomeConfigCache];
    }
}
///获取首页配置缓存
+ (NSDictionary *)getHomeConfigCache {
    NSDictionary *dic = [WHUserDefaults objectForKey:WHHomeConfigCache];
    return dic;
}

///推送提醒【设置时间&次数】
+ (void)setAPNSAuthorityAlertCache:(NSDictionary *)dic {
    if (dic) {
        [WHUserDefaults setWhObject:dic forKey:WHAPNSAuthorityAlertCache];
    }
}

///推送提醒【获取时间&次数】
+ (NSDictionary *)getAPNSAuthorityAlertCache {
    NSDictionary *dic = [WHUserDefaults objectForKey:WHAPNSAuthorityAlertCache];
    return dic;
}

@end
