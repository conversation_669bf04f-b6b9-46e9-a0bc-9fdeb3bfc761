

#import "WHUserDefaults.h"

static NSString *const WHGlobalDefaultsSuiteName = @"WHCommonGlobalDefaultsSuiteName";

static NSUserDefaults *globalDefaults;

@implementation WHUserDefaults

+ (void)initialize {
    globalDefaults = [[NSUserDefaults alloc] initWithSuiteName:WHGlobalDefaultsSuiteName];
}

#pragma mark - Public

+ (NSUserDefaults *)globalDefaults {
    return globalDefaults;
}

+ (void)setWhObject:(nullable id)value forKey:(NSString *)key; {
    [globalDefaults setObject:value forKey:key];
    [globalDefaults synchronize];
}

+ (nullable id)objectForKey:(NSString *)key {
    id obj = [globalDefaults objectForKey:key];
    return obj;
}

+ (void)removeObjectForKey:(NSString *)key {
    [globalDefaults removeObjectForKey:key];
    [globalDefaults synchronize];
}

@end
