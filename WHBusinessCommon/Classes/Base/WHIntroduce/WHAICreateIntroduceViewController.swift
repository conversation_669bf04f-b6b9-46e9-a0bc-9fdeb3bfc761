//
//  WHAICreateIntroduceViewController.swift
//  MeituWhee
//
//  Created by z<PERSON>lifei on 2024/6/14.
//  功能介绍

import Foundation
import WHBaseLibrary
import WHBusinessCommon
import Lottie

var photosOffset = WH_NAVIGATION_BAR_HEIGHT + 16 + 211 + 16 + 28 + 24

public class WHAICreateIntroduceViewController: WHViewController, WHImageErrorViewDelegate {
    
    private var webScheme: String = ""
    private var imageUrl: String = ""
    private let height = (WH_SCREEN_WIDTH - 48) * 211 / 327
    
    private lazy var iconView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        view.layer.cornerRadius = 16
        view.layer.cornerCurve = .continuous
        view.layer.masksToBounds = true
        view.backgroundColor = UIColor(wh_hexString: "#222326")
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textAlignment = .center
        label.textColor = .white
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var photosView: WHIntroducePhotosView = {
        let view = WHIntroducePhotosView(parentVC: self)
        view.layer.masksToBounds = true
        view.webScheme = webScheme
        return view
    }()
    
    private lazy var loadingView: LottieAnimationView = {
        let lottie = LottieAnimationView(name: "image_loading", bundle: WHCMBundle.main)
        lottie.isUserInteractionEnabled = true
        lottie.loopMode = .loop
        lottie.backgroundBehavior = .pauseAndRestore
        lottie.contentMode = .scaleAspectFill
        lottie.layer.cornerRadius = 16
        return lottie
   }()
    
    private lazy var errorView: WHImageErrorView = {
        let view = WHImageErrorView()
        view.delegate = self
        view.layer.cornerRadius = 16
        view.layer.cornerCurve = .continuous
        view.isHidden = true
        return view
    }()
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = WHFigmaColor.backgroundGlobalPage
        setUp()
        showBackButton(isShow: true)
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        photosView.photosVC?.collectionView.reloadData()
        photosView.photosVC?.currentFetchContentView?.collectionView.reloadData()
    }
    
    deinit {
        WHPrint("释放了")
    }
    
    func setUp() {
        photosOffset = WH_NAVIGATION_BAR_HEIGHT + 16 + height + 16 + 28 + 24
        view.addSubview(iconView)
        iconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT + 16)
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(height)
        }
        
        view.addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.edges.equalTo(iconView)
        }
        
        view.addSubview(errorView)
        errorView.snp.makeConstraints { make in
            make.edges.equalTo(iconView)
        }
        
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconView.snp.bottom).offset(23)
            make.left.right.equalToSuperview().inset(24)
        }
        
        let offset = photosOffset - WH_NAVIGATION_BAR_HEIGHT
        view.addSubview(photosView)
        photosView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(offset)
            make.height.equalTo(WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT)
        }
        photosView.clipCorner(corners: [.topLeft, .topRight], radius: 24)
    }
    
    public override func loadRouteParams(_ params: [String : Any]) {
        if let webScheme = params["webScheme"] as? String {
            self.webScheme = webScheme
        }
        if let color = params["bgColor"] as? String,color.count > 0 {
            view.backgroundColor = UIColor(wh_hexString: color)
            myNavigationBar.backgroundColor = UIColor(wh_hexString: color)
        } else {
            view.backgroundColor = WHFigmaColor.backgroundGlobalPage
        }
        if let title = params["title"] as? String {
            myNavigationBar.title = title
        }
        if let desc = params["desc"] as? String {
            titleLabel.text = desc
        }
        if let imageUrl = params["imageUrl"] as? String {
            self.imageUrl = imageUrl
            loadingView.isHidden = false
            loadingView.play()
            iconView.sd_setImage(with: URL(string: imageUrl)) { [weak self] image, error, tyle, url in
                if error != nil {
                    self?.errorView.isHidden = false
                } else {
                    self?.errorView.isHidden = true
                }
                self?.loadingView.stop()
                self?.loadingView.isHidden = true
            }
        }
    }
    
    func config(webScheme: String, params: [String : Any]) {
        self.webScheme = webScheme
        if let title = params["title"] as? String {
            myNavigationBar.title = title
        }      
        if let desc = params["desc"] as? String {
            titleLabel.text = desc
        }
        if let color = params["bgColor"] as? String,color.count > 0 {
            view.backgroundColor = UIColor(wh_hexString: color)
            myNavigationBar.backgroundColor = UIColor(wh_hexString: color)
        }
        if let imageUrl = params["imageUrl"] as? String, imageUrl.count > 0 {
            self.imageUrl = imageUrl
            loadingView.isHidden = false
            loadingView.play()
            iconView.sd_setImage(with: URL(string: imageUrl)) { [weak self] image, error, tyle, url in
                if error != nil {
                    self?.errorView.isHidden = false
                } else {
                    self?.errorView.isHidden = true
                }
                self?.loadingView.stop()
                self?.loadingView.isHidden = true
            }
        }
        photosView.isDark = params["isDark"] as? String ?? ""
        if let imageLevel = params["imagelevel"] as? Int,imageLevel > 0 {
            let selectLevel = WHAlbumSelectLevel(rawValue: imageLevel) ?? .normal
            photosView.imgSelectLevel = selectLevel
        }
    }
    
    func imageErrorViewReloadButtonClick() {
        errorView.isHidden = true
        loadingView.isHidden = false
        loadingView.play()
        iconView.sd_setImage(with: URL(string: imageUrl)) { [weak self] image, error, tyle, url in
            if error != nil {
                self?.errorView.isHidden = false
            } else {
                self?.errorView.isHidden = true
            }
            self?.loadingView.stop()
            self?.loadingView.isHidden = true
        }
    }
}

// MARK: - WHImageErrorView

protocol WHImageErrorViewDelegate: NSObjectProtocol {
    func imageErrorViewReloadButtonClick()
}

class WHImageErrorView: UIView {
    
    weak var delegate: WHImageErrorViewDelegate?
    
    private var iconView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        view.image = UIImage(cm_named: "icon_introduce_error")
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .regular)
        label.textAlignment = .center
        label.textColor = UIColor(wh_hexString: "#AEAFB7")
        label.text = WHLocalizedString("加载失败，请重试")
        return label
    }()
    
    private lazy var reloadButton: UIButton = {
        let btn = UIButton()
        btn.setTitle(WHLocalizedString("点击重试"), for: .normal)
        btn.titleLabel?.textColor = .white
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        btn.backgroundColor = UIColor(wh_hexString: "#222326")
        btn.layer.cornerRadius = 20
        btn.addTarget(self, action: #selector(reloadButtonClick), for: .touchUpInside)
        return btn
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = UIColor(wh_hexString: "#1A1A1A")
        setUp()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setUp() {
        addSubview(iconView)
        addSubview(titleLabel)
        addSubview(reloadButton)
        
        iconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(34)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(22)
        }
        
        reloadButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(98)
            make.height.equalTo(40)
        }
    }
    
    @objc func reloadButtonClick() {
        self.delegate?.imageErrorViewReloadButtonClick()
    }
}

