//
//  WHAIIntroduceResponder.swift
//  WHBusinessCommon
//
//  Created by z<PERSON>lifei on 2024/6/26.
//

import Foundation

class WHAIIntroduceResponder: WHRouterBaseResponder {
    
    override func respond(with URL: URL,
                          viewController: UIViewController? = nil,
                          params: [String: Any]? = nil,
                          application: UIApplication? = nil) {
        mergeRouteParams(with: URL, params: params)
        prepareRoute(with: viewController,
                     application: application)
        
        let schemeModel = WHMiniAppSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
        guard let webScheme = schemeModel?.queryDic?["webScheme"] as? String, webScheme.count > 0 else {
            return
        }
        if WHAccountShareManager.isLogin() {
            let introduceVC = WHAICreateIntroduceViewController()
            introduceVC.config(webScheme: webScheme, params: params ?? [:])
            UIViewController.wh_top().navigationController?.pushViewController(introduceVC, animated: true)
        } else {
            WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {(sucess, info) in
                let introduceVC = WHAICreateIntroduceViewController()
                introduceVC.config(webScheme: webScheme, params: params ?? [:])
                UIViewController.wh_top().navigationController?.pushViewController(introduceVC, animated: true)
            } failure: {
            }
        }
    }
}


