//
//  WHIntroducePhotosView.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2024/6/14.
//

import WHBaseLibrary
import WHBusinessCommon
import MTPhotoLibrary
import MTResourceUpload
import Lottie

public class WHIntroducePhotosView: UIView, WHPanable, WHExpandable, UIGestureRecognizerDelegate {
        
    var select:WHCommonAlbumSelect = WHCommonAlbumSelect()
    
    var webScheme: String = ""
    
    weak var photosVC: WHPhotosViewController?
    
    var isDark: String = ""
    
    var imgSelectLevel:WHAlbumSelectLevel = .normal //选图级别
    
    private var parentVC:UIViewController
        
    private lazy var lineView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(wh_hexString: "#43464D")
        view.layer.cornerRadius = 2.5
        return view
    }()
    
    // 相册项目动画遮挡
    private lazy var topBgView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = true
        view.backgroundColor = UIColor(wh_hexString: "#17171A")
        return view
    }()
    
    private lazy var loadingView: LottieAnimationView = {
        let lottie = LottieAnimationView(name: "image_upload", bundle: WHCMBundle.main)
        lottie.isUserInteractionEnabled = true
        lottie.loopMode = .loop
        lottie.backgroundBehavior = .pauseAndRestore
        return lottie
   }()
    
    public init(parentVC:UIViewController) {
        self.parentVC = parentVC
        super.init(frame: .zero)
        self.backgroundColor = UIColor(wh_hexString: "#17171A")
        setUp()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("请使用 init(parentVC:) 来初始化")
    }
    
    func setUp() {
        WHPhotoAlbumManager.shared.photoLibraryAuth {
            MTPhotoLibrary.shared().fetchPhotoAlbums(with: .photos) { photoAlbums in
                let photosVC = WHPhotosViewController()
                photosVC.delegate = self
                photosVC.isIntrodunce = true
                photosVC.photoAlbums = photoAlbums
                photosVC.myNavigationBar.isHidden = true
                photosVC.view.backgroundColor = UIColor(wh_hexString: "#17171A")
                self.addSubview(photosVC.view)
                self.parentVC.addChild(photosVC)
                self.photosVC = photosVC
                
                photosVC.view.snp.makeConstraints { make in
                    make.top.equalToSuperview().offset(16)
                    make.left.right.bottom.equalToSuperview()
                }
                
                self.addSubview(self.topBgView)
                self.topBgView.snp.makeConstraints { make in
                    make.top.left.right.equalToSuperview()
                    make.height.equalTo(16)
                }
                
                self.addSubview(self.lineView)
                self.lineView.snp.makeConstraints { make in
                    make.top.equalToSuperview().offset(8)
                    make.centerX.equalToSuperview()
                    make.size.equalTo(CGSize(width: 31, height: 4))
                }
            }
        }
        self.addPanGes()
        
        WHAnalyticsManager.otherTrackEvent("album_expo", params: ["function": "ai_printing"])
    }
    
    ///MARK --滑动手势相关
    public var isPanAnimating: Bool = false

    public var isExpand: Bool {
        return panViewHeight.rounded() == panViewHeightMax.rounded()
    }
    
    public var extraView: UIScrollView? {
        return self.photosVC?.currentFetchContentView?.collectionView
    }
    
    public var panView: UIView {
        return self
    }
    
    public var panViewHeightMin: CGFloat {
        return (WH_SCREEN_HEIGHT - photosOffset)
    }
    
    public var panViewHeightMax: CGFloat {
        return WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT
    }
    
    public var panViewHeight: CGFloat {
        if let sv = panView.superview {
            return sv.bounds.height - panView.frame.minY
        } else {
            return 0
        }
    }
    
    public var gestureDelegate: UIGestureRecognizerDelegate? {
        return self
    }
    
    public func shouldAdjustExtraView(_ scrollView: UIScrollView) -> Bool {
        return false
    }
    
    public var forbiddenOperation: Bool {
        return true
    }
    
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
    
    public func panableCompletion() {
        self.endEditing(true)
    }
    
    public override func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
          return commonGestureRecognizerShouldBegin(gestureRecognizer)
    }
}

extension WHIntroducePhotosView: WHPhotosViewControllerDelegate {
    public func selectPhotoWithMtasset(mtAssets: [MTPhotoAsset], vc: UIViewController) {
        if let vc = vc as? WHPhotosViewController,let view = vc.currentCell {
            view.addSubview(loadingView)
            loadingView.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.width.height.equalTo(36)
            }
            loadingView.play()
        }
        let mtAsset = mtAssets.object(at: 0)
        if let asset = mtAsset {
            select = WHCommonAlbumSelect()
            select.delegate = self
            select.selectType = .picUpload
            select.photoLibraryType = .introduce
            select.selectLevel = imgSelectLevel
            select.limitLenght = imgSelectLevel.limitLenght()
            select.loadFullResolutionImageWithAsset(asset) { [weak self] image in
                let isSuccess = self?.select.localCheckPicLimitAndGoForwardIfNeeded(mtAsset: asset, image: image)
                if isSuccess == false {
                    self?.loadingView.stop()
                    self?.loadingView.removeFromSuperview()
                }
            }
        }
        
        WHAnalyticsManager.otherTrackEvent("album_click", params: ["function": "ai_printing"])
    }
}

extension WHIntroducePhotosView: WHCommonAlbumSelectDelegate {
    
    public func wh_albumSelectImage(assetModels: [WHCommonSelectAssetModel]) {
        guard let assetModel = assetModels.object(at: 0) else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到内容，请重试或更换图片"))
            return
        }
        self.loadingView.stop()
        self.loadingView.removeFromSuperview()
        
        if let imageUrl = assetModel.imageUrl, imageUrl.count > 0 {
            let utlString = imageUrl.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let url = self.webScheme + "&img_url=\(utlString)"
            let encodeUrl = url.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let scheme = "wheeai://web?url=" + encodeUrl + "&fullscreen=1" + "&hiddenClose=1" + "&isDark=\(isDark)"
            WHRouter.route(with: scheme)
        }
    }
    
    public func wh_albumSelectFail() {
        self.loadingView.stop()
        self.loadingView.removeFromSuperview()
    }
    
    public func wh_albumSelectCancel() {}
}
