//
//  WHRouterAlbumWebResponder.swift
//  WHBusinessCommon
//
//  Created by z<PERSON><PERSON><PERSON> on 2024/6/19.
//  

import Foundation
import MTPhotoLibrary

class WHRouterAlbumWebResponder: WHRouterBaseResponder {

    override func setConfig(with config: [String: Any]) {
        configDict = config
    }
    
    override func respond(with URL: URL,
                 viewController: UIViewController? = nil,
                 params: [String: Any]? = nil,
                 application: UIApplication? = nil) {
        mergeRouteParams(with: URL, params: params)
        prepareRoute(with: viewController,
                          application: application)
    
        let schemeModel = WHMiniAppSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
        let webScheme = schemeModel?.queryDic?["url"]
        if (webScheme == nil) { //没有小程序链接则不需要继续处理了
            return
        }
        let isDark = schemeModel?.queryDic?["isDark"] as? String ?? ""
        var source = "image_extension" //默认是图片扩展
        if let s = schemeModel?.queryDic?["source"] as? String,s.count > 0 {
            source = s
        }
        var imageSelectLevel = WHAlbumSelectLevel.normal
        if let imageLevel = Int(schemeModel?.queryDic?["imagelevel"] as? String ?? "0"),imageLevel > 0 {
            imageSelectLevel = WHAlbumSelectLevel(rawValue: imageLevel) ?? .normal
        }
        WHAlbumWebManager.shared.startAlumbTask(scheme: webScheme as? String, isDark: isDark,selectLevel: imageSelectLevel)
    }
}

class WHAlbumWebManager: NSObject, WHCommonAlbumSelectDelegate {
    
    static let shared = WHAlbumWebManager()
    
    var imageSeletor:WHCommonAlbumSelect?
    
    var webScheme: String?
    
    var isDark: String = ""
    
    func startAlumbTask(scheme: String?, isDark: String, selectLevel:WHAlbumSelectLevel = .normal) {
        let selecter = WHCommonAlbumSelect()
        self.imageSeletor = selecter
        selecter.selectPhotoWithParmas(delegate: self,selectType: .picUpload,selectLevel: selectLevel)
        self.webScheme = scheme
        self.isDark = isDark
    }
    
    func wh_albumSelectImage(assetModels: [WHCommonSelectAssetModel]) {
        guard let assetModel = assetModels.object(at: 0) else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到内容，请重试或更换图片"))
            return
        }
        if let imageUrl = assetModel.imageUrl, let scheme = self.webScheme, imageUrl.count > 0, scheme.count > 0 {
            let url = imageUrl.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let webscheme = scheme + "&img_url=" + url
            let encodeUrl = webscheme.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let jumpSchema = "wheeai://web?url=" + encodeUrl + "&fullscreen=1" + "&hiddenClose=1" + "&isDark=\(isDark)"
            WHRouter.route(with: jumpSchema)
        }
    }
    
    func wh_albumSelectCancel() {}
    
    func wh_albumSelectFail() {}
}
