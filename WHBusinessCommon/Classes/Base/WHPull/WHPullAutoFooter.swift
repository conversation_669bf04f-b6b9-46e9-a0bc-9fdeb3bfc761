//
//  WHPullAutoFooter.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/31.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import MTPullToRefresh

public let kScreenH: CGFloat = max(UIScreen.main.bounds.width, UIScreen.main.bounds.height)

public class WHPullAutoFooter: WHPullFooter {
    
    var size: CGSize = CGSize.zero
    public override func contentOffsetChange(scrollView: UIScrollView?) {
        if let scrollView = scrollView, size != scrollView.contentSize, !isNoDataStatus, self.state == .initial, scrollView.bottomPullToRefresh != nil {
            let contentOffsetY = scrollView.contentOffset.y
            var threshold = scrollView.contentSize.height / 3
            if threshold > kScreenH * 3 {
                threshold = kScreenH * 3
            }
            if scrollView.contentSize.height > scrollView.bounds.height * 1.5, contentOffsetY + scrollView.bounds.height > scrollView.contentSize.height - threshold {
                action?()
                size = scrollView.contentSize
            }
        }
    }
}
