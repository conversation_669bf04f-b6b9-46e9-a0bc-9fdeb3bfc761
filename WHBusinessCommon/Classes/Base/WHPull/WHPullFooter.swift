//
//  WHPullFooter.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import MTPullToRefresh

public enum MTPullFooterStyle {
    case defaultText
    case picCustomText((initialDesc: String?, releasingDesc: String?, loadingDesc: String?, noMoreDesc: String?))
}

public class WHPullFooter: PullToRefresh {
    
    var originBottom: CGFloat?
    public var firstNoData: Bool = true
    public override var isNoDataStatus: Bool {
        didSet {
            if let footerView  = self.refreshView as? WHPullFooterView {
                if isNoDataStatus {
                    footerView.noDataLabel.isHidden = false
                    footerView.lottieView.isHidden = true
                    footerView.descLabel.isHidden = true  
                    if self.firstNoData, let scroll = footerView.superview as? UIScrollView, !scroll.isPagingEnabled {
                        if scroll.bounds.height > scroll.contentSize.height + footerView.height {
                            // 不满一屏只改top属性
                            footerView.top -= footerView.height
                        } else {
                            self.originBottom = scroll.contentInset.bottom
                            scroll.contentInset.bottom += footerView.height
                        }
                        self.firstNoData = false
                    }
                } else {
                    footerView.noDataLabel.isHidden = true
                    footerView.lottieView.isHidden = false
                    footerView.descLabel.isHidden = false
                    self.firstNoData = true
                    if let bottom = self.originBottom, let scroll = footerView.superview as? UIScrollView {
                        scroll.contentInset.bottom = bottom
                    }
                }
            }
        }
    }
    
    public init(style: MTPullFooterStyle) {
        let footerView = WHPullFooterView(frame: CGRect.zero)
        let animator = WHPullFooterAnimator(refreshView: footerView, style: style)
        super.init(refreshView: footerView, animator: animator, height: footerView.viewHeight, position: .bottom)
        self.setEnable(isEnabled: true)
    }
    
    public init() {
        let footerView = WHPullFooterView(frame: CGRect.zero)
        let animator = WHPullFooterAnimator(refreshView: footerView)
        super.init(refreshView: footerView, animator: animator, height: footerView.viewHeight, position: .bottom)
        self.setEnable(isEnabled: true)
    }
    
}
