//
//  WHPullFooterAnimator.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import MTPullToRefresh

public class WHPullFooterAnimator: RefreshViewAnimator {
    
    var initialDesc: String = WHLocalizedString("上拉可以加载更多", comment: "")
    var releasingDesc: String = WHLocalizedString("上拉可以加载更多", comment: "")
    var loadingDesc: String = WHLocalizedString("加载中...", comment: "")
    var noMoreDesc: String = WHLocalizedString("以上", comment: "")
    
    private let refreshView: WHPullFooterView
    
    public init(refreshView: WHPullFooterView, style: MTPullFooterStyle = .defaultText) {
        switch style {
        case .picCustomText((let initialDesc, let releasingDesc, let loadingDesc, let noMoreDesc)):
            if let initial = initialDesc {
                self.initialDesc = initial
            }
            if let releasing = releasingDesc {
                self.releasingDesc = releasing
            }
            if let loading = loadingDesc {
                self.loadingDesc = loading
            }
            if let noMore = noMoreDesc {
                self.noMoreDesc = noMore
            }
            break
        default:
            break
        }
        self.refreshView = refreshView
        refreshView.noMoreDateDesc = noMoreDesc
    }
    
    public func animate(_ state: State) {
        switch state {
        case .initial:
            refreshView.lottieView.stop()
            refreshView.lottieViewIsShow = false
            refreshView.text = initialDesc
        case .releasing(_):
            refreshView.lottieView.stop()
            refreshView.lottieViewIsShow = false
            refreshView.text = releasingDesc
        case .loading:
            refreshView.lottieView.play()
            refreshView.lottieViewIsShow = true
            refreshView.text = loadingDesc
        case .finished:
            refreshView.lottieView.stop()
            refreshView.lottieViewIsShow = false
            refreshView.text = initialDesc
        }
    }
}
