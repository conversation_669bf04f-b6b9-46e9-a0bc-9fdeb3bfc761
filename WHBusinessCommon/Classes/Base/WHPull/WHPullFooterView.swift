//
//  WHPullFooterView.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import Lottie

open class WHPullFooterView: WHPullView {
    var noMoreDateDesc: String = "" {
        didSet {
            noDataLabel.text = noMoreDateDesc
        }
    }
    public lazy var noDataLabel: UILabel = {
        let noDataLabel = UILabel()
        noDataLabel.isHidden = true
        noDataLabel.textColor = UIColor(r: 92, g: 95, b: 102)
        noDataLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        noDataLabel.textAlignment = .center
        return noDataLabel
    }()
    open override var lottieViewWidth: CGFloat {
        return fitScreenLength(v: 26)
    }
    open override var lottieViewHeight: CGFloat {
        return fitScreenLength(v: 26)
    }
    open override var viewHeight: CGFloat {
        return 49
    }
    override func createUI() {
        super.createUI()
        addSubview(noDataLabel)
        
        lottieView.animation = LottieAnimation.named("footer_loading", bundle: WHCMBundle.main)
    }
    
    override func changeViewLocation() {
        super.changeViewLocation()
        noDataLabel.frame = self.bounds
    }

}
