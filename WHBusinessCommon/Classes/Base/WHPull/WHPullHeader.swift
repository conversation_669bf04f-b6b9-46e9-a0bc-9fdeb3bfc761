//
//  WHPullHeader.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/29.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import MTPullToRefresh

public enum WHPullHeaderStyle {
    case pic
    case picDefaultText
    case picCustomText((initialDesc: String, releasingDesc: String, loadingDesc: String))
}
public class WHPullHeader: PullToRefresh {
    
    public init(style: WHPullHeaderStyle) {
        let headerView = WHPullHeaderView(frame: CGRect.zero)
        let animator = WHPullHeaderAnimator(refreshView: headerView, style: style)
        super.init(refreshView: headerView, animator: animator, height: headerView.viewHeight, position: .top)
        self.setEnable(isEnabled: true)
    }
    
}

