//
//  MTPullHederAnimator.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/29.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import MTPullToRefresh

public class WHPullHeaderAnimator: RefreshViewAnimator {
    
    var defaultText: String {
        return WHLocalizedString("下拉刷新")
    }
    var initialDesc: String = ""
    var releasingDesc: String = ""
    var loadingDesc: String = ""
    private var isTapicEngineOccurred = false
    private let refreshView: WHPullHeaderView
    var isShowDefaultText = false
    
    public init(refreshView: WHPullHeaderView, style: WHPullHeaderStyle) {
        self.refreshView = refreshView
        switch style {
        case .pic:
            break
        case .picDefaultText:
            isShowDefaultText = true
        case .picCustomText((let initialDesc, let releasingDesc, let loadingDesc)):
            self.initialDesc = initialDesc
            self.releasingDesc = releasingDesc
            self.loadingDesc = loadingDesc
        }
    }
    
    public func animate(_ state: State) {
        
        switch state {
        case .initial:
            refreshView.isPlaying = false
            refreshView.text = isShowDefaultText ? defaultText : initialDesc
        case .releasing(let p):
            refreshView.isPlaying = false
            refreshView.text = isShowDefaultText ? defaultText : releasingDesc
            if p >= 1 {
                if !isTapicEngineOccurred {
                    tapicEngineOccurred(.light)
                    
                    isTapicEngineOccurred = true
                }
            } else {
                isTapicEngineOccurred = false
            }
        case .loading:
            refreshView.isPlaying = true
            refreshView.text = isShowDefaultText ? defaultText : loadingDesc
        case .finished:
            refreshView.isPlaying = false
            refreshView.text = isShowDefaultText ? defaultText : initialDesc
        }
    }
}

/// 震动
/// - Parameter style: <#style description#>
public func tapicEngineOccurred(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
    let generator = UIImpactFeedbackGenerator(style: style)
    generator.prepare()
    generator.impactOccurred()
}
