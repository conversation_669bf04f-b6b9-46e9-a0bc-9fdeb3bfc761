//
//  WHPullHeaderView.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/29.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import Lottie

public class WHPullHeaderView: WHPullView {
    
    public override var lottieViewWidth: CGFloat {
        return fitScreenLength(v: 36)
    }
    public override var lottieViewHeight: CGFloat {
        return fitScreenLength(v: 25)
    }
    public override var viewHeight: CGFloat {
        return 44
    }
    override func createUI() {
        super.createUI()
        isPlaying = false
    }
    
    var isPlaying: Bool = true {
        didSet {
            if isPlaying == oldValue {
                return
            }
            if isPlaying {
                lottieViewIsShow = true
                descLabel.isHidden = true
                lottieView.animation = LottieAnimation.named("dropDownRefresh", bundle: WHCMBundle.main, subdirectory: nil, animationCache: nil)
                lottieView.play()
            } else {
                lottieViewIsShow = false
                descLabel.isHidden = false
                lottieView.stop()
                lottieView.animation = LottieAnimation.named("dropDownRefresh", bundle: WHCMBundle.main, subdirectory: nil, animationCache: nil)
                lottieView.currentProgress = 1
            }
        }
    }

}
