//
//  WHPullView.swift
//  MTXX
//
//  Created by 吕小康 on 2020/12/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import Lottie
import MTPullToRefresh

func fitScreenLength(v: CGFloat) -> CGFloat {
    return v * UIScreen.main.bounds.size.width / 375
}
open class WHPullView: MTPullBaseView {

    public override init(frame: CGRect) {
        super.init(frame: frame)
        createUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    var text: String = "" {
        didSet {
            descLabel.text = text
            if text != oldValue {
                descWidth = ceil(descLabel.text?.getStringWidth(lottieViewHeight, font: descLabel.font) ?? 0)
                changeViewLocation()
            }
        }
    }
    public lazy var lottieView: LottieAnimationView = {
        let lottieView = LottieAnimationView()
        lottieView.loopMode = .loop
        return lottieView
    }()
    
    public lazy var descLabel: UILabel = {
        let descLabel = UILabel(frame: CGRect.zero)
        descLabel.textColor = UIColor(r: 160, g: 163, b: 166)
        descLabel.font = UIFont.systemFont(ofSize: 12)
        return descLabel
    }()
    
    open var lottieViewWidth: CGFloat {
        return fitScreenLength(v: 36)
    }
    open var lottieViewHeight: CGFloat {
        return fitScreenLength(v: 25)
    }
    
    var lottieViewIsShow: Bool = true {
        didSet {
            if lottieViewIsShow != oldValue {
                changeViewLocation()
            }
        }
    }
    
    var descWidth: CGFloat = 0
    
    open var viewHeight: CGFloat {
        return lottieViewHeight
    }
    
    public override func layoutSubviews() {
        setupFrame(in: superview)
        super.layoutSubviews()
    }
    
    public override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        setupFrame(in: superview)
    }
    
    func setupFrame(in newSuperview: UIView?) {
        guard let superview = newSuperview else { return }
        frame = CGRect(x: frame.origin.x, y: frame.origin.y, width: superview.frame.width, height: viewHeight)
        self.changeViewLocation()
    }
    
    func changeViewLocation() {
        if lottieViewIsShow {
            descLabel.frame = CGRect(x: 0, y: (viewHeight - lottieViewHeight) / 2, width: descWidth, height: lottieViewHeight)
            descLabel.center = CGPoint(x: frame.width / 2, y: viewHeight / 2)
            lottieView.frame = CGRect(x: (frame.width - lottieViewWidth) / 2, y: (viewHeight - lottieViewHeight) / 2, width: lottieViewWidth, height: lottieViewHeight)
            lottieView.isHidden = false
        } else {
            descLabel.frame = CGRect(x: 0, y: (viewHeight - lottieViewHeight) / 2, width: descWidth, height: lottieViewHeight)
            descLabel.center = CGPoint(x: frame.width / 2, y: viewHeight / 2)
            lottieView.frame = CGRect(x: descLabel.frame.minX - lottieViewWidth, y: (viewHeight - lottieViewHeight) / 2, width: 0, height: 0)
            lottieView.isHidden = true
        }
    }
    
    func createUI() {
        self.backgroundColor = .clear
        addSubview(lottieView)
        addSubview(descLabel)
    }

}
