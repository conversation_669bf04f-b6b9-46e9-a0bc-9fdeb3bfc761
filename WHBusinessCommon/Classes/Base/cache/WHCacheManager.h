//
//  WHCacheManager.h
//  MTXX
//
//  Created by meitu on 2020/6/24.
//  Copyright © 2020 Meitu. All rights reserved.
//

#import <UIKit/UIKit.h>

@class YYCache;

typedef NS_ENUM(NSInteger, WHCacheKeyType) {
    MTXXCacheKeyTypeDefault                     = 0,   /**< 默认全局的缓存 */
    MTXXCacheKeyTypeWebRequest                  = 1,   /**< 网络缓存 */
    MTXXCacheKeyTypeImage                       = 2,   /**< 图片缓存 */
    MTXXCacheKeyTypeVideo                       = 4,   /**< 视频缓存 */
    MTXXCacheKeyTypeMusic                       = 5,   /**< 音乐缓存 */
};

NS_ASSUME_NONNULL_BEGIN

typedef void (^MTXXCacheGetSuccessBlock)(id value);
typedef void (^MTXXCacheGetFailBlock)(void);

@interface WHCacheManager : NSObject

@property (nonatomic, strong, readonly) YYCache *defaultCache;

+ (WHCacheManager*)sharedManager;

///设置全局的缓存
+ (void)setCacheWithKey:(NSString *)key value:(nullable id<NSCoding>)value;
///设置网络请求的缓存
+ (void)setRequestCacheWithUrl:(NSString *)url key:(NSString *)key value:(nullable id<NSCoding>)value;
///设置自定义枚举的缓存
+ (void)setCacheWithType:(WHCacheKeyType)type key:(NSString *)key value:(nullable id<NSCoding>)value;

///获取全局的缓存
+ (void)getCacheWithKey:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(nullable MTXXCacheGetFailBlock)fail;
+ (id)getCacheWithKey:(NSString *)key;
///获取网络请求的缓存
+ (void)getRequestCacheWithUrl:(NSString *)url key:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(nullable MTXXCacheGetFailBlock)fail;
///获取自定义枚举的缓存
+ (void)getCacheWithType:(WHCacheKeyType)type key:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(nullable MTXXCacheGetFailBlock)fail;

///删除网络缓存
+ (void)removeRequestCache;

/// 删除对应type，对应key
+ (void)removeCacheWithType:(WHCacheKeyType)type forKey:(NSString *)key;

/// 删除默认类型对应键值的缓存
+ (void)removeCacheForKey:(NSString *)key;

///删除枚举缓存
+ (void)removeCacheWithType:(WHCacheKeyType)type;
///删除所有缓存
+ (void)removeAllCache;

///  清理YYCache相关内存缓存
//+ (void)cleanMemoryCache;

@end

NS_ASSUME_NONNULL_END
