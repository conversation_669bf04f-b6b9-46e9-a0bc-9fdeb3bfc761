//
//  WHCacheManager.m
//
//
//  Created by meitu on 2020/6/24.
//  Copyright © 2020 Meitu. All rights reserved.
//

#import "WHCacheManager.h"
#import <YYCache/YYCache.h>
//#import "MTSwizzle.h"

#define kMTXXCacheDiskLimitTimeSecond 7*24*60         // 磁盘缓存过期时间7天

@interface WHCacheManager()
/// 所有YYCache管理的path
@property (nonatomic, strong) NSMutableArray<NSString*> *allCachePath;

///枚举对应的key值
@property (nonatomic ,strong) NSDictionary *keyMap;
@property (nonatomic, strong) YYCache *defaultCache;

@end

@implementation WHCacheManager

#pragma mark - 初始化

+ (WHCacheManager*)sharedManager {
    static dispatch_once_t once;
    static WHCacheManager *sharedManager;
    dispatch_once(&once, ^{
        sharedManager = [[self alloc] init];
        sharedManager.allCachePath = [NSMutableArray arrayWithCapacity:0];
    });
    return sharedManager;
}

- (YYCache *)defaultCache {
    if (!_defaultCache) {
        _defaultCache = [YYCache cacheWithName:@"MTXXCacheKeyTypeDefault"];
    }
    return _defaultCache;
}

#pragma mark - 设置
+ (void)setCacheWithKey:(NSString *)key value:(nullable id<NSCoding>)value{
    YYCache *cache = [WHCacheManager sharedManager].defaultCache;
    if (cache) {
        [cache setObject:value forKey:key];
    }
}

+ (void)setRequestCacheWithUrl:(NSString *)url key:(NSString *)key value:(nullable id<NSCoding>)value{
    
    NSString *keyString = [NSString stringWithFormat:@"%@?id=%@",url,key];
    
    [[self class]setCacheWithType:MTXXCacheKeyTypeWebRequest key:keyString value:value];
}

+ (void)setCacheWithType:(WHCacheKeyType)type key:(NSString *)key value:(nullable id<NSCoding>)value{
    
    [[self class]setCacheWithName:[[self sharedManager].keyMap objectForKey:@(type)] key:key value:value];
}

+ (void)setCacheWithName:(NSString *)name key:(NSString *)key value:(nullable id<NSCoding>)value{
    YYCache *cache = [WHCacheManager sharedManager].defaultCache;
    if (![name isEqualToString:@"MTXXCacheKeyTypeDefault"]) {
        cache = [[YYCache alloc] initWithName:name];
        cache.diskCache.ageLimit = kMTXXCacheDiskLimitTimeSecond;
    }
    [cache setObject:value forKey:key];
}

#pragma mark - 读取
+ (void)getCacheWithKey:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(MTXXCacheGetFailBlock)fail{
    
    [[self class]getCacheWithType:MTXXCacheKeyTypeDefault key:key complte:block fail:fail];
}

+ (id)getCacheWithKey:(NSString *)key {
    
    YYCache *cache = [WHCacheManager sharedManager].defaultCache;
    if (cache && [cache containsObjectForKey:key]) {
        return [cache objectForKey:key];
    }
    return nil;
}

+ (void)getRequestCacheWithUrl:(NSString *)url key:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(MTXXCacheGetFailBlock)fail{
    
    NSString *keyString = [NSString stringWithFormat:@"%@?id=%@",url,key];
    [[self class]getCacheWithType:MTXXCacheKeyTypeWebRequest key:keyString complte:block fail:fail];
}

+ (void)getCacheWithType:(WHCacheKeyType)type key:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(MTXXCacheGetFailBlock)fail{
    
    [[self class]getCacheWithName:[[self sharedManager].keyMap objectForKey:@(type)] key:key complte:block fail:fail];
}

+ (void)getCacheWithName:(NSString *)name key:(NSString *)key complte:(MTXXCacheGetSuccessBlock)block fail:(MTXXCacheGetFailBlock)fail{
    
    YYCache *cache = [WHCacheManager sharedManager].defaultCache;
    if (![name isEqualToString:@"MTXXCacheKeyTypeDefault"]) {
        cache = [YYCache cacheWithName:name];
    }
    if (cache && [cache containsObjectForKey:key]) {
        id result = [cache objectForKey:key];
        if(result != nil){
            block(result);
        }else{
            if (fail) {
                fail();
            }
        }
    }else{
        if (fail) {
            fail();
        }
    }
}

#pragma mark - 删除
+ (void)removeRequestCache{
    
    [[self class]removeCacheWithType:MTXXCacheKeyTypeWebRequest];
}

+ (void)removeCacheWithType:(WHCacheKeyType)type{
    
    [[self class]removeCacheWithName:[[self sharedManager].keyMap objectForKey:@(type)]];
}

/// 删除对应type，对应key
+ (void)removeCacheWithType:(WHCacheKeyType)type forKey:(NSString *)key {
    [[YYCache cacheWithName:[[self sharedManager].keyMap objectForKey:@(type)]] removeObjectForKey:key];
}

/// 删除默认类型对应键值的缓存
+ (void)removeCacheForKey:(NSString *)key {
    [[YYCache cacheWithName:[[self sharedManager].keyMap objectForKey:@(MTXXCacheKeyTypeWebRequest)]] removeObjectForKey:key];
}

+ (void)removeCacheWithName:(NSString *)name{
    
    YYCache *cache = [YYCache cacheWithName:name];
    if (cache) {
        [cache removeAllObjects];
    }
}

+ (void)removeAllCache{
    
    NSArray *keysArray = [[self sharedManager].keyMap allValues];
    for (NSString *name in keysArray) {
        [[self class]removeCacheWithName:name];
    }
}

//+ (void)cleanMemoryCache {
//    NSArray *keysArray = [[self sharedManager].allCachePath copy];
//    for (NSString *path in keysArray) {
//        [[YYCache cacheWithPath:path].memoryCache removeAllObjects];
//    }
//}
//
//+ (void)addCachePath:(NSString *)path {
//    if (![[WHCacheManager sharedManager].allCachePath containsObject:path]) {
//        [[WHCacheManager sharedManager].allCachePath addObject:path];
//    }
//}

#pragma mark - set方法

- (NSDictionary *)keyMap{
    
    if (!_keyMap) {
        _keyMap = @{@(MTXXCacheKeyTypeDefault):@"MTXXCacheKeyTypeDefault",
                    @(MTXXCacheKeyTypeWebRequest):@"MTXXCacheKeyTypeWebRequest",
                    @(MTXXCacheKeyTypeImage):@"MTXXCacheKeyTypeImage",
                    @(MTXXCacheKeyTypeVideo):@"MTXXCacheKeyTypeVideo",
                    @(MTXXCacheKeyTypeMusic):@"MTXXCacheKeyTypeMusic",
        };
    }
    
    return _keyMap;
}

@end

//@interface YYCache (cacheHook)
//
//@end
//@implementation YYCache (cacheHook)
//
//+ (void)initialize {
//    [YYCache ag_swizzleMethod:@selector(initWithPath:) withMethod:@selector(mt_initWithPath:) error:nil];
//}
//
//- (instancetype)mt_initWithPath:(NSString *)path {
//    YYCache *cache = [self mt_initWithPath:path];
//    [WHCacheManager addCachePath:path];
//    return cache;
//}

//@end
