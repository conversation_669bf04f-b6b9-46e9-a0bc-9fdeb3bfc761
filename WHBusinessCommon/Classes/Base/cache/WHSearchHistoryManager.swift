//
//  WHSearchHistoryManager.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/9/28.
//

import Foundation

@objc public enum WHSearchHistoryType: Int, RawRepresentable {
    case home
    case inspiration
    case textToImage
    case imageToImage
    case editor          //编辑器
    
    public typealias RawValue = String
    
    public var rawValue: RawValue {
        switch self {
        case .home:
            return "home"
        case .inspiration:
            return "gallery"
        case .textToImage:
            return "text_to_image"
        case .imageToImage:
            return "image_to_image"
        case .editor:
            return "editor"
        }
    }
    
    public init?(rawValue: RawValue) {
        switch rawValue {
        case "home":
            self = .home
        case "gallery":
            self = .inspiration
        case "text_to_image":
            self = .textToImage
        case "image_to_image":
            self = .imageToImage
        case "editor":
            self = .editor
        default:
            self = .inspiration
        }
    }
}


public class WHSearchHistoryManager: NSObject {
    
    private static let sharedManager = WHSearchHistoryManager()
    
    private var historyCache:[String: NSMutableArray] = [:]
    private var historyKeywordTypeCache: [String: NSMutableDictionary] = [:]
    private var maxHistoryCount:[String: Int] = [:]
    
    private class func searchHistoryPath(type: WHSearchHistoryType) -> String {
        guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
            return ""
        }
        
        return path + "/searchHistory\(type.rawValue.capitalized).plist"
    }
    
    private class func searchHistoryKeywordTypePath(type: WHSearchHistoryType) -> String {
        guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
            return ""
        }
        return path + "/searchHistoryKeywordType\(type.rawValue.capitalized).plist"
    }
    
    @objc public class func clearCache(type: WHSearchHistoryType) {
        let manager = WHSearchHistoryManager.sharedManager
        manager.historyCache.removeValue(forKey: type.rawValue)
        manager.maxHistoryCount.removeValue(forKey: type.rawValue)
        manager.historyKeywordTypeCache.removeValue(forKey: type.rawValue)
    }
    
    @objc public class func config(maxHistoryCount count: Int, historyCache: NSMutableArray?, type: WHSearchHistoryType) {
        let manager = WHSearchHistoryManager.sharedManager

        manager.maxHistoryCount[type.rawValue] = count
        
        let list = self.historyList(type: type)
        if list.count > count {
            let range = NSMakeRange(count - 1, list.count - count)
            list.removeObjects(in: range)
            list.write(toFile: self.searchHistoryPath(type: type), atomically: true)
        }
        
        if let cacheList = historyCache {
            cacheList.removeAllObjects()
            cacheList.addObjects(from: list as! [Any])
            manager.historyCache[type.rawValue] = cacheList
        }
    }
    
    @objc public class func saveHistory(text: String, type: WHSearchHistoryType) {
        let historyList = self.historyList(type: type)
        if historyList.contains(text) {
            historyList.remove(text)
        }
        historyList.insert(text, at: 0)
        
        let manager = WHSearchHistoryManager.sharedManager
        if let maxHistoryCount = manager.maxHistoryCount[type.rawValue], historyList.count > maxHistoryCount {
            historyList.removeLastObject()
        }
        
        historyList.write(toFile: self.searchHistoryPath(type: type), atomically: true)
    }
    
    @objc public class func saveHistoryKeywordType(text: String, type: WHSearchHistoryType, keyWordType: Int = 0) {//0 热门搜索，1：素材搜索
        let historyKeywordTypeList = self.historyKeywordTypeList(type: type)
        historyKeywordTypeList.setValue(keyWordType, forKey: text)
        historyKeywordTypeList.write(toFile: self.searchHistoryKeywordTypePath(type: type), atomically: true)
    }
    
    @objc public class func deleteHistory(text: String, type: WHSearchHistoryType) {
        let historyList = self.historyList(type: type)

        if historyList.contains(text) {
            historyList.remove(text)
            historyList.write(toFile: self.searchHistoryPath(type: type), atomically: true)
        }
    }
    
    @objc public class func deleteHistoryKeyType(text: String = "", type: WHSearchHistoryType) {
        var historyDic = self.historyKeywordTypeList(type: type)
        historyDic = [:]
        historyDic.write(toFile: self.searchHistoryKeywordTypePath(type: type), atomically: true)
//        let historyKeywordList = self.searchHistoryKeywordTypePath(type: type)
//        if let list = historyKeywordList?.keys, list.contains(text)
            
    }
    
    @objc public class func clearHistory(type: WHSearchHistoryType) {
        let historyList = self.historyList(type: type)
        historyList.removeAllObjects()
        historyList.write(toFile: self.searchHistoryPath(type: type), atomically: true)
    }
    
    @objc public class func historyList(type: WHSearchHistoryType) -> NSMutableArray {
        let manager = WHSearchHistoryManager.sharedManager
        guard let list = manager.historyCache[type.rawValue] else {
            if let list = NSArray(contentsOfFile: self.searchHistoryPath(type: type)) {
                manager.historyCache[type.rawValue] = NSMutableArray(array: list)
            } else {
                manager.historyCache[type.rawValue] = NSMutableArray()
            }
            
            return manager.historyCache[type.rawValue]!
        }
        
        return list
    }
    
    @objc public class func historyKeywordTypeList(type: WHSearchHistoryType) -> NSMutableDictionary {
        let manager = WHSearchHistoryManager.sharedManager
        guard let list = manager.historyKeywordTypeCache[type.rawValue] else {
            if let list = NSDictionary(contentsOfFile: self.searchHistoryKeywordTypePath(type: type)) {
                manager.historyKeywordTypeCache[type.rawValue] = NSMutableDictionary(dictionary: list)
            } else {
                manager.historyKeywordTypeCache[type.rawValue] = NSMutableDictionary()
            }
            
            return manager.historyKeywordTypeCache[type.rawValue]!
        }
        
        return list
    }
}
