//
//  MTCustomSearchBar.m
//  MTXX
//
//  
//  Copyright © 2018年 Meitu. All rights reserved.
//

#import "WHCustomSearchBar.h"
#import <WHBusinessCommon/WHBusinessCommon-Swift.h>
@import WHBaseLibrary;

#define RGBCOLOR(r,g,b)    [UIColor colorWithRed:r/255.f green:g/255.f blue:b/255.f alpha:1.f]

@implementation WHCustomSearchBar

- (instancetype)init {
    if (self = [super init]) {
        [self commonInit];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self commonInit];
    }
    return self;
}


- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self commonInit];
    }
    return self;
}

- (void)setPlaceholder:(NSString *)placeholder {
    [super setPlaceholder:placeholder];
    
    for (UIView *view in self.subviews.lastObject.subviews) {
        if([view isKindOfClass:NSClassFromString(@"UISearchBarTextField")]) {
            UITextField *textField = (UITextField *)view;
            textField.attributedPlaceholder =
            [[NSAttributedString alloc] initWithString:placeholder
                                            attributes:@{NSForegroundColorAttributeName:RGBCOLOR(92, 95, 102),
                                                         NSFontAttributeName:textField.font}];
        }
    }
    
    if ([UIDevice currentDevice].systemVersion.floatValue < 11.0) {
        SEL centerSelector = NSSelectorFromString([NSString stringWithFormat:@"%@%@", @"setCenter", @"Placeholder:"]);
        if ([self respondsToSelector:centerSelector]) {
            BOOL centeredPlaceholder = NO;
            NSMethodSignature *signature = [[UISearchBar class] instanceMethodSignatureForSelector:centerSelector];
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
            [invocation setTarget:self];
            [invocation setSelector:centerSelector];
            [invocation setArgument:&centeredPlaceholder atIndex:2];
            [invocation invoke];
        }
    }
    
}


-(void)changeLeftPlaceholder:(NSString *)placeholder {
    self.placeholder = placeholder;
    
    for (UIView *view in self.subviews.lastObject.subviews) {
        if([view isKindOfClass:NSClassFromString(@"UISearchBarTextField")]) {
            UITextField *textField = (UITextField *)view;
            textField.attributedPlaceholder =
            [[NSAttributedString alloc] initWithString:placeholder
                                            attributes:@{NSForegroundColorAttributeName:RGBCOLOR(92, 95, 102),
                                                         NSFontAttributeName:textField.font}];
        }
    }
    
    SEL centerSelector = NSSelectorFromString([NSString stringWithFormat:@"%@%@", @"setCenter", @"Placeholder:"]);
    if ([self respondsToSelector:centerSelector]) {
        BOOL centeredPlaceholder = NO;
        NSMethodSignature *signature = [[UISearchBar class] instanceMethodSignatureForSelector:centerSelector];
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
        [invocation setTarget:self];
        [invocation setSelector:centerSelector];
        [invocation setArgument:&centeredPlaceholder atIndex:2];
        [invocation invoke];
    }
}


- (void)commonInit {
    self.exclusiveTouch = YES;
    self.returnKeyType = UIReturnKeySearch;
    self.barTintColor = [UIColor whiteColor];
    self.contentMode = UIViewContentModeRedraw;
    self.searchBarStyle = UISearchBarStyleMinimal;
    self.searchTextPositionAdjustment = UIOffsetMake(2.f, 0.f);
//    [self setPositionAdjustment:UIOffsetMake(2.f, 0.f) forSearchBarIcon:UISearchBarIconSearch];
    
    // 设置搜索框icon
    [self setImage:[UIImage imageNamed:@"icon_searchbar_search"]
  forSearchBarIcon:UISearchBarIconSearch
             state:UIControlStateNormal];
    [self setImage:[UIImage imageNamed:@"icon_searchbar_clear_normal"]
  forSearchBarIcon:UISearchBarIconClear
             state:UIControlStateNormal];
    [self setImage:[UIImage imageNamed:@"icon_searchbar_clear_normal"]
  forSearchBarIcon:UISearchBarIconClear
             state:UIControlStateHighlighted];
    
    // 设置搜索框字体及文字颜色
    UITextField *searchTextFiled = nil;
    if (@available(iOS 13.0, *)) {
        searchTextFiled = self.searchTextField;
    } else {
        searchTextFiled = [self valueForKey:@"searchField"];
        if (![searchTextFiled isKindOfClass:[UITextField class]]) {
            searchTextFiled = nil;
        }
    }
    if (searchTextFiled) {
        searchTextFiled.textColor = [UIColor whiteColor];
        searchTextFiled.font = [UIFont systemFontOfSize:14];
        UIButton *btn = [searchTextFiled valueForKey:@"_clearButton"];
        if(btn) {
            btn.imageEdgeInsets = UIEdgeInsetsMake(0, -0, 0, 0);
        }
    }
    
    // 设置搜索框底部颜色
    [self setBackgroundImage:[RGBCOLOR(0, 0, 1) wh_image]
              forBarPosition:UIBarPositionAny
                  barMetrics:UIBarMetricsDefaultPrompt];
    [self setBackgroundImage:[RGBCOLOR(0, 0, 1) wh_image]
              forBarPosition:UIBarPositionAny
                  barMetrics:UIBarMetricsDefault];
    
    // 搜索框输入背景色
    UIImage *textBgImage = [self roundedImageWithSize:CGSizeMake(36, 32)
                                                   color:RGBCOLOR(34, 35, 38)
                                                  radius:0];
    [self setSearchFieldBackgroundImage:textBgImage forState:UIControlStateNormal];
    
    // 设置搜索框取消按钮
    NSDictionary *titleTextAttributes = @{NSForegroundColorAttributeName: [UIColor wh_colorWithRGB:0x7A7E85], NSFontAttributeName: [UIFont systemFontOfSize:14]};
    
    UIBarButtonItem *barBtnItem = [UIBarButtonItem appearanceWhenContainedInInstancesOfClasses:@[[UISearchBar class]]];
    [barBtnItem setTitleTextAttributes:titleTextAttributes forState:UIControlStateNormal];
    [barBtnItem setTitle:[NSString stringWithFormat:@" %@", [WHLocalizedConvertor localizedString:@"取消"]]];
}

- (UIImage *)roundedImageWithSize:(CGSize)size color:(UIColor *)color radius:(CGFloat)radius {
    CGRect rect = CGRectZero;
    rect.size = size;
    
    UIBezierPath* path = [UIBezierPath bezierPathWithRoundedRect:rect cornerRadius:radius];
    return [self imageWithBezierPath:path color:color backgroundColor:color];
}

- (UIImage *)imageWithBezierPath:(UIBezierPath *)path color:(UIColor *)color backgroundColor:(UIColor *)backgroundColor
{
    UIGraphicsBeginImageContextWithOptions(path.bounds.size, NO, [UIScreen mainScreen].scale);
    
    if (backgroundColor) {
        [backgroundColor set];
        [path fill];
    }
    if (color) {
        [color set];
        [path stroke];
    }
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return [image stretchableImageWithLeftCapWidth:image.size.width/2 topCapHeight:image.size.height/2];
}

- (void)setIsTextToImage:(BOOL)isTextToImage {
    _isTextToImage = isTextToImage;
    if (isTextToImage) {
        [self setBackgroundImage:[RGBCOLOR(20, 20, 20) wh_image]
                  forBarPosition:UIBarPositionAny
                      barMetrics:UIBarMetricsDefault];
    }
}

@end
