//
//  WHShareManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON>iaoqi on 2023/10/8.
//

import Foundation
import MTSSOShareKit
import MTWebKit

@objc public class WHShareViewManager: NSObject {
    
    /// 显示分享弹窗
    /// - Parameter content: 分享内容
    public  static func showShareSheet(with content: [String: Any]?,
                                       tools: [WHShareSheetToolsItem] = [],
                                       toolsSelectBlock: ((WHShareSheetToolsItem) -> ())? = nil,
                                       selectChannel: ((String?,Bool)->Void)? = nil,
                                       completion: @escaping MTSSOSimpleShareCompletionBlock){
        guard let content = content else { return }
        let sheet = WHShareSheetView(content: content, tools: tools)
        sheet.toolsSelectBlock = { type in
            sheet.hide {
                toolsSelectBlock?(type)
            }
        }
        sheet.selectedBlock = { (type,contentType,image) in
            selectChannel?(type.channel(), true)
            sheet.hide()
            self.share(content: content, plamType: type, contentType: contentType,shareImage: image) { basemode in
                completion(basemode)
            }
        }
        sheet.closeBlock = {
            selectChannel?( nil, false)
        }
       sheet.show()
    }
    
    /// H5协议显示分享弹窗（浏览器调起）
    /// - Parameter content: 分享内容
    @objc public  static func showWebCommonShareSheet(with shareInfo: MTWebViewJSShareInfo?,
                                                      selectChannel: ((String?,Bool)->Void)? = nil,
                                                      completion: @escaping MTSSOSimpleShareCompletionBlock){
        guard let shareInfo = shareInfo as? MTWebViewJSShareInfo else { return }
        let sheet = WHShareWebCommonSheetView(shareInfo: shareInfo)
        let content = self.convertShareInfoToContent(shareInfo: shareInfo)
        sheet.selectedBlock = { (type,contentType,image) in
            selectChannel?(type.channel(), true)
            sheet.hide()
            self.share(content: content, plamType: type, contentType: contentType,shareImage: image) { basemode in
                completion(basemode)
            }
        }
        sheet.closeBlock = {
            selectChannel?( nil, false)
        }
       sheet.show()
    }
    
    ///
    /// - Parameters:
    ///   - content: 分享数据
    ///   - plamType: 分享渠道
    ///   - contentType: 分享类型
    ///   - shareImage: 加工后的图片 图片分享比有
    ///   - completion: 完成回调
    static func share(content: [String: Any],plamType:WHShareSheetViewItemType,contentType: WHShareContentType,shareImage:UIImage? = nil,completion: @escaping MTSSOSimpleShareCompletionBlock){
        switch contentType {
        case .H5:
            let model = WHShareH5Model.getH5ModelWith(content: content)
            self.shareH5(to: plamType, model: model, completion: completion)
        case .image:
            if (shareImage != nil) {
                shareLocalPic(to: plamType, image: shareImage, completion: completion)
            } else {
                if let imageString = content["pic"] as? String{
                    downImage(imageString: imageString) { sucess, sharedImage in
                        if sucess {
                            shareLocalPic(to: plamType, image: sharedImage, completion: completion)
                        }else {
                            var model = MTSSOAPIBaseResp()
                            model.result = 100
                            completion(model)
                        }
                    }
                }
            }
        case .miniApp:
            let model = WHShareMiniProgramModel.getMiniModelWith(content: content)
            self.shareMiniprogram(to: plamType, model: model, hdImageData: nil, completion: completion)
        case .images:
            if let imageArray = content["pic_list"] as? [String]{
                downImage(images: imageArray) { sucess, images in
                    if sucess {
                        shareImageToXiaoshongshu(content: content, images: images, completion: completion)
                    }else{
                        var model = MTSSOAPIBaseResp()
                        model.result = 100
                        completion(model)
                    }
                }
            }

        }
    }
    
    static func shareImageToXiaoshongshu(content: [String: Any],
                                         images:[UIImage],
                           completion: @escaping MTSSOSimpleShareCompletionBlock){
        var messageArray:[MTSSOAPIImageMessage] = []
        var title: String = content["title"] as? String ?? ""
        var description: String = content["content"] as? String ?? ""
        images.forEach { image in
            if let message =  MTSSOAPIImageMessage("#WHEE \(description) \(content["url"] as? String ?? "")", description: description, image: image, thumbnail: image, controller: nil){
                messageArray.append(message)
            }
        }
        MTSSOXiaohongshu.share(messageArray,completion: completion)
    }
    
    static func downImage(images:[String],comple:@escaping(Bool, [UIImage]) -> Void){
        DispatchQueue.global().async {
            var imageArray: [UIImage] = []
            for urlString in images{
                if let url = URL(string: urlString){
                    if let imgData = try? Data(contentsOf: url), let image = UIImage(data: imgData) {
                        imageArray.append(image)
                    } else {
                    }
                }
            }
            DispatchQueue.main.async {
                if imageArray.count == images.count{
                    comple(true,imageArray)
                }else {
                    comple(false,imageArray)
                }
            }
        }
    }
    
    static func downImage(imageString:String,comple:@escaping(Bool, UIImage?) -> Void){
        DispatchQueue.global().async {
            if let url = URL(string: imageString){
                if let imgData = try? Data(contentsOf: url), let image = UIImage(data: imgData) {
                    DispatchQueue.main.async {
                        comple(true,image)
                    }
                } else {
                    comple(false,nil)
                }
            }else {
                comple(false,nil)
            }
            
        }
    }
    
    /// 分享本地图片
    static func shareLocalPic(to platform: WHShareSheetViewItemType,
                              image: UIImage?,
                              assetID: String? = nil,
                              title: String = "",
                              completion: @escaping MTSSOSimpleShareCompletionBlock)
    {
        var shareContent = title
        let msg = MTSSOAPIImageMessage(shareContent, description: "", image: image, thumbnail: image, controller: nil)
        msg?.assetIdentifier = assetID
        switch platform {
        case .wechat:
            MTSSOWeChat.share(msg, completion: completion)
        case .timeline:
            MTSSOWeChatTimeLine.share(msg, completion: completion)
        case .qq:
            MTSSOTencent.share(msg, completion: completion)
        case .weibo:
            MTSSOWeibo.share(msg, completion: completion)
        case .facebook:
            MTSSOFacebook.share(msg, completion: completion)
        case .redBook:
            MTSSOXiaohongshu.share(msg, completion: completion)
        }
    }
    /// 分享小程序
    static func shareMiniprogram(to platform: WHShareSheetViewItemType,
                                 model: WHShareMiniProgramModel,
                                 hdImageData: Data?,
                                 completion: @escaping MTSSOSimpleShareCompletionBlock)
    {
//        guard let hdImageData = hdImageData else { return }
        MTSSOWeChat.shareMiniProgram(withPageURL: "", userName: model.userName, path: model.path, hdImageData: hdImageData, title: model.title, description: model.content, miniProgramType: .release, completion: completion)
    }
    
    /// 分享H5
    static func shareH5(to platform: WHShareSheetViewItemType,
                        model: WHShareH5Model?,
                        completion: @escaping MTSSOSimpleShareCompletionBlock)
    {
        guard let model = model else { return }
        let thumbnailImage: UIImage? = isNotEmptyString(model.picUrl) ? nil : UIImage(named: "shareappIcon")
        let msg = MTSSOAPILinkMessage(title: model.title ?? "", description: model.content ?? "", linkToShare: model.link ?? "", thumbnailImage: thumbnailImage, thumbnailURL: model.picUrl ?? "", controller: nil)
        switch platform {
        case .wechat:
            MTSSOWeChat.share(msg, completion: completion)
        case .timeline:
            let timelineMsg = MTSSOAPILinkMessage(title: model.content ?? "", description: model.content ?? "", linkToShare: model.link ?? "", thumbnailImage: thumbnailImage, thumbnailURL: model.picUrl ?? "", controller: nil)
            MTSSOWeChatTimeLine.share(timelineMsg, completion: completion)
        case .qq:
            MTSSOTencent.share(msg, completion: completion)
        case .weibo:
            let weiboMsg = MTSSOAPILinkMessage(title:"", description: model.content ?? "", linkToShare: model.link ?? "", thumbnailImage: thumbnailImage, thumbnailURL: model.picUrl ?? "", controller: nil)
            MTSSOWeibo.share(weiboMsg, completion: completion)
        case .facebook:
            MTSSOFacebook.share(msg, completion: completion)
        case .redBook:
            MTSSOXiaohongshu.share(msg, completion: completion)
        }
    }
    
    static var localId:String = ""
    static func saveImage(_ image: UIImage,comple:@escaping((Bool,URL?)-> Void)) {
            PHPhotoLibrary.shared().performChanges({
                let result = PHAssetChangeRequest.creationRequestForAsset(from: image)
                let assetPlaceholder = result.placeholderForCreatedAsset
                //保存标志符
                self.localId = assetPlaceholder?.localIdentifier ?? ""
            }) { (isSuccess: Bool, error: Error?) in
                if isSuccess {
                    print("保存成功!")
                    //通过标志符获取对应的资源
                    let assetResult = PHAsset.fetchAssets(
                        withLocalIdentifiers: [self.localId], options: nil)
                    let asset = assetResult[0]
                    let options = PHContentEditingInputRequestOptions()
                    options.canHandleAdjustmentData = {(adjustmeta: PHAdjustmentData)
                        -> Bool in
                        return true
                    }
                    //获取保存的图片路径
                    asset.requestContentEditingInput(with: options, completionHandler: {
                        (contentEditingInput:PHContentEditingInput?, info: [AnyHashable : Any]) in
                        comple(true,contentEditingInput!.fullSizeImageURL!)
                    })
                } else{
//                    print("保存失败：", error!.localizedDescription)
                    comple(false,nil)

                }
            }
        }
    ///将shareInfo转为原始分享的数据结构
    static func convertShareInfoToContent(shareInfo:MTWebViewJSShareInfo) -> [String: Any] {
        var content:[String:Any] = ["title":shareInfo.title ?? "",
                                    "content":shareInfo.desc ?? "",
                                    "ui_type":((shareInfo.type ?? .link) == .photo ? 0 : 1)]
        if let link = shareInfo.link as? String,link.count > 0 {
            content["url"] = link
        }
        if let images = shareInfo.images as? [String],images.count > 0 {
            content["pic"] = images[0]
            content["pic_list"] = images
        }
        if let extraBizData = shareInfo.extraBizData as? String, extraBizData.count > 0 {
            let data = extraBizData.data(using: String.Encoding.utf8)
            if let dict = try? JSONSerialization.jsonObject(with: data ?? Data(), options: JSONSerialization.ReadingOptions.mutableContainers) as? [String : Any] {
                if let applet_appid = dict["applet_appid"] {
                    content["applet_appid"] = applet_appid
                }
                if let applet_username = dict["applet_username"] {
                    content["applet_username"] = applet_username
                }
                if let applet_path = dict["applet_path"] {
                    content["applet_path"] = applet_path
                }
                if let applet_type = dict["applet_type"] {
                    content["applet_type"] = applet_type
                }
            }
        }
        return content;
    }
    
}



func isNotEmptyString(_ str: String?) -> Bool {
    guard let str = str else { return false }
    return str.count > 0
}
