//
//  WHShareModel.swift
//  WHBusinessCommon
//
//  Created by x<PERSON><PERSON><PERSON> on 2023/10/23.
//

import Foundation
/// 小程序分享模型
class WHShareMiniProgramModel: NSObject{
    /// 标题
    public var title: String?
    /// 内容
    public var content: String?
    /// 图片URL
    public var picUrl: String?
    /// 小程序userName
    public var userName: String?
    /// 页面路径
    public var path: String?
    /// 小程序类型
    public var miniProgramType: Int = 0
    
    static func getMiniModelWith(content:[String:Any]) -> WHShareMiniProgramModel{
        let model = WHShareMiniProgramModel()
        model.title = content["tilte"] as? String
        model.content = content["content"] as? String
        model.path = content["applet_path"] as? String
        model.picUrl = content["pic"] as? String
        model.userName = content["applet_username"] as? String
        return model
    }
}

/// 图片分享模型
class WHSharePicModel: NSObject{
    /// 标题
    public var title: String?
    /// 描述
    public var desc: String?
    /// 二维码
    public var qrcode: String?
    /// 图片URL
    public var content: String?
    /// 宽度
    public var width: Int = 0
    /// 高度
    public var height: Int = 0
}

/// H5分享模型
class WHShareH5Model: NSObject {
    /// 标题
    public var title: String?
    /// 副标题
    public var subTitle: String?
    /// 内容
    public var content: String?
    /// 图片URL
    public var picUrl: String?
    /// 跳转链接
    public var link: String?
    /// 是否是默认分享
//    public var isDefault: Bool = false
     
    static func getH5ModelWith(content:[String:Any]) -> WHShareH5Model{
        let model = WHShareH5Model()
        model.title = content["title"] as? String
        model.subTitle = content["subTitle"] as? String
        model.content = content["content"] as? String
        model.link = content["url"] as? String
        model.picUrl = content["pic"] as? String
        return model
    }
}
