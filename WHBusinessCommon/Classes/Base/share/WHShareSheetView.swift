//
//  WHShareSheetView.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/10/8.
//
import WHBaseLibrary
import Foundation
import MTSSOShareKit
public class WHShareSheetView: WHBaseSheetView{
    var pageType:WHSharePageType = .publish
    var fromPageType:WHShareFromPageType = .normalDetailPage
    
    private let countOfLine: CGFloat = 5
    private var content:[String:Any] = [:]
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = WHLocalizedString("分享给")
        label.textColor = WHFigmaColor.white
        label.font = UIFont.systemFont(ofSize: 20)
        label.textAlignment = .center
        return label
    } ()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        layout.itemSize = CGSize(width: 66, height: 69)
        var leading = floor((WH_SCREEN_WIDTH - 32 - 66 * countOfLine) / (countOfLine - 1))
        if leading < 0 {
            leading = 0
        }
        layout.minimumLineSpacing = leading
        layout.minimumInteritemSpacing = 0
        layout.scrollDirection = .horizontal
        let view = UICollectionView(frame: .zero,
                                    collectionViewLayout: layout)
        view.backgroundColor = .clear
        view.delegate = self
        view.dataSource = self
        view.register(WHShareSheetViewCell.self,
                      forCellWithReuseIdentifier: "WHShareSheetViewCell")
        return view
    } ()
    
    private lazy var toolsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        layout.itemSize = CGSize(width: 66, height: 69)
        var leading = floor((WH_SCREEN_WIDTH - 32 - 66 * countOfLine) / (countOfLine - 1))
        if leading < 0 {
            leading = 0
        }
        layout.minimumLineSpacing = leading
        layout.minimumInteritemSpacing = 0
        layout.scrollDirection = .horizontal
        let view = UICollectionView(frame: .zero,
                                    collectionViewLayout: layout)
        view.backgroundColor = .clear
        view.delegate = self
        view.dataSource = self
        view.register(WHShareSheetViewCell.self,
                      forCellWithReuseIdentifier: "WHShareSheetViewCell")
        return view
    } ()
    
    lazy var bgImageView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_share_close"), for: .normal)
        btn.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        return btn
    } ()
    
    private lazy var coreImage: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    private var dataSource: [WHShareSheetViewItemType] = []
    private var toolsDataSource: [WHShareSheetToolsItem] = []
    private var sheetHeight: CGFloat = 0
    
    var toolsSelectBlock: ((WHShareSheetToolsItem) -> ())?
    var selectedBlock: ((WHShareSheetViewItemType, WHShareContentType, UIImage?) -> Void)?
    var clickBlick: (()->Void)?
    
    init(content:[String:Any],
         tools: [WHShareSheetToolsItem] = []) {
        super.init(frame: .zero)
        self.content = content
        self.toolsDataSource = tools
        setupSubviews()
        if let uiType = content["ui_type"] as? Int, uiType == 0,let imagePath = content["pic"] as? String {
            setBGImage(imagePath: imagePath,taskCategory: (content["task_category"] as? String ?? ""))
            pageType = .publish
        } else {
            pageType = .works
        }
        if let fromType = content["from_page_type"] as? Int {
            fromPageType = WHShareFromPageType(rawValue: fromType) ?? .normalDetailPage
        }

        resetDate()
    }
    
    func resetDate(){
        if (MTSSOWeChat.isAppInstalled()){
            dataSource.append(.wechat)
            dataSource.append(.timeline)
        }
        if (MTSSOXiaohongshu.isAppInstalled()) {
            dataSource.append(.redBook)

        }
        if (MTSSOTencent.isAppInstalled()) {
            dataSource.append( .qq)
        }
        dataSource.append(.weibo)
//        if !WHEditionKit.isChinaMainLand{
//            dataSource.append(WHShareSheetViewItem(with: .facebook))
//        }
        collectionView.reloadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        sheetHeight = 169 + WH_SCREEN_BOTTOM_SPACE
        
        self.backgroundColor = WHFigmaColor.backgroundBottomTabBar
        self.addSubview(titleLabel)
        self.addSubview(collectionView)
        self.addSubview(cancelBtn)
        titleLabel.snp.makeConstraints { (make) in
            make.left.equalTo(16)
            make.top.equalTo(22)
            make.height.equalTo(28)
        }
        collectionView.snp.makeConstraints { (make) in
            make.top.equalTo(titleLabel.snp.bottom).offset(28)
            make.left.right.equalToSuperview()
            make.height.equalTo(69)
        }
        cancelBtn.snp.makeConstraints { (make) in
            make.right.equalTo(-16)
            make.top.equalTo(12)
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
        
        if toolsDataSource.count > 0 {
            sheetHeight += 16 + 69
            self.addSubview(toolsCollectionView)
            toolsCollectionView.snp.makeConstraints { make in
                make.top.equalTo(collectionView.snp.bottom).offset(16)
                make.left.right.height.equalTo(collectionView)
            }
        }
        
        self.clipCorner(corners: [.topLeft, .topRight],
                        size: CGSize(width: WH_SCREEN_WIDTH, height: height()),
                        radius: 16)
    }
    
    func setBGImage(imagePath:String,taskCategory:String=""){
        let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.dark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame.size = CGSize(width: WH_SCREEN_WIDTH, height:WH_SCREEN_HEIGHT)
        bgView.addSubview(blurView)
        let bottomView = UIView()
        bgView.addSubview(bottomView)
        let topleading:CGFloat = (WH_SCREEN_HEIGHT - height()  - 430)/2
        bottomView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 267, height: 430))
            make.top.equalTo(topleading)
        }
        bottomView.addSubview(bgImageView)
        bgImageView.backgroundColor = UIColor.black
        bgImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        bottomView.layer.cornerRadius = 16
        bottomView.clipsToBounds = true
        coreImage.sd_setImage(with: URL(string: imagePath))
        bgImageView.addSubview(coreImage)
        coreImage.contentMode = .scaleAspectFit
        coreImage.layer.cornerRadius = 12
        coreImage.clipsToBounds = true
        coreImage.snp.makeConstraints { make in
            make.top.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(302)
        }
        if taskCategory == "magicsr" { //如果任务类型是magicsr，则添加个超清标签
            let tagImageView = UIImageView()
            coreImage.addSubview(tagImageView)
            tagImageView.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(8.0)
                make.bottom.equalToSuperview().offset(-8.0)
                make.size.equalTo(CGSizeMake(61, 20))
            }
            tagImageView.image = UIImage(named: "wh_common_tag_icon_hd_image_normal")
        }
        let topIcon = UIImageView(image: UIImage(cm_named: "share_top_logo"))
        bgImageView.addSubview(topIcon)
        topIcon.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(coreImage.snp.bottom).offset(16)
            make.size.equalTo(CGSize(width: 64, height: 24))
        }
        
        let label = UILabel()
        label.font = UIFont.pingFangSCFont(ofSize: 13,weight: .medium)
        label.text = "欢迎应用商店搜索WHEE\n体验更多功能"
        label.numberOfLines = 2
        label.textColor = WHFigmaColor.white
        bgImageView.addSubview(label)
        label.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(topIcon.snp.bottom).offset(12)
            make.width.equalTo(217)
        }
        let bg = UIImageView(image: UIImage(cm_named: "share_logo"))
        bgImageView.addSubview(bg)
        bg.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 117, height: 117))
            make.right.equalTo(35)
            make.bottom.equalTo(35)
        }
    }
    
    @objc
    private func cancelAction() {
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheePopupClick,params: [.clickType:"close_click",.whsource:self.fromPageType.expoFromType()])
        hide()
        closeBlock?()
    }
    
    public override func height() -> CGFloat {
        return sheetHeight
    }
    
    
    public override func show(in view: UIView? = UIApplication.wh_currentViewController?.view) {
        WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"share", "source": self.fromPageType.expoFromType()])
        let window = UIApplication.shared.keyWindow
        window?.addSubview(self)
    }
    
}


extension WHShareSheetView: UICollectionViewDelegate,
UICollectionViewDataSource {
    // MARK: - UICollectionViewDataSource
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == toolsCollectionView {
            return toolsDataSource.count
        }
        return dataSource.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "WHShareSheetViewCell",
                                                      for: indexPath) as! WHShareSheetViewCell
        if collectionView == toolsCollectionView {
            cell.icon.image = UIImage(cm_named: toolsDataSource[indexPath.row].imageName())
            cell.titleLabel.text = toolsDataSource[indexPath.row].title()
        } else {
            cell.icon.image = UIImage(cm_named: dataSource[indexPath.row].imageName())
            cell.titleLabel.text = dataSource[indexPath.row].title()
        }
        return cell
    }
    
    // MARK: - UICollectionViewDelegate
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == toolsCollectionView {
            toolsSelectBlock?(toolsDataSource[indexPath.row])
            return
        }
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheePopupClick,params: [.clickType:self.dataSource[indexPath.row].clickType(),.whsource:self.fromPageType.expoFromType()])
        if !(isCanShare(type: self.dataSource[indexPath.row])){
            bgView.showToast(title: "您安装的\(self.dataSource[indexPath.row].APPName())版本过低或尚未安装～")
            return
        }
        switch pageType {
        case .works:
            if self.dataSource[indexPath.row] == .wechat{
                if let path =  self.content["applet_path"] as? String {
                    self.selectedBlock?(self.dataSource[indexPath.row], .miniApp, nil)
                } else if let link =  self.content["url"] as? String, link.count > 0 {
                    self.selectedBlock?(self.dataSource[indexPath.row], .H5, nil)
                } else {
//                    if let imagePath = content["pic"] as? String {
//                        coreImage.sd_setImage(with: URL(string: imagePath)) { image, error, type, url in
//                            if (image != nil) {
//                                self.selectedBlock?(self.dataSource[indexPath.row], .image, self.bgImageView.wh_snapshotImage())
//                            } else {
//                                self.bgView.showToast(title: "图片下载失败，请稍后重试")
//                            }
//                        }
//                        return
//                    }
                    self.selectedBlock?(self.dataSource[indexPath.row], .image, nil)
                }
            } else if self.dataSource[indexPath.row] == .redBook{
                if WHAccountShareManager.isLogin() {
                    self.selectedBlock?(self.dataSource[indexPath.row], .images, nil)
                } else {
                    WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                        self?.selectedBlock?(.redBook, .images, nil)
                    } failure: {
                    }
                }
                
            } else{
                self.selectedBlock?(self.dataSource[indexPath.row], .H5, nil)
            }
        case .publish:
            if let imagePath = content["pic"] as? String {
                coreImage.sd_setImage(with: URL(string: imagePath)) { image, error, type, url in
                    if (image != nil) {
                        self.selectedBlock?(self.dataSource[indexPath.row], .image, self.bgImageView.wh_snapshotImage())
                    } else {
                        self.bgView.showToast(title: "图片下载失败，请稍后重试")
                    }
                }
                return
            }
            self.selectedBlock?(self.dataSource[indexPath.row], .image, nil)
        }
    }
    
    func isCanShare(type:WHShareSheetViewItemType) -> Bool {
        switch type {
        case .qq:
            return MTSSOTencentQZone.isAppInstalled()
        case .timeline:
            return MTSSOWeChat.isAppInstalled()
        case .wechat:
            return MTSSOWeChat.isAppInstalled()
        case .redBook:
            return MTSSOXiaohongshu.isAppInstalled()
        case .weibo:
            return self.getWheekk() == true ? MTSSOWeibo.isAppInstalled() : true
        default:
            return true
        }
    }
    
    func getWheekk() -> Bool {
        var isKK = false
        if let iosExamined:String = WHConfigCenterManager.config(for: "switch.whee_kk.switch") {
            if iosExamined == "1" {
                isKK = true
            }
        }
        return isKK
    }
}

@objc
public enum WHShareSheetToolsItem: Int {
    
    case link
    
    func title() -> String {
        switch self{
        case .link:
            return WHLocalizedString("复制链接")
        }
    }
    
    func imageName() -> String {
        switch self {
        case .link:
            return "wh_share_link_icon"
        }
    }
    
}

enum WHShareSheetViewItemType {
    case wechat
    case timeline
    case weibo
    case qq
    case redBook
    case facebook
    
    func clickType() -> String{
        switch self{
        case .weibo:
            return "weibo"
        case .wechat:
            return "wechat_friend"
        case .timeline:
            return "moments"
        case .redBook:
            return "red"
        case .qq:
            return "qq_friend"
        case .facebook:
            return "facebook"
        }
    }
    
    func title() -> String {
        switch self{
        case .weibo:
            return WHLocalizedString("微博")
        case .wechat:
            return WHLocalizedString("微信好友")
        case .timeline:
            return WHLocalizedString("朋友圈")
        case .redBook:
            return WHLocalizedString("小红书")
        case .qq:
            return WHLocalizedString("QQ好友")
        case .facebook:
            return WHLocalizedString("facebook")
        }
    }
    
    func APPName() -> String {
        switch self{
        case .weibo:
            return WHLocalizedString("微博")
        case .wechat:
            return WHLocalizedString("微信")
        case .timeline:
            return WHLocalizedString("微信")
        case .redBook:
            return WHLocalizedString("小红书")
        case .qq:
            return WHLocalizedString("QQ")
        case .facebook:
            return WHLocalizedString("facebook")
        }
    }
    
    func imageName() -> String {
        switch self {
        case .wechat:
            return "wh_share_wechat"
        case .timeline:
            return "wh_share_timeline"
        case .weibo:
            return "wh_share_weibo"
        case .qq:
            return "wh_share_qq"
        case .redBook:
            return "wh_share_redbook"
        case .facebook:
            return "wh_share_facebook"
        }
    }
    
    func channel() -> String {
        switch self {
        case .wechat:
            return "Weixin"
        case .timeline:
            return "WeixinMoments"
        case .weibo:
            return "Weibo"
        case .qq:
            return "QQ"
        case .redBook:
            return "XiaoHongShu"
        case .facebook:
            return "wh_share_facebook"
        }
    }
}

enum WHShareContentType {
    case H5
    case image
    case miniApp
    case images
}

enum WHSharePageType: Int{
    case works = 1 ///风格模型/作品分享
    case publish = 0 ///发布页
}

enum WHShareFromPageType: Int{
    case gcResultPage = 0 ///生成结果页
    case normalDetailPage = 1 ///作品详情页
    case modelDetailPage = 2 ///模型详情页
    /// 任务中心
    case taskCenterPage = 3
    
    func expoFromType() -> String{
        switch self{
        case .gcResultPage:
            return "results_page"
        case .normalDetailPage:
            return "work_detail_page"
        case .modelDetailPage:
            return "model_detail_page"
        case .taskCenterPage:
            return "task_center_invite"
        }
    }
}


class WHShareSheetViewCell: UICollectionViewCell {
    
    lazy var icon: UIImageView = {
        let view = UIImageView()
        return view
    } ()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(rgb: 0x7A7E85)
        label.font = UIFont.pingFangSCFont(ofSize: 11)
        label.textAlignment = .center
        return label
    } ()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(icon)
        contentView.addSubview(titleLabel)
        icon.frame = CGRect(x: 9, y: 0, width: 48, height: 48)
        titleLabel.snp.makeConstraints { (make) in
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
