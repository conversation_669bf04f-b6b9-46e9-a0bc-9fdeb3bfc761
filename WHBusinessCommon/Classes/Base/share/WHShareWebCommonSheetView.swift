//
//  WHShareWebCommonSheetView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/1/22.
//

import Foundation
import WHBaseLibrary
import MTSSOShareKit
import MTWebKit

public class WHShareWebCommonSheetView: WHBaseSheetView{
//    var pageType:WHSharePageType = .publish
//    var fromPageType:WHShareFromPageType = .normalDetailPage
    
    private let countOfLine: CGFloat = 5
    private var shareInfo:MTWebViewJSShareInfo?
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = WHLocalizedString("分享给")
        label.textColor = WHFigmaColor.white
        label.font = UIFont.systemFont(ofSize: 20)
        label.textAlignment = .center
        return label
    } ()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        layout.itemSize = CGSize(width: 66, height: 69)
        var leading = floor((WH_SCREEN_WIDTH - 32 - 66 * countOfLine) / (countOfLine - 1))
        if leading < 0 {
            leading = 0
        }
        layout.minimumLineSpacing = leading
        layout.minimumInteritemSpacing = 0
        layout.scrollDirection = .horizontal
        let view = UICollectionView(frame: .zero,
                                    collectionViewLayout: layout)
        view.backgroundColor = .clear
        view.delegate = self
        view.dataSource = self
        view.register(WHShareSheetViewCell.self,
                      forCellWithReuseIdentifier: "WHShareSheetViewCell")
        return view
    } ()
    
    lazy var bgImageView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_share_close"), for: .normal)
        btn.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        return btn
    } ()
    
    private lazy var coreImage: UIImageView = {
        let imageView = UIImageView()
        return imageView
    }()
    
    private var dataSource: [WHShareSheetViewItemType] = []
    
    var selectedBlock: ((WHShareSheetViewItemType, WHShareContentType, UIImage?) -> Void)?
    var clickBlick: (()->Void)?
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .white
        self.clipCorner(corners: [.topLeft, .topRight],
                        size: CGSize(width: WH_SCREEN_WIDTH, height: height()),
                        radius: 16)
        setupSubviews()
    }
    
    convenience init(shareInfo:MTWebViewJSShareInfo){
        self.init(frame: .zero)
        self.shareInfo = shareInfo
        if shareInfo.previewEntry == true,shareInfo.images.count > 0 {
            setBGImage(imagePath: (shareInfo.images[0] as? String) ?? "")
        }
        resetDate()
    }
    
    func resetDate(){
        if (MTSSOWeChat.isAppInstalled()){
            dataSource.append(.wechat)
            dataSource.append(.timeline)
        }
        if (MTSSOXiaohongshu.isAppInstalled()) {
            dataSource.append(.redBook)

        }
        if (MTSSOTencent.isAppInstalled()) {
            dataSource.append( .qq)
        }
        dataSource.append(.weibo)
//        if !WHEditionKit.isChinaMainLand{
//            dataSource.append(WHShareSheetViewItem(with: .facebook))
//        }
        collectionView.reloadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        self.backgroundColor = UIColor.black
        self.addSubview(titleLabel)
        self.addSubview(collectionView)
        self.addSubview(cancelBtn)
        titleLabel.snp.makeConstraints { (make) in
            make.left.equalTo(16)
            make.top.equalTo(16)
            make.height.equalTo(28)
        }
        collectionView.snp.makeConstraints { (make) in
            make.top.equalTo(titleLabel.snp.bottom).offset(40)
            make.left.right.equalToSuperview()
            make.height.equalTo(69)
        }
        cancelBtn.snp.makeConstraints { (make) in
            make.right.equalTo(-16)
            make.top.equalTo(16)
            make.size.equalTo(CGSize(width: 32, height: 32))
        }
    }
    
    func setBGImage(imagePath:String){
        let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.dark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame.size = CGSize(width: WH_SCREEN_WIDTH, height:WH_SCREEN_HEIGHT)
        bgView.addSubview(blurView)
        let bottomView = UIView()
        bgView.addSubview(bottomView)
        let topleading:CGFloat = (WH_SCREEN_HEIGHT - height()  - 430)/2
        bottomView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 267, height: 430))
            make.top.equalTo(topleading)
        }
        coreImage.sd_setImage(with: URL(string: imagePath))
        bottomView.addSubview(coreImage)
        coreImage.contentMode = .scaleAspectFit
        coreImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    @objc
    private func cancelAction() {
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheePopupClick,params: [.clickType:"close_click",.whsource:"wh_web"])
        hide()
        closeBlock?()
    }
    
    public override func height() -> CGFloat {
        return 185 + WH_SCREEN_BOTTOM_SPACE
    }
    
    
    public override func show(in view: UIView? = UIApplication.wh_currentViewController?.view) {
        WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"share", "source": "wh_web"])
        let window = UIApplication.shared.keyWindow
        window?.addSubview(self)
    }
    
}

extension WHShareWebCommonSheetView: UICollectionViewDelegate,
UICollectionViewDataSource {
    // MARK: - UICollectionViewDataSource
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return dataSource.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "WHShareSheetViewCell",
                                                      for: indexPath) as! WHShareSheetViewCell
        cell.icon.image = UIImage(cm_named: dataSource[indexPath.row].imageName())
        cell.titleLabel.text = dataSource[indexPath.row].title()
        return cell
    }
    
    // MARK: - UICollectionViewDelegate
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        var whSource = "whee_h5"
        if let extraBizData = self.shareInfo?.extraBizData as? String, extraBizData.count > 0 {
            let data = extraBizData.data(using: String.Encoding.utf8)
            if let dict = try? JSONSerialization.jsonObject(with: data ?? Data(), options: JSONSerialization.ReadingOptions.mutableContainers) as? [String : Any] {
                if let page_source = dict["page_source"] as? String, page_source.count > 0{
                    whSource = page_source
                }
            }
        }
        WHAnalyticsManager.trackEvent(WHEEAnalyticsEventId.wheePopupClick,params: [.clickType:self.dataSource[indexPath.row].clickType(),.whsource:whSource])
        if !(isCanShare(type: self.dataSource[indexPath.row])){
            bgView.showToast(title: "您安装的\(self.dataSource[indexPath.row].APPName())版本过低或尚未安装～")
            return
        }
        if let shareInfo = self.shareInfo, shareInfo.type == .photo { //分享图片
            self.selectedBlock?(self.dataSource[indexPath.row], .image, nil)
        } else if let shareInfo = self.shareInfo { //普通分享，H5
            let content = WHShareViewManager.convertShareInfoToContent(shareInfo: shareInfo)
            if self.dataSource[indexPath.row] == .wechat{
                if let path = content["applet_path"] as? String {
                    self.selectedBlock?(self.dataSource[indexPath.row], .miniApp, nil)
                } else if let link = content["url"] as? String, link.count > 0 {
                    self.selectedBlock?(self.dataSource[indexPath.row], .H5, nil)
                } else {
                    self.selectedBlock?(self.dataSource[indexPath.row], .image, nil)
                }
            } else if self.dataSource[indexPath.row] == .redBook{
                if WHAccountShareManager.isLogin() {
                    self.selectedBlock?(self.dataSource[indexPath.row], .images, nil)
                } else {
                    WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                        self?.selectedBlock?(.redBook, .images, nil)
                    } failure: {
                    }
                }
                
            } else{
                self.selectedBlock?(self.dataSource[indexPath.row], .H5, nil)
            }
        }
        
    }
    
    func isCanShare(type:WHShareSheetViewItemType) -> Bool {
        switch type {
        case .qq:
            return MTSSOTencentQZone.isAppInstalled()
        case .timeline:
            return MTSSOWeChat.isAppInstalled()
        case .wechat:
            return MTSSOWeChat.isAppInstalled()
        case .redBook:
            return MTSSOXiaohongshu.isAppInstalled()
        case .weibo:
            return self.getWheekk() == true ? MTSSOWeibo.isAppInstalled() : true
        default:
            return true
        }
    }
    
    func getWheekk() -> Bool {
        var isKK = false
        if let iosExamined:String = WHConfigCenterManager.config(for: "switch.whee_kk.switch") {
            if iosExamined == "1" {
                isKK = true
            }
        }
        return isKK
    }
}

