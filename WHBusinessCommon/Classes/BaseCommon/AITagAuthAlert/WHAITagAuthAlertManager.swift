//
//  WHPhotoAlbumManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary
import WHBaseLibrary
import YYModel

public let WHAITagAuthAlertShareManager = WHAITagAuthAlertManager.shared

public class WHAITagAuthAlertManager {
    
    public static let shared = WHAITagAuthAlertManager()
    
    //根据需要展示AI标识确认弹窗，回调是否下载有水印的图
    public func showAITagAuthAlertIfNeeded(
        inView: UIView? = nil,
        completion: @escaping (_ isWaterMark: Bool) -> Void
    ) {
        //如果是海外，直接返回不需要水印
        if WHEditionKit.isChinaMainLand == false {
            completion(false)
            return;
        }
        // 安全地获取展示视图
        guard let containerView = inView ?? UIViewController.wh_top().view else {
            completion(true)
            return;
        }
        let isUnNeed = WHUserDefaultsManager.getAIAlertAuthIsUnNeedShow()
        if isUnNeed { //不需要再弹窗的，直接返回结果。
            let isNeedWaterMark = WHUserDefaultsManager.getAIAlertIsNeedWaterMark()
            completion(isNeedWaterMark)
            return;
        }
        //获取是否需要显示“不再提醒”
        let times = WHUserDefaultsManager.getAIAlertShowRemindNeedNum()
        let isShowNoRemind = times > 0 ? false : true
        //获取提示文案
        var tipsText = WHUserDefaultsManager.getAIAlertAuthDescText()
        if tipsText.count <= 0 {
            tipsText = "根据您的请求，我们将为您提供“含AI水印（标识）”和“不含AI水印（标识）”的图片，如您选择使用“不含AI水印”的图片，即代表您同意以下的标识义务和使用责任：您不得利用基于WHEE AI功能AI等新技术新应用制作、发布、传播虚假信息。您在任何场景下使用、发布或传播利用基于WHEE AI功能等新技术新应用制作的内容时，应当符合相关法律法规的要求，并以显著方式予以标识。\n"
        }
        
        // 显示弹窗
        let alert = WHAITagAuthAlertView(desStr: tipsText, isShowNoRemind: isShowNoRemind)
        alert.show(in: containerView)
        alert.alertSureHandler = {isWaterMark,isUnAlert in
            completion(isWaterMark)
            let isNeedUnAlert = isWaterMark ? isUnAlert : true
            WHUserDefaultsManager.setAIAlertAuthIsUnNeedShow(isNeed: isNeedUnAlert)
            WHUserDefaultsManager.setAIAlertIsNeedWaterMark(isNeed: isWaterMark)
            //同步服务端状态
            WHAITagAuthAlertRequest.requestUpdataWaterMarkAlert(isUnRemind: isUnAlert, isWaterMark: isWaterMark) { whResponse in
            }
        }
        //更新"不再提醒"次数
        let newTimes = (times - 1) > 0 ? (times - 1) : 0
        WHUserDefaultsManager.setAIAlertShowRemindNeedNum(num: newTimes)
    }
    
    //获取弹窗配置并更新本地
    public func getAlertConfigIfNeeded() {
        if WHAccountShareManager.isLogin() == false { //未登录不获取
            return;
        }
        WHAITagAuthAlertRequest.requestAlertConfig { whResponse, model in
            guard let model = model else { return }
            //更新本地配置信息
            WHUserDefaultsManager.setAIAlertAuthDescText(text: model.remindText)
            WHUserDefaultsManager.setAIAlertAuthIsUnNeedShow(isNeed: model.isAlert == 2 ? true : false)
            WHUserDefaultsManager.setAIAlertIsNeedWaterMark(isNeed: model.isWatermark == 2 ? false : true)
            WHUserDefaultsManager.setAIAlertShowRemindNeedNum(num: model.remainingNum)
        }
    }
    // 获取当前是否需要展示有水印的效果
    public func getIsShowWaterMark() -> Bool {
        //如果是海外，直接返回不需要水印
        if WHEditionKit.isChinaMainLand == false {
            return false;
        }
        let isUnNeed = WHUserDefaultsManager.getAIAlertAuthIsUnNeedShow()
        if isUnNeed {
            let isNeedWaterMark = WHUserDefaultsManager.getAIAlertIsNeedWaterMark()
            return isNeedWaterMark
        }
        return true
    }
    //根据海内外逻辑返回一个使用的资源地址
    public func getUsePicUrl(watermarkUrl:String,unWaterMarkUrl:String) -> String {
        if WHEditionKit.isChinaMainLand == true {
            return watermarkUrl.count > 0 ? watermarkUrl : unWaterMarkUrl
        } else {
            return unWaterMarkUrl
        }
    }
    
    private init() {
        addObersvers()
    }
    
    private func addObersvers() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusSuccess),
                                               name: NSNotification.Name.WHACCountLoginSuccess,
                                               object: nil)
    }
    
    @objc func loginStatusSuccess(_ notify: Notification) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.getAlertConfigIfNeeded()
        }
    }
}

public class WHAITagAuthAlertRequest: NSObject {
    ///获取弹窗配置
    public static func requestAlertConfig(completion: ((_ whResponse: WHOriginalResponse, _ model: WHAIAlertConfigModel? ) -> Void)?) {
        WHSharedRequest.GET("/common/watermark_alert.json",params: nil) { response in
            let result = response.data() as? [String: Any]
            let model = WHAIAlertConfigModel.yy_model(with: result ?? [:])
            if ((model == nil)) {
                completion?(response,nil)
            } else {
                completion?(response,model)
            }
        }
    }
    //更新弹窗配置
    public static func requestUpdataWaterMarkAlert(isUnRemind: Bool,isWaterMark:Bool, completion: ((_ whResponse: WHOriginalResponse) -> Void)?) {
        var params:[String:Any] = ["is_watermark":(isWaterMark ? 1 : 2)]
        if isUnRemind {
            params["is_remind"] = 2
        }
        WHSharedRequest.POST("/common/watermark_alert.json", params: params) { response in
            completion?(response)
        }
    }
    
}

@objcMembers public class WHAIAlertConfigModel: NSObject, YYModel, NSCoding {
    
    public var remindText: String = "" //弹窗文案
    public var isAlert: Int = 1        //是否弹窗 1-是 2-否
    public var isRemind: Int = 1       //是否显示提醒 1-是 2-否
    public var isWatermark: Int = 1 //是否加水印 1-是 2-否
    public var remainingNum: Int = 0  //剩余不展示再次提醒的次数
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "remindText": "remind_text",
            "isAlert":"is_alert",
            "isRemind": "is_remind",
            "isWatermark":"is_watermark",
            "remainingNum": "remaining_num",
        ]
    }
}

let kWHEEAIAlertAuthIsUnNeedShowKey = "kWHEEAIAlertAuthIsUnNeedShowKey"    // AI标识授权弹窗是否不需展示
let kWHEEAIAlertAuthDescTextKey = "kWHEEAIAlertAuthDescTextKey"    // AI标识授权弹窗文案
let kWHEEAIAlertAuthShowRemindNeedNumKey = "kWHEEAIAlertAuthShowRemindNeedNumKey"    // AI标识授权弹窗显示不再提示，还需要的展示次数
let kWHEEAIAlertAuthIsNeedWaterMarkKey = "kWHEEAIAlertAuthIsNeedWaterMarkKey"    // 是否保存水印，在不需再次展示弹窗时再取此字段

extension WHUserDefaultsManager {
    /// 设置AI标识授权弹窗是否不需展示
    public static func setAIAlertAuthIsUnNeedShow(isNeed: Bool) {
        UserDefaults.standard.setValue(isNeed, forKey: kWHEEAIAlertAuthIsUnNeedShowKey)
        UserDefaults.standard.synchronize()
    }
    /// 获取是否bu需要弹AI标识授权弹窗
    public static func getAIAlertAuthIsUnNeedShow() -> Bool {
        let isNeed = UserDefaults.standard.bool(forKey: kWHEEAIAlertAuthIsUnNeedShowKey)
        return isNeed;
    }
    
    /// 设置AI标识授权弹窗文案
    public static func setAIAlertAuthDescText(text: String) {
        UserDefaults.standard.setValue(text, forKey: kWHEEAIAlertAuthDescTextKey)
        UserDefaults.standard.synchronize()
    }
    /// 获取AI标识授权弹窗文案
    public static func getAIAlertAuthDescText() -> String {
        let text = UserDefaults.standard.string(forKey: kWHEEAIAlertAuthDescTextKey) ?? ""
        return text;
    }
    
    /// 设置是否需要水印
    public static func setAIAlertIsNeedWaterMark(isNeed: Bool) {
        UserDefaults.standard.setValue(isNeed, forKey: kWHEEAIAlertAuthIsNeedWaterMarkKey)
        UserDefaults.standard.synchronize()
    }
    /// 获取是否需要水印
    public static func getAIAlertIsNeedWaterMark() -> Bool {
        let isNeed = UserDefaults.standard.bool(forKey: kWHEEAIAlertAuthIsNeedWaterMarkKey)
        return isNeed;
    }
    
    /// 设置AI标识授权弹窗是否展示再次提醒的剩余次数
    public static func setAIAlertShowRemindNeedNum(num: Int) {
        UserDefaults.standard.setValue(num, forKey: kWHEEAIAlertAuthShowRemindNeedNumKey)
        UserDefaults.standard.synchronize()
    }
    /// 获取是否bu需要弹AI标识授权弹窗
    public static func getAIAlertShowRemindNeedNum() -> Int {
        let num = UserDefaults.standard.integer(forKey: kWHEEAIAlertAuthShowRemindNeedNumKey) ?? 0
        return num;
    }
}
