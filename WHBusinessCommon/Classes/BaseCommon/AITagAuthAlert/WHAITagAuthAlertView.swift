//
//  WHAITagAuthAlertView.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2025/8/30.
//

import Foundation
import WHBaseLibrary

open class WHAITagAuthAlertView: WHCommonBaseAlertView {
    
    public var alertSureHandler: ((_ isNeedWaterMark:Bool,_ isUnAlert:Bool) -> Void)?
    
    private var isUnReAlert:Bool = false  //默认不选中“不再提醒”
    private var isNeedWaterMark:Bool = true //默认选中“包含AI水印”
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor(wh_hexString: "#121212")
        label.font = UIFont.pingFangSCFont(ofSize: 18, weight: .semibold)
        label.numberOfLines = 0
        label.text = ""
        return label
    }()
    
    private lazy var desTextView: UITextView = {
        let view = UITextView()
        view.textAlignment = .left
        view.backgroundColor = .clear
        view.textColor = UIColor(wh_hexString: "#7A7E85")
        view.font = UIFont.systemFont(ofSize: 13)
        view.textContainerInset = UIEdgeInsets(top: 1, left: 0, bottom: 0, right: 0)
        view.isEditable = false
        view.isSelectable = false
        view.isScrollEnabled = true
        return view
    }()
    
    private lazy var sureBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.backgroundColor = UIColor(wh_hexString: "#121212")
        btn.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        btn.titleLabel?.textAlignment = .center
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.setTitle(WHLocalizedString("继续"), for: .normal)
        btn.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        btn.layer.cornerRadius = 20
        btn.layer.cornerCurve = .continuous
        return btn
    }()
    
    private lazy var topBgView: UIImageView = {
        let imgView = UIImageView()
        imgView.image = UIImage(cm_named: "wh_common_alert_top_gradient_bg_image")
        return imgView
    }()
    
    private lazy var gradientView: WHGradientLayerView = {
        let view = WHGradientLayerView()
        view.startPoint = CGPoint(x: 1, y: 0.25)
        view.endPoint = CGPoint(x: 1, y: 1)
        view.locations = [0,0.5,1]
        view.colors = [
            UIColor(red: 1, green: 1, blue: 1, alpha: 0),
            UIColor(red: 1, green: 1, blue: 1, alpha: 0.8),
            UIColor(red: 1, green: 1, blue: 1, alpha: 1)
        ]
        return view
    }()
    
    private lazy var waterMarkSwitchView: UISwitch = {
        let sw = UISwitch()
        sw.onTintColor = WHFigmaColor(wh_hexString: "#3549FF")
        sw.addTarget(self, action: #selector(waterMarkSwitchClickAction), for: .touchUpInside)
        sw.isOn = true
        return sw
    }()
    
    private lazy var unAlertTagImageView: UIImageView = {
        let imgView = UIImageView()
        imgView.image = UIImage(named: "icon_alert_select_normal")
        return imgView
    }()
    
    private lazy var unReAlertView: UIView = {
        let view = UIView()
        
        view.addSubview(unAlertTagImageView)
        unAlertTagImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(CGSize(width: 14, height: 14))
        }
        
        let label = UILabel()
        label.font = UIFont.pingFangSCFont(ofSize: 12)
        label.textColor = UIColor(rgb: 0x7A7E85, alpha: 1)
        label.text = WHLocalizedString("不再提醒")
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.left.equalTo(unAlertTagImageView.snp.right).offset(4.0)
            make.centerY.equalToSuperview()
            make.size.equalTo(CGSizeMake(54, 18))
        }
        
        let button = UIButton()
        view.addSubview(button)
        button.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        button.addTarget(self, action: #selector(unReAlertTagClickAction), for: .touchUpInside)
        
        return view
    }()
    
    var isShowNoRemind:Bool = true
    var desStr:String = ""
    
    public init(desStr: String,isShowNoRemind:Bool) {
        self.isShowNoRemind = isShowNoRemind
        self.desStr = desStr
        super.init(frame: CGRect.zero)
        setupUI()
        alertViewConfig()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        print("WHAITagAuthAlertView deinit")
    }
    
    private func setupUI() {
        containerView.addSubview(topBgView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(desTextView)
        containerView.addSubview(gradientView)
        containerView.addSubview(sureBtn)
        containerView.addSubview(unReAlertView)
      
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(52)
        }
        
        topBgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(57.0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(24)
        }
        
        let grayBgView = UIView()
        grayBgView.backgroundColor = WHFigmaColor(wh_hexString: "#F7F7F8")
        grayBgView.layer.cornerRadius = 16.0
        grayBgView.layer.cornerCurve = .continuous
        containerView.addSubview(grayBgView)
        grayBgView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(24.0)
            make.right.equalToSuperview().offset(-24.0)
            make.top.equalTo(titleLabel.snp.bottom).offset(16.0)
            make.height.equalTo(56.0)
        }
        
        let waterMarkLabel = UILabel()
        waterMarkLabel.textColor = WHFigmaColor(wh_hexString: "#7A7E85")
        waterMarkLabel.font = UIFont.pingFangSCFont(ofSize: 13.0)
        grayBgView.addSubview(waterMarkLabel)
        waterMarkLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12.0)
            make.centerY.equalToSuperview()
            make.height.equalTo(20)
            make.width.equalTo(100)
        }
        waterMarkLabel.text = WHSonicLocalizedString("包含AI水印")
        
        grayBgView.addSubview(waterMarkSwitchView)
        waterMarkSwitchView.snp.makeConstraints { make in
            make.centerY.equalToSuperview().offset(-2.0)
            make.right.equalToSuperview().offset(-16.0)
            make.size.equalTo(CGSizeMake(44, 24))
        }
        
        desTextView.snp.makeConstraints { make in
            make.top.equalTo(grayBgView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(180)
        }
        
        gradientView.snp.makeConstraints { make in
            make.bottom.equalTo(desTextView)
            make.left.equalToSuperview().offset(17)
            make.right.equalToSuperview().offset(-17)
            make.height.equalTo(40)
        }
        
        let btnWidth = (WH_SCREEN_WIDTH - 52 * 2 - 40 * 2)
        sureBtn.snp.makeConstraints { make in
            make.top.equalTo(desTextView.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(btnWidth)
            make.height.equalTo(40)
            
        }
        
        unReAlertView.snp.makeConstraints { make in
            make.top.equalTo(sureBtn.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(72)
            make.height.equalTo(18)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        //添加手势关闭view
        let gesView = UIView()
        self.insertSubview(gesView, belowSubview: containerView)
        gesView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let tap = UITapGestureRecognizer(target: self, action: #selector(closeGesClick))
        gesView.addGestureRecognizer(tap)
    }
    
    open func alertViewConfig(){
        titleLabel.text = WHLocalizedString("文件保存偏好")
        desTextView.text = desStr+"\n"
        self.desTextView.snp.updateConstraints { make in
            make.height.equalTo(0)
        }
        self.sureBtn.snp.updateConstraints { make in
            make.top.equalTo(self.desTextView.snp.bottom)
        }
        self.unReAlertView.snp.updateConstraints { make in
            make.top.equalTo(self.sureBtn.snp.bottom).offset(self.isShowNoRemind ? 16 : 0)
            make.height.equalTo(self.isShowNoRemind ? 18 : 0)
        }
        self.sureBtn.setTitle(WHLocalizedString("继续"), for: .normal)
        self.unReAlertView.isHidden = !self.isShowNoRemind
    }
    
    @objc open func sureBtnAction() {
        alertSureHandler?(isNeedWaterMark,isUnReAlert)
        dismiss()
    }
    @objc open func closeGesClick() {
        dismiss()
    }
    //水印开关按钮点击
    @objc func waterMarkSwitchClickAction(sw:UISwitch) {
        if sw.isOn == false { //不包含水印
            isNeedWaterMark = false
            UIView.animate(withDuration: 0.3) {
                self.desTextView.snp.updateConstraints { make in
                    make.height.equalTo(180)
                }
                self.sureBtn.snp.updateConstraints { make in
                    make.top.equalTo(self.desTextView.snp.bottom).offset(16)
                }
                self.unReAlertView.snp.updateConstraints { make in
                    make.top.equalTo(self.sureBtn.snp.bottom)
                    make.height.equalTo(0)
                }
                self.containerView.layoutIfNeeded()
                self.sureBtn.setTitle(WHLocalizedString("同意并继续"), for: .normal)
                self.unReAlertView.isHidden = true
            }
        } else {
            isNeedWaterMark = true
            UIView.animate(withDuration: 0.3) {
                self.desTextView.snp.updateConstraints { make in
                    make.height.equalTo(0)
                }
                self.sureBtn.snp.updateConstraints { make in
                    make.top.equalTo(self.desTextView.snp.bottom)
                }
                self.unReAlertView.snp.updateConstraints { make in
                    make.top.equalTo(self.sureBtn.snp.bottom).offset(self.isShowNoRemind ? 16 : 0)
                    make.height.equalTo(self.isShowNoRemind ? 18 : 0)
                }
                self.containerView.layoutIfNeeded()
                self.sureBtn.setTitle(WHLocalizedString("继续"), for: .normal)
            } completion: { completion in
                self.unReAlertView.isHidden = !self.isShowNoRemind
            }
            
        
        }
    }
    //不再提醒按钮点击
    @objc func unReAlertTagClickAction() {
        isUnReAlert = !isUnReAlert
        if isUnReAlert {
            unAlertTagImageView.image = UIImage(named: "icon_alert_select")
        } else {
            unAlertTagImageView.image = UIImage(named: "icon_alert_select_normal")
        }
    }
    
}
