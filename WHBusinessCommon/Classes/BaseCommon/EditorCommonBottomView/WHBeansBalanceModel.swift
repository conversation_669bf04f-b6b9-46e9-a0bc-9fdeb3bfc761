//
//  WHBeansBalanceModel.swift
//  MeituWhee
//
//  Created by <PERSON> on 2023/12/12.
//

import Foundation
import YYModel
import WHBaseLibrary

@objcMembers public class WHBeansBalanceModel: NSObject, YYModel, NSCoding {
    
    public var amountList: [WHBeansBalanceAmountListModel]?
    
    public var availableAmount: String?
    public var tips: String?
    public var vipRightInfo: String?
    public var detailTitle: String?
    public var detailDesc: String?
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["amountList": WHBeansBalanceAmountListModel.classForCoder(),
                ]
    }
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "availableAmount": "available_amount",
            "tips":"tips",
            "vipRightInfo": "vip_right_info",
            "detailTitle":"detail_title",
            "detailDesc": "detail_desc",
            "amountList":"amount_list",
        ]
    }
    
    func cellTotalHeight() -> CGFloat {
        var height: CGFloat = 0
        if let list = amountList {
            for _ in list {
                height += 40
            }
        }
        height =  height > 0 ? height + 20 : height
        return height > 240 ? 240 + 20 : height
    }
}

@objcMembers public class WHBeansBalanceAmountListModel: NSObject, YYModel, NSCoding {
    
    public var type: String?
    public var num: String?
    public var expireStr: String?
    public var desc: String?
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "type": "type",
            "num":"num",
            "expireStr": "expire_str",
            "desc": "desc",
        ]
    }
}
