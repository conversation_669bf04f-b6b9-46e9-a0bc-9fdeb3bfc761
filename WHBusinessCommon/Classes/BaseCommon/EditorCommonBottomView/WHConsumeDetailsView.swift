//
//  WHConsumeDetailsView.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/12/13.
//  明细页

import UIKit
import WHBaseLibrary
import WHBusinessCommon

public enum WHConsumeDetailsViewType {
    case normal
    case videoHD
    
    var mixFreeExtraString: String {
        switch self {
        case .videoHD:
            return "分钟"
        default:
            return "张"
        }
    }

}

open class WHConsumeDetailsView: WHCommonBaseHalfView {
    
    var viewType: WHConsumeDetailsViewType = .normal
    
    lazy var centerView: UIView = {
        let make = UIView()
        make.backgroundColor = WHFigmaColor.init(rgb: 0x292A2B)
        make.layer.cornerRadius = 2
        return make
    }()
    
    lazy var borderView: UIView = {
        let view = UIView()
        view.layer.borderColor = UIColor(wh_hexString: "#222326").cgColor
        view.layer.borderWidth = 1.0
        view.layer.cornerCurve = .continuous
        view.layer.cornerRadius = 24.0
        view.layer.masksToBounds = true
        return view
    }()
    
    lazy var titleLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        make.text = WHLocalizedString("明细")
        return make
    }()
    
    lazy var mixFreeLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 13.0)
        make.textColor = UIColor(wh_hexString: "#7A7E85")
        make.textAlignment = .right
        return make
    }()
    
    lazy var mixFreeDouImageView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(cm_named: "mine_meidou")
        return view
    }()
    
    lazy var mixFreeDouLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 14.0)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        return make
    }()
    
    lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero,style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(wh_hexString: "#1C1C1C")
        tableView.separatorStyle = .none
        tableView.bounces = false
        tableView.layer.cornerRadius = 16
        tableView.contentInset = UIEdgeInsets(top: 8, left: 0, bottom: 8, right: 0)
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "UITableViewCell")
        tableView.register(WHMeidouDetailsCell.self, forCellReuseIdentifier: "WHMeidouDetailsCell")
        return tableView
    }()
    
    lazy var kownButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(WHLocalizedString("我知道了"), for: .normal)
        button.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
        button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17,weight: .semibold)
        button.backgroundColor = UIColor(wh_hexString: "#222326")
        button.layer.cornerRadius = 16
        button.layer.masksToBounds = true
        button.layer.cornerCurve = .continuous
        button.clipsToBounds = true
        button.addTarget(self, action: #selector(kownButtonClick), for: .touchUpInside)
        return button
    }()
    
    lazy var meidouButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(cm_named: "icon_mark_meidouBi"), for: .normal)
        button.setTitle("8273", for: .normal)
        button.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
        button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 11,weight: .semibold)
        button.backgroundColor = .clear
        button.layoutButton(style: .Top, imageTitleSpace: 4)
        button.addTarget(self, action: #selector(meidouButtonClick), for: .touchUpInside)
        button.titleLabel?.adjustsFontSizeToFitWidth = true
        return button
    }()
    
    private let generator = UIImpactFeedbackGenerator(style: .medium)

    var priceModel: WHGcPriceModel? //当前Gc消费明细
    
    var meidouModel: WHBeansBalanceModel? //当前美豆余额
    
    public init(priceModel: WHGcPriceModel, meidouModel: WHBeansBalanceModel?,viewType:WHConsumeDetailsViewType = .normal) {
        self.viewType = viewType
        self.priceModel = priceModel
        self.meidouModel = meidouModel
        super.init(frame: .zero)
        self.isPan = true
        setup()
        config()
        generator.prepare()
    }
    
    required public init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    func setup() {
        containerView.addSubview(centerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(mixFreeDouLabel)
        containerView.addSubview(mixFreeDouImageView)
        containerView.addSubview(mixFreeLabel)
        containerView.addSubview(tableView)
        let bottomBgView = UIView()
        containerView.addSubview(bottomBgView)
        bottomBgView.addSubview(borderView)
        
        containerView.addSubview(kownButton)
        containerView.addSubview(meidouButton)
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
        }
        
        centerView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 31, height: 5))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(centerView.snp.bottom).offset(3)
            make.left.right.equalToSuperview().inset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(32)
        }
        
        mixFreeDouLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-16.0)
            make.height.equalTo(20.0)
            make.width.equalTo(10.0)
        }
        
        mixFreeDouImageView.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalTo(mixFreeDouLabel.snp.left)
            make.size.equalTo(CGSizeMake(16.0, 16.0))
        }
        
        mixFreeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalTo(mixFreeDouImageView.snp.left).offset(-4.0)
            make.size.equalTo(CGSizeMake(250.0, 18.0))
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(self.priceModel?.cellTotalHeight() ?? 56)
        }
        
        bottomBgView.layer.cornerRadius = 24.0
        bottomBgView.layer.masksToBounds = true
        bottomBgView.layer.cornerCurve = .continuous
        bottomBgView.backgroundColor = UIColor(wh_hexString: "#17171A")
        
        bottomBgView.snp.makeConstraints { make in
            make.top.equalTo(tableView.snp.bottom).offset(24 + 4)
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        borderView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(-2.0)
            make.right.equalToSuperview().offset(2.0)
            make.bottom.equalToSuperview().offset(50.0)
        }
        
        kownButton.snp.makeConstraints { make in
            make.top.equalTo(tableView.snp.bottom).offset(24 + 16)
            make.left.equalToSuperview().offset(64)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 12)
        }
        
        meidouButton.snp.makeConstraints { make in
            make.top.equalTo(kownButton.snp.top).offset(2)
            make.left.equalToSuperview().offset(14)
            make.height.equalTo(43)
            make.width.equalTo(40)
        }
        meidouButton.layoutButton(style: .Top, imageTitleSpace: 4)
    }
    
    public func config() {
        if let model = self.meidouModel {
            meidouButton.setTitle(model.availableAmount, for: .normal)
        } else {
//            WHInpaindingRequest.requestBalanceAmount {[weak self] whResponse, model in
//                if whResponse.isSuccess() {
//                    self?.meidouModel = model
//                    self?.meidouButton.setTitle(model?.availableAmount, for: .normal)
//                }
//            }
        }
        meidouButton.layoutButton(style: .Top, imageTitleSpace: 4)
        //处理混合结算
        if let priceModel = self.priceModel {
            if (Int(priceModel.useFreeNum ?? "0") ?? 0)  > 0,(Int(priceModel.amount ?? "0") ?? 0) > 0 { //即消耗了限免又消耗了美豆
                let meidouWidth = priceModel.amount?.getStringWidth(20.0, font: UIFont.systemFont(ofSize: 14.0)) ?? 10.0
                self.mixFreeDouLabel.text = priceModel.amount ?? ""
                self.mixFreeDouLabel.snp.updateConstraints { make in
                    make.width.equalTo(meidouWidth)
                }
                self.mixFreeDouImageView.isHidden = false
                self.mixFreeLabel.text = "限免剩余 \(priceModel.totalFreeNum) \(viewType.mixFreeExtraString)，还需"
            } else {
                self.mixFreeDouImageView.isHidden = true
            }
            
        }
    }
    
    @objc func kownButtonClick() {
        tapicEngineOccurred(.heavy) //震动
        dismiss()
    }
    
    @objc func meidouButtonClick() {
        if let model = self.meidouModel {
            let detaiView = WHMeidouDetailsView(meidouModel: model)
            detaiView.show()
        }
        generator.impactOccurred()
    }
}

extension WHConsumeDetailsView: UITableViewDelegate, UITableViewDataSource {
    
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.priceModel?.priceDetailList?.count ?? 0
    }
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 40
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let model = self.priceModel?.priceDetailList?[indexPath.row]
        let cell = tableView.dequeueReusableCell(withIdentifier: "WHMeidouDetailsCell") as? WHMeidouDetailsCell
        cell?.config(model: model ?? WHGcPriceDetailModel(),
                     isVip: self.priceModel?.isVip ?? false,
                     isUseFree: (Int(self.priceModel?.useFreeNum ?? "0") ?? 0) > 0 ? true : false)
        return cell ?? UITableViewCell()
    }
}

public class WHMeidouDetailsCell: UITableViewCell {
    
    lazy var titleLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        make.textAlignment = .left
        make.isHidden = true
        return make
    }()
    
    lazy var desLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        make.textColor = UIColor(wh_hexString: "#7A7E85")
        make.textAlignment = .left
        return make
    }()
    
    lazy var meidouImageView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(cm_named: "mine_meidou")
        return view
    }()
    
    lazy var meidouLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 14)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        make.text = ""
        return make
    }()
    
    lazy var originPriceLabel: UILabel = {
        let make = UILabel.init()
        make.font = UIFont.systemFont(ofSize: 13)
        make.textColor = UIColor(wh_hexString: "#43464D")
        make.text = ""
        make.isHidden = true
        return make
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style,reuseIdentifier:reuseIdentifier)
        self.selectionStyle = .none
        self.backgroundColor = UIColor(wh_hexString: "#1C1C1C")
        creatSubviews()
    }
        
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func creatSubviews(){
        contentView.addSubview(titleLabel)
        contentView.addSubview(desLabel)
        contentView.addSubview(meidouLabel)
        contentView.addSubview(meidouImageView)
        contentView.addSubview(originPriceLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(18)
        }
        
        desLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(18)
        }
        
        meidouLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(20.0)
            make.width.equalTo(10.0)
        }
        
        meidouImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(meidouLabel.snp.left)
            make.size.equalTo(CGSizeMake(16.0, 16.0))
        }
        
        originPriceLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(meidouImageView.snp.left).offset(-8)
            make.height.equalTo(18)
        }
    }
    
    public func config(model: WHGcPriceDetailModel, isVip: Bool = false, isUseFree: Bool = false) {
        desLabel.text = "\(model.itemName ?? "")\(model.itemCount ?? "")"
        
        let meidouWidth = model.priceNowValue.getStringWidth(20.0, font: UIFont.systemFont(ofSize: 14.0)) ?? 10.0
        meidouLabel.text = model.priceNowValue
        self.meidouLabel.snp.updateConstraints { make in
            make.width.equalTo(meidouWidth)
        }
        
        if isVip == true || isUseFree == true {
            originPriceLabel.isHidden = (model.priceNow == model.priceOrigin ? true : false)
        } else {
            originPriceLabel.isHidden = true
        }
        if let priceString = model.priceOrigin, priceString.count > 0 {
            originPriceLabel.attributedText = NSAttributedString(string: priceString, attributes: [
                .strikethroughStyle: NSUnderlineStyle.single.rawValue,
                .foregroundColor: UIColor(wh_hexString: "#43464D")
            ])
        }
    }
    
    public func configMeidou(model: WHBeansBalanceAmountListModel, isVip: Bool = false) {
        let mdText = "\(model.num ?? "")"
        let width = mdText.getStringWidth(20.0, font: UIFont.systemFont(ofSize: 14.0)) ?? 50.0
        self.meidouLabel.snp.updateConstraints { make in
            make.width.equalTo(width)
        }
        meidouLabel.text = mdText
        if let desc = model.desc, desc.count > 0 {
            titleLabel.isHidden = false
            titleLabel.text = model.expireStr
            let text = desc.replacingOccurrences(of: "（", with: "(")
            desLabel.text = text
            desLabel.snp.remakeConstraints { make in
                make.bottom.equalToSuperview()
                make.left.equalToSuperview().offset(16)
                make.height.equalTo(18)
            }
        } else {
            titleLabel.isHidden = true
            desLabel.text = model.expireStr
            desLabel.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(16)
                make.height.equalTo(18)
            }
        }
    }
}

