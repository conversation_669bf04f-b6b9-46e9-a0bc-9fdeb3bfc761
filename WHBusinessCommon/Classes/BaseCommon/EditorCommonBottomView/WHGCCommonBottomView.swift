//
//  WHGCCommonBottomView.swift
//  MeituWhee
//
//  Created by <PERSON> on 2025/7/4.
//

import Foundation
import WHBaseLibrary
import WHBusinessCommon
import UIKit

public enum WHGCCommonBottomViewType {
    case aiClear
    case oneSentence
    case other
    
    var title: String {
        switch self {
        case .aiClear:
            return WHLocalizedString("立即消除")
        default:
            return WHLocalizedString("立即生成")
        }
    }
    
    var continueTitle: String {
        switch self {
        case .aiClear:
            return WHLocalizedString("继续消除")
        default:
            return WHLocalizedString("继续生成")
        }
    }

}
 
public protocol WHGCCommonBottomViewDelegete: NSObjectProtocol {
    ///开始按钮点击
    func creatbuttonClick()
    // 消耗美豆明细
    func detailButtonClick()
    
    func saveButtonClick()
}

public let WHGCCommonBottomView_Height:CGFloat = WH_SCREEN_BOTTOM_SPACE + 72
///底部开始创作试图
open class WHGCCommonBottomView: UIView {
    
    public weak var delegate: WHInpaindingBottomViewDelegete?
    
    var douImageView:UIImageView?
    
    public var vipCostPriceTips:String = ""
    
    var detailButton:UIButton?
    //当前是否隐藏所有美豆信息
    private var currentIsHiddenMeidou:Bool = false
    //是否是vip
    private var isVip:Bool = false
    
    private let bgSetColor:UIColor
        
    private let type: WHGCCommonBottomViewType
    
    private var noramlTitle:String = WHLocalizedString("立即生成")  //按钮默认的title
    private var freeTrialTitle:String = WHLocalizedString("限免生成") //按钮loading的title
    
    public init(bgColor:UIColor = UIColor(wh_hexString: "#17171A"),
                type: WHGCCommonBottomViewType = .other) {
        bgSetColor = bgColor
        self.type = type
        super.init(frame: CGRectZero)
        initViews()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    //更新底部按钮
    public func updateCreateButton(useMiedouNum:Int = 0) {
        if useMiedouNum <= 0 { //限免生成
            creatButton.setTitle(freeTrialTitle, for: .normal)
            creatButton.setImage(UIImage(cm_named: "wh_inpaint_create_button_normal"), for: .normal)
            creatButton.layoutButton(style: .Left, imageTitleSpace: 8)
        } else {
            let titleString = "\(String(useMiedouNum))  \(noramlTitle)"
            creatButton.setTitle(titleString, for: .normal)
            creatButton.setImage(UIImage(cm_named: "mine_meidou"), for: .normal)
            creatButton.layoutButton(style: .Left, imageTitleSpace: 0)
        }
    }
    
    public func showSaveBtn(_ show: Bool) {
        saveBtn.isHidden = !show
        creatButton.setTitle(show ? type.continueTitle : type.title, for: .normal)
        creatButton.snp.updateConstraints { make in
            make.left.equalToSuperview().offset(show ? 64 : 12)
        }
        
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5) {
            self.saveBtn.alpha = show ? 1 : 0
            self.saveBtn.transform = show ? .identity : CGAffineTransform(scaleX: 0.5, y: 0.5)
            
            self.layoutIfNeeded()
        }
    }
    
    func initViews(){
        backgroundColor = bgSetColor
        
        clipCorner(corners: [.topLeft, .topRight],
                   size: CGSize(width: WH_SCREEN_WIDTH, height: WHGCCommonBottomView_Height),
                   radius: 24)
//        let douImage = UIImageView(image: UIImage(cm_named: "mine_meidou"))
//        douImage.isHidden = true
//        self.douImageView = douImage
//        addSubview(douImage)
//        douImage.snp.makeConstraints { make in
//            make.size.equalTo(CGSize(width: 24, height: 24))
//            make.top.equalTo(16)
//            make.left.equalTo(12)
//        }
//        addSubview(topLabel)
//        topLabel.snp.makeConstraints { make in
//            make.left.equalTo(douImage.snp.right).offset(4)
//            make.centerY.equalTo(douImage.snp.centerY)
//        }
//        
//        topLabel.text = ""
        
//        addSubview(vipButton)
//        vipButton.snp.makeConstraints { make in
//            make.centerY.equalTo(topLabel.snp.centerY)
//            make.size.equalTo(CGSize(width: 24, height: 24))
//            make.left.equalTo(topLabel.snp.right).offset(8)
//        }
        addSubview(borderView)
        borderView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(-2.0)
            make.right.equalToSuperview().offset(2.0)
            make.bottom.equalToSuperview().offset(50.0)
        }
        
        let detailButton = UIButton(type: .custom)
        detailButton.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 13)
        detailButton.setTitle(WHLocalizedString("消耗明细"), for: .normal)
        detailButton.setTitleColor(WHFigmaColor.contentBottomSheetInactive, for: .normal)
        detailButton.setImage(UIImage(cm_named:"wh_common_bottom_view_detail_btn_image_normal"), for: .normal)
        detailButton.layoutButton(style: .Right, imageTitleSpace: 20)
        detailButton.addTarget(self, action: #selector(detailButtonClick), for: .touchUpInside)
        addSubview(detailButton)
        detailButton.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 82, height: 48))
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(12.0)
        }
        self.detailButton = detailButton
        
        addSubview(bgImageView)
        addSubview(creatButton)
        creatButton.snp.makeConstraints { make in
            make.left.equalTo(detailButton.snp.right).offset(12.0)
            make.right.equalToSuperview().offset(-12)
            make.height.equalTo(48.0)
            make.top.equalToSuperview().offset(12.0)
        }
        bgImageView.snp.makeConstraints { make in
            make.edges.equalTo(creatButton)
        }
        
        
        addSubview(saveBtn)
        saveBtn.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(6)
            make.centerY.equalTo(creatButton)
            make.width.height.equalTo(56)
        }
        saveBtn.layoutButton(style: .Top, imageTitleSpace: 4)
    }
    
    public func showMeiDouWith(consumeText:String?){
        topLabel.text = consumeText ?? ""
        if consumeText != nil,consumeText?.count ?? 0 > 0,self.currentIsHiddenMeidou == false {
            self.douImageView?.isHidden = false
        } else {
            self.douImageView?.isHidden = true
        }
        
    }
    
    public func hiddedVipButton(isHidden:Bool) {
        self.isVip = !isHidden
        if currentIsHiddenMeidou == true { //当前在隐藏美豆信息（开启了自动消耗模式）
            return;
        }
        self.vipButton.isHidden = isHidden
    }
    
    @objc func showMemerAlert(){
        if self.vipCostPriceTips.count > 0 { //如果下发的提示词是空，则不展示提示
            let alert = WHMemberBenefitsAlertView(with: self.vipCostPriceTips, fromView: self.vipButton)
            alert.show(needTransform: false)
        }
    }
    
    @objc func creatButtonClick(){
        //高震动反馈
        let generator = UIImpactFeedbackGenerator(style: .heavy)
        generator.prepare()
        generator.impactOccurred()
        
        delegate?.creatbuttonClick()
    }
    
    @objc func detailButtonClick() {
        tapicEngineOccurred(.medium)
        delegate?.detailButtonClick()
    }
    
    @objc
    private func saveBtnAction(_ sender: UIButton) {
        delegate?.saveButtonClick()
    }
    
    public lazy var creatButton: UIButton = {
        let button = UIButton(type: .custom)
        button.tintColor = WHFigmaColor.contentButtonOnMain
        button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17,weight: .semibold)
        button.setTitle(type.title, for: .normal)
        button.setImage(UIImage(cm_named: "wh_inpaint_create_button_normal"), for: .normal)
        button.layoutButton(style: .Left, imageTitleSpace: 8)
        button.addTarget(self, action: #selector(creatButtonClick), for: .touchUpInside)
        button.layer.cornerRadius = 16.0
        button.layer.masksToBounds = true
        button.backgroundColor = UIColor.clear
        return button
    }()
    
    private lazy var bgImageView: UIImageView = {
        let imageView = UIImageView()
        //如果是XR以下机型，使用静图
        if WHDeviceSupport.compare(.iPhoneXR, compareBlock: { current, target in
            current <= target
        }) {
            imageView.image = UIImage(cm_named: "whee_common_sure_button_bg_image")
        } else {
            if let backgroundPath = WHCMBundle.main.path(forResource: "wh_gc_button_brandGradient", ofType: "webp") {
                let url = URL(fileURLWithPath: backgroundPath)
                imageView.sd_setImage(with: url)
            }
        }
        imageView.layer.cornerRadius = 16.0
        imageView.layer.cornerCurve = .continuous
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private lazy var vipButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(cm_named: "bottom_vip"), for: .normal)
        button.addTarget(self, action: #selector(showMemerAlert), for: .touchUpInside)
        button.isHidden = true //默认隐藏，会员显示
        return button
    }()
    
    lazy var topLabel: UILabel = {
        let label = UILabel()
        label.textColor = WHFigmaColor.contentBottomSheetInactive
        label.font = UIFont.pingFangSCFont(ofSize: 13)
        return label
    }()
    
    private lazy var loadingButtonView: WHGCCommonBottomViewLoadingView = {
        let loadingView = WHGCCommonBottomViewLoadingView()
        return loadingView
    }()
    
    private lazy var borderView: UIView = {
        let view = UIView()
        view.layer.borderColor = UIColor(wh_hexString: "#222326").cgColor
        view.layer.borderWidth = 1.0
        view.layer.cornerCurve = .continuous
        view.layer.cornerRadius = 24.0
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var saveBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.titleLabel?.font = .pingFangSCFont(ofSize: 11, weight: .semibold)
        btn.setTitle(WHLocalizedString("保存"), for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.setImage(UIImage(cm_named: "wh_ai_clear_download"), for: .normal)
        btn.addTarget(self, action: #selector(saveBtnAction(_ :)), for: .touchUpInside)
        btn.alpha = 0
        btn.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
        return btn
    }()
    //设置按钮的默认文案
    public func setCreatButtonTitle(noramlTitle:String = WHLocalizedString("立即生成"),freeTrialTitle:String = WHLocalizedString("限免生成")) {
        self.noramlTitle = noramlTitle
        self.freeTrialTitle = freeTrialTitle
    }
    //新接口--设置按钮loading时的文案
    public func setCreateButtonLoadingTitle(_ title: String) {
        self.loadingButtonView.createBtnLoadingTitle = title
    }
    //新接口--设置按钮loading状态
    public func setButtonLoading(isLoading:Bool) {
        if isLoading == true { //显示loading
            if self.loadingButtonView.superview == nil {
                addSubview(loadingButtonView)
                loadingButtonView.snp.remakeConstraints { make in
                    make.left.equalTo(self.detailButton?.snp.right ?? 0).offset(12.0)
                    make.right.equalToSuperview().offset(-12)
                    make.height.equalTo(48.0)
                    make.top.equalToSuperview().offset(12.0)
                }
                self.creatButton.isHidden = true
                self.loadingButtonView.showLoadingView()
            }
        } else { //默认状态
            self.loadingButtonView.dismissView()
            self.loadingButtonView.removeFromSuperview()
            self.creatButton.isHidden = false
        }
    }
    //隐藏美豆消耗和明细
    public func hiddenMeidouInfo(isHidden:Bool) {
        self.currentIsHiddenMeidou = isHidden
        if self.topLabel.text?.count ?? 0 > 0,isHidden == false {
            self.douImageView?.isHidden = false
        } else {
            self.douImageView?.isHidden = true
        }
        self.topLabel.isHidden = isHidden
        if self.isVip == true,isHidden == false {
            self.vipButton.isHidden = false
        } else {
            self.vipButton.isHidden = true
        }
        self.detailButton?.isHidden = isHidden
    }
}

class WHGCCommonBottomViewLoadingView: UIView {
    
    var createBtnLoadingTitle: String = WHLocalizedString("生成中...")   //生成按钮，loading时的文案
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func showLoadingView() {
        var titleWidth = 0.0
        if createBtnLoadingTitle.count > 0 {
            let width = createBtnLoadingTitle.getStringWidth(24.0, font: titleLabel.font)
            titleWidth = width
        }
        let circleWidth = 20.0
        
        let totalWidth = circleWidth + titleWidth + (titleWidth > 0 ? 8 : 0)
        loadingImageView.snp.remakeConstraints { make in
            make.size.equalTo(CGSizeMake(20.0, 20.0))
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(20.0 - (totalWidth / 2.0))
        }
        titleLabel.snp.remakeConstraints { make in
            make.height.equalTo(24.0)
            make.centerY.equalToSuperview()
            make.left.equalTo(loadingImageView.snp.right).offset(8.0)
            make.width.equalTo(titleWidth)
        }
        titleLabel.text = createBtnLoadingTitle
        startLoading() //转圈
    }
    
    func dismissView() {
        stopLoading()
    }
    
    func setup(){
        addSubview(loadingImageView)
        addSubview(titleLabel)
    }
    
    private lazy var loadingImageView: UIImageView = {
        let loadingView = UIImageView()
        loadingView.image = UIImage(cm_named: "whee_bottom_common_sure_button_loading_image")
        return loadingView
    }()
    private lazy var titleLabel: UILabel = {
        let loadingLabel = UILabel()
        loadingLabel.font = UIFont.pingFangSCFont(ofSize: 16.0, weight: .semibold)
        loadingLabel.textColor = .white
        return loadingLabel
    }()
    
    func startLoading(duration: Double = 1.0) {
        let rotation = CABasicAnimation(keyPath: "transform.rotation.z")
        rotation.toValue = NSNumber(value: Double.pi * 2)
        rotation.duration = duration
        rotation.isCumulative = true
        rotation.repeatCount = .infinity
        rotation.isRemovedOnCompletion = false
        loadingImageView.layer.add(rotation, forKey: "rotationAnimation")
    }

    func stopLoading() {
        loadingImageView.layer.removeAnimation(forKey: "rotationAnimation")
    }
}
