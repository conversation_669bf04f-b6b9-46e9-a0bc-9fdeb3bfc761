//
//  WHGcPriceModel.swift
//  MeituWhee
//
//  Created by <PERSON> on 2023/12/12.
//

import Foundation
import YYModel

@objcMembers public class WHGcPriceModel: NSObject, YYModel, NSCoding {
    
    public var priceDetailList: [WHGcPriceDetailModel]?
    
    public var isVip: Bool = false
    public var payScene: String?
    public var functionCode: String?
    public var functionName: String?
    public var amount: String?
    public var costPriceText: String?
    public var costPriceTextOrigin: String?
    public var costPriceTips: String?
    public var useFreeNum: String?
    public var totalFreeNum: String = ""
    public var costPriceDesc: String = ""
    /// 商品限免次数
    public var freeNum: Int = 0
    /// 会员权益次数
    public var rightNum: Int = 0

    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["priceDetailList": WHGcPriceDetailModel.classForCoder(),
                ]
    }
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "isVip": "is_vip",
            "payScene":"pay_scene",
            "functionCode": "function_code",
            "functionName":"function_name",
            "amount": "amount",
            "costPriceText":"cost_price_text",
            "costPriceTextOrigin": "cost_price_text_origin",
            "costPriceTips":"cost_price_tips",
            "useFreeNum": "use_free_num",
            "priceDetailList":"price_detail",
            "totalFreeNum": "total_free_num",
            "costPriceDesc": "cost_price_desc",
            "freeNum": "free_num",
            "rightNum": "right_num",
        ]
    }
    
    func cellTotalHeight() -> CGFloat {
        var height: CGFloat = 0
        if let list = priceDetailList {
            for _ in list {
                height += 40
            }
        }
        return height > 0 ? height + 16 : height
    }
}

@objcMembers public class WHGcPriceDetailModel: NSObject, YYModel, NSCoding {
    
    public var itemName: String?
    public var priceOrigin: String?
    public var itemCount: String?
    public var priceNow: String?
    public var priceNowValue: String = ""
    public var priceOriginValue: String = ""
    public var unitPrice: Int = 0
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "itemName": "item_name",
            "priceOrigin":"price_origin",
            "itemCount": "item_count",
            "priceNow": "price_now",
            "priceNowValue": "price_now_value",
            "priceOriginValue": "price_origin_value",
            "unitPrice": "unit_price",
        ]
    }
}
