//
//  WHMeidouDetailsView.swift
//  MeituWhee
//
//  Created by z<PERSON>life<PERSON> on 2023/12/13.
//  剩余美豆页

import UIKit
import WHBaseLibrary
import WHBusinessCommon

open class WHMeidouDetailsView: WHCommonBaseHalfView {
    
    lazy var centerView: UIView = {
        let make = UIView()
        make.backgroundColor = WHFigmaColor.init(rgb: 0x292A2B)
        make.layer.cornerRadius = 2
        return make
    }()
    
    lazy var meidouImageView: UIImageView = {
        let make = UIImageView()
        make.image = UIImage(cm_named: "icon_mark_meidou")
        make.contentMode = .scaleAspectFill
        return make
    }()
    
    lazy var countLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.pingFangSCFont(ofSize: 28,weight: .bold)
        make.textColor = UIColor(wh_hexString: "#FFFFFF")
        make.textAlignment = .center
        return make
    }()
    
    lazy var lastLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        make.textColor = UIColor(wh_hexString: "#93979E")
        make.textAlignment = .center
        return make
    }()
    
    lazy var desLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        make.textColor = UIColor(wh_hexString: "#93979E")
        make.textAlignment = .center
        return make
    }()
    
    lazy var vipInfoLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        make.textColor = .white
        make.textAlignment = .left
        make.numberOfLines = 0
        return make
    }()
    
    lazy var vipInfoView: UIView = {
        let make = UIView()
        make.backgroundColor = UIColor(wh_hexString: "#1C1C1C")
        make.layer.cornerRadius = 12
        make.addSubview(vipInfoLabel)
        vipInfoLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(17)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-8)
        }
        
        let line = UIView()
        line.backgroundColor = UIColor(wh_hexString: "#222326")
        make.addSubview(line)
        line.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
            make.left.right.equalToSuperview()
            make.height.equalTo(1)
        }
        make.isHidden = true
        return make
    }()
    
    lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero,style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(wh_hexString: "#1C1C1C")
        tableView.separatorStyle = .none
        tableView.bounces = false
        tableView.layer.cornerRadius = 16
        tableView.contentInset = UIEdgeInsets(top: 12, left: 0, bottom: 8, right: 0)
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "UITableViewCell")
        tableView.register(WHMeidouDetailsCell.self, forCellReuseIdentifier: "WHMeidouDetailsCell")
        return tableView
    }()
    
    lazy var kownButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(WHLocalizedString("我知道了"), for: .normal)
        button.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
        button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17,weight: .semibold)
        button.backgroundColor = UIColor(wh_hexString: "#222326")
        button.layer.cornerRadius = 16.0
        button.layer.masksToBounds = true
        button.layer.cornerCurve = .continuous
        button.clipsToBounds = true
        button.addTarget(self, action: #selector(kownButtonClick), for: .touchUpInside)
        return button
    }()
    
    var meidouModel: WHBeansBalanceModel? //当前美豆余额
    
    public init(meidouModel: WHBeansBalanceModel) {
        self.meidouModel = meidouModel
        super.init(frame: .zero)
        self.isPan = true
        setup()
        config()
    }
    
    required public init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    func setup() {
        containerView.addSubview(centerView)
        let meidouView = UIView()
        containerView.addSubview(meidouView)
        meidouView.addSubview(meidouImageView)
        meidouView.addSubview(countLabel)
        meidouView.addSubview(lastLabel)
        containerView.addSubview(desLabel)
        containerView.addSubview(vipInfoView)
        containerView.addSubview(tableView)
        containerView.addSubview(kownButton)
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
        }
        
        centerView.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 31, height: 5))
        }
        
        meidouView.snp.makeConstraints { make in
            make.top.equalTo(centerView.snp.bottom).offset(20)
            make.height.equalTo(32)
            make.centerX.equalToSuperview()
            make.leading.equalTo(meidouImageView)
            make.trailing.equalTo(lastLabel)
        }
        
        meidouImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.width.equalTo(32)
        }
        
        countLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalTo(meidouImageView.snp.trailing)
            make.right.equalTo(lastLabel.snp.left).offset(-8)
            make.height.equalTo(32)
        }
        
        lastLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(3)
            make.height.equalTo(29)
        }
        
        desLabel.snp.makeConstraints { make in
            make.top.equalTo(meidouView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(18)
        }
        
        if let vipInfo = self.meidouModel?.vipRightInfo, vipInfo.count > 0 {
            vipInfoView.isHidden = false
            vipInfoView.snp.makeConstraints { make in
                make.top.equalTo(desLabel.snp.bottom).offset(20)
                make.left.right.equalToSuperview().inset(16)
                make.height.equalTo(52+20)
            }
            
            tableView.snp.makeConstraints { make in
                make.top.equalTo(vipInfoView.snp.bottom).offset(-20)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
                make.height.equalTo(self.meidouModel?.cellTotalHeight() ?? 56)
            }
        } else {
            tableView.snp.makeConstraints { make in
                make.top.equalTo(desLabel.snp.bottom).offset(20)
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
                make.height.equalTo(self.meidouModel?.cellTotalHeight() ?? 56)
            }
        }
    
        kownButton.snp.makeConstraints { make in
            make.top.equalTo(tableView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 12)
        }
    }
    
    func config() {
        countLabel.text = self.meidouModel?.availableAmount
        lastLabel.text = self.meidouModel?.detailTitle
        desLabel.text = self.meidouModel?.detailDesc
        vipInfoLabel.text = self.meidouModel?.vipRightInfo
    }
    
    @objc func kownButtonClick() {
        tapicEngineOccurred(.heavy) //震动
        dismiss()
    }
}

extension WHMeidouDetailsView: UITableViewDelegate, UITableViewDataSource {
    
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.meidouModel?.amountList?.count ?? 0
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 40
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let model = self.meidouModel?.amountList?[indexPath.row] as? WHBeansBalanceAmountListModel
        let cell = tableView.dequeueReusableCell(withIdentifier: "WHMeidouDetailsCell") as? WHMeidouDetailsCell
        cell?.configMeidou(model: model ?? WHBeansBalanceAmountListModel())
        return cell ?? UITableViewCell()
    }
}

