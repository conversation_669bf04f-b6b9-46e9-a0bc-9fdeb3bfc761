//
//  WHMemberBenefitsAlertView.swift
//  MeituWhee
//
//  Created by xiaoqi on 2023/12/8.
//

import Foundation
import WHBusinessCommon
import WHBaseLibrary
///会员权益弹窗
open class WHMemberBenefitsAlertView: WHCommonBaseAlertView{
    
    init(with title: String,fromView: UIView){
        super.init(frame: CGRect.zero)
        upView(with: title, fromView: fromView)
    }
    
    func upView(with title:String,fromView: UIView){
        backgroundColor = UIColor.black.withAlphaComponent(0.01)
        let viewFrame = fromView.frame
        let bottomImage = UIImageView(image: UIImage(cm_named:"alter_polygon"))
        containerView.addSubview(bottomImage)
        containerView.backgroundColor = UIColor.clear
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        containerView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(closeAction)))
        bottomImage.snp.makeConstraints { make in
            make.left.equalTo(viewFrame.origin.x)
            make.bottom.equalTo(-WH_SCREEN_BOTTOM_SPACE - 110)
            make.size.equalTo(CGSize(width: 14, height: 8))
        }
        let bgView = UIView()
        containerView.addSubview(bgView)
        bgView.backgroundColor = WHFigmaColor.backgroundHud
        bgView.snp.makeConstraints { make in
            make.left.equalTo(bottomImage.snp.left).offset(-37)
            make.height.greaterThanOrEqualTo(40)
            make.bottom.equalTo(bottomImage.snp.top).offset(2)
            make.width.lessThanOrEqualTo(WH_SCREEN_WIDTH - viewFrame.origin.x + 37)
        }
        bgView.layer.cornerRadius = 20
        bgView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(28)
            make.bottom.equalTo(-12)
            make.width.lessThanOrEqualTo(WH_SCREEN_WIDTH - viewFrame.origin.x + 37 - 60)
        }
        titleLabel.text = title
        let closeButton = UIButton(type: .custom)
        closeButton.setImage(UIImage(cm_named: "alert_close"), for: .normal)
        closeButton.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        bgView.addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-17)
            make.size.equalTo(CGSize(width: 20, height: 20))
            make.centerY.equalToSuperview()
            make.left.equalTo(titleLabel.snp.right).offset(5)
        }
        
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.pingFangSCFont(ofSize: 12,weight: .medium)
        label.textColor = WHFigmaColor.contentHudText
        label.numberOfLines = 0
        return label
    }()
    
    @objc
    private func closeAction() {
        dismiss()
    }
}
