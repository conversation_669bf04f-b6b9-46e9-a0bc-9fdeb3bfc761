//
//  MTAnalyticsGID+WHGIDDebugger.m
//  WHBusinessCommon
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/30.
//

#import "MTAnalyticsGID+WHGIDDebugger.h"
#import <objc/runtime.h>

@implementation MTAnalyticsGID (WHGIDDebugger)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Method originalGID = class_getInstanceMethod(self, @selector(gid));
        Method newGID = class_getInstanceMethod(self, @selector(debug_gid));
        method_exchangeImplementations(originalGID, newGID);
    });
}

- (NSString *)debug_gid {
    NSString *debugGID = [[NSUserDefaults standardUserDefaults] objectForKey:WHDEBUG_KEY_GID];
    if (CHECK_VALID_STRING(debugGID)) {
        return debugGID;
    }
    [[NSUserDefaults standardUserDefaults] setObject:[self debug_gid] forKey:WHDEBUG_KEY_ORIGINAL_GID];
    return [self debug_gid];
}

- (NSString *)debug_originalGID {
    return [[NSUserDefaults standardUserDefaults] objectForKey:WHDEBUG_KEY_ORIGINAL_GID] ?: @"";
}

BOOL CHECK_VALID_STRING(__nullable id string) {
    return (string && [string isKindOfClass:[NSString class]] && [(NSString *)string length] > 0);
}

@end
