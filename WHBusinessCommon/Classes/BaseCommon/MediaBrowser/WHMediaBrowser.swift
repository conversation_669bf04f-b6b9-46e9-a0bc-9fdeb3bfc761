//
//  WHMediaBrowser.swift
//  MTXX
//
//  Created by meitu on 2020/6/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit
import WHBaseLibrary
import WHBusinessCommon
import SDWebImage


@objcMembers
open class WHMediaBrowser: JXPhotoBrowser {
    ///上报的source
    public var source:String = ""
    ///保存时是否需要弹授权弹窗
    public var isSaveShowAIAlert:Bool = true
    /// 是不是IM进来的,是的话需要重置视频
    open var isFromIM = false
    
    ///IM进来消息id埋点需要
    open var messageId = ""
        
    /// 数据源
    public var dataSource: [WHMediaBrowserModel] = []
    
    /// 有前一页
    open var preCursor: String = ""
    
    /// 第一次加载
    open var isFirstAppear: Bool = true
    
    /// 有后一页
    open var nextCursor: String = "" {
        didSet {
            self.browserView.lastTipsView.isHidden = nextCursor != ""
        }
    }
    /// 保存按钮点击事件
    public var saveClickOnTap: ((_ urlString:String ) -> Void)?
    /// 当前对应feed的scm
    public var scm: String?
    
    /// 是否隐藏视图
    open var hideMenu: Bool = false
    
    /// 是否暂停视频播放
    open var shouldStopVideo: Bool = true
    
    /// 是否可滑动
    open var canScroll = false
    
    /// 上级页面是否有视频播放
    open var isPerVideoPlay = false
    
    /// 上级页面是否有音乐播放
    open var isPerMusicPlay = false
    
    /// 本次页面消失的时候是否有视频播放
    open var isVideoPlay = false
    
    /// 本次页面消失的时候是否有音乐播放
    open var isMusicPlay = false
    
    ///视频控制器的样式
    public var controlStyle: WHVideoBrowserControlStyle = .fullScreen
    
    /// 水印上的用户名
    open var username: String? {
        didSet {
            if selectedCell is JXPhotoBrowserImageCell {
                (selectedCell as? JXPhotoBrowserImageCell)?.username = username
            } else if selectedCell is WHVideoBrowserCell {
                (selectedCell as? WHVideoBrowserCell)?.username = username
            }
        }
    }
    
    /// 是否可以下载图片
    open var allowDownloadPic: Bool = true
    
    /// 是否可以下载视频
    open var allowDownloadVideo: Bool = true
    
    /// 下载控件
    private var downloader: WHMediaDownloadPlugin?
    
    /// 下载视图
    public lazy var downloadIMButton: UIButton = {
        var downloadButton = UIButton(type: .custom)
        downloadButton.setImage(UIImage.init(named: "wh_ai_video_detail_save_button_normal"), for: .normal)
        downloadButton.addTarget(self, action: #selector(downloadMedia), for: .touchUpInside)
        return downloadButton
    }()
    
    /// 点击取消事件 当页面还没加载出数据可以点击取消
    lazy var shortTapper: UITapGestureRecognizer = {
        let tap = UITapGestureRecognizer()
        tap.addTarget(self, action: #selector(shortTapped(_:)))
        return tap
    }()
    
    lazy var backButton: UIButton = {
        let backBtn = UIButton(type: .custom)
        backBtn.setImage(UIImage(named: "icon_search_back"),for: .normal)
        backBtn.addTarget(self,action: #selector(backActionClick),for: .touchUpInside)
        return backBtn
    }()
    
    lazy var pageLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 17, weight: .medium)
        label.textColor = .white
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    ///下载是否带水印
    open var showWatermark: Bool = false
    
    ///是否显示水印
    open var showWatermarkView: Bool = false {
        didSet {
            if oldValue != showWatermarkView {
                if selectedCell is JXPhotoBrowserImageCell {
                    (selectedCell as? JXPhotoBrowserImageCell)?.showWatermark = showWatermark
                } else if selectedCell is WHVideoBrowserCell {
                    (selectedCell as? WHVideoBrowserCell)?.showWatermark = showWatermark
                }
            }
        }
    }
    
    ///当前选中的cell
    public var selectedCell: JXPhotoBrowserCell?
    
    ///首次选中的cell
    public var firstSelectedCell: JXPhotoBrowserCell?
    
    ///当前选中的index
    open var selectedIndex: Int = 0
    
    ///是否显示分页指示器
    open var showPageIndicator: Bool = true {
        didSet {
            if showPageIndicator {
                self.pageIndicator = JXPhotoBrowserNumberPageIndicator()
            } else {
                self.pageIndicator = nil
            }
        }
    }
    
    // MARK: - 生命周期
    
    open override func viewDidLoad() {
        self.configTheBrowser()
        super.viewDidLoad()
        self.isPreviousNavigationBarHidden = true
        self.maskView.addGestureRecognizer(shortTapper)
        addSubViews()
    }
    
    deinit {
//        MTCommunityImageDiskCache.clearCatchImage()
        WHPrint("销毁")
    }
    
    open override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if isVideoPlay {
            if self.selectedCell is WHVideoBrowserCell {
                let browserCell = self.selectedCell as? WHVideoBrowserCell
                browserCell?.playload()
                browserCell?.play()
            }
        }
    }
    
    open override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if self.selectedCell is WHVideoBrowserCell {
            let browserCell = self.selectedCell as? WHVideoBrowserCell
            if browserCell?.isPlaying() ?? false {
                isVideoPlay = true
                if shouldStopVideo {
                    browserCell?.pause()
                }
            } else {
                isVideoPlay = false
            }
        }
    }
    
    func addSubViews() {
        self.view.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT - 12 - 24)
            make.width.height.equalTo(24)
        }
        
        self.view.addSubview(downloadIMButton)
        downloadIMButton.snp.makeConstraints { (make) in
            make.right.equalToSuperview().offset(-16)
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT - 12 - 24)
            make.width.height.equalTo(24)
        }
        
        self.view.addSubview(pageLabel)
        pageLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT - 12 - 24)
            make.centerX.equalToSuperview()
            make.height.equalTo(24)
        }
    }
    
    func reloaPageIndexView(isHidden: Bool = false) {
        pageLabel.isHidden = isHidden
        pageLabel.text = String(selectedIndex + 1) + "/" + String(dataSource.count)
    }
    
    @objc func backActionClick() {
        self.dismiss()
    }
    
    open override func reloadData() {
        super.reloadData()
        if canScroll {
            self.browserView.canSingleScrollow = true
            return
        }
        //改变contentsize使其能滑动
        if self.nextCursor == "" && self.preCursor == "" && self.dataSource.count == 1 {
            self.browserView.canSingleScrollow = false
        } else {
            self.browserView.canSingleScrollow = true
        }
    }
    
    public override func viewshouldHide(isPaning: Bool = true) {
        super.viewshouldHide(isPaning: isPaning)
        downloadIMButton.isHidden = true
        pageLabel.isHidden = true
        self.isPaning = isPaning
    }
    
    public override func viewshouldShow(isPaning: Bool = true) {
        super.viewshouldShow(isPaning: isPaning)
        downloadIMButton.isHidden = false
        if let cell = self.selectedCell as? WHVideoBrowserCell {
            pageLabel.isHidden = true
        } else {
            pageLabel.isHidden = false
        }
        self.isPaning = false
    }
    /// 视频进度条开始滑动时调用
    public override func beginDragging() {
        if isFromIM {
            downloadIMButton.isHidden = true
        }
    }
    /// 视频进度条结束滑动时调用
    public override func endDragging() {
        if isFromIM {
            downloadIMButton.isHidden = false
        }
    }
    
    // MARK: - 配置方法
    private func configTheBrowser() {
        //个数
        self.numberOfItems = {[weak self] in
            guard let this = self else { return 0}
            return this.dataSource.count
        }
        //配置cell
        self.cellClassAtIndex = {[weak self] index in
            guard let `self` = self, index >= 0 && index < self.dataSource.count else { return JXPhotoBrowserImageCell.self}
            let model = self.dataSource[index]
            return model.mediaType == .video ? WHVideoBrowserCell.self : JXPhotoBrowserImageCell.self
        }
        //刷新cell
        self.reloadCellAtIndex = {[weak self] context in
            guard let this = self ,!this.isPaning else { return }
            let model = this.dataSource[context.index]
            if model.mediaType == .video, context.cell is WHVideoBrowserCell {
                let browserCell = context.cell as? WHVideoBrowserCell
                browserCell?.isFromIM = this.isFromIM
                browserCell?.showWatermark = this.showWatermarkView
                browserCell?.username = this.username
                // 添加长按事件
                browserCell?.longPressedAction = {[weak this] cell, _ in
                    guard let that = this else { return }
//                    that.longPress()
                }
                // 添加长按事件
                browserCell?.doubleTapAction = {[weak this] cell, tap in
                    guard let that = this else { return }
                    that.doubleTap(tap)
                }
                browserCell?.controlStyle = this.controlStyle
                browserCell?.mediaModel = model
            } else if context.cell is JXPhotoBrowserImageCell {
                
                let browserCell = context.cell as? JXPhotoBrowserImageCell
                let mediaUrl = model.coverUrl
                ///使用之前缓存的小图
                var smallPicUrl = URL(string: mediaUrl.thumbCover(width: 320))
                var url = URL(string: mediaUrl)
                
                if !mediaUrl.hasPrefix("http") {
                    if !mediaUrl.hasPrefix("file") {
                        if (mediaUrl.components(separatedBy: ".").count > 1) {
                            url = URL.init(fileURLWithPath: mediaUrl)
                        }
                    }
                    this.showWatermarkView = false
                    if this.dataSource.count == 1 {
                        this.pageIndicator = nil
                    }
                    smallPicUrl = nil
                }
                browserCell?.showWatermark = this.showWatermarkView
                browserCell?.username = this.username
                // 添加长按事件
                browserCell?.longPressedAction = {[weak this] cell, _ in
                    guard let that = this else { return }
                    that.longPress()
                }
                
                if let image = model.mediaData {
                    browserCell?.imageView.image = image
                    browserCell?.progressView.progress = 1
                    return
                }
                
                var placeHoloder:UIImage?
                if smallPicUrl != nil {
                    let cacheKey = SDWebImageManager.shared.cacheKey(for: smallPicUrl)
                    placeHoloder = SDImageCache.shared.imageFromCache(forKey: cacheKey)
//                    if placeHoloder == nil {
//                        placeHoloder = MTAvatarRules.findCacheImage(urlString: mediaUrl)
//                    }
                } else {
                    let cacheKey = SDWebImageManager.shared.cacheKey(for: url)
                    placeHoloder = SDImageCache.shared.imageFromCache(forKey: cacheKey)
                }
                var originUrl: URL?
                if let temp = model.originUrl, let url = URL(string: temp) {
                    originUrl = url
                }
                if smallPicUrl == nil && placeHoloder != nil && browserCell?.imageView.image == placeHoloder {
                    
                } else {
                    browserCell?.reloadData(placeholder: placeHoloder, url: url, originUrl: originUrl)
                }
            }
        }
        //cell出现的事件
        self.cellWillAppear = {[weak self] cell, index in
            ///正在执行手势操作的时候不执行
            guard let `self` = self ,!self.isPaning else { return }
            self.indexWillChange(index,cell)
            self.selectedCell = cell
            if self.firstSelectedCell == nil {
                self.firstSelectedCell = cell
            }
            if cell.isKind(of: WHVideoBrowserCell.classForCoder()) {
                (cell as? WHVideoBrowserCell)?.playload()
                if self.isFirstAppear, (!self.isFromIM || (self.isFromIM && self.firstSelectedCell ?? cell == cell)) {
                    (cell as? WHVideoBrowserCell)?.autoPlay()
                }
            }
        }
        //cell即将消失的事件
        self.cellWillDisappear = {[weak self] cell, index in
            ///正在执行手势操作的时候不执行
            guard let `self` = self ,!self.isPaning else { return }
            if let cell = cell as? WHVideoBrowserCell, VideoPlayerManager.shared.currentId == cell.mediaModel?.mediaID {
                cell.stop()
            }
        }
        //cell已经出现的事件
        self.cellDidAppear = {[weak self] cell, index in
            guard let `self` = self ,!self.isPaning else { return }
            self.indexDidChange(index)
            self.isFirstAppear = false
            if cell.isKind(of: WHVideoBrowserCell.classForCoder()) {
                if !self.isFromIM {
                    (cell as? WHVideoBrowserCell)?.autoPlay()
                }
            }
            if self.isDeleteing {
                self.isDeleteing = false
                if self.dataSource.count == 1 && self.nextCursor != "" {
                    self.getNextPageDataFromWeb()
                }
                return
            }
            //提前加载页数
            let tip = self.dataSource.count > 3 ? 3 : self.dataSource.count - 1
            
            if index >= self.dataSource.count - tip && self.nextCursor != "" && self.panDirection != .right {
                // 加载后一页
                self.getNextPageDataFromWeb()
                
            } else if index == 0 && self.preCursor != "" && self.panDirection == .right {
                // 加载前一页
                self.isFirstAppear = true
                self.getPerPageDataFromWeb()
            }
        }
        // 监听页码变化
        self.didChangedPageIndex = {[weak self] index in
            guard self != nil else { return }
        }
    }
    
    // MARK: - 请求数据方法
    
    ///获取前一页数据
    func getPerPageDataFromWeb() {
        
        
    }
    
    ///获取后一页数据
    func getNextPageDataFromWeb() {
        
        
    }
    
    // MARK: - 私有方法
    
    /// 下载按钮
    @objc private func downloadIMAction() {
        self.longPressDownloadMedia()
    }
    
    func longPress() {
        let model: WHMediaBrowserModel = self.dataSource[selectedIndex]
        if (model.mediaType == .image && self.allowDownloadPic) ||  (model.mediaType == .video && self.allowDownloadVideo) {
            let name: String = "图片"
            let style: UIAlertController.Style = .actionSheet
            let alert = UIAlertController(title: nil, message: nil, preferredStyle: style)
            alert.addAction(UIAlertAction(title: "保存" + name, style: .default, handler: { _ in
                self.longPressDownloadMedia()
            }))
            alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
            self.present(alert, animated: true, completion: nil)
        }
    }
    
    ///长按手势
    func doubleTap(_ tap:UITapGestureRecognizer) {
        
        
    }
    
    ///跳转
    private func browserPush(_ viewController: UIViewController, animated: Bool) {
        self.navigationController?.pushViewController(viewController, animated: animated)
    }
    
    ///页面已经改变
    func indexDidChange(_ index: NSInteger) {
        
        
    }
    
    ///页面即将改变
    func indexWillChange(_ index: NSInteger, _ cell: JXPhotoBrowserCell) {
        
        self.selectedIndex = index
        self.selectedCell = cell
        var isVideo: Bool = false
        if let videoCell = cell as? WHVideoBrowserCell {
            isVideo = true
        }
        reloaPageIndexView(isHidden: isVideo)
    }
    
    ///页面销毁
    @objc func shortTapped(_ gesture: UITapGestureRecognizer) {

        if self.dataSource.count > 0 {
            
            return
        }
        self.dismiss()
    }
    
    /// 长按下载
    @objc func longPressDownloadMedia() {
        downloadMedia()
    }
    
    ///下载操作
    @objc func downloadMedia() {
        if !WHNetwork.isReachable() {
            return
        }
        let model: WHMediaBrowserModel = self.dataSource[selectedIndex]
        WHAnalyticsManager.otherTrackEvent("whee_results_page_click", params: ["click_type": "save_click","source":source])
        
        if isSaveShowAIAlert == false {
            let url = model.mediaUrl
            self.saveClickOnTap?(url)
            self.downloadSaveMedia(url: url, mediaModel: model)
        } else {
            WHAITagAuthAlertShareManager.showAITagAuthAlertIfNeeded(inView: self.view) {[weak self] isWaterMark in
                guard let self = self else { return }
                let url = isWaterMark ? model.waterMarkUrl : model.unWaterMarkUrl
                self.saveClickOnTap?(url)
                self.downloadSaveMedia(url: url, mediaModel: model)
            }
        }
    }
    
    private func downloadSaveMedia(url:String,mediaModel: WHMediaBrowserModel) {
        if downloader == nil {
            downloader = WHMediaDownloadPlugin(mediaType: .image, containerVC: self, urlStrs: [url])
            downloader?.delegate = self
            downloader?.setup(useDefaultUI: false)
        }
        downloader?.originMediaUrlStrs = [url]
        downloader?.currentIndex = 0
        downloader?.watermarkType = self.showWatermark ? .name(self.username ?? "") : .none
        downloader?.mediaType = (mediaModel.mediaType == .image ?  .image: .video)
        downloader?.start()
    }
    
    // 埋点
    private func trackMAT( _ name: String) {}
}

extension WHMediaBrowser {
    
    @discardableResult
    @objc
    public class func show(data: [String: Any], messageID: String? = nil) -> WHMediaBrowser? {
        return self.show(datas: [data], messageID: messageID)
    }
    
    @discardableResult
    @objc
    public class func show(datas: [[String: Any]], messageID: String? = nil) -> WHMediaBrowser? {
        let dataSource = NSArray.yy_modelArray(with: WHMediaBrowserModel.self, json: datas) as! [WHMediaBrowserModel]
        return self.show(models: dataSource, messageID: messageID )
    }
    
    @discardableResult
    @objc
    public class func show(models: [WHMediaBrowserModel], messageID: String? = nil) -> WHMediaBrowser? {
        //动态创建子类实例
        var browser = WHMediaBrowser.init()
        if let typeClass = self.className.toControllerClass() as? WHMediaBrowser.Type {
            browser = typeClass.init()
        }
        browser.show(method: .push(inNC: nil))
        browser.messageId = messageID ?? ""
        browser.dataSource = models
        return browser
    }
    
    @discardableResult
    @objc
    public class func show(images: [UIImage], messageID: String? = nil) -> WHMediaBrowser? {
        //动态创建子类实例
        var browser = WHMediaBrowser.init()
        if let typeClass = self.className.toControllerClass() as? WHMediaBrowser.Type {
            browser = typeClass.init()
        }
        browser.show(method: .push(inNC: nil))
        browser.messageId = messageID ?? ""
        let models = images.compactMap { (image) -> WHMediaBrowserModel? in
            let info = WHMediaBrowserModel()
            info.mediaData = image
            info.mediaWidth = image.size.width
            info.mediaWidth = image.size.height
            info.mediaType = .image
            return info
        }
        browser.dataSource = models
        return browser
    }
    
    @discardableResult
    @objc
    public class func show(model: WHMediaBrowserModel, index: Int = 0 , view: UIView? = nil,params: [String: Any]? = nil,showWatermark: Bool = false,source:String,isSaveShowAIAlert:Bool = true) -> WHMediaBrowser? {
        return self.show(local: [model],index: index,view: view, params: params, showWatermark: showWatermark,source: source,isSaveShowAIAlert: isSaveShowAIAlert)
    }
    
    @discardableResult
    @objc
    public class func show(local: [WHMediaBrowserModel], index: Int = 0 , view: UIView? = nil, params: [String: Any]? = nil,showWatermark: Bool = false,source:String,isSaveShowAIAlert:Bool = true) -> WHMediaBrowser? {
    
        // 动态创建子类实例
        var browser: WHMediaBrowser!
        let vcClass: UIViewController.Type? = self.className.toControllerClass()
        if vcClass is WHMediaBrowser.Type {
            let typeClass = vcClass as! WHMediaBrowser.Type
            browser = typeClass.init()
        } else {
            browser = WHMediaBrowser.init()
        }
        browser.show(method: .push(inNC: nil))
        // 使用Zoom动画
        browser.transitionAnimator = JXPhotoBrowserZoomAnimator(previousView: { index -> UIView? in
            if let collectionView = view as? UICollectionView {
                let path = IndexPath(item: index, section: 0)
                let cell = collectionView.cellForItem(at: path)
                return cell
            }
            return view
        })
        
        browser.allowDownloadPic = true
        // 获取用户隐私权限 是否可以下载，是否有水印
        browser.dataSource = local
        browser.pageIndex = index
        browser.showWatermark = showWatermark
        browser.source = source
        browser.isSaveShowAIAlert = isSaveShowAIAlert
        return browser
    }
}


// MARK: - 下载代理
extension WHMediaBrowser: NewMediaDownloadPluginDelegate {
    public func beginLayout(_ plugin: WHMediaDownloadPlugin) {

    }

    public func willBeginDownload(_ plugin: WHMediaDownloadPlugin) {

    }

    public func didBeginDownload(_ plugin: WHMediaDownloadPlugin) {

    }

    public func downloadFailure(_ plugin: WHMediaDownloadPlugin) {

    }

    /// 下载取消
    public func downloadCancelled(_ plugin: WHMediaDownloadPlugin) {
        trackMAT("big_picture_download_cancel")
    }
    
    /// 下载并保存成功
    @objc
    public func downloadSuccess(_ plugin: WHMediaDownloadPlugin) {
        trackMAT("big_picture_download_success")
    }

}
