//
//  MediaModel.swift
//  MTXX
//
//  Created by meitu on 2020/7/1.
//  Copyright © 2020 Meitu. All rights reserved.
//

import UIKit

@objc
public enum WHMediaState: Int {
    case normal = 0
    case image = 1 //图片
    case video = 2 //视频
    case sougouEmoji = 4 //表情包
}

@objcMembers
public class WHMediaBrowserModel: NSObject {
    
    ///多媒体ID
    public var mediaID: String = ""
    /// 多媒体url--实际预览使用播放的地址
    public var mediaUrl: String = ""
    /// 无水印Url
    public var unWaterMarkUrl: String = ""
    /// 有水印Url
    public var waterMarkUrl: String = ""
    
    public var originUrl: String?
    // 封面图 一般用于视频封面
    public var coverUrl: String = ""
    
    public var thumb: String = ""
    
    ///多媒体资源
    public var mediaData: UIImage?
        
    ///多媒体宽度
    public var mediaWidth: CGFloat = 0
    
    ///多媒体高度
    public var mediaHeight: CGFloat = 0
    
    public var duration: Float = 0         
    
    /// 记录清屏状态
    @objc public var screenState: Bool = false
    
    /// 记录播放状态
    @objc public var playState: Bool = true
    
    public var mediaType: WHMediaState = .normal
    
}
