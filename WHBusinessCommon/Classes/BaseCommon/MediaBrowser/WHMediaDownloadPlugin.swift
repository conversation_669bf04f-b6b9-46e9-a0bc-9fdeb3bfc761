//
//  WHMediaDownloadPlugin.swift
//  MTXX
//
//  Created by songgeb on 2019/7/30.
//  Copyright © 2019 Meitu. All rights reserved.
//

import UIKit
import SnapKit
import MTPhotoLibrary
import WHBaseLibrary
import WHBusinessCommon
import SDWebImage

public protocol NewMediaDownloadPluginDelegate: AnyObject {
  func beginLayout(_ plugin: WHMediaDownloadPlugin)
  func willBeginDownload(_ plugin: WHMediaDownloadPlugin)
  func didBeginDownload(_ plugin: WHMediaDownloadPlugin)
  func downloadCancelled(_ plugin: WHMediaDownloadPlugin)
  
  /// 下载并保存成功
  ///
  /// - Parameter plugin:
  func downloadSuccess(_ plugin: WHMediaDownloadPlugin)
  
  /// 下载或保存失败
  ///
  /// - Parameter plugin:
  func downloadFailure(_ plugin: WHMediaDownloadPlugin)
}

/// 包含UI的下载控件，可用于PhotoBrowserPlugin，也可单独使用，单独使用时需手动执行setup触发布局。 默认允许下载视频、图片不添加水印
public class WHMediaDownloadPlugin: NSObject {
    
    // MARK: - public property
    
    public weak var delegate: NewMediaDownloadPluginDelegate?
    /// 与shouldWaitExtraInfoToLayout配合使用，当其设置为true时，该值设置为true时会触发布局
    public var shouldContinueLayout = false {
        didSet {
            if delegate == nil || isLayoutFinished { return }
            if shouldContinueLayout {
                setupUI()
            }
        }
    }
    
    /// 是否允许下载视频
    public var allowDownloadVideo: Bool
    
    public enum WaterMarkType {
        case none
        case name(String) //水印上的字符
    }
    /// 是否需要添加水印
    public var watermarkType: WaterMarkType
    
    public enum MediaType {
        case image
        case video
    }
    
    /// 当使用setup的方式时，当外部资源index改变时，应主动修改该值
    public var currentIndex = 0
    
    public var mediaType: MediaType
    // 是否自定相册内图片名字
    public var isWriteImageName: Bool = false
    
    // MARK: - private UI property
    private weak var containerVC: UIViewController?
    private weak var containerView: UIView?
    /// 是否已经开始布局UI
    private var isSetupUI = false
    private var isLayoutFinished = false
    
    /// 若该值为ture，则需要等待delegate对象将shouldBeginLayout设置为true时才进行布局
    private let shouldWaitExtraInfoToLayout = false
    
    private lazy var downloadLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.text = WHLocalizedString("下载", comment: "")
        label.font = UIFont.systemFont(ofSize: 14)
        label.textAlignment = .center
        label.layer.shadowColor = UIColor.black.cgColor
        label.layer.shadowOpacity = 0.3
        label.layer.shadowRadius = 1
        label.layer.shadowOffset = .zero
        return label
    }()
    
    private lazy var downloadIcon: UIImageView = {
        let image = UIImage(named: "icon_download")
        let imageView = UIImageView(image: image)
        return imageView
    }()
    
    private lazy var downloadControl: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        v.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(downloadBtnTapped))
        tap.numberOfTapsRequired = 1
        v.addGestureRecognizer(tap)
        return v
    }()
    
    private lazy var longPressGesture: UILongPressGestureRecognizer = {
        let lp = UILongPressGestureRecognizer(target: self, action: #selector(longPressed(_:)))
        lp.delegate = self
        return lp
    }()
    
    private lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        return view
    }()
    
    /// 进度条底部的蒙层
    private let hudBgMaskView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
//        view.layer.cornerRadius = 4
//        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var progressView: WHCircleProgressView = {
        let make = WHCircleProgressView()
        return make
    }()
    
    private lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var videoCancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.layer.masksToBounds = true
        btn.layer.cornerRadius = 18
        btn.layer.borderWidth = 1
        btn.layer.borderColor = UIColor.white.cgColor
        btn.setTitle(WHLocalizedString("取消", comment: ""), for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        btn.addTarget(self, action: #selector(cancelBtnTapped), for: .touchUpInside)
        return btn
    }()
    
    // MARK: - private data property
    /// 当前media所在的位置
    private var showingDownloadIcon = false
    private var isVideoAd = false
    /// 原图或原视频地址
    public var originMediaUrlStrs: [String]
    
    // for cancelling
    //  private var videoWatermarkExporter: MTVideoWatermark?
    private var videoDownloadTask: URLSessionTask?
    private var cancelVideoDownloading = false
    
    // MARK: - public
    public init(
        mediaType: MediaType,
        containerVC: UIViewController,
        urlStrs: [String],
        allowDownloadVideo: Bool = true,
        watermarkType: WaterMarkType = .none) {
            self.mediaType = mediaType
            self.originMediaUrlStrs = urlStrs
            self.containerVC = containerVC
            self.allowDownloadVideo = allowDownloadVideo
            self.watermarkType = watermarkType
            self.isVideoAd = false
        }
    
    /// 手动触发布局(当用于PhotoBrowser的插件时，无需执行该方法)
    public func setup(useDefaultUI: Bool = true) {
        if isSetupUI { return }
        isSetupUI = true
        prepareUIData()
        if useDefaultUI {
            setupUI()
        }
    }
    
    public func start() {
        downloadBtnTapped()
    }
    
    // MARK: - private
    private func prepareUIData() {
        if mediaType == .video && !allowDownloadVideo {
            return
        }
        
        containerView = containerVC?.view
        delegate?.beginLayout(self)
        if delegate != nil && shouldWaitExtraInfoToLayout {
            return
        }
    }
    
    private func setupUI() {
        guard let vc = containerVC else { return }
        defer {
            isLayoutFinished = true
        }
        
        containerView?.addGestureRecognizer(longPressGesture)
        
        //add download icon for image media
        var bottomGuide: ConstraintItem
        if #available(iOS 11, *) {
            bottomGuide = containerView!.safeAreaLayoutGuide.snp.bottom
        } else {
            bottomGuide = vc.bottomLayoutGuide.snp.bottom
        }
        
        containerView!.addSubview(downloadIcon)
        containerView!.addSubview(downloadLabel)
        containerView!.addSubview(downloadControl)
        showingDownloadIcon = true
        downloadIcon.snp.makeConstraints { (make) in
            make.right.equalTo(self.downloadLabel.snp.left)
            make.bottom.equalTo(bottomGuide).offset(-8)
            make.width.height.equalTo(32)
        }
        downloadLabel.snp.makeConstraints { (make) in
            make.right.equalToSuperview().inset(16)
            make.centerY.equalTo(downloadIcon)
            make.width.equalTo(28)
            make.height.equalTo(18)
        }
        downloadControl.snp.makeConstraints { (make) in
            make.left.top.equalTo(self.downloadIcon).offset(-6)
            make.bottom.equalTo(self.downloadIcon).offset(6)
            make.right.equalTo(self.downloadLabel).offset(6)
        }
    }
    
    private func setupProgressView(in container: UIView) {
        container.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
//        bgView.addSubview(hudBgMaskView)
//        hudBgMaskView.snp.makeConstraints { (make) in
//            make.center.equalToSuperview()
//            make.width.equalTo(140)
//            make.height.equalTo(154)
//        }
        
        bgView.addSubview(progressView)
        bgView.addSubview(progressLabel)
        bgView.addSubview(videoCancelBtn)
        
        progressView.snp.makeConstraints { (make) in
            make.size.equalTo(60)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-47)
        }
        
        progressLabel.snp.makeConstraints { make in
            make.top.equalTo(progressView.snp.bottom).offset(22)
            make.centerX.equalToSuperview()
            make.height.equalTo(18)
        }
        
        videoCancelBtn.snp.makeConstraints { (make) in
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 100, height: 36))
            make.top.equalTo(progressLabel.snp.bottom).offset(16)
        }
        
        progressLabel.text = "正在保存中 0%"
        progressView.setProgress(0)
    }
    
    @objc private func downloadBtnTapped() {
        showVideoDownloadingNetworkAlertIfNeed { [weak self] (allowed) in
            guard let self = self else { return }
            if !allowed { return }
            self.checkLoginStatusAndDownload()
        }
    }
    
    @objc private func longPressed(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            let style: UIAlertController.Style = .actionSheet
            let alertController = UIAlertController(title: nil, message: nil, preferredStyle: style)
            let cancel = UIAlertAction(title: WHLocalizedString("取消", comment: ""), style: .cancel, handler: nil)
            let title = mediaType == .image ? WHLocalizedString("保存图片", comment: "") : WHLocalizedString("保存视频", comment: "")
            let download = UIAlertAction(title: title, style: .default) { [weak self] (action) in
                guard let self = self else { return }
                self.showVideoDownloadingNetworkAlertIfNeed { [weak self] (allowed) in
                    guard let self = self else { return }
                    if !allowed { return }
                    self.checkLoginStatusAndDownload()
                }
            }
            alertController.addAction(cancel)
            alertController.addAction(download)
            containerVC?.present(alertController, animated: true, completion: nil)
        }
    }
    // 当使用移动数据下载视频时，给用户提示，如果用户同意下载则block中返回true
    private func showVideoDownloadingNetworkAlertIfNeed(completion: @escaping (Bool) -> Void) {
        if mediaType == .image || WHNetworkChangeMonitorDefault.currentNetwork == .ethernetOrWiFi {
            completion(true)
            return
        }
        
        let alert = UIAlertController(title: nil, message: WHLocalizedString("当前使用的网络不是WiFi，是否继续下载", comment: ""), preferredStyle: .alert)
        let yes = UIAlertAction(title: WHLocalizedString("是", comment: ""), style: .default, handler: { (action) in
            completion(true)
        })
        let no = UIAlertAction(title: WHLocalizedString("否", comment: ""), style: .cancel, handler: { (action) in completion(false) })
        alert.addAction(yes)
        alert.addAction(no)
        containerVC?.present(alert, animated: true, completion: nil)
    }
    
    private func checkLoginStatusAndDownload() {
        delegate?.willBeginDownload(self)
        
        if !WHNetwork.isReachable() {
            return
        }
        
        //先登录
        if WHAccountShareManager.isLogin() {
            checkPhotoLibrary()
        } else {
            WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) { sucess, info in
                self.checkPhotoLibrary()
            } failure: {
            }
        }
    }
    //check相册权限
    func checkPhotoLibrary() {
        MTPhotoLibrary.requestAuthorization { (status) in
            switch status {
            case .authorized, .limited:
                self.downloadMedia()
            default:
                WHPhotoAlbumSharedManager.showAlbumAuthorizationAlert()
            }
        }
    }
}

// MARK: - 下载相关
extension WHMediaDownloadPlugin {
    private func downloadMedia() {
        guard let container = containerView else { return }
        
        if !WHNetwork.isReachable() {
            WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
            return
        }
        //点击icon后，控制view不能编辑
        disableAllGesturesButCancelBtn()
        setupProgressView(in: container.superview!)
        
        delegate?.didBeginDownload(self)
        
        if mediaType == .image {
            cancelVideoDownloading = false
            let downloadMediaUrl = URL(string: originMediaUrlStrs[currentIndex])
            SDWebImageDownloader.shared.downloadImage(with: downloadMediaUrl, options: [.highPriority, .progressiveLoad], progress: { (received, expected, url) in
                DispatchQueue.main.async {
                    var value = CGFloat(received) / CGFloat(expected)
                    if value.isFinite && !value.isNaN {
                        self.progressView.setProgress(Int(value * 100))
                        self.progressLabel.text = "正在保存中 \(Int(value * 100))%"
                    }
                }
            }) { [weak self] (image, data, error, finished) in
                guard let self = self else { return }
                if let _ = error {
                    self.clearStatusAfterDownloading()
                    if !self.cancelVideoDownloading {
                        WHRouter.topViewController?.showToast(title:WHLocalizedString("下载失败，请重试"))
                    }
                }
                if finished, let image = image {
                    
                    if self.cancelVideoDownloading {
                        return
                    }
                    //watermark
                    switch self.watermarkType {
                    case .none:
                        self.saveToAlbum(image, data)
                    case .name(let name):
                        if !image.sd_isAnimated {
                            var toSaveImage = image
                            if let resultImage = self.addWatermark(image, name: name) {
                                toSaveImage = resultImage
                            } else {
                                WHPrint("add watermark error when downloading image!")
                            }
                            self.saveToAlbum(toSaveImage)
                        }
                    }
                    
                    self.clearStatusAfterDownloading()
                }
            }
        } else {
            //download video, save to album
            guard let videoURL = URL(string: originMediaUrlStrs[currentIndex]) else {
                WHRouter.topViewController?.showToast(title: WHLocalizedString("网络好像出问题了，请重试。"))
                clearStatusAfterDownloading()
                return
            }
            cancelVideoDownloading = false
            let config = URLSessionConfiguration.default
            config.requestCachePolicy = .reloadIgnoringCacheData
            let session = URLSession(configuration: config, delegate: self, delegateQueue: nil)
            videoDownloadTask = session.downloadTask(with: videoURL)
            videoDownloadTask?.resume()
        }
    }
    
    fileprivate func disableAllGesturesButCancelBtn() {
        containerView?.isUserInteractionEnabled = false
    }
    
    fileprivate func enableAllGestures() {
        containerView?.isUserInteractionEnabled = true
    }
    
    fileprivate func clearStatusAfterDownloading() {
        enableAllGestures()
        let time = mediaType == .image ? 0.25 : 0
        DispatchQueue.main.asyncAfter(deadline: .now() + time) {
            self.videoCancelBtn.removeFromSuperview()
            self.progressView.removeFromSuperview()
            self.hudBgMaskView.removeFromSuperview()
            self.bgView.removeFromSuperview()
        }
        videoDownloadTask = nil
        //    videoWatermarkExporter = nil
    }
    
    @objc private func cancelBtnTapped() {
        cancelVideoDownloading = true
        
        if mediaType == .video {
            videoDownloadTask?.cancel()
            //        videoWatermarkExporter?.cancel()
        } else {
            SDWebImageDownloader.shared.cancelAllDownloads()
        }
        
        clearStatusAfterDownloading()
        
        delegate?.downloadCancelled(self)
    }
    
    //右下角添加水印
    private func addWatermark(_ image: UIImage, name: String) -> UIImage? {
        guard let waterMark = WHMediaWaterMarkHelper.waterMark(in: image.size, with: name) else {
            return nil
        }
        let padding = WHMediaWaterMarkHelper.padding(for: waterMark, in: image.size)
        let x = padding.left
        let y = image.size.height - (padding.bottom + waterMark.size.height)
        if x < 0 || y < 0 {
            return nil
        }
        UIGraphicsBeginImageContextWithOptions(image.size, true, image.scale)
        image.draw(at: .zero)
        waterMark.draw(at: CGPoint(x: x, y: y))
        let result = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return result
    }
    
    private func saveToAlbum(_ image: UIImage, _ data: Data? = nil) {
        MTPhotoLibrary.requestAuthorization { [weak self] (status) in
            guard let self = self else { return}
            switch status {
            case .authorized, .limited:
                let callback: (Bool, Error?, MTPhotoAsset?) -> () = { (success, error, _) in
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        if success {
                            WHRouter.topViewController?.showToast(title:WHLocalizedString("已保存到系统相册"))
                            self.delegate?.downloadSuccess(self)
                        } else {
                            WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
                            self.delegate?.downloadFailure(self)
                        }
                    }
                }
                if image.sd_isAnimated {
                    let data = SDImageGIFCoder.shared.encodedData(with: image, format: .GIF)
                    MTPhotoLibrary.shared().writeImageData(data!, metadata: nil, to: nil, resultBlock: callback)
                } else if let imageData = data {
                    if self.isWriteImageName {
                        let option = PHAssetResourceCreationOptions()
                        let time: String = self.timeString()
                        option.originalFilename = "whee_remover_\(time)"
                        MTPhotoLibrary.shared().writeImageData(imageData, metadata: nil, options: option, to: nil, resultBlock: callback)
                    } else {
                        MTPhotoLibrary.shared().writeImageData(imageData, metadata: nil, to: nil, resultBlock: callback)
                    }
                } else {
                    MTPhotoLibrary.shared().write(image, metadata: nil, to: nil, resultBlock: callback)
                }
                
            default:
                WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
                self.delegate?.downloadFailure(self)
                WHPhotoAlbumSharedManager.showAlbumAuthorizationAlert()
            }
        }
    }
  
    private func saveVideoToAlbum(fileURL: URL, autoDeleteFile: Bool = false) {
        MTPhotoLibrary.requestAuthorization { (status) in
            switch status {
            case .authorized, .limited:
                let callback: (Bool, Error?, MTPhotoAsset?) -> () = { (success, error, _) in
                    if autoDeleteFile {
                        try? FileManager.default.removeItem(at: fileURL)
                    }
                    if success {
                        self.delegate?.downloadSuccess(self)
                        WHRouter.topViewController?.showToast(title:WHLocalizedString("已保存到系统相册"))
                    } else {
                        self.delegate?.downloadFailure(self)
                        WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
                    }
                }
                MTPhotoLibrary.shared().writeMediaFileAtURL(
                    toSavedPhotosAlbum: fileURL,
                    mediaType: .video,
                    complete: callback)
            default:
                if autoDeleteFile {
                    try? FileManager.default.removeItem(at: fileURL)
                }
                WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
                WHPhotoAlbumSharedManager.showAlbumAuthorizationAlert()
            }
        }
    }
    
    func timeString() -> String {
        let date = Date()
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "yyyyMMddHHmmss"
        let currentTime = timeFormatter.string(from: date as Date) as String
        return currentTime
    }
}

//MARK: - video download
extension WHMediaDownloadPlugin: URLSessionDownloadDelegate {
    
    // MARK: - URLSessionDownloadDelegate
    public func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didFinishDownloadingTo location: URL) {
        
        if cancelVideoDownloading {
            return
        }
        
        //non main thread
        //move to cache directory
        guard let cacheDir = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).first else {
            DispatchQueue.main.async {
                self.clearStatusAfterDownloading()
            }
            return
        }
        
        func removeFileIfExist(_ url: URL) {
            do {
                if FileManager.default.fileExists(atPath: url.path) {
                    try FileManager.default.removeItem(at: url)
                }
            } catch {}
        }
        
        var locationURL = location
        var newFileName = locationURL.lastPathComponent
        if let fileExtension = URL(string: originMediaUrlStrs[currentIndex])?.pathExtension {
            locationURL.deletePathExtension()
            locationURL.appendPathExtension(fileExtension)
            newFileName = locationURL.lastPathComponent
        }
        
        let movedURL = URL(fileURLWithPath: cacheDir).appendingPathComponent(newFileName)
        do {
            removeFileIfExist(movedURL)
            try FileManager.default.moveItem(at: location, to: movedURL)
        } catch {
            DispatchQueue.main.async {
                self.clearStatusAfterDownloading()
            }
            return
        }
        
        DispatchQueue.main.async {
            
            if self.cancelVideoDownloading {
                removeFileIfExist(movedURL)
                return
            }
            let watermarkName: String
            switch self.watermarkType {
            case .none:
                self.saveVideoToAlbum(fileURL: movedURL, autoDeleteFile: true)
                self.clearStatusAfterDownloading()
                return
            case .name(let name):
                watermarkName = name
            }
            
            //add watermark
//            let asset = AVURLAsset(url: movedURL)
//            guard let track = asset.tracks(withMediaType: .video).first else {
//                self.clearStatusAfterDownloading()
//                removeFileIfExist(movedURL)
//                return
//            }
//            let videoSize = track.naturalSize
//
//            guard let watermark = MediaWaterMarkHelper.waterMark(in: videoSize, with: watermarkName) else {
//                self.clearStatusAfterDownloading()
//                removeFileIfExist(movedURL)
//                return
//            }
//            let padding = MediaWaterMarkHelper.padding(for: watermark, in: videoSize)
//            var location = CGRect(origin: CGPoint(x: padding.left,
//                                                  y: padding.bottom),
//                                  size: watermark.size)
//
//            if self.isVideoAd {
//                // 视频广告保存不需要加水印
//                location = .zero
//            }
//
//            guard let waterMarker = MTVideoWatermark(assetURL: movedURL, watermark: watermark, display: location)
//            else {
//                self.clearStatusAfterDownloading()
//                removeFileIfExist(movedURL)
//                self.delegate?.downloadFailure(self)
//                WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
//                return
//            }
//
//            self.videoWatermarkExporter = waterMarker
//            waterMarker.progressHandler = { [weak self] (percentage) in
//                guard let self = self else { return }
//                self.progressView.progress = 0.5 * percentage + 0.5
//            }
//            waterMarker.completionHandler = { [weak self] (result, savePath, assetURL, error) in
//                guard let self = self else { return }
//                defer {
//                    removeFileIfExist(movedURL)
//                }
//
//                switch result {
//                case .success:
//                    self.delegate?.downloadSuccess(self)
//                    WHRouter.topViewController?.showToast(title:WHLocalizedString("已保存到系统相册"))
//                case .failure:
//                    self.delegate?.downloadFailure(self)
//                    WHRouter.topViewController?.showToast(title:WHLocalizedString("未成功保存至相册"))
//                case .cancelled:
//                    return
//                }
//
//                self.clearStatusAfterDownloading()
//            }
//            waterMarker.export(withAutoSave: true)
        }
    }
  
    public func urlSession(_ session: URLSession, downloadTask: URLSessionDownloadTask, didWriteData bytesWritten: Int64, totalBytesWritten: Int64, totalBytesExpectedToWrite: Int64) {
        //non main thread
        //update progressview
        DispatchQueue.main.async {
            
            var progress: CGFloat = CGFloat(totalBytesWritten) / CGFloat(totalBytesExpectedToWrite)
            switch self.watermarkType {
            case .name(_):
                //下载视频且需要添加水印时，下载过程仅占进度条一半，另一半由加水印操作占用
                progress = progress * 0.5
            case .none:
                break
            }
            let value = Int(progress * 100)
            self.progressView.setProgress(value)
            self.progressLabel.text = "正在保存中 \(value)%"
        }
    }
    
    public func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let _ = error {
            DispatchQueue.main.async {
                if !self.cancelVideoDownloading {
                    self.delegate?.downloadFailure(self)
                    WHRouter.topViewController?.showToast(title:WHLocalizedString("下载失败，请重试"))
                    self.clearStatusAfterDownloading()
                }
            }
        }
    }
}

extension WHMediaDownloadPlugin: UIGestureRecognizerDelegate {
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        if otherGestureRecognizer is UILongPressGestureRecognizer {
            return true
        }
        return false
    }
}

open class WHProgressView: UIView {

    /// 进度
    open var progress: CGFloat = 0 {
        didSet {
            fanshapedLayer.path = makeProgressPath(progress).cgPath
        }
    }

    /// 外边界
    private var circleLayer: CAShapeLayer!

    /// 扇形区
    private var fanshapedLayer: CAShapeLayer!

    private let strokeCGColor: CGColor
    private let fillCGColor: CGColor
    private let fansLayerCGColor: CGColor
    
    // 进度条半径
    private var fanshapedRadius: CGFloat = 0

    convenience override init(frame: CGRect) {
        let color = UIColor(wh_hexString: "#e7e7e7")
        self.init(frame: .zero , strokeColor: color, fansLayerColor: color)
    }
  
    public init(frame: CGRect, strokeColor: UIColor, fansLayerColor: UIColor) {
        let viewFrame = frame.equalTo(.zero) ? CGRect(x: 0, y: 0, width: 50, height: 50) : frame
        self.strokeCGColor = strokeColor.cgColor
        self.fillCGColor = UIColor.clear.cgColor
        self.fansLayerCGColor = fansLayerColor.cgColor
        super.init(frame: viewFrame)
        fanshapedRadius = bounds.size.width/2 - 2.5
        setupUI()
        progress = 0
    }
    
    public init(frame: CGRect, fillColor: UIColor, fansLayerColor: UIColor) {
        let viewFrame = frame.equalTo(.zero) ? CGRect(x: 0, y: 0, width: 50, height: 50) : frame
        self.strokeCGColor = UIColor.clear.cgColor
        self.fillCGColor = fillColor.cgColor
        self.fansLayerCGColor = fansLayerColor.cgColor
        super.init(frame: viewFrame)
        fanshapedRadius = bounds.size.width/2 - 2.5
        setupUI()
        progress = 0
    }

    public required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = UIColor.clear

        circleLayer = CAShapeLayer()
        circleLayer.strokeColor = strokeCGColor
        circleLayer.fillColor = fillCGColor
        circleLayer.path = makeCirclePath().cgPath
        layer.addSublayer(circleLayer)

        fanshapedLayer = CAShapeLayer()
        fanshapedLayer.fillColor = fansLayerCGColor
        layer.addSublayer(fanshapedLayer)
    }

    private func makeCirclePath() -> UIBezierPath {
        let arcCenter = CGPoint(x: bounds.midX, y: bounds.midY)
        let path = UIBezierPath(
          arcCenter: arcCenter,
          radius: bounds.size.width/2,
          startAngle: 0,
          endAngle: CGFloat.pi * 2,
          clockwise: true)
        path.lineWidth = 1
        return path
    }

    private func makeProgressPath(_ progress: CGFloat) -> UIBezierPath {
        let center = CGPoint(x: bounds.midX, y: bounds.midY)
        fanshapedRadius = fanshapedRadius > 0 ? fanshapedRadius : center.y - 2.5
        let path = UIBezierPath()
        path.move(to: center)
        path.addLine(to: CGPoint(x: bounds.midX, y: center.y - fanshapedRadius))
        path.addArc(
          withCenter: center,
          radius: fanshapedRadius,
          startAngle: -CGFloat.pi / 2,
          endAngle: -CGFloat.pi / 2 + CGFloat.pi * 2 * progress,
          clockwise: true)
        path.close()
        path.lineWidth = 1
        return path
    }
    
    /// 清除边框
    public func clearCircleLayer() {
        setCircleColor(circleColor: UIColor.clear.cgColor)
        fanshapedRadius = bounds.size.width/2
    }
    
    /// 设置边框颜色
    public func setCircleColor(circleColor: CGColor) {
        circleLayer.strokeColor = circleColor
    }
}

public class WHCircleProgressView: UIView {
    // 灰色静态圆环
    var staticLayer: CAShapeLayer!
    // 进度可变圆环
    var arcLayer: CAShapeLayer!
    
    // 为了显示更精细，进度范围设置为 0 ~ 1000
    var progress = 0

    override init(frame: CGRect) {
        super.init(frame: frame)

    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setProgress(_ progress: Int) {
        self.progress = progress
        setNeedsDisplay()
    }
    
    public override func draw(_ rect: CGRect) {
        if staticLayer == nil {
            staticLayer = createLayer(100, UIColor(white: 1, alpha: 0.25))
        }
        self.layer.addSublayer(staticLayer)
        if arcLayer != nil {
            arcLayer.removeFromSuperlayer()
        }
        arcLayer = createLayer(self.progress, UIColor(white: 1, alpha: 1))
        self.layer.addSublayer(arcLayer)
    }
    
    private func createLayer(_ progress: Int, _ color: UIColor) -> CAShapeLayer {
        let endAngle = -CGFloat.pi / 2 + (CGFloat.pi * 2) * CGFloat(progress) / 100
        let layer = CAShapeLayer()
        layer.lineWidth = 4
        layer.strokeColor = color.cgColor
        layer.fillColor = UIColor.clear.cgColor
        let radius = self.bounds.width / 2 - layer.lineWidth
        let path = UIBezierPath.init(arcCenter: CGPoint(x: bounds.width / 2, y: bounds.height / 2), radius: radius, startAngle: -CGFloat.pi / 2, endAngle: endAngle, clockwise: true)
        layer.path = path.cgPath
        return layer
    }
}
