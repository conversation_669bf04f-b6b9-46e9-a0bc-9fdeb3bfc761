//
//  WHMediaWaterMarkHelper.swift
//  MTXX
//
//  Created by Xfcmy on 2019/2/12.
//  Copyright © 2019 Meitu. All rights reserved.
//

import Foundation
import UIKit

// 根据图片或者视频的尺寸以及用户名生成对应的水印贴图
// 用于在为视频或图片添加水印时合成
// 由于规则是根据尺寸来，对于某些小图来说放大后会存在模糊现象
// 所以在大图页展示水印时不使用该规则来生成图片
// 而是直接使用UIImageView和UILabel展示水印

typealias WaterMarkPadding = (left: CGFloat, bottom: CGFloat)

class WHMediaWaterMarkHelper {
    private static func newNameLabel() -> UILabel {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 12)
        label.textAlignment = .left
        label.isUserInteractionEnabled = false
        label.layer.shadowColor = UIColor.black.cgColor
        label.layer.shadowOpacity = 0.3
        label.layer.shadowRadius = 1
        label.layer.shadowOffset = .zero
        return label
    }
    
    static func padding(for waterMark: UIImage, in containerSize: CGSize) -> WaterMarkPadding {
        let width = containerSize.width
        let left = min(width * 0.0198, 20)
        let bottom = min(width * 0.0133, 15)
        return (left, bottom)
    }
    
    /// 为视频/图片添加水印时，合成的水印图片（包括logo+username）
    ///
    /// - Parameter containerSize: image size or video size
    /// - Returns: 水印
    static func waterMark(in containerSize: CGSize, with userName: String) -> UIImage? {
        
        let tmp = UIImage(named:"ic_fullScreen_waterMark")
        guard let logo = tmp else {
            assert(false, "水印图片不能为空")
            return nil
        }
        
        let nameLabel = newNameLabel()
        nameLabel.textColor = UIColor(white: 1, alpha: 0.7)
        nameLabel.font = UIFont.systemFont(ofSize: containerSize.width * 0.029)
        nameLabel.textAlignment = .left
        nameLabel.layer.shadowColor = UIColor(white: 0, alpha: 0.5).cgColor
        nameLabel.layer.shadowOffset = CGSize(width: 0, height: 0.5)
        nameLabel.layer.shadowOpacity = 0.5
        nameLabel.layer.shadowRadius = 0.5
        nameLabel.text = userName
        nameLabel.sizeToFit()
        let labelW = nameLabel.frame.size.width
        let labelH = nameLabel.frame.size.height
        let logoW = containerSize.width * 0.1030
        let logoH = logoW * 0.465
        let interval = containerSize.width * 0.0040
        
        let size = CGSize(width: logoW + interval + labelW, height: max(logoH, labelH))
        if size.width > containerSize.width || size.height > containerSize.height {
            return nil
        }
        //draw text
        //logo has alpha channel
        UIGraphicsBeginImageContextWithOptions(nameLabel.bounds.size, false, UIScreen.main.scale)
        guard let labelContext = UIGraphicsGetCurrentContext() else {
            return nil
        }
        nameLabel.layer.render(in: labelContext)
        guard let labelImage = UIGraphicsGetImageFromCurrentImageContext() else {
            return nil
        }
        UIGraphicsEndImageContext()
        //draw logo + labelImage
        UIGraphicsBeginImageContextWithOptions(size, false, UIScreen.main.scale)
        let logoY = size.height / 2 - logoH / 2
        let labelY = size.height / 2 - labelH / 2
        logo.draw(in: CGRect(x: 0, y: logoY, width: logoW, height: logoH))
        labelImage.draw(in: CGRect(origin: CGPoint(x: logoW + interval, y: labelY), size: labelImage.size))
        let result = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return result
    }
}
