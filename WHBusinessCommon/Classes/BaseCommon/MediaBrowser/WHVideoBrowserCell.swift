//
//  WHVideoBrowserCell.swift
//  MTXX
//
//  Created by meitu on 2020/6/30.
//  Copyright © 2020 Meitu. All rights reserved.
//

import Foundation
import UIKit
import AVFoundation
import Lottie
import WHBaseLibrary

@objc
public enum WHVideoBrowserControlStyle: Int {
    case fullScreen = 0                       /**< 全屏控制器，可隐藏 */
    case halfScreen = 1                       /**< 半屏控制器，不可隐藏 */
}

class WHVideoBrowserCell: UIView, JXPhotoBrowserCell , JXPhotoBrowserZoomSupportedCell {
    
    open var showContentView: UIView {
        return playerView
    }
    
    weak var photoBrowser: WHMediaBrowser?
    
    ///视频控制器的样式
    public var controlStyle: WHVideoBrowserControlStyle = .fullScreen {
        didSet {
            if controlStyle == .halfScreen && controlPlugin != nil {
                controlPlugin?.hideControlViews = true
                createHalfController()
            }
        }
    }
    
    private var isFirstLoad: Bool = true       //第一次加载
    
    ///播放相关参数
    private let shouldAutoPlay: Bool = true     //是否自动播放
    private var shouldAutoLoop: Bool = false     //是否循环播放
    var paused: Bool = false             //暂停状态
    public var isFromIM = false 
    
    fileprivate var firstTransform = CGAffineTransform.identity
    
    private var playerContainer = UIView(backgroundColor:.clear)
    private var playerView = UIView(backgroundColor: .clear)          //播放容器
    //播放器背景
    private var coverImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    ///播放控制组件
    private var controlPlugin: WHMediaPlayControllerPlugin?
    
    private var loadingView: LottieAnimationView = {
        let make = LottieAnimationView.init(name: "video_loading")
        make.loopMode = .loop
        make.isUserInteractionEnabled = false
        return make
    }()
    
    public lazy var playIcon: UIButton = {
        let b = UIButton()
        let blurEffect = UIBlurEffect(style: .dark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame.size = CGSize(width: 70, height:70)
        blurView.isUserInteractionEnabled = false
        b.addSubview(blurView)
        b.setImage(UIImage(named: "icon_detail_play_small"),
                   for: UIControl.State.normal)
        b.bringSubviewToFront(b.imageView ?? UIView())
        b.layer.cornerRadius = 35
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(tapAction), for: .touchUpInside)
        return b
    }()
    
    private lazy var playIMIcon: UIImageView? = {
        if photoBrowser?.isFromIM == true { return nil }
        let btn = UIImageView(image: UIImage(named: "icon_detail_play"))
        return btn
    }()
    
    private lazy var backIcon: UIButton = {
        let btn = UIButton()
        btn.setImage(UIImage(named: "icon_back_white_bload"), for: .normal)
        btn.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return btn
    }()
    
    private lazy var videoProgressBar: WHFullScreenVideoPlaySlider = {
        let make = WHFullScreenVideoPlaySlider()
        make.delegate = self
        make.seekHandler = { time in
            let adjustTime = TimeInterval(time) + 1.5
            VideoPlayerManager.shared.seek(toTime: adjustTime)
        }
        return make
    }()
    
    ///滑动方向
    open var scrollDirection: JXPhotoBrowser.ScrollDirection = .horizontal {
        didSet {
            if scrollDirection == .vertical,let existed = existedPan {
                self.removeGestureRecognizer(existed)
            }
        }
    }
    /// 控制器的隐藏状态
    private var menuHide = true
    ///手势相关
    private var panGesture: UIPanGestureRecognizer!
    private var tapGesture: UITapGestureRecognizer!
    private weak var existedPan: UIPanGestureRecognizer?
    private var beganFrame = CGRect.zero                   /// 记录pan手势开始时imageView的位置
    private var beganTouch = CGPoint.zero                  /// 记录pan手势开始时，手势位置
    var isPaning: Bool = false
    // 长按事件
    public typealias LongPressAction = (WHVideoBrowserCell, UILongPressGestureRecognizer) -> Void
    /// 长按时回调。赋值时自动添加手势，赋值为nil时移除手势
    open var longPressedAction: LongPressAction? {
        didSet {
            if oldValue != nil && longPressedAction == nil {
                removeGestureRecognizer(longPress)
            } else if oldValue == nil && longPressedAction != nil {
                addGestureRecognizer(longPress)
            }
        }
    }
    /// 已添加的长按手势
    private lazy var longPress: UILongPressGestureRecognizer = {
        return UILongPressGestureRecognizer(target: self, action: #selector(onLongPress(_:)))
    }()
    // 双击事件
    public typealias DoubleTapAction = (WHVideoBrowserCell, UITapGestureRecognizer) -> Void
    /// 长按时回调。赋值时自动添加手势，赋值为nil时移除手势
    open var doubleTapAction: DoubleTapAction? {
        didSet {
            if oldValue != nil && doubleTapAction == nil {
                removeGestureRecognizer(doubleTap)
            } else if oldValue == nil && doubleTapAction != nil {
                addGestureRecognizer(doubleTap)
            }
        }
    }
    /// 已添加的长按手势
    private lazy var doubleTap: UITapGestureRecognizer = {
        
        let doubleTap = UITapGestureRecognizer(target: self, action: #selector(onDoubleTap(_:)))
        doubleTap.numberOfTapsRequired = 2
        return doubleTap
    }()
    
    
    ///水印相关
    private var logo: UIImageView = {
        let make = UIImageView()
        return make
    }()
    
    private var usernameLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(white: 1, alpha: 0.7)
        label.font = UIFont.systemFont(ofSize: 12.0)
        label.textAlignment = .left
        label.layer.shadowColor = UIColor(white: 0, alpha: 0.5).cgColor
        label.layer.shadowOffset = CGSize(width: 0, height: 0.5)
        label.layer.shadowOpacity = 0.5
        label.layer.shadowRadius = 0.5
        return label
    }()
    
    /// 水印上的用户名
    open var username: String? {
        didSet {
            usernameLabel.text = username
        }
    }
    ///是否显示水印
    open var showWatermark: Bool = false {
        didSet {
            logo.isHidden = !showWatermark
            usernameLabel.isHidden = !showWatermark
        }
    }
    ///赋值的model 用来刷新数据
    open var mediaModel: WHMediaBrowserModel? {
        didSet {
            reloadTheSubViews()
        }
    }
    
    open var scrollView: UIScrollView = {
        let view = UIScrollView()
        view.maximumZoomScale = 1.0
        view.showsVerticalScrollIndicator = false
        view.showsHorizontalScrollIndicator = false
        if #available(iOS 11.0, *) {
            view.contentInsetAdjustmentBehavior = .never
        }
        return view
    }()
    
    // MARK: - 生命周期
    static func generate(with browser: JXPhotoBrowser) -> Self {
        let instance = Self.init(frame: .zero)
        instance.photoBrowser = browser as? WHMediaBrowser
        return instance
    }
    
    required override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        
        createSubViews()
        configureGestureControl()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        scrollView.frame = bounds
        if isFirstLoad {
            reloadTheSubViews()
        }
        if let isIm = photoBrowser?.isFromIM, isIm {
            playIMIcon?.alpha = 1
        } else {
            playIMIcon?.alpha = 0
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 私有方法
    func createSubViews() {
        
        addSubview(scrollView)
        scrollView.addSubview(playerView)
        playerView.addSubview(coverImageView)
        playerView.addSubview(logo)
        playerView.addSubview(usernameLabel)
        playerView.addSubview(loadingView)
        loadingView.isHidden = false
        loadingView.snp.makeConstraints { (make) in
            make.center.equalToSuperview()
            make.width.height.equalTo(65)
        }
        if self.controlStyle == .fullScreen {
            if UIViewController.wh_top() != nil {
                controlPlugin = WHMediaPlayControllerPlugin(duration: 100, shouldAutoPlay: shouldAutoPlay)
                controlPlugin?.delegate = self
            }
            playerView.addSubview(playIcon)
            playIcon.snp.makeConstraints { (make) in
                make.center.equalToSuperview()
                make.width.height.equalTo(70)
            }
        }
        let autoPlay = VideoPlayerManager.shared.isPlaying || VideoPlayerManager.shared.isLoading
        if autoPlay {
            playIcon.isHidden = true
        }
    }
    
    ///半屏控制器
    private func createHalfController() {
        
        addSubview(backIcon)
        self.addSubview(videoProgressBar)
        playIcon.snp.makeConstraints { (make) in
            make.center.equalToSuperview()
            make.width.height.equalTo(96)
        }
        videoProgressBar.snp.makeConstraints { (make) in
            make.bottom.equalToSuperview().offset(-5)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(84)
        }
    }
    ///退出按钮点击事件
    @objc func closeAction() {
        self.photoBrowser?.dismiss()
    }
    
    func reloadTheSubViews() {
        
        self.controlPlugin?.resetControlViewsState()
        
        if scrollView.frame.size == CGSize.zero {
            return
        }
        isFirstLoad = false
        
        var size = computeImageLayoutSize(model: mediaModel)
        if size == .zero {
            size = scrollView.bounds.size
        }
        if self.controlStyle == .halfScreen {
            
        } else {
            let topVC = UIViewController.wh_top()
            if let control = controlPlugin {
                control.setupUIForFullScreenVC(topVC, view: self, style: .media)
                if isFromIM {
                    control.resetControlViewsState()
                }
            }
        }
        
        playerView.snp.remakeConstraints { (make) in
            make.center.equalToSuperview()
            make.size.equalTo(size)
        }
        coverImageView.snp.remakeConstraints { (make) in
            make.center.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalToSuperview()
        }
        logo.snp.makeConstraints { (make) in
            make.left.equalTo(10)
            make.bottom.equalTo(-10)
            make.width.equalTo(43)
            make.height.equalTo(20)
        }
        usernameLabel.snp.makeConstraints { (make) in
            make.left.equalTo(logo.snp.right).offset(4)
            make.centerY.equalTo(logo)
            make.width.equalTo(WH_SCREEN_WIDTH - 67)
            make.height.equalTo(17)
        }
        
        var cover = mediaModel?.coverUrl
        if cover == nil || cover == "" {
            cover = mediaModel?.thumb
        }
        coverImageView.sd_setImage(with: URL.init(string: cover ?? ""), completed: nil)
    }
    
    private func computeImageLayoutSize(model: WHMediaBrowserModel?) -> CGSize {
        
        guard let model = model, model.mediaWidth > 0 && model.mediaHeight > 0 else {
            return .zero
        }
        var width: CGFloat
        var height: CGFloat
        let containerSize = scrollView.bounds.size
        if scrollDirection == .horizontal {
            // 横竖屏判断
            if containerSize.width < containerSize.height {
                width = containerSize.width
                height = model.mediaHeight / model.mediaWidth * WH_SCREEN_WIDTH
            } else {
                height = containerSize.height
                width = model.mediaWidth / model.mediaHeight * height
                if width > containerSize.width {
                    width = containerSize.width
                    height = model.mediaHeight / model.mediaWidth * width
                }
            }
        } else {
            width = containerSize.width
            height = model.mediaHeight / model.mediaWidth * width
            if height > containerSize.height {
                height = containerSize.height
                width = model.mediaWidth / model.mediaHeight * height
            }
        }
        
        return CGSize(width: width, height: height)
    }
    
    ///加载视频
    func playload() {
        
        if controlStyle == .halfScreen {
            videoProgressBar.duration = TimeInterval(self.mediaModel?.duration ?? 0)
        } else {
            self.controlPlugin?.resetControlViewsState()
            self.controlPlugin?.duration = self.mediaModel?.duration ?? 0
        }
        
        guard let videoUrl = self.mediaModel?.mediaUrl,videoUrl.count > 0 else {
            return
        }
        VideoPlayerManager.shared.load(videoId: self.mediaModel?.mediaID,
                                       playerContentMode: .scaleAspectFit,
                                           url: videoUrl,
                                    coverImage: self.coverImageView.image,
                                            on: playerView,
                                      autoLoop: shouldAutoLoop,
                                      preservedCallbacks: [])?
        .loading(capture: self, block: { (self, currentId) in
            self.paused = false
            self.loadingView.isHidden = false
            self.playIcon.isHidden = true
            self.playIMIcon?.isHidden = true
            self.loadingView.play()
            self.controlPlugin?.playButton.isSelected = false
        })
        .playing(capture: self, block: { (self, currentId) in
            self.paused = false
            self.loadingView.isHidden = true
            self.playIcon.isHidden = true
            self.playIMIcon?.isHidden = true
            self.loadingView.pause()
            self.controlPlugin?.playButton.isSelected = false
        })
        .paused(capture: self, block: { (self, currentId) in
            self.paused = true
            self.loadingView.isHidden = true
            self.loadingView.pause()
            self.playIcon.isHidden = false
            self.playIMIcon?.isHidden = false
            self.controlPlugin?.playButton.isSelected = true
        })
        .stopped(capture: self, block: { (self, currentId) in
            self.paused = false
            self.loadingView.isHidden = true
            self.loadingView.pause()
            self.playIcon.isHidden = true
            self.playIMIcon?.isHidden = true
            self.controlPlugin?.playButton.isSelected = true
        })
        .onPrrogress(capture: self, block: { (self, currentId) in
            guard currentId != nil else { return }
            let time = VideoPlayerManager.shared.playedTime / 1000
            if self.controlStyle == .halfScreen {
                self.videoProgressBar.time = TimeInterval(time)
            } else {
                self.controlPlugin?.updatePlayedTime(time, playedPercentage: VideoPlayerManager.shared.playedPercent)
            }
        })
        .onReachEnd(capture: self, block: { (self, currentId) in
            if !self.shouldAutoLoop {
                VideoPlayerManager.shared.pause()
            }
        })
        .done()
        
        // 防止播放视图盖住水印
        logo.superview?.bringSubviewToFront(logo)
        usernameLabel.superview?.bringSubviewToFront(usernameLabel)
        loadingView.superview?.bringSubviewToFront(loadingView)
        playIcon.superview?.bringSubviewToFront(playIcon)
        if let player = playIMIcon {
            player.superview?.bringSubviewToFront(player)
        }
    }
    
    func autoPlay() {
        
        if self.shouldAutoPlay, self.mediaModel?.playState ?? true {
            play()
        }
    }
    
    func play() {
        self.mediaModel?.playState = true
        VideoPlayerManager.shared.player.audioVolume = 1
        if VideoPlayerManager.shared.isPlaying || VideoPlayerManager.shared.isLoading {
            return
        }
        VideoPlayerManager.shared.play()
    }
    
    func isPlaying() -> Bool {
        return VideoPlayerManager.shared.isPlaying
    }
    
    func stop() {
        self.mediaModel?.playState = true
        VideoPlayerManager.shared.stop()
    }
    
    func pause() {
        self.mediaModel?.playState = false
        if VideoPlayerManager.shared.isPlaying {
            VideoPlayerManager.shared.pause()
        }
    }
    
    // MARK: 手势相关
    
    open override func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // 只处理pan手势
        guard let pan = gestureRecognizer as? UIPanGestureRecognizer else {
            return true
        }
        let velocity = pan.velocity(in: self)
        // 向上滑动时，不响应手势
        if velocity.y < 0 {
            return true
        }
        // 横向滑动时，不响应pan手势
        if abs(Int(velocity.x)) > Int(velocity.y) {
            return false
        }
        // 向下滑动，如果图片顶部超出可视区域，不响应手势
        if scrollView.contentOffset.y > 0 {
            return false
        }
        // 响应允许范围内的下滑手势
        return true
    }
    ///添加手势
    private func configureGestureControl() {
        
        panGesture = PanDirectionGestureRecognizer(
        axis: .vertical,
        target: self,
        action: #selector(panGestureRecognized(_:)))
        panGesture.minimumNumberOfTouches = 1
        panGesture.maximumNumberOfTouches = 1
        
        if scrollDirection == .horizontal {
            panGesture.delegate = self
            scrollView.addGestureRecognizer(panGesture)
            existedPan = panGesture
        }

        tapGesture = UITapGestureRecognizer(target: self, action: #selector(tapAction))
        tapGesture.numberOfTapsRequired = 1
        tapGesture.require(toFail: doubleTap)
        self.addGestureRecognizer(tapGesture)
        
        self.addGestureRecognizer(longPress)
    }
    
    ///拖动手势
    @objc private func panGestureRecognized(_ pan: UIPanGestureRecognizer) {
        
        if photoBrowser?.scrollDirection == .vertical {
            return
        }
        var translatedPoint: CGPoint = pan.translation(in: scrollView)
        switch pan.state {
        case .began:
            beganFrame = playerView.frame
            beganTouch = pan.location(in: scrollView)
            self.menuHide = controlPlugin?.hideControlViews ?? true
        case .changed:
            let result = panResult(pan)
            
            translatedPoint = CGPoint(x: beganFrame.wh_center().x + translatedPoint.x,
                                      y: beganFrame.wh_center().y + translatedPoint.y)
            playerView.center = translatedPoint
            let scale = CGAffineTransform(scaleX: result.scale, y: result.scale)
            playerView.transform = scale
            photoBrowser?.maskView.alpha = result.scale * result.scale
            photoBrowser?.setStatusBar(hidden: result.scale > 0.99)
//            photoBrowser?.pageIndicator?.isHidden = result.scale < 0.99
            if !menuHide {
                controlPlugin?.hideControlViews = true
            }
            photoBrowser?.viewshouldHide()
            photoBrowser?.browserView.scrollView.isScrollEnabled = false
            backIcon.isHidden = true
            videoProgressBar.isHidden = true
        case .ended, .cancelled:
            let isDown = abs(pan.velocity(in: self).y) > 40
            if isDown {
                photoBrowser?.dismiss()
            } else {
                photoBrowser?.maskView.alpha = 1.0
                photoBrowser?.setStatusBar(hidden: true)
//                photoBrowser?.pageIndicator?.isHidden = false
                if !menuHide {
                    controlPlugin?.hideControlViews = false
                } else {
                    photoBrowser?.viewshouldShow()
                }
                resetVideoViewPosition()
                backIcon.isHidden = false
                videoProgressBar.isHidden = false
            }
            photoBrowser?.browserView.scrollView.isScrollEnabled = true
        default:
            resetVideoViewPosition()
            photoBrowser?.browserView.scrollView.isScrollEnabled = true
        }
    }
    
    /// 长按
    @objc open func onLongPress(_ press: UILongPressGestureRecognizer) {
        if press.state == .began {
            longPressedAction?(self, press)
        }
    }

    /// 双击
    @objc open func onDoubleTap(_ tap: UITapGestureRecognizer) {
        
        if tap.state == UIGestureRecognizer.State.ended {
            if tapGesture.isEnabled {
                tapGesture.isEnabled = false
            }
            NSObject.cancelPreviousPerformRequests(withTarget: self)
            // 延迟单击手势响应，使双击点赞可以不容易被打断
            self.perform(#selector(delaySingleTapEnabled), with: nil, afterDelay: 0.8)
            doubleTapAction?(self, tap)
        }
    }
    
    @objc func delaySingleTapEnabled() {
        tapGesture.isEnabled = true
    }
    
    /// 单击
    @objc private func tapAction() {
        if self.controlStyle == .halfScreen {
            if playIcon.isHidden {
                playIcon.isHidden = false
                pause()
            } else {
                playIcon.isHidden = true
                play()
            }
            
        } else {
            if playIcon.isHidden {
                playIcon.isHidden = false
                pause()
            } else {
                playIcon.isHidden = true
                play()
                WHAnalyticsManager.otherTrackEvent("whee_results_page_click", params: ["click_type": "play_click","source":"ai_to_video"])
            }
//            if !isFromIM {
                controlPlugin?.toggleControlHiddenStatus()
//            }
        }
    }
    
    /// 计算拖动时图片应调整的frame和scale值
    private func panResult(_ pan: UIPanGestureRecognizer) -> (frame: CGRect, scale: CGFloat) {
        // 拖动偏移量
        let translation = pan.translation(in: scrollView)
        let currentTouch = pan.location(in: scrollView)
        
        // 由下拉的偏移值决定缩放比例，越往下偏移，缩得越小。scale值区间[0.3, 1.0]
        let scale = min(1.0, max(0.3, 1 - abs(translation.y) / bounds.height))
        
        let width = beganFrame.size.width * scale
        let height = beganFrame.size.height * scale
        
        // 计算x和y。保持手指在图片上的相对位置不变。
        // 即如果手势开始时，手指在图片X轴三分之一处，那么在移动图片时，保持手指始终位于图片X轴的三分之一处
        let xRate = (beganTouch.x - beganFrame.origin.x) / beganFrame.size.width
        let currentTouchDeltaX = xRate * width
        let x = currentTouch.x - currentTouchDeltaX
        
        let yRate = (beganTouch.y - beganFrame.origin.y) / beganFrame.size.height
        let currentTouchDeltaY = yRate * height
        let y = currentTouch.y - currentTouchDeltaY
        
        return (CGRect(x: x.isNaN ? 0 : x, y: y.isNaN ? 0 : y, width: width, height: height), scale)
    }
    
    /// 复位ImageView
    private func resetVideoViewPosition() {
        
        UIView.animate(withDuration: 0.25) {
            self.playerView.center = self.beganFrame.wh_center()
            self.playerView.transform = CGAffineTransform.identity
        }
    }
}

// MARK: - 代理方法
extension WHVideoBrowserCell: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

// MARK: 播放器代理
extension WHVideoBrowserCell: MediaPlayControllerPluginDelegate {
    
    func mediaPlayControllerPluginContinuePlay() {
        play()
    }

    func mediaPlayControllerPluginPausePlay() {
        pause()
    }

    func mediaPlayControllerPluginClose() {

        self.photoBrowser?.dismiss()
    }
    
    func mediaPlayControllerDisplayState(display :Bool) {
      
        hanleTheMenuView(display)
    }
    
    private func hanleTheMenuView(_ shouldShow: Bool) {
        
        if shouldShow {
            self.photoBrowser?.viewshouldShow(isPaning: false)
            self.backIcon.isHidden = false
        } else {
            self.photoBrowser?.viewshouldHide(isPaning: false)
            self.backIcon.isHidden = true
        }
    }
}

extension WHVideoBrowserCell {
    //返回该view所在VC
    func firstViewController() -> UIViewController? {
        for view in sequence(first: self.superview, next: { $0?.superview }) {
            if let responder = view?.next {
                if responder.isKind(of: UIViewController.self) {
                    return responder as? UIViewController
                }
            }
        }
        return nil
    }
}

extension WHVideoBrowserCell: FullScreenVideoPlaySliderDelegate {
    func didBeginDragging(_ slider: WHFullScreenVideoPlaySlider) {
        self.photoBrowser?.beginDragging()
    }
    
    func didEndDragging(_ slider: WHFullScreenVideoPlaySlider) {
        self.photoBrowser?.endDragging()
    }
}

public extension UIView {
    convenience init(backgroundColor: UIColor) {
        self.init()
        self.backgroundColor = backgroundColor
    }
}
