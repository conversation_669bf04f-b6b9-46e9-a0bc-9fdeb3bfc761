//
//  VideoPlayerManager.swift
//  MTXX
//
//  Created by daemon on 2017/9/30.
//  Copyright © 2017年 Meitu. All rights reserved.
//

import UIKit
import MTAPM
import MTMFFPlayer
import AVFAudio
//import WHBusinessCommon
import WHBaseLibrary

public let kFeedCoverMaxPlayedTime: UInt = 3500

// 目前VideoPlayerManager支持的external player只支持播放到完毕1次
// 当播放1次完毕, 或者通过其他路径收到stop消息时, 会自动从`VideoPlayerManager`中移除
public protocol ExternalViderPlayerHost {
    /// 实现者需要在play到结尾时, 自动回调最近一次play传入的`onComplete`方法
    func play(onComplete: @escaping(() -> Void))
    func stop()
    func pause()
    
    var previewView: UIView { get }
    var isMute: Bool { get set }
}

public protocol VideoPlayerAutoPlayDelegate {
    
    // channel
    func playerChannel() -> String
    
    // scm
    func playerScm() -> String
    
    // player position
    func position() -> Int
}

@objc final public class VideoPlayerManager: NSObject {
    
    public static let didFinishFirstPlayNotification = NSNotification.Name(rawValue: "VideoPlayerManagerDidFinishFirstPlayNotification")
    
    @objc public private(set) var isPause: Bool = false
    /// 视频是否正在播放
    @objc public private(set) var isPlaying: Bool = false
    /// 是否处在加载状态
    @objc public private(set) var isLoading: Bool = false
    
    @objc public private(set) var currentId: String?
    @objc public private(set) var loopCount: Int = 0
    @objc public private(set) var playedTime: Int = 0
    @objc public var isLive: Bool = false {
        didSet {
            self.player.player.useProxy = !isLive
        }
    }
    
    /// 当存在`externalPlayer`时, 优先播放externalPlayer, 没有才使用`player`
    public var externalPlayer: ExternalViderPlayerHost?
    
    @objc public private(set) var preContentMode: UIView.ContentMode = .scaleAspectFit
    
    @objc public private(set) dynamic var playedPercent: CGFloat = 0.0
    
    @objc public var hasShowMuteGuide: Bool = false
    
    /// played表示已经执行了play()方法，但不一定真的在播放状态
    private var playedInfo: (Bool, String?) = (false, nil)
    
    fileprivate(set) lazy var playerContainer: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = false
        return view
    }()
    
    public var player: WHStreamingPlayer = {
        let player = WHStreamingPlayer()
        player.videoToolBoxDecoderEnable = true
        player.handleAudioSession = false
        player.isLoopPlay = true
        player.autoPlay = false
        player.lowCostMode = false
        player.exactSeek = false
        player.type = .video
        player.player.fromSource = 4
        return player
    }()
    
    public var isMuted: Bool {
        return player.isMute
    }
    
    /// 用户手动触发的暂停
    var manualPause: Bool  = false
    
    fileprivate var needPrepare = false
    
    fileprivate var statisticTime: Int = 0
    fileprivate var coverPlayStatisticTime: Int = 0
    
    public typealias GeneralCallback = (_ currentId: String?) -> Void
    public typealias PreparedCallback = () -> Void
    
    // 用于load方法
    public struct CallbackMasks: OptionSet {
        public let rawValue: Int
        public init(rawValue: Int) {
            self.rawValue = rawValue
        }
        public static let loading = CallbackMasks(rawValue: 1)
        public static let playing = CallbackMasks(rawValue: 1 << 1)
        public static let stopped = CallbackMasks(rawValue: 1 << 2)
        public static let paused = CallbackMasks(rawValue: 1 << 3)
        public static let onReachEnd = CallbackMasks(rawValue: 1 << 4)
        public static let onProgress = CallbackMasks(rawValue: 1 << 5)
    }
    
    // state change callbacks
    private var loadingBlock: [GeneralCallback] = []
    private var playingBlock: [GeneralCallback] = []
    private var stoppedBlock: [GeneralCallback] = []
    private var pausedBlock: [GeneralCallback] = []
    private var muteChangedBlock: ((_ isMute: Bool) -> Void)?
    
    // event callbacks
    private var onReachEndBlock: [GeneralCallback] = []
    private var onProgressBlocks: [GeneralCallback] = []
    
    // prepared callback
    public var preparedBlock: PreparedCallback?
    
    private var preloadURL: String?
    private var maxPlayedTime: NSNumber?   /**< ms */
    
    private var handleCoverStat = false
    private var stopForReachEnd = false
    
    // 判断本次stop是否因为播放错误导致的stop
    private var hasError = false
    
    @objc public static let shared = VideoPlayerManager()
    
    deinit {
        
    }
    
    override private init() {
        super.init()
        
        self.playerContainer.addSubview(self.player.view)
        
        self.player.view.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(splashAdDisappearred(_:)),
//                                               name: NSNotification.Name.MTBSplashAdDidDisappear,
//                                               object: nil)
//
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(splashAdWillAppearred(_:)),
//                                               name: NSNotification.Name.MTBSplashAdWillAppear,
//                                               object: nil)
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(appDidEnterBackground(_:)),
                                               name: UIApplication.didEnterBackgroundNotification,
                                               object: nil)
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(handleMuteNotification(_:)),
//                                               name: .CommunityMediaMuteDidChangedManually,
//                                               object: nil)
    }
    
    @objc private func appDidEnterBackground(_ noti: Notification?) {
        guard self.player.state == .paused else { return }
        
        if handleCoverStat {
//            statistics()
        }
    }
    
    @objc private func splashAdDisappearred(_ noti: Notification?) {
        if externalPlayer != nil { return }
        if player.state == .paused && !self.manualPause {
            playerPlay()
        }
    }
    
    @objc private func splashAdWillAppearred(_ noti: Notification?) {
        if externalPlayer != nil { return }
        pause()
    }
    
    @objc private func handleMuteNotification(_ notification: Notification) {
        guard let isMute = notification.userInfo?[WHMuteDidChangeInfoKey] as? Bool else {
            return
        }
        
        doWhenMuteChanged(isMute)
    }
    
//    public func statistics(traceVC: UIViewController = SPMTraceManager.currentTraceVC()) {
//        // 外部提供的player目前不参与我们的统计
//        if externalPlayer != nil { return }
//
//        if let feedId = self.currentId, (loopCount != 0 || playedTime != 0) {
//            let maxTime = maxPlayedTime?.floatValue ?? 0
//            let duration = maxTime > 0 ? Int(maxTime) : Int(player.duration * 1000.0)
//            var totalPlayTime = duration * loopCount + playedTime - coverPlayStatisticTime
//            coverPlayStatisticTime = duration * loopCount + playedTime
//
//            if totalPlayTime <= 0 {
//                totalPlayTime = playedTime
//            }
//
//            var channel = "0"
//            var position = "0"
//
//            var params: [String: Any] = ["feed_id": feedId, "autoload_time": totalPlayTime]
//            if let currentVC = traceVC as? VideoPlayerAutoPlayDelegate {
//                channel = currentVC.playerChannel()
//                position = "\(currentVC.position())"
//                params["scm"] = currentVC.playerScm()
//            }
//            let spminfo = SPMInfo(with: traceVC,
//                                  replaceChannel: channel,
//                                  replacePosition: position)
//            MTAnalytics.Community.Auto.SPM(info: spminfo).event(
//                "autoload_read",
//                attributes: params
//            )
//        }
//    }
    
    public func restore(videoId: String?, toView: UIView?, at index: Int = -1) {
        guard let videoId = videoId,
              let view = toView else {
                  return
              }
        
        if let cid = self.currentId, cid == videoId {
            addPlayingView(on: view, at: index)
            
            if self.isPlaying {
                playingBlock.forEach { $0(currentId) }
            } else if self.isLoading {
                loadingBlock.forEach { $0(currentId) }
            } else if self.isPause {
                pausedBlock.forEach { $0(currentId) }
            } else {
                stoppedBlock.forEach { $0(currentId) }
            }
        }
    }
    
    public func restorePlayerView() {
        if externalPlayer != nil { return }
        
        
        self.player.view.removeAllConstraints()
        self.playerContainer.insertSubview(self.player.view,
                                           at: self.playerContainer.subviews.count - 1)
        self.player.view.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
    }
    
    public func currentPlayerShot() -> UIImage? {
        if externalPlayer != nil { return nil }
        
        guard let parent = self.playerContainer.superview else { return nil }
        
        let rect = CGRect(x: parent.bounds.origin.x,
                          y: parent.bounds.origin.y,
                          width: floor(parent.bounds.size.width),
                          height: floor(parent.bounds.size.height))
        
        UIGraphicsBeginImageContextWithOptions(rect.size, false, UIScreen.main.scale)
        parent.drawHierarchy(in: rect, afterScreenUpdates: false)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
    
    // MARK: - chain call-back
    
    /// - Note:
    ///     调用本方法后, 所有之前遗留的callback都会移除, 除非在preservedCallbacks中包含对应属性
    ///
    /// - Parameters:
    ///     - autoLoop: 是否播放完一遍后自动从头播放
    ///     - externalPlayer: 不使用manager默认的player, 而是使用传入的player
    ///     - preservedCallbacks: 指定不清除哪些已有的callbcak回调
    ///
    public func load(videoId: String?,
                     isLive: Bool = false,
                     playerContentMode: UIView.ContentMode = .scaleAspectFill,
                     url: String?,
                     coverImage: UIImage? = nil,
                     on view: UIView?,
                     at viewIndex: Int? = -1,
                     autoLoop: Bool = true,
                     customPlayRect: CGRect? = .zero,
                     externalPlayer: ExternalViderPlayerHost? = nil,
                     preservedCallbacks: CallbackMasks = []) -> Self? {
        guard let videoId = videoId,
              let url = url,
              let view = view else {
                  return nil
              }
        
        self.isLive = isLive
        
        player.view.contentMode = playerContentMode
        player.isLoopPlay = autoLoop
        
        // clear callbacks if this method returns non-nil
        var needsFurtherConfiguration = true
        defer {
            if needsFurtherConfiguration {
                clearCallbacks(preserving: preservedCallbacks)
            }
        }
        
        if let cid = self.currentId, cid == videoId {
            let container = externalPlayer?.previewView ?? playerContainer
            let playingCell = findCellViewIfNeed(for: container) ?? container.superview
            let onViewCell = findCellViewIfNeed(for: view) ?? view.superview
            
            if playingCell == onViewCell {
                needsFurtherConfiguration = false
                return nil
            }
            
            handleCoverStat = false
            maxPlayedTime = nil
            needPrepare = false
            
            addPlayingView(on: view, at: viewIndex)
            return self
        }
        
        stop()
        self.externalPlayer = externalPlayer
        handleCoverStat = false
        maxPlayedTime = nil
        needPrepare = true
        
        currentId = videoId
        player.player.coverImage = coverImage
        player.urlStr = url
        player.player.extraInfo = [
            "vid" : videoId
        ]
        
        player.player.viewCustomRect = customPlayRect ?? .zero
        
        addPlayingView(on: view, at: viewIndex)
        
        return self
    }
    
    public func loading<T: AnyObject>(capture object: T, block: @escaping (_ object: T, _ currentId: String?) -> Void) -> Self {
        loadingBlock.append {[weak object] (currentFeedId) in
            guard let object = object else { return }
            block(object, currentFeedId)
        }
        return self
    }
    
    @discardableResult
    public func paused<T: AnyObject>(capture object: T, block: @escaping (_ object: T, _ currentId: String?) -> Void) -> Self {
        pausedBlock.append {[weak object] (currentFeedId) in
            guard let object = object else { return }
            block(object, currentFeedId)
        }
        return self
    }
    
    @discardableResult
    public func playing<T: AnyObject>(capture object: T, block: @escaping (_ object: T, _ currentId: String?) -> Void) -> Self {
        playingBlock.append {[weak object] (currentFeedId) in
            guard let object = object else { return }
            block(object, currentFeedId)
        }
        return self
    }
    
    @discardableResult
    public func stopped<T: AnyObject>(capture object: T, block: @escaping (_ object: T, _ currentId: String?) -> Void) -> Self {
        stoppedBlock.append {[weak object] (currentFeedId) in
            guard let object = object else { return }
            block(object, currentFeedId)
        }
        return self
    }
    
    @discardableResult
    public func muteChanged<T: AnyObject>(capture object: T, block: @escaping (_ object: T, _ isMute: Bool) -> Void) -> Self {
        muteChangedBlock = {[weak object] (isMute) in
            guard let object = object else { return }
            block(object, isMute)
        }
        return self
    }
    
    @discardableResult
    public func onReachEnd<T: AnyObject>(capture onGuard: T, block: @escaping (T, _ currentId: String?) -> Void) -> Self {
        onReachEndBlock.append { [weak onGuard] (currentId) in
            guard let ref = onGuard else { return }
            block(ref, currentId)
        }
        return self
    }
    
    @discardableResult
    public func onPrrogress<T: AnyObject>(capture onGuard: T, block: @escaping (T, _ currentId: String?) -> Void) -> Self {
        onProgressBlocks.append { [weak onGuard] (currentId) in
            guard let ref = onGuard else { return }
            block(ref, currentId)
        }
        return self
    }
    
    public static func checkNetworkHint(with allowBlock: () -> Void) {
        defer {
            // 原先该方法异步触发, 现在非wifi下视频也会播放, 因此必定回调
            allowBlock()
        }
        
        if !WHNetwork.isReachable() {
            return
        }
        
        // 有网络且非wifi
        if WHUserDefaultsManager.showUnWIFITips(), WHNetworkChangeMonitorDefault.currentNetwork == .cellular {
            WHRouter.topViewController?.showToast(title: WHLocalizedString("当前为非wifi环境，注意流量消耗"))
        }
    }
    
    @discardableResult
    public func handleCoverStatistics() -> Self {
        handleCoverStat = true
        return self
    }
    
    public func max(msTime: UInt) -> Self {
        maxPlayedTime = msTime == 0 ? nil : NSNumber(value: msTime)
        return self
    }
    
    public func preload(url: String) {
        preloadURL = url
    }
    
    public func done() {
        player.setupStateHandle {[weak self] (state) in
            guard let self = self else { return }
            self.isPause = (state == .paused)
            self.isLoading = (state == .loading)
            self.isPlaying = (state == .playing)
            
            let currentId = self.currentId
            
            switch state {
            case .playing:
                self.playingBlock.forEach { $0(currentId) }
            case .paused:
                self.pausedBlock.forEach { $0(currentId) }
            case .loading:
                self.loadingBlock.forEach { $0(currentId) }
            case .stopped:
                self.stoppedBlock.forEach { $0(currentId) }
//                如果因为播放error导致的stop，不移除播放器
                if !self.hasError {
                    self.stoppedConfiguration()
                }
                self.hasError = false
            default: break
            }
        }
        
        player.setupEventHandle {[weak self, currentId] (event, detail) in
            guard let self = self else { return }
            let playerCapturedVideoId = currentId
            
            switch event {
            case .prepared:
                self.preparedBlock?()
                self.preparedBlock = nil
                
                if self.playedInfo.1 == playerCapturedVideoId, self.playedInfo.0 {
                    // http://jira.meitu.com/browse/MTXX-67117
                    // 解决播放器错误(100003)，当执行播放任务时内部出错的话，会自动执行prepare，但并不会自动播放
                    // 所以在该类的play方法中，并不立即进行播放，延后到prepare结束后，即使播放器出错，最终也会执行到prepared回调这里
                    if !self.isPlaying {
                        self.playerPlay()
                    }
                }
            case .reachEnd:
                if self.loopCount == 0 {
                    NotificationCenter.default.post(name:VideoPlayerManager.didFinishFirstPlayNotification,
                                                    object: self,
                                                    userInfo: nil)
                }
                self.onReachEndBlock.forEach { $0(self.currentId) }
                self.loopCount += 1
            case .progressUpdated:
                if let progress = detail as? CGFloat, !self.isLive {
                    self.playedTime = Int(progress * CGFloat(self.player.duration * 1000))
                    self.playedPercent = progress
                    
                    self.onProgressBlocks.forEach { $0(self.currentId) }
                    
                    guard let max = self.maxPlayedTime else {
                        return
                    }
                    
                    // 有设置最长播放时长
                    let maxPrecent = CGFloat(max.floatValue) / CGFloat(self.player.duration * 1000) * 100
                    
                    guard maxPrecent < 100 else {
                        return
                    }
                    
                    let currentPercent = progress * 100
                    if currentPercent > maxPrecent {
                        self.loopCount += 1
                        self.player.seek(toPercent: 0.0, completion: nil)
                    }
                }
            case .enoughBuffer:
                if let preloadUrl = self.preloadURL {
                    self.preloadURL = nil
                    MTMFFPreload.preloadURL(preloadUrl)
                }
            case .error:
                self.hasError = true
            default:
                break
            }
        }
        
        if needPrepare {
            player.prepare()
        }
    }
    
    func removeContentPlayer() {
        playerContainer.removeFromSuperview()
    }
    
    public func setAudioCategory(category: AVAudioSession.Category, mute: Bool) {
        do {
            if #available(iOS 10.0, *) {
                try AVAudioSession.sharedInstance().setCategory(category, mode: .default, options: mute ? [.mixWithOthers] : [])
            } else {
                try AVAudioSession.sharedInstance().setCategory(mute ? .ambient : .playback)
                try AVAudioSession.sharedInstance().setMode(.default)
            }
            if mute {  // 恢复音乐播放
                try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            } else {
                try AVAudioSession.sharedInstance().setActive(true)
            }
        } catch _ { }
    }
    
    public func reFigureAudioSession() {
        if player.isMute {
            setAudioCategory(category: .ambient, mute: true)
        } else {
            setAudioCategory(category: .playback, mute: false)
        }
    }
    
    // 供OC类桥接使用
    @objc public func load(videoId: String?,
                           url: String?,
                           coverImage: UIImage?,
                           on view: UIView?,
                           autoLoop: Bool,
                           maxPlayedTime: UInt,
                           loadingBlock: ((_ currentId: String?) -> ())?,
                           playingBlock: ((_ currentId: String?) -> ())?,
                           pausedBlock: ((_ currentId: String?) -> ())?,
                           stoppedBlock: ((_ currentId: String?) -> ())?) {
        self.load(videoId: videoId,
                  url: url,
                  coverImage: coverImage,
                  on: view,
                  autoLoop: autoLoop)?
            .loading(capture: self, block: { (self, currentId) in
                loadingBlock?(currentId)
            })
            .playing(capture: self, block: { (self, currentId) in
                playingBlock?(currentId)
            })
            .paused(capture: self, block: { (self, currentId) in
                pausedBlock?(currentId)
            })
            .stopped(capture: self, block: { (self, currentId) in
                stoppedBlock?(currentId)
            })
            .max(msTime: maxPlayedTime)
            .handleCoverStatistics()
            .done()
        set(mute: true, manual: false)
    }
    
    @objc public func play() {
        
        reFigureAudioSession()
        if let externalPlayer = externalPlayer {
            externalPlayer.play { [weak self] in
                self?.uninstallExternalPlayer()
            }
        } else if needPrepare || !self.isPlaying {
            // needPrepare时也播放是因为当播放器更换了新的播放链接时，needPrepare就是true了
            playedInfo = (true, currentId)
            playerPlay()
        }
        
        muteChangedBlock?(self.isMuted)
    }
    
    private func playerPlay() {
        guard (player.player.view.window != nil) else { return }
        player.view.isHidden = false
        player.play()
    }
    
    public func seek(toPercent percent: CGFloat, completion: WHPlayerCompletionHandle? = nil) {
        player.seek(toPercent: percent, completion: completion)
    }
    
    public func seek(toTime time: TimeInterval, completion: WHPlayerCompletionHandle? = nil) {
        player.seek(toTime: time, completion: completion)
    }
    
    public func fastSeek(toPercent percent: CGFloat) {
        player.fastSeek(toPercent: percent)
    }
    
    // manual: 表示是否通过静音按钮设置
    @objc public func set(mute: Bool, manual: Bool) {
        if manual {
            WHCommunity.isMediaMute = mute
        } else {
            doWhenMuteChanged(mute)
        }
    }
    
    @objc public var isManualMute: Bool {
        if WHCommunity.defaultMuteExpired {
            return WHCommunity.isMediaMute
        } else {
            return false
        }
    }
    
    @objc public func pause() {
        if let externalPlayer = externalPlayer {
            externalPlayer.pause() // pause方法未实现，需求如此，存在商业化播放器无法暂停的问题
        } else {
            playedInfo = (false, nil)
            player.pause()
        }
    }
    
    @objc public func stop() {
        if externalPlayer != nil {
            uninstallExternalPlayer()
        } else {
            playedInfo = (false, nil)
            player.stop()
            player.view.isHidden = true
        }
    }
    
//    @objc public func stopWithStatistic(traceVC: UIViewController = SPMTraceManager.currentTraceVC()) {
//        guard externalPlayer != nil || player.state != .stopped else { return }
////        statistics(traceVC: traceVC)
//        stop()
//    }
    
    @objc public func hasCached(for url: String) -> Bool {
        return MTMFFDataCache.shared().cacheExist(withURL: url)
    }
    
    @objc public func clearDiskCache(completion handler: (()->())? = nil) {
        stop()
        MTMFFDataCache.shared().cleanAll()
        handler?()
    }
    
    @objc public func getSize() -> UInt {
        var size: Int = MTMFFDataCache.shared().size()
        if size < 0 {
            size = 0
        }
        return UInt(size)
    }
    
    /// 该方法用于获取总播放时长与视频长度的比值，播放总时长为"(播放次数*视频长度+当前播放时长)/视频长度"。仅用于`MTFeedBrowsingStatistic`逻辑
    /// 注意：针对同一个feedid，每次执行该方法，返回的比值是不同的，比如第一次获取时视频播放了刚满一遍，返回值为1，又播了一遍后，再次执行该方法，计算起点是上次执行该方法的时间，所以第二次执行时返回值仍然是1
    /// - Parameter feedId:
    @objc public func getNeedStatisticPlayedCount(for feedId: String?) -> TimeInterval {
        guard externalPlayer == nil, let feedId = feedId else { return 0.0 }
        if currentId == feedId {
            if loopCount != 0 || playedTime != 0 {
                let duration = Int(player.duration * 1000.0)
                let totalPlayTime = duration * loopCount + playedTime - statisticTime
                statisticTime = duration * loopCount + playedTime
                if (totalPlayTime < 0) {
                    return 0.0
                }
                return TimeInterval(Float(totalPlayTime) / Float(duration))
            } else {
                return 0.0
            }
        } else {
            return 0.0
        }
    }
    
    // 在一遍播放完毕或者外部stop时, 清除external player
    public func uninstallExternalPlayer() {
        guard let externalPlayer = externalPlayer else { return }
        externalPlayer.stop()
        externalPlayer.previewView.removeFromSuperview()
        self.externalPlayer = nil
        self.currentId = nil
    }
    
    private func doWhenMuteChanged(_ mute: Bool) {
        externalPlayer?.isMute = mute
        player.isMute = mute
        player.player.audioDisable = mute

        reFigureAudioSession()
        muteChangedBlock?(mute)
    }
    
    private func clearCallbacks(preserving masks: CallbackMasks) {
        if !masks.contains(.playing) {
            playingBlock.removeAll()
        }
        if !masks.contains(.loading) {
            loadingBlock.removeAll()
        }
        if !masks.contains(.paused) {
            pausedBlock.removeAll()
        }
        if !masks.contains(.stopped) {
            stoppedBlock.removeAll()
        }
        if !masks.contains(.onReachEnd) {
            onReachEndBlock.removeAll()
        }
        if !masks.contains(.onProgress) {
            onProgressBlocks.removeAll()
        }
    }
    
    public func isPlayerOn(view: UIView) -> Bool {
        let container = externalPlayer?.previewView ?? playerContainer
        return container.superview == view
    }
    
    public func addPlayingView(on view: UIView, at index: Int? = -1) {
        if player.state == .playing {
            player.suspendedDrawing = true
        }
        
        let container = externalPlayer?.previewView ?? playerContainer
        if container.superview != view {
            container.removeFromSuperview()
            if let i = index, i >= 0 , i <= view.subviews.count {
                view.insertSubview(container, at: i)
            } else {
                view.addSubview(container)
            }
            container.snp.makeConstraints({ (make) in
                make.edges.equalToSuperview()
            })
        }
        
        if player.state == .playing {
            player.suspendedDrawing = false
        }
    }
    
    private func findCellViewIfNeed(for view: UIView) -> UIView? {
        if let superView = view.superview {
            if superView.isKind(of: UITableViewCell.classForCoder()) ||
                superView.isKind(of: UICollectionViewCell.classForCoder()) {
                return superView
            } else {
                return findCellViewIfNeed(for: superView)
            }
        } else {
            return nil
        }
    }
    
    private func stoppedConfiguration() {
        // 在有externalPlayer的情况下, 始终保持currentId
        if externalPlayer == nil {
            currentId = nil
        }
        
        loopCount = 0
        playedTime = 0
        statisticTime = 0
        handleCoverStat = false
        coverPlayStatisticTime = 0
        
        playerContainer.removeFromSuperview()
    }

    
}

extension UIView {
    func removeAllConstraints() {
        var superView = self.superview
        while superView != nil {
            for constraint in superView!.constraints {
                if let firstItem = constraint.firstItem as? UIView, firstItem == self {
                    superView?.removeConstraint(constraint)
                }
                
                if let secondItem = constraint.secondItem as? UIView, secondItem == self {
                    superView?.removeConstraint(constraint)
                }
            }
            superView = superView?.superview
        }
        
        self.removeConstraints(self.constraints)
        self.translatesAutoresizingMaskIntoConstraints = true
    }
}
