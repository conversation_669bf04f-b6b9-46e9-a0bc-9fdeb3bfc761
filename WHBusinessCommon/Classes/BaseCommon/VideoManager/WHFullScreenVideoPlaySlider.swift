//
//  WHFullScreenVideoPlaySlider.swift
//  MTXX
//
//  Created by songgeb on 2019/12/4.
//  Copyright © 2019 Meitu. All rights reserved.
//

import UIKit
import SnapKit
import WHBaseLibrary

@objc
enum WHVideoPlaySliderType: Int{
    case fullScreen = 0
    case clearScreen
}

protocol FullScreenVideoPlaySliderDelegate: AnyObject {
  
  /// 开始拖拽
  /// - Parameter slider:
  func didBeginDragging(_ slider: WHFullScreenVideoPlaySlider)
  
  /// 结束拖拽
  /// - Parameter slider:
  func didEndDragging(_ slider: WHFullScreenVideoPlaySlider)
}

/// 视屏大屏详情页视频进度条
class WHFullScreenVideoPlaySlider: UIView {
    
    var playBlock: ((Bool) -> ())?
    
    // 样式
    var sliderType: WHVideoPlaySliderType = .fullScreen {
        didSet {
            updateViews()
        }
    }
    
    private lazy var indicator: UIView = {
        let make = UIView()
        make.backgroundColor = .white
        make.clipsToBounds = true
        make.layer.cornerRadius = 5
        make.isHidden = true
        return make
    }()
    
    private lazy var progressView: UIProgressView = {
        let make = UIProgressView()
        make.trackTintColor = UIColor(red: 151, green: 151, blue: 151, alpha: 0.3)
        make.progressTintColor = UIColor.white
        return make
    }()
    private let progressViewBg = UIView()
   
    private lazy var dataLabel: UILabel = {
        let make = UILabel()
        make.font = UIFont.systemFont(ofSize: 15, weight: .bold)
        make.textAlignment = .center
        make.textColor = .white
        make.isHidden = true
        make.setContentHuggingPriority(.required, for: .horizontal)
        make.setContentCompressionResistancePriority(.required, for: .horizontal)
        return make
    }()
    
    private lazy var playButton: UIButton = {
        let btn = UIButton()
        btn.setImage(UIImage(named: "full_screen_clear_play"), for: .normal)
        btn.setImage(UIImage(named: "full_screen_clear_pause"), for: .selected)
        btn.addTarget(self, action: #selector(playAction(_ :)), for: .touchUpInside)
        return btn
    }()
    
    private var playLeftConstrain: ConstraintMakerFinalizable?
  
    weak var delegate: FullScreenVideoPlaySliderDelegate?
  
  /// 设置视频总时长
  var duration: TimeInterval = 0 {
    didSet {
      realDuration = Int(floor(duration))
    }
  }
  
  private var realDuration: Int = 0
  
  /// 拖拽后的时间
  var seekHandler: ((Int) -> Void)?
  
  /// label显示、隐藏变化回调
  var visibleChangedCallback: ((Bool) -> Void)?
  
  private var indicatorShouldShow = false {
    didSet {
        indicator.isHidden = sliderType == .clearScreen ? false : !indicatorShouldShow
    }
  }
  
    private var isDragging = false {
        didSet {
            if isDragging {
                indicator.layer.cornerRadius = 8
                indicator.snp.updateConstraints { (make) in
                    make.width.height.equalTo(16)
                }
                indicatorShouldShow = true
            } else {
                indicator.layer.cornerRadius = 5
                indicator.snp.updateConstraints { (make) in
                    make.width.height.equalTo(10)
                }
            }
            dataLabel.isHidden =  sliderType == .clearScreen ? false :!isDragging
            visibleChangedCallback?(isDragging)
        }
    }
  
  /// 开始拖动时progressview尺寸
  private var initialProgressWidth: CGFloat = 0
  
  /// 设置当前的播放时间，进度条跟随变化
  var time: TimeInterval = 0 {
    didSet {
      if isDragging { return }
      let t = time.wh_clamped(to: 0...Double(realDuration))
      updateTime(t)
    }
  }
  
  override init(frame: CGRect) {
    super.init(frame: frame)
    setupViews()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
    private func setupViews() {
        addSubview(playButton)
        addSubview(progressViewBg)
        addSubview(progressView)
        addSubview(indicator)
        addSubview(dataLabel)
        
        progressViewBg.snp.makeConstraints { (make) in
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().priority(600)
            make.height.equalTo(24)
        }
        progressView.snp.makeConstraints { (make) in
            make.leading.trailing.equalTo(progressViewBg)
            make.bottom.equalTo(progressViewBg).inset(4)
            make.height.equalTo(0.5)
        }
        
        indicator.snp.makeConstraints { (make) in
            make.width.height.equalTo(10)
            make.centerX.equalTo(progressView.snp.leading).offset(progressView.progress * Float(progressView.width))
            make.centerY.equalTo(progressView)
        }
        
        dataLabel.snp.makeConstraints { (make) in
            make.centerX.bottom.top.equalToSuperview()
        }
        
        playButton.snp.makeConstraints { (make) in
            playLeftConstrain = make.leading.equalToSuperview().offset(8).priority(500)
            make.centerY.equalTo(progressView)
            make.trailing.equalTo(progressViewBg.snp.leading).offset(-8)
            make.width.height.equalTo(32)
        }
        
        let pan = UIPanGestureRecognizer(target: self, action: #selector(onPan(_:)))
        pan.maximumNumberOfTouches = 1
        progressViewBg.addGestureRecognizer(pan)
    }
    
    func updateViews() {
        if sliderType == .fullScreen {
            dataLabel.font = UIFont.systemFont(ofSize: 15, weight: .bold)
            dataLabel.snp.remakeConstraints { (make) in
                make.centerX.bottom.top.equalToSuperview()
            }
            progressView.snp.updateConstraints { (make) in
                make.height.equalTo(0.5)
            }
            
            playLeftConstrain?.constraint.update(priority: 500)
            
            indicatorShouldShow = false
            dataLabel.isHidden = true
        } else if sliderType == .clearScreen {
            dataLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            dataLabel.snp.remakeConstraints { (make) in
                make.centerY.equalTo(progressView)
                make.trailing.equalToSuperview().offset(-16)
                make.leading.equalTo(progressViewBg.snp.trailing).offset(16)
            }
            progressView.snp.updateConstraints { (make) in
                make.height.equalTo(2)
            }
            playLeftConstrain?.constraint.update(priority: 700)
            self.layoutIfNeeded()
            indicator.snp.updateConstraints { (make) in
                make.centerX.equalTo(progressView.snp.leading).offset(progressView.progress * Float(progressView.width))
            }
            
            indicatorShouldShow = true
            dataLabel.isHidden = false
        }
    }
  
  private func updateTime(_ time: TimeInterval) {
    if realDuration <= 0 || time < 0 { return }
    let realTime = Int(floor(time))
    let progress = Float(realTime) / Float(realDuration)
    progressView.progress = progress
    indicator.snp.updateConstraints { (make) in
        make.centerX.equalTo(progressView.snp.leading).offset(progressView.progress * Float(progressView.width))
    }
    updateLabel(realTime)
  }
  
  private func updateLabel(_ time: Int) {
    dataLabel.text = "\(formatTime(time)) / \(formatTime(realDuration))"
  }
  
  private func formatTime(_ time: Int) -> String {
    let minute = time / 60
    let second = time % 60
    
    let m = minute < 10 ? "0\(minute)" : "\(minute)"
    let s = second < 10 ? "0\(second)" : "\(second)"
    
    return "\(m):\(s)"
  }
  
  @objc private func changeIndicatorVisibility(_ visible: Bool) {
    indicatorShouldShow = visible
  }
  
    @objc private func onPan(_ gesture: UIPanGestureRecognizer) {
        
        switch gesture.state {
        case .began:
            if gesture.velocity(in: progressViewBg).x != 0 {
                NSObject.cancelPreviousPerformRequests(withTarget: self)
                isDragging = true
                initialProgressWidth = progressView.width * CGFloat(progressView.progress)
                
//                MTAnalytics.Community.event("progress_bar_click")
                
                delegate?.didBeginDragging(self)
            }
        case .changed:
            if !isDragging {
                return
            }
            let delta = gesture.translation(in: progressViewBg).x
            let width = (initialProgressWidth + delta).wh_clamped(to: 0...progressView.width)
            let progress = Float(width) / Float(progressView.width)
            progressView.progress = progress
            indicator.snp.updateConstraints { (make) in
                make.centerX.equalTo(progressView.snp.leading).offset(progressView.progress * Float(progressView.width))
            }
            let time = Int(progress * Float(realDuration))
            updateLabel(time)
        case .ended:
            if !isDragging {
                return
            }
            perform(
                #selector(changeIndicatorVisibility(_:)),
                with: false,
                afterDelay: 2)
            isDragging = false
            let time = Int(progressView.progress * Float(realDuration))
            seekHandler?(time)
            delegate?.didEndDragging(self)
            playButton.isSelected = false
        default:
            if !isDragging {
                return
            }
            perform(
                #selector(changeIndicatorVisibility(_:)),
                with: false,
                afterDelay: 2)
            isDragging = false
        }
    }
  
    // MARK: - public method
    func reset(pause: Bool) {
        time = 0
        isDragging = false
        indicatorShouldShow = false
        playButton.isSelected = pause
    }
    
    @objc func playAction(_ sender: UIButton) {
        playButton.isSelected = !playButton.isSelected
        if let playBlock = playBlock {
            playBlock(playButton.isSelected)
        }
    }
}
