//
//  WHMediaPlayControllerPlugin.swift
//  MTXX
//
//  Created by yumeng tang on 2019/7/2.
//  Copyright © 2019 Meitu. All rights reserved.
//

import UIKit
import SnapKit
import WHBaseLibrary
import WHBusinessCommon

@objc
public enum MediaPlayControllerStyle: Int {
    case full = 0                       /**< 默认样式 */
    case media = 1                       /**< 大图样式 */
}

@objc public protocol MediaPlayControllerPluginDelegate: NSObjectProtocol{
    func mediaPlayControllerPluginContinuePlay()
    func mediaPlayControllerPluginPausePlay()
    func mediaPlayControllerPluginClose()
    @objc optional func mediaPlayControllerPluginMute(_ isMute: Bool)
    @objc optional func mediaPlayControllerDisplayState(display: Bool)
}

public class WHMediaPlayControllerPlugin: NSObject {
    
    private var style: MediaPlayControllerStyle = .full
    
    public init(duration: Float, shouldAutoPlay: Bool) {
        self.duration = duration
        self.shouldAutoPlay = shouldAutoPlay
        self.hideControlViews = shouldAutoPlay
        super.init()
    }
    
    deinit {
        removeTimer()
    }
    
    public func setupUIForFullScreenVC(_ vc: UIViewController,view: UIView, style:MediaPlayControllerStyle = .full) {
        self.style = style
        prepareUIData(vc: vc, view: view)
    }
    
    public func updatePlayedTime(_ time: Int, playedPercentage: CGFloat) {
        timeSlider.value = Float(playedPercentage)
        leftTimeLabel.attributedText = timeLabelAttributedText(duration: Float(time))
    }
    
    public func toggleControlHiddenStatus() {
        hideControlViews = !hideControlViews
    }
    
    public func resetControlViewsState() {
        hideControlViews = shouldAutoPlay
        playButton.isSelected = !shouldAutoPlay
        timeSlider.value = 0.0
    }
    
    public func view() -> UIView {
        return backgroundView
    }
    
    public weak var delegate: MediaPlayControllerPluginDelegate?
    
    private var timer: Timer?
    private var timerInterval: TimeInterval = 0.0
    
    public var showMuteButton = false {
        didSet {
            muteButton.isHidden = !showMuteButton

            guard let containerView = containerView, let containerVC = containerVC else { return }
            let bottomGuide: ConstraintItem
            if #available(iOS 11, *) {
                bottomGuide = containerView.safeAreaLayoutGuide.snp.bottom
            } else {
                bottomGuide = containerVC.bottomLayoutGuide.snp.bottom
            }
            sliderBgView.snp.remakeConstraints { (m) in
                m.bottom.equalTo(bottomGuide).offset(-46)
                if showMuteButton {
                    m.left.equalTo(muteButton.snp.right)
                } else {
                    m.left.right.equalToSuperview()
                }
                m.right.equalToSuperview()
                m.height.equalTo(40)
            }
        }
    }
    
    public var isMute = false {
        didSet {
            updateMuteState()
        }
    }
    
    open var duration: Float {
        didSet {
            rightTimeLabel.attributedText = timeLabelAttributedText(duration: ceilf(duration), isMedia:style == .media)
        }
    }
    private let shouldAutoPlay: Bool
    open var hideControlViews: Bool {
        didSet {
            backgroundView.isHidden = hideControlViews
            if hideControlViews {
                removeTimer()
            } else {
                if !isPlayerPaused() {
                    addTimer()
                }
            }
            delegate?.mediaPlayControllerDisplayState?(display: hideControlViews)
        }
    }
    
    private weak var containerView: UIView?
    private weak var containerVC: UIViewController?
    //和 containerView 一样大的 view ，方便做隐藏显示操作
    private let backgroundView = UIView()
    
    lazy private var closeButton : UIButton = {
        let cb = UIButton(type: .custom)
        cb.setImage(UIImage(named: "icon_media_control_close"), for: .normal)
        cb.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return cb
    }()
    
    lazy private var muteButton: UIButton = {
        let button = UIButton()
        button.isHidden = !showMuteButton
        button.addTarget(self, action: #selector(muteAction(_:)), for: .touchUpInside)
        return button
    }()
    
    // isSelected == true 暂停，显示播放按钮
    // 初始状态根据外部传入的 自动播放来控制，一般 shouldAutoPlay == true，初始状态 isSelected == false
    public lazy var playButton : UIButton = {
        let playButton = UIButton(type: .custom)
        playButton.setImage(UIImage(named: "icon_media_control_pause"), for: .normal)
        playButton.setImage(UIImage(named: "icon_media_control_play"), for: .selected)
        playButton.isSelected = !shouldAutoPlay
        playButton.addTarget(self, action: #selector(playAction), for: .touchUpInside)
        return playButton
    }()
    
    lazy private var leftTimeLabel : UILabel = {
        let label = UILabel()
        label.attributedText = timeLabelAttributedText(duration: 0)
        return label
    }()
    
    lazy private var rightTimeLabel : UILabel = {
        let label = UILabel()
        return label
    }()
    
    lazy private var sliderBgView: UIView = {
        let sliderBgView = UIView()
        return sliderBgView
    }()
    
    lazy private var timeSlider: MediaPlayControllerSlider = {
        let slider = MediaPlayControllerSlider()
        slider.addTarget(self, action: #selector(sliderValueBegin(slider: )), for: .touchDown)
        slider.addTarget(self, action: #selector(sliderValueChanged(slider: )), for: .valueChanged)
        slider.addTarget(self, action: #selector(sliderValueEnd(slider: )), for: .touchUpInside)
        slider.addTarget(self, action: #selector(sliderValueEnd(slider: )), for: .touchUpOutside)
        slider.addTarget(self, action: #selector(sliderValueEnd(slider: )), for: .touchCancel)
        
        let tap = UITapGestureRecognizer(target: self, action: #selector(sliderTappedAction(sender:)))
        slider.addGestureRecognizer(tap)
        
        return slider
    }()
    
}

//private methods
extension WHMediaPlayControllerPlugin {
    
    public func isPlayerPaused() -> Bool {
        return playButton.isSelected
    }
    
    private func beginToPlay() {
        if isPlayerPaused() {
            playButton.isSelected = false
            addTimer()
            delegate?.mediaPlayControllerPluginContinuePlay()
        }
    }
    
    private func addObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil)
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil)
    }
    
    private func removeTimer() {
        guard style == .full else {
            return
        }
        timer?.invalidate()
    }
    
    private func addTimer() {
        guard style == .full else {
            return
        }
        timer?.invalidate()
        timerInterval = 0
        timer = Timer.scheduledTimer(
            timeInterval: 1.0,
            target: self,
            selector: #selector(timerAction),
            userInfo: nil,
            repeats: true)
    }
    
    private func prepareUIData(vc: UIViewController, view: UIView) {
        containerVC = vc
        containerView = view
        //      vc.view
        
        if let _ = closeButton.superview { return }
        guard let containerView = containerView else { return }
        
        containerView.addSubview(backgroundView)
        
//        backgroundView.addSubview(muteButton)
        backgroundView.addSubview(closeButton)
        backgroundView.addSubview(playButton)
        backgroundView.addSubview(sliderBgView)
//        sliderBgView.addSubview(leftTimeLabel)
//        sliderBgView.addSubview(rightTimeLabel)
//        sliderBgView.addSubview(timeSlider)
        
        hideControlViews = shouldAutoPlay
        // 4.8s 总时间向上取整
        rightTimeLabel.attributedText = timeLabelAttributedText(duration: floorf(duration),isMedia:style == .media)
        
        backgroundView.snp.makeConstraints { (m) in
            m.edges.equalTo(containerView)
        }
        
        let topGuide: ConstraintItem
        let bottomGuide: ConstraintItem
        if #available(iOS 11, *) {
            topGuide = containerView.safeAreaLayoutGuide.snp.top
            bottomGuide = containerView.safeAreaLayoutGuide.snp.bottom
        } else {
            topGuide = vc.topLayoutGuide.snp.top
            bottomGuide = vc.bottomLayoutGuide.snp.bottom
        }
        
        closeButton.snp.makeConstraints { (m) in
            m.size.equalTo(CGSize(width: 48, height: 48))
            m.top.equalTo(topGuide).offset(WH_IS_IPHONE_X() ? 4 : 24)
            m.left.equalTo(containerView.snp.left).offset(4)
        }
        
        sliderBgView.snp.makeConstraints { (m) in
            m.bottom.equalTo(bottomGuide).offset(-36)
            m.left.right.equalToSuperview()
            m.height.equalTo(40)
        }
        
//        muteButton.snp.makeConstraints { maker in
//            maker.size.equalTo(CGSize(width: 32, height: 32))
//            maker.centerY.equalTo(timeSlider)
//            maker.left.equalToSuperview().offset(16)
//        }
        
        if style == .media {
            closeButton.setImage(UIImage(named: "icon_back_white_bload"), for: .normal)
            sliderBgView.addSubview(playButton)
            playButton.snp.makeConstraints { (make) in
                make.width.height.equalTo(32)
                make.leading.equalToSuperview().offset(8)
                make.centerY.equalToSuperview()
            }
//            timeSlider.snp.makeConstraints { (m) in
//                m.left.equalTo(48)
//                m.right.equalTo(leftTimeLabel.snp.left).offset(-16)
//                m.top.bottom.centerY.equalToSuperview()
//            }
//            leftTimeLabel.snp.makeConstraints { (m) in
//                m.left.equalTo(timeSlider.snp.right).offset(16)
//                m.top.bottom.centerY.equalToSuperview()
//            }
//            rightTimeLabel.snp.makeConstraints { (m) in
//                m.right.equalTo(-16)
//                m.leading.equalTo(leftTimeLabel.snp.trailing)
//                m.top.bottom.centerY.equalToSuperview()
//            }
        } else {
            playButton.snp.makeConstraints { (m) in
                m.size.equalTo(CGSize(width: 96, height: 96))
                m.center.equalTo(containerView.snp.center)
            }
//            leftTimeLabel.snp.makeConstraints { (m) in
//                m.left.equalTo(16)
//                m.top.bottom.centerY.equalToSuperview()
//            }
//            rightTimeLabel.snp.makeConstraints { (m) in
//                m.right.equalTo(-16)
//                m.top.bottom.centerY.equalToSuperview()
//            }
//            timeSlider.snp.makeConstraints { (m) in
//                m.left.equalTo(64)
//                m.right.equalTo(-64)
//                m.top.bottom.centerY.equalToSuperview()
//            }
        }
        updateMuteState()
        
        if let containerVC = containerView as? WHVideoBrowserCell {
            let panGesture = PanDirectionGestureRecognizer(axis: .vertical,
                                                           target: containerVC,
                                                           action: #selector(panGestureRecognized(_:)))
            panGesture.minimumNumberOfTouches = 1
            panGesture.maximumNumberOfTouches = 1
            backgroundView.addGestureRecognizer(panGesture)
        }
        
    }
    
    @objc public func panGestureRecognized(_ gesture: UIPanGestureRecognizer) {
    
    }
    
    private func twoCharFor(second: Int) -> String {
        assert(second <= 60, "second should never exceed 60")
        if second >= 10 { return "\(second)" }
        return "0\(second)"
    }
    
    private func strForDuration(duration: Float) -> String {
        let seconds = Int(duration)
        if seconds < 60 { return "00:\(twoCharFor(second: seconds))" }
        return "\(twoCharFor(second: seconds / 60)): \(twoCharFor(second: seconds % 60))"
    }
    
    private func timeLabelAttributedText(duration: Float, isMedia: Bool = false) -> NSAttributedString {
        let shadow = NSShadow()
        shadow.shadowOffset = .zero
        shadow.shadowColor = UIColor(white: 0, alpha: 0.3)
        shadow.shadowBlurRadius = 1
        let attributes: [NSAttributedString.Key : AnyHashable] = [
            .font : UIFont.systemFont(ofSize: 12, weight: .medium),
            .foregroundColor : UIColor.white,
            .shadow : shadow
        ]
        var text = strForDuration(duration: duration)
        if isMedia {
            text = "/" + text
        }
        return NSAttributedString(string: text, attributes: attributes)
    }
}

// MARK: Control Action
extension WHMediaPlayControllerPlugin: UIGestureRecognizerDelegate {
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view is UISlider {
            return false
        }
        return true
    }
}
extension WHMediaPlayControllerPlugin {
    
    @objc private func sliderTappedAction(sender: UITapGestureRecognizer) {
        if let slider = sender.view as? UISlider {
            
            if slider.isHighlighted { return }
            
            let point = sender.location(in: slider)
            let percentage = Float(point.x / slider.bounds.width)
            let delta = percentage * (slider.maximumValue - slider.minimumValue)
            let value = slider.minimumValue + delta
            slider.setValue(value, animated: true)
            sliderValueChanged(slider: slider)
            sliderValueEnd(slider: slider)
        }
    }
    
    @objc private func sliderValueBegin(slider: UISlider) {
        removeTimer()
        delegate?.mediaPlayControllerPluginPausePlay()
    }
    
    @objc private func sliderValueChanged(slider: UISlider) {
        removeTimer()
        let uiDuration = slider.value == 1.0 ? ceilf(slider.value * duration) : slider.value * duration
        leftTimeLabel.attributedText = timeLabelAttributedText(duration: uiDuration)
        VideoPlayerManager.shared.fastSeek(toPercent: CGFloat(slider.value))
        addTimer()
    }
    
    @objc private func sliderValueEnd(slider: UISlider) {
        VideoPlayerManager.shared.seek(toPercent: CGFloat(slider.value))
        playButton.isSelected = false
        addTimer()
        delegate?.mediaPlayControllerPluginContinuePlay()
    }
    
    @objc private func closeAction() {
        delegate?.mediaPlayControllerPluginClose()
    }
    
    @objc private func playAction() {
        playButton.isSelected = !playButton.isSelected
        if playButton.isSelected {
            removeTimer()
            delegate?.mediaPlayControllerPluginPausePlay()
        } else {
            addTimer()
            delegate?.mediaPlayControllerPluginContinuePlay()
        }
    }
    
    @objc
    private func muteAction(_ sender: UIButton) {
        isMute = !isMute
    }
    
    private func updateMuteState() {
//        let image = isMute ? #imageLiteral(resourceName: "icon_publish_is_mute") : #imageLiteral(resourceName: "icon_publish_not_mute")
//        muteButton.setImage(image, for: .normal)
        delegate?.mediaPlayControllerPluginMute?(isMute)
    }
    
    @objc private func applicationDidEnterBackground() {
        removeTimer()
    }
    
    @objc private func applicationWillEnterForeground() {
        addTimer()
    }
    
    @objc private func timerAction() {
        timerInterval += 1.0
        if timerInterval >= 3.0 {
            hideControlViews = true
            timerInterval = 0.0
        }
    }
}

class MediaPlayControllerSlider: UISlider {

    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    private func commonInit() {
//        let thumbImage = #imageLiteral(resourceName: "icon_video_slider_thumb")
//        var minImage = #imageLiteral(resourceName: "icon_video_slider_min")
//        var maxImage = #imageLiteral(resourceName: "icon_video_slider_max")
//        minImage = minImage.stretchableImage(withLeftCapWidth: Int(minImage.size.width/2), topCapHeight: Int(minImage.size.width/2))
//        maxImage = maxImage.stretchableImage(withLeftCapWidth: Int(maxImage.size.width/2), topCapHeight: Int(maxImage.size.width/2))
//
//        setThumbImage(thumbImage, for: .normal)
//        setMinimumTrackImage(minImage, for: .normal)
//        setMaximumTrackImage(maxImage, for: .normal)
    }
    
    override func thumbRect(forBounds bounds: CGRect, trackRect rect: CGRect, value: Float) -> CGRect {
        // thumbImage四周有阴影
        let inset: CGFloat = 6
        var rect = super.thumbRect(forBounds: bounds, trackRect: rect, value: value)
        let mBounds = bounds.insetBy(dx: -inset, dy: 0)
        rect.origin.x = -inset + CGFloat(value) * (mBounds.width - rect.width)
        return rect
    }

}
