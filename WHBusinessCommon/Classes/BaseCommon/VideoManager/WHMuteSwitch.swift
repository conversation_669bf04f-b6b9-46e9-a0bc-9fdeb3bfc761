//
//  WHMuteSwitch.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/12/25.
//

import Foundation


// MARK: - app静音总开关

extension Notification.Name {
  /// 社区视频、音乐静音状态因为人为选择导致的静音状态改变. 注意，一些通过代码强制静音的改变不会触发该通知
  static let WHCommunityMediaMuteDidChangedManually = Notification.Name("CommunityMediaMuteDidChangedManually")
}

let WHMuteDidChangeInfoKey = "communityMuteDidChangeInfoKey"

struct WHCommunity {
  // isMediaMute是社区视频音乐的统一的静音开关, 下面是应用实例
  /// VideoPlayerManager/FeedMusicPlayingManager --> setmute --> isMediaMute = isMute --> post notification
  
  /// VideoPlayerManager/FeedMusicPlayingManager --> receive notification --> update internal property
  
  /// FeedMusicCell/FeedContentCellMusicView --> receive notification --> update ui
  
  /// 社区视频、音乐静音状态因为人为选择导致的静音状态改变, 同时发送通知. 注意，一些通过代码强制静音的改变不会触发该通知
  static var isMediaMute = true {
    didSet {
      defaultMuteExpired = true
      NotificationCenter.default.post(
        name: .WHCommunityMediaMuteDidChangedManually,
        object: nil,
        userInfo: [WHMuteDidChangeInfoKey: isMediaMute])
    }
  }
  /// 默认的静音状态是否已经无效, 不同页面有自己的静音状态，但当用户中改变静音状态后，所有页面的静音状态值都将无效
  static var defaultMuteExpired = false
}
