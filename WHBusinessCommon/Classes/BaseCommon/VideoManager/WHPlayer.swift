//
//  WHPlayer.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/12/25.
//

import UIKit
import MTMFFPlayer
import AVFoundation


@objc
public enum WHPlayerState: Int {
    /// 初始化状态
    case unknown
    /// 正在加载 URL
    case loading
    /// 正在播放
    case playing
    /// 暂停
    case paused
    /// 停止
    case stopped
}

@objc
public enum WHPlayerEvent: Int {
    ///  即将 prepare
    case willPrepare
    ///  加载完成
    case prepared
    ///  开始缓冲
    case bufferBegin
    ///  结束缓冲
    case bufferEnd
    /// 首帧渲染开始
    case renderingStart
    /// 播放进度更新
    case progressUpdated
    case enoughBuffer
    ///  将要 seek
    case willSeek
    ///  播放到结尾
    case reachEnd
    ///  即将重新播放
    case willPlayback
    ///  重新播放
    case playbacked
    ///  发生错误
    case error
    ///  其他
    case other
}

@objc
public enum WHPlayerMetadataType: Int {
    /// mp4 文件
    case container
    /// 视频流
    case video
    /// 音频流
    case audio
}

@objc
public enum WHPlayerType: Int {
    case auto
    case video
    case audio
}

@objc
public class WHPlayRange: NSObject {
    @objc public private(set) var startTime: TimeInterval = 0
    @objc public private(set) var endTime: TimeInterval = 0
    
    @objc public convenience init(startTime: TimeInterval, endTime: TimeInterval) {
        self.init()
        
        self.startTime = startTime
        self.endTime = endTime
    }
}

public typealias WHPlayerEventHandle = (_ event: WHPlayerEvent, _ detail: Any?) -> Void
public typealias WHPlayerStateHandle = (_ event: WHPlayerState) -> Void
public typealias WHPlayerCompletionHandle = (_ finished: Bool) -> Void
public typealias WHPlayerProgressHandle = (_ progress: CGFloat, _ progressInRange: CGFloat, _ currentTime: TimeInterval) -> Void

@objc
public protocol WHPlayerProtocol: WHPlayerStatisticsProtocol {
    @objc var urlStr: String? { get set }
    
    @objc var requestHeader: Dictionary<String, Any>? { get set }
    
    /// 播放范围
    @objc var type: WHPlayerType { get set }
    
    /// 播放范围
    @objc var playRange: WHPlayRange? { get }
    
    /// 视图，调整尺寸直接设置其 frame / bounds / contentMode(AspectFit/AspectFill)
    @objc var view: UIView { get }

    /// 进度
    @objc var progress: CGFloat { get }

    /// 时长
    @objc var duration: TimeInterval { get }

    /// 视频尺寸
    @objc var videoSize: CGSize { get }

    /// 当前画面截图
    @objc var snapshot: UIImage? { get }

    /// 当前的状态
    @objc var state: WHPlayerState { get }

    /// 静音
    @objc var isMute: Bool { get set }

    /// 播放速度
    @objc var rate: CGFloat { get set }

    /// 是否循环播放
    @objc var isLoopPlay: Bool { get set }
    
    /// 循环播放次数
    @objc var loopCount: Int { get set }

    /// 准备完成后是否自动播放
    @objc var autoPlay: Bool { get set }

    /// 进度更新频率
    @objc var progressUpdateInterval: CGFloat { get set }

    /// 是否处理 AudioSession 相关逻辑
    @objc var handleAudioSession: Bool { get set }

    /// 音频淡出
    @objc var audioFadeOut: Bool { get set }
    
    /// 音频淡出时间
    @objc var audioFadeOutTime: CGFloat { get set }
    
    /// 音频淡入
    @objc var audioFadeIn: Bool { get set }
    
    /// 音频淡入时间
    @objc var audioFadeInTime: CGFloat { get set }

    /// 音频音量
    @objc var audioVolume: CGFloat { get set }
    
    @objc var isSeeking: Bool { get }

    @objc var eventHandle: WHPlayerEventHandle? { get }

    @objc func setupStateHandle(_ handle: @escaping WHPlayerStateHandle)
    @objc func setupEventHandle(_ handle: @escaping WHPlayerEventHandle)
    @objc func setupProgressHandle(_ handle: @escaping WHPlayerProgressHandle)
    
    @objc func seek(toTime time: TimeInterval, completion: WHPlayerCompletionHandle?)
    @objc func seek(toPercent percent: CGFloat, completion: WHPlayerCompletionHandle?)
    
    @objc(updatePlayRangeWithStart:end:)
    func updatePlayRange(_ start: TimeInterval, _ end: TimeInterval)
    @objc func updatePlayRange(_ range: WHPlayRange)
    
    @objc func play()
    @objc func pause()
    @objc func stop()
    @objc func prepare()
    
    @objc func play(url: String?)
    @objc optional func play(asset: AVAsset?)
    @objc optional func play(item: AVPlayerItem?)
    
    @objc func metadata(for type: WHPlayerMetadataType, key: String) -> String?
}

extension WHPlayerProtocol {
    func convertedProgress(_ progress: CGFloat) -> CGFloat {
        let currentTime = Double(progress) * duration
        let range = self.playRange ?? WHPlayRange(startTime: 0, endTime: duration)
        let rangeDuration = range.endTime - range.startTime
        let convertedProgress = rangeDuration <= 0 ? 0 : (currentTime - range.startTime) / rangeDuration
        
        return CGFloat(convertedProgress)
    }
    
    func checkPlayProgress(_ progress: CGFloat) -> (isNeedSeek: Bool, percent: CGFloat) {
        guard let range = playRange, duration > 0 else {
            return (isNeedSeek: false, percent: progress)
        }

        let duration = CGFloat(self.duration)
        let currentTime = progress * duration
        let startTime = CGFloat(range.startTime).wh_clamped(to: 0...duration)
        let endTime = CGFloat(range.endTime).wh_clamped(to: 0...duration)
        let isRangeLegal = duration > 0
        
        // 对 startTime 有 1000ms 的容忍误差值，防止播放器 seek 后返回的进度值偏小导致的重复 seek
        if isRangeLegal, currentTime < duration,
           (round(startTime) - round(currentTime) > 1 || currentTime > endTime) {
            let percent = startTime / duration
            return (isNeedSeek: true, percent: percent)
        }

        return (isNeedSeek: false, percent: progress)
    }
    
    func updatePlayProgress(_ progress: CGFloat) {
        let result = checkPlayProgress(progress)
        
        if result.isNeedSeek {
            if isLoopPlay {
                if !isSeeking {
                    eventHandle?(.willPlayback, nil)
                    seek(toPercent: result.percent + 0.001) { (finished) in
                        self.eventHandle?(.playbacked, nil)
                    }
                    loopCount += 1
                }
            } else {
                pause()
            }
        }
    }
}

public typealias WHPlayerStatisticsInfoHandle = (_ info: WHPlayerInfo?) -> Void
@objc
public protocol WHPlayerStatisticsProtocol: NSObjectProtocol {
    /// 在线url，用于辅助解析统计信息
    @objc var analysisUrl: URL? { get set }
    
    /// 外部添加的统计信息
    @objc var extendedInfo: [AnyHashable: Any]? { get set }

    /// 统计回调，回调时机为播放器 stop
    @objc func setupStatisticsInfoHandle(_ handle: @escaping WHPlayerStatisticsInfoHandle)
    
    @objc func processCurrentStatisticsInfo()
}

extension WHPlayerStatisticsProtocol {
    public func innerStatisticsHandle(_ playerInfo: WHPlayerInfo?) {
        if let playerInfo = playerInfo {
//            var model = PlayerMusicStatisticsModel(metadataValue: playerInfo.metadata)
//
//            if model == nil, let remoteUrl = playerInfo.remoteUrl?.absoluteString {
//                model = PlayerMusicStatisticsModel(remoteUrl: remoteUrl)
//            }
            
//            if let model = model {
//                model.sync(with: playerInfo)
//                PlayerMusicStatistics.analyticsPlay(model)
//            }
        }
    }
}

@objc
public class WHPlayerInfo: NSObject {
    /// 开始播放的时间
    @objc public var startPlayTime: TimeInterval = 0
    /// 结束播放的时间
    @objc public var endPlayTime: TimeInterval = 0
    /// 播放时长
    @objc public var playedTime: TimeInterval = 0
    /// 播放次数
    @objc public var playedCount: Int = 0
    /// 音乐时长
    @objc public var duration: TimeInterval = 0
    /// 视频时长
    @objc public var videoDuration: TimeInterval = 0
    /// 是否是视频
    @objc public var isVideo = false
    /// 是否在线播放
    @objc public var isOnline = false
    /// 在线url
    @objc public var remoteUrl: URL?
    /// 目前仅用于获取腾讯音乐 tid
    @objc public var metadata: String?
    /// 外部扩展信息
    @objc public var extendedInfo: [AnyHashable: Any]?
    
    public func sync(with info: [AnyHashable: Any]?) {
        guard let info = info else {
            return
        }
        
        if let playTime = info["play_time"] as? Int {
            playedTime = TimeInterval(playTime)
        }
        
        if let mediaTime = info["media_time"] as? NSNumber {
            duration = mediaTime.doubleValue
            videoDuration = duration
        }
        
        if let useBufferInfo = info["use_buffer_info"] as? MTMFFInfoUseCacheType {
            isOnline = useBufferInfo != .all
        }
    }
}
