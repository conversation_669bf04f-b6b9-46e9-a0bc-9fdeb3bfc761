//
//  WHStreamingPlayer.swift
//  MTXX
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/6/3.
//  Copyright © 2019 Meitu. All rights reserved.
//

import UIKit
import MTMFFPlayer

precedencegroup MTStreamingPlayerForwardComposition {
  associativity : left
  higherThan: TernaryPrecedence
  lowerThan: LogicalDisjunctionPrecedence
}

infix operator |>: MTStreamingPlayerForwardComposition

func |> <T, S>(t: T, block: (T) -> S) -> S {
    return block(t)
}

func |> <T>(t: inout T, block: (inout T) -> Void) {
    block(&t)
}

@objc
open class WHStreamingPlayer: NSObject, WHPlayerProtocol {
    @objc public private(set) lazy var player = MTMFFPlayer()

    public var urlStr: String? {
        didSet {
            player.url = urlStr
            player.httpHeaderInfo = ["User-Agent" : "MeituWhee/1.2.0"]
        }
    }
    
    public var analysisUrl: URL?
    
    public var type: WHPlayerType = .auto
    
    public var requestHeader: Dictionary<String, Any>? {
        get {
            return player.httpHeaderInfo as? Dictionary<String, Any>
        }
        
        set {
            player.httpHeaderInfo = newValue
        }
    }
    
    public var loopCount: Int = 0 {
        didSet {
            if loopCount != 0 {
                playerInfo?.playedCount = loopCount
            }
        }
    }
    
    public private(set) var playRange: WHPlayRange?
    
    /// 当前播放进度回调（注：返回的是在 playRange 范围内的进度值）
    private var progressHandle: WHPlayerProgressHandle?
    public private(set) var eventHandle: WHPlayerEventHandle?

    public var extendedInfo: [AnyHashable : Any]?
    
    private var statisticsInfoHandle: WHPlayerStatisticsInfoHandle?
    
    private var playerInfo: WHPlayerInfo?
    
    public var isVideo = true
    
    /// 视图，调整尺寸直接设置其 frame / bounds / contentMode(AspectFit/AspectFill)
    public var view: UIView {
        return player.view
    }

    /// 进度
    public var progress: CGFloat {
      return player.progress |> { $0.isNaN ? 0 : $0 }
    }

    /// 时长, 单位为秒
    public var duration: TimeInterval {
        return TimeInterval(player.duration) / 1000
    }

    /// 视频尺寸
    public var videoSize: CGSize {
        return player.videoSize
    }

    /// 当前画面截图
    public var snapshot: UIImage? {
        return player.snapshot
    }

    /// 当前的状态
    public var state: WHPlayerState {
        return WHPlayerState(rawValue: player.state)
    }
  
    #warning("The following property wrappers can be optimized away in Swift5.1")

    /// 静音
    public var isMute: Bool {
        get {
            return player.mute
        }
        
        set {
            player.mute = newValue
        }
    }

    /// 镜像
    public var reflection: Bool {
        get {
            return player.reflection
        }
        
        set {
            player.reflection = newValue
        }
    }

    /// 播放速度
    public var rate: CGFloat {
        get {
            return player.rate
        }
        
        set {
            player.rate = newValue
        }
    }

    /// 是否循环播放
    public var isLoopPlay: Bool {
        get {
            return player.loopPlay
        }
        
        set {
            player.loopPlay = newValue
        }
    }

    /// 准备完成后是否自动播放
    public var autoPlay: Bool {
        get {
            return player.autoPlay
        }
        
        set {
            player.autoPlay = newValue
        }
    }

    /// 多少像素更新一次进度
    public var updatePixel: CGFloat {
        get {
            return player.updatePixel
        }
        
        set {
            player.updatePixel = newValue
        }
    }

    /// 进度更新频率，为 0 时由播放器决定
    public var progressUpdateInterval: CGFloat {
        get {
            return player.progressUpdateInterval
        }
        
        set {
            player.progressUpdateInterval = newValue
        }
    }

    /// 精准seek
    public var exactSeek: Bool {
        get {
            return player.seekMode == .exact
        }
        
        set {
            player.seekMode = newValue ? .exact : .auto
        }
    }

    /// 是否处理 AudioSession 相关逻辑
    public var handleAudioSession: Bool {
        get {
            return player.handleAudioSession
        }
        
        set {
            player.handleAudioSession = newValue
        }
    }

    /// 是否挂起绘制
    public var suspendedDrawing: Bool {
        get {
            return player.suspendedDrawing
        }
        
        set {
            player.suspendedDrawing = newValue
        }
    }

    /// H264 硬解码器是否开启
    public var videoToolBoxDecoderEnable: Bool {
        get {
            return player.videoToolBoxDecoderEnable
        }
        
        set {
            player.videoToolBoxDecoderEnable = newValue
        }
    }

    /// 当前可用的缓冲字节数
    public var availableBufferBytes: Int {
        return player.availableBufferBytes
    }

    /// 首次播放前的 loading 延迟几秒触发
    public var loadingDelayBeforePlay: CGFloat {
        get {
            return player.loadingDelayBeforePlay
        }
        
        set {
            player.loadingDelayBeforePlay = newValue
        }
    }

    /// 省流模式
    public var lowCostMode: Bool {
        get {
            return player.lowCostMode
        }
        
        set {
            player.lowCostMode = newValue
        }
    }

    /// 强制视频同步
    public var videoSyncEnable: Bool {
        get {
            return player.videoSyncEnable
        }
        
        set {
            player.videoSyncEnable = newValue
        }
    }

    /// 音频淡出
    public var audioFadeOut: Bool {
        get {
            return player.audioFadeOut
        }
        
        set {
            player.audioFadeOut = newValue
        }
    }
    
    public var audioFadeOutTime: CGFloat = 1
    
    public var audioFadeIn: Bool = false
    
    public var audioFadeInTime: CGFloat = 1

    /// 音频音量
    public var audioVolume: CGFloat {
        get {
            return player.audioVolume
        }
        
        set {
            player.audioVolume = newValue
        }
    }
    
    public private(set) var isSeeking = false
    
    deinit {
        progressHandle = nil
        statisticsInfoHandle = nil
        eventHandle = nil
    }
    
    public override init() {
        super.init()
        
        player.enableAirPlay = false
        setupInnerEventHandle()
        setupInnerStatisticsInfoHandle()
    }

    public func setupStateHandle(_ handle: @escaping WHPlayerStateHandle) {
        player.setupStateHandle { (innerState) in
            handle(WHPlayerState(rawValue: innerState))
        }
    }

    public func setupEventHandle(_ handle: @escaping WHPlayerEventHandle) {
        setupInnerEventHandle(with: handle)
    }
    
    public func setupProgressHandle(_ handle: @escaping WHPlayerProgressHandle) {
        progressHandle = handle
    }
    
    public func setupStatisticsInfoHandle(_ handle: @escaping WHPlayerStatisticsInfoHandle) {
        setupInnerStatisticsInfoHandle(handle)
    }
    
    private func setupInnerStatisticsInfoHandle(_ handle: WHPlayerStatisticsInfoHandle? = nil) {
        let innerHandel: WHPlayerStatisticsInfoHandle = {[weak self] (playerInfo) in
            guard let self = self else {
                return
            }
            
            playerInfo?.extendedInfo = self.extendedInfo
            playerInfo?.remoteUrl = self.analysisUrl
            
            self.innerStatisticsHandle(playerInfo)
            
            handle?(playerInfo)
        }
        
        statisticsInfoHandle = innerHandel
    }
    
    public func processCurrentStatisticsInfo() {
        statisticsInfoHandle?(playerInfo)
    }

    public func prepare() {
        player.prepare()
    }

    public func play() {
        player.play()
    }

    public func pause() {
        player.pause()
    }

    public func stop() {
        player.stop()
        
        loopCount = 0
        
        if playerInfo != nil {
            statisticsInfoHandle?(playerInfo)
            playerInfo = nil
        }
    }
    
    public func play(url: String?) {
        if url != self.urlStr {
            stop()
            self.urlStr = url
            if url != nil {
                prepare()
                play()
            }
        }
    }

    private func setupInnerEventHandle(with externalHandle: WHPlayerEventHandle? = nil) {
        eventHandle = externalHandle
        let innerEventHandle: MTMFFPlayerEventHandle = {[weak self] (event, detail) in
            guard let self = self else {
                return
            }

            switch event {
            case .prepared:
                self.playerInfo = WHPlayerInfo()
                self.playerInfo?.isVideo = self.type != .audio
                if let metadata = self.metadata(for: .container, key: "comment") {
                    self.playerInfo?.metadata = metadata
                }
                
                self.playerInfo?.duration = self.duration
                self.playerInfo?.videoDuration = self.duration
                if let url = self.urlStr {
                    let isLocal = URL(string: url)?.isFileURL ?? false || MTMFFDataCache.shared().cacheExist(withURL: url)
                    self.playerInfo?.isOnline = !isLocal
                }
            case .renderingStart:                
                let result = self.checkPlayProgress(self.progress)
                if result.isNeedSeek {
                    self.seek(toPercent: result.percent, completion: nil)
                }

            case .progressUpdated:
                guard let detail = detail as? NSNumber else {
                    break
                }

                let progress = CGFloat(detail.floatValue)

                self.updatePlayProgress(progress)

                let convertedProgress = self.convertedProgress(progress)

                let currentTime = Double(progress) * self.duration
                self.progressHandle?(progress, convertedProgress, currentTime)
                
                self.playerInfo?.playedTime = currentTime

            case .reachEnd:
                self.eventHandle?(.reachEnd, detail)
                
                if self.isLoopPlay {
                    self.eventHandle?(.willPlayback, nil)
                    
                    if let startTime = self.playRange?.startTime, startTime != 0 {
                        self.seek(toTime: startTime, completion: { (finished) in
                            self.eventHandle?(.playbacked, nil)
                        })
                    }
                    
                    self.loopCount += 1
                }

            default:
                break
            }

            if event != .reachEnd {
                self.eventHandle?(WHPlayerEvent(rawValue: event), detail)
            }
        }

        player.setupEventHandle(innerEventHandle)
    }
    
    public func updatePlayRange(_ start: TimeInterval, _ end: TimeInterval) {
        playRange = WHPlayRange(startTime: start, endTime: end)
    }
    
    public func updatePlayRange(_ range: WHPlayRange) {
        updatePlayRange(range.startTime, range.endTime)
    }
    
    public func seek(toTime time: TimeInterval, completion: WHPlayerCompletionHandle?) {
        let duration = TimeInterval(self.duration)
        guard duration > 0 else {
            completion?(false)
            return
        }
        
        let percent = time.wh_clamped(to: 0...duration) / duration
        seek(toPercent: CGFloat(percent), completion: completion)
    }
    
    public func seek(toPercent percent: CGFloat, completion: WHPlayerCompletionHandle?) {
        isSeeking = true
        player.seek(toPercent: percent) { (finished) in
            self.isSeeking = false
            completion?(finished)
        }
    }
    
    public func fastSeek(toPercent percent: CGFloat) {
        player.fastSeek(toPercent: percent)
    }
    
    public func metadata(for type: WHPlayerMetadataType, key: String) -> String? {
        return player.metadata(with: type.rawValue, key: key)
    }

}

extension WHPlayerMetadataType: RawRepresentable {
    public typealias RawValue = MTMFFMetadataType
    
    public init(rawValue: MTMFFMetadataType) {
        switch rawValue {
        case .container:
            self = .container
        case .video:
            self = .video
        case .audio:
            self = .audio
        @unknown default:
            self = .container
        }
    }
    
    public var rawValue: MTMFFMetadataType {
        switch self {
        case .container:
            return .container
        case .video:
            return .video
        case .audio:
            return .audio
        }
    }
}

extension WHPlayerState: RawRepresentable {
    public typealias RawValue = MTMFFPlayerState
    
    public init(rawValue: MTMFFPlayerState) {
        switch rawValue {
        case .unknown:
            self = .unknown
        case .loading:
            self = .loading
        case .playing:
            self = .playing
        case .paused:
            self = .paused
        case .stopped:
            self = .stopped
        @unknown default:
            self = .unknown
        }
    }
    
    public var rawValue: MTMFFPlayerState {
        switch self {
        case .unknown:
            return .unknown
        case .loading:
            return .loading
        case .playing:
            return .playing
        case .paused:
            return .paused
        case .stopped:
            return .stopped
        }
    }
}

extension WHPlayerEvent: RawRepresentable {
    public typealias RawValue = MTMFFPlayerEvent
    
    public init(rawValue: MTMFFPlayerEvent) {
        switch rawValue {
        case .willPrepare:
            self = .willPrepare
        case .prepared:
            self = .prepared
        case .bufferBegin:
            self = .bufferBegin
        case .bufferEnd:
            self = .bufferEnd
        case .renderingStart:
            self = .renderingStart
        case .enoughBuffer:
            self = .enoughBuffer
        case .progressUpdated:
            self = .progressUpdated
        case .willSeek:
            self = .willSeek
        case .reachEnd:
            self = .reachEnd
        case .error:
            self = .error
        @unknown default:
            self = .other
        }
    }
    
    public var rawValue: MTMFFPlayerEvent {
        switch self {
        case .willPrepare:
            return .willPrepare
        case .prepared:
            return .prepared
        case .bufferBegin:
            return .bufferBegin
        case .bufferEnd:
            return .bufferEnd
        case .renderingStart:
            return .renderingStart
        case .enoughBuffer:
            return .enoughBuffer
        case .progressUpdated:
            return .progressUpdated
        case .willSeek:
            return .willSeek
        case .reachEnd:
            return .reachEnd
        case .error:
            return .error
        case .other:
            return .error
        case .willPlayback, .playbacked:
            return .reachEnd
        }
    }
}
