//
//  WHAILiveUploadManager.swift
//  Pods
//
//  Created by linda_zl on 2025/3/17.
//

import UIKit
import Foundation
import WHBaseLibrary
import MTPhotoLibrary
import MTResourceUpload
import SDWebImage


public class WHAILiveUploadManager {
    
    public static let shared = WHAILiveUploadManager()

    private init() {}
    
    // 检测图片合法
    func checkImageIsPermissible(_ mtAsset: MTPhotoAsset) -> Bool {
        guard let imgFormat = mtAsset.mediaFormat() else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片格式不支持，请更换图片后再试"))
            return false
        }
        let suffixName = getSuffixName(imgFormat: imgFormat)
        //检测格式
        if !([".jpg", ".jpeg", ".png"].contains(suffixName)) {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片格式不支持，请更换图片后再试"))
            return false
        }
         //检测宽高比
         if exceedMaxSizeLimit(mtAsset: mtAsset) == true {
             UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片比例较长，请更换图片后再试"))
             return false
         }
         //检测分辨率
         let uploadImg = mtAsset.fetchImage(with: .imagefullResolutionImage)
         if let img = uploadImg, imagePixelsLimit(image: img) == true {
             UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片输入短边小于300像素，请更换图片后再试"))
             return false
         }
        
        return true
    }
    
    
    public func checkImageIsPermissible(of image: UIImage) -> Bool {
        //检测宽高比
        if imageMaxSizeLimit(image) == true {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片比例较长，请更换图片后再试"))
            return false
        }
        //检测分辨率
        if imagePixelsLimit(image: image) == true {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片输入短边小于300像素，请更换图片后再试"))
            return false
        }
        
        if beyondLengthBoundary(image){
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过10M，请更换图片后再试"))
            return false
        }
        return true
    }
    
    public func imageMaxSizeLimit(_ image: UIImage) -> Bool {
        let size = image.size
        if ( (size.width / size.height) > 2.5 || (size.height / size.width) > 2.5 ) {
            return true
        }
        return false
    }
    
    public func imageFormat(from urlString: String) -> String {
        guard let url = URL(string: urlString) else { return ""}
        let pathExtension = url.pathExtension.lowercased()
        var suffix = ".jpg"
        switch pathExtension {
            case "jpeg":
                suffix = ".jpeg"
            case "png":
                suffix = ".png"
            default:
                suffix = ".jpg"
        }
        return suffix
    }
    
    func getSuffixName(imgFormat: String) -> String {
        var suffix = ".jpg"
        switch imgFormat {
        case "jpeg":
            suffix = ".jpeg"
        case "png":
            suffix = ".png"
        default:
            suffix = ".jpg"
        }
        return suffix
    }
    
    //检测像素大于 300 * 300
    func imagePixelsLimit(image: UIImage) -> Bool {
        let pixelWidth = image.size.width * image.scale
        let pixelHeight = image.size.height * image.scale
        return pixelWidth < 300 || pixelHeight < 300
    }

    //展示图片不符合要求
    func showImageAlert() {
        let alertView = WHCommonAlertView(title: WHLocalizedString("图片不符合要求"), desStr: WHLocalizedString("要求格式: jpg、jpeg、png \n大小: 10M以内"), alertViewType: .sureStyle)
        alertView.show(in: UIApplication.wh_currentWindow)
    }

    //变长>= 10000会爆内存
    func beyondLengthBoundary(_ image: UIImage) -> Bool {
        max(image.size.width, image.size.height) >= 10000
    }
    
    //检测图片文件大小
    func beyondLimitImageSize(mtAsset: MTPhotoAsset?) -> Bool {
        let length = mtAsset?.fileSize() ?? 0
        let MB = Double(length) / 1024.0 / 1024.0
        return MB >= 10
    }
    //imageUrlString：用户判断本地时候有缓存，如果有磁盘缓存的话，用磁盘缓存数据判断会更准确一些
    public func beyondLimitSize(for image: UIImage, imageSuffix: String, imageUrlString: String = "") -> Bool{
        var imageData : Data?
        if imageSuffix == ".png" {
            if imageUrlString.count > 0,
               let cacheKey = SDWebImageManager.shared.cacheKey(for: URL(string: imageUrlString)),
                let cacheData = SDImageCache.shared.diskImageData(forKey: cacheKey) {
                imageData = cacheData
            } else {
                imageData = image.pngData()
            }
        } else {
            imageData = image.jpegData(compressionQuality: 1.0)
        }

        if let data = imageData, Double(data.count) / (1024 * 1024) <= 10 {
            return false
        }else{
            return true
        }
    }

    //检测图片宽高比是否超过2.5 : 1
    func exceedMaxSizeLimit(mtAsset: MTPhotoAsset?) -> Bool {
        let width = mtAsset?.dimensions.width ?? 1
        let height = mtAsset?.dimensions.height ?? 1
        if (CGFloat(width) / CGFloat(height) > 2.5) || (CGFloat(height) / CGFloat(width) > 2.5) {
            return true
        }
        return false
    }

}









