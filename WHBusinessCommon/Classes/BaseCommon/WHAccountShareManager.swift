//
//  WHAccountShareManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/9/7.
//

import Foundation
import MTAccount
import MTSSOShareKit

public protocol WHAccountLoginManagerDelegate: AnyObject{
    /// 即将登录
    func willFinishLogin(completeType: MTACCompleteType, account: MTACAccountInfo)
    /// 登录完成
    func didFinishLogin(completeType: MTACCompleteType, account: MTACAccountInfo)
    /// 取消登录
    func didCancelLogin()
    /// 登录失败
    func didFailedLogin()
    ///推出登录
    func didLoginOut()
    /// 修改个人资料
    func didModifyPersonalInfo()
}

/// MARK : 大账号管理类
public let WHAccountShareManager = WHAccountManager.share

@objc public class WHAccountManager: NSObject {

    
    static let share = WHAccountManager()
    public weak var delegate: WHAccountLoginManagerDelegate?
    
    /// 大账号token
    public func getAccessToken() -> String {
        MTACAccountManager.share().accessToken ?? ""
    }
    /// 是否登录态
    public func isLogin() -> Bool{
        let token = getAccessToken()
        if token.count ?? 0 <= 0 {
            return false
        }
        return true
    }
    
    /// 退出登录态
    public func loginOut() {
        MTACAccountManager.share().logout()
        MTACInfoManager.clearLoginInfo()
        delegate?.didLoginOut()
    }
    
    /// MARK : 个人资料
    public func getUserProfileVC() -> UIViewController {
        delegate = WHLoginViewsharedManager
        return MTACNewLoginViewController.init(string: kProfile, isLocal: true, delegate: self)
    }
    
    /// MARK : 账号与安全
    public func getAccountManagerVC() -> UIViewController {
        let vc = MTACNewLoginViewController.init(string: kAccountManager, isLocal: true, delegate: self)
        delegate = WHLoginViewsharedManager
        return vc
    }
    
}
extension WHAccountManager: MTACNewLoginViewControllerDelegate, MTACLoginAlertDelegate {
    /// MARK : 登录完成回调时机
    private func loginCompleteStatus(_ account : MTACAccountInfo, completeType: MTACCompleteType) {
//        if ((self.delegate?.willFinishLogin) != nil) {
//            self.delegate?.willFinishLogin(completeType: completeType, account: account)
//        }
        
        switch completeType{
        case .loginSuccess:
            if ((self.delegate?.didFinishLogin) != nil) {
                self.delegate?.didFinishLogin(completeType: completeType, account: account)
            }
        case .switchSuccess:
            if ((self.delegate?.didFinishLogin) != nil) {
                self.delegate?.didFinishLogin(completeType: completeType, account: account)
            }
        default:
            break
        }
        
    }
    
    public func newLoginViewController(_ loginViewController: UIViewController, account: MTACAccountInfo, completeType: MTACCompleteType) {
        loginViewController.dismiss(animated: true) {
            self.loginCompleteStatus(account, completeType: completeType)
        }
//        loginCompleteStatus(account, completeType: completeType)
    }
    
    public func newLoginViewController(_ loginViewController: UIViewController, account: MTACAccountInfo, completeType: MTACCompleteType, webLoginResultBlock: MTACWebLoginResultBlock? = nil) {
        loginViewController.dismiss(animated: true) {
            self.loginCompleteStatus(account, completeType: completeType)
        }
        webLoginResultBlock?(nil, nil)
    }
    ///统一登录协议web唤起的半屏登录成功，返回token
    public func loginAlert(_ alertViewController: UIViewController, account: MTACAccountInfo, webLoginResultBlock: MTACWebLoginResultBlock? = nil) {
        alertViewController.dismiss(animated: true) {
            self.loginCompleteStatus(account, completeType: .loginSuccess)
        }
        webLoginResultBlock?(nil, nil)
    }
    ///统一登录协议web唤起的带弹窗类型的半屏登录成功,返回token （暂时只有半屏运营活动弹窗类型MTACActivityLoginAlert）
    public func loginAlert(_ alertViewController: UIViewController, account: MTACAccountInfo, alertType: MTACAlertType, activityInfo: MTACActivityInfo, webLoginResultBlock: MTACWebLoginResultBlock? = nil) {
        alertViewController.dismiss(animated: true) {
            self.loginCompleteStatus(account, completeType: .loginSuccess)
        }
        webLoginResultBlock?(nil, nil)
    }
    public func loginAlert(_ alertViewController: UIViewController, account: MTACAccountInfo) {
        alertViewController.navigationController?.dismiss(animated: true, completion: {})
        if ((self.delegate?.didFinishLogin) != nil) {
            self.delegate?.didFinishLogin(completeType: .loginSuccess, account: account)
        }
    }
    
    public func leaveNewLoginViewController(_ loginViewController: UIViewController) {
        if ((self.delegate?.didCancelLogin) != nil) {
            self.delegate?.didCancelLogin()
        }
    }
    
    public func leaveLoginAlertView() {
        if ((self.delegate?.didCancelLogin) != nil) {
            self.delegate?.didCancelLogin()
        }
    }

    public func logoutAccount(_ loginViewController: UIViewController, isRelogin: Bool) {
        if !isRelogin {
            loginOut()
            loginViewController.navigationController?.popToRootViewController(animated: true)
        }
    }

    public func thirdPartyAuthFailed(withType type: String, errorMessage: String, errorCode code: Int, loginViewController: UIViewController) {
        debugPrint(errorMessage)
    }
    
    public func accountNotice(_ loginViewController: UIViewController, code: Int, data: [AnyHashable : Any]?) {
        if let noticeCode = MTACAccountNoticeCode(rawValue: code) {
            if noticeCode == .cancellationSuccess {
                // 注销
                loginOut()
                loginViewController.navigationController?.popToRootViewController(animated: true)
            }
            if noticeCode == .modifyPersonalInfo {//修改个人资料
                delegate?.didModifyPersonalInfo()
                loginViewController.navigationController?.popViewController(animated: true)
            }
        }
    }
 
    public func closeVC(_ loginViewController: UIViewController) {
//        if let navigationController = loginViewController.navigationController,
//           navigationController.viewControllers.count > 1 {
//            navigationController.popViewController(animated: true)
//        } else if loginViewController.presentingViewController != nil {
            loginViewController.dismiss(animated: true) {
//            }
        }
    }
}

extension WHAccountManager: MTACAccountDelegate{
    public func login(with platform: MTACThirdPartyPlatform, currentViewController: UIViewController, completion: @escaping MTACLoginThirdPartyCompletionBlock) {
       
        switch platform {
        case .MTACWeChat :
            let url = NSURL(string:"weixin://")
            if let resultUrl = url,UIApplication.shared.canOpenURL(resultUrl as URL){
                MTSSOWeChat.requestCode(currentViewController as? UIViewController & SKStoreProductViewControllerDelegate) { (userInfo) in
                    let code = userInfo?["code"]
                    if code != nil {
                        completion(true, code as? String, nil)
                    }else {
                        completion(false, nil, nil)
                    }

                } failure: { (error) in
                    debugPrint(error.debugDescription)
                }
            }else {
                WHRouter.topViewController?.showToast(title: "您安装的微信版本过低或尚未安装")
            }
            break
        case .MTACQQ :
            let url = NSURL(string:"mqqapi://")
            if let resultUrl = url,UIApplication.shared.canOpenURL(resultUrl as URL){
                MTSSOTencent.login { (userInfo : [AnyHashable : Any]?, oauth : Any?, error : Error?) in
                    //授权登录成功
                    if (error == nil) {
                        let tencentOAuth : TencentOAuth = oauth as! TencentOAuth
                        let token : String = tencentOAuth.accessToken
                        let expirationDate = tencentOAuth.expirationDate.timeIntervalSince1970
                        let expirationstr = String(expirationDate)
                        completion(true, token, [MTACThirdLoginExtraInfoExpiresInKey: expirationstr])
                    }else {
                         completion(false, nil, nil)
                    }
                }
            }else {
                WHRouter.topViewController?.showToast(title: "您安装的QQ版本过低或尚未安装")

            }

            break
        case .MTACWeibo :
            MTSSOWeibo.login { (userInfo : [AnyHashable : Any]?, accessToken : String?, error : Error?) in
                // 微博授权成功
                if (error == nil) {
                    completion(true, accessToken, nil)
                }else {
                    completion(false, nil, nil)
                }
            }
            break
        case .MTACApple :
            if #available(iOS 13.0, *) {
                MTSSOApple.login { (userInfo : [AnyHashable : Any]?, accessToken : String?, error : Error?) in
                    if error == nil {
                        completion(true, MTACLoginManager.token(withAppleToken: accessToken ?? "", userInfo: userInfo ?? [:]),nil)
                    }else {
                        completion(false, nil, nil)
                    }
                }
            }

        break
        default:
            break
        }
    }
}


