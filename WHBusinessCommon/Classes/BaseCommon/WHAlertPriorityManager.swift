//
//  WHAlertPriorityManager.swift
//  WHBusinessCommon
//
//  Created by Devin on 2025/3/12.
//

import Foundation
import YYModel

public let WHAlertPrioritySharedManager = WHAlertPriorityManager.shared

public class WHAlertPriorityManager {
    
    static let shared = WHAlertPriorityManager()
    private init() {}
    
    // MARK: - 助力弹窗在“我的”页面触发登录，管理“助力得美豆”和“新用户得美豆”弹窗显示顺序
    public var boostLoginInMinePage: Bool {
        return _boostLoginInMinePage
    }
    private var _boostLoginInMinePage: Bool = false
    
    
    func manageBoostAndNewUserAlert(with code: String,
                                    doLogin: Bool,
                                    currentPageName: String?) {
        _boostLoginInMinePage = doLogin && currentPageName == "WHMineViewController"

        if doLogin {
            // 调用me.json接口，服务端才能把用户信息从大账号同步到WHEE的数据表中。
            // 新注册用户，如果不在WHEE的数据表中，服务端会判定不是新用户。所以登录之后先调用此接口
            WHSharedRequest.GET("/user/me.json") { response in
                var newUserAward: WHNewUserAwardTipInfo?
                if let result = response.data() as? [String: Any] {
                    let info = WHNewUserAward.yy_model(with: result)
                    if info?.awardTip?.isShow == true {
                        newUserAward = info?.awardTip
                    }
                }
                
                // 无论me.json是否成功，都调用助力接口。不能阻塞存量的“新用户”助力
                self.requestBoostWith(code: code,
                                      newUserAward: newUserAward)
            }
        } else {
            requestBoostWith(code: code,
                             newUserAward: nil)
        }
        
    }
    
    private func requestBoostWith(code: String,
                                  newUserAward: WHNewUserAwardTipInfo?) {
        WHInviteBoostRequest.requestBoostWith(code: code) { (boostResult, msg) in
            var count: Int?
            if let boostResult = boostResult,
               boostResult.result {
                count = boostResult.reawardAmount
            } else {
                UIApplication.wh_currentWindow?.showToast(title: msg ?? "网络异常")
            }
            
            if let count = count {
                self.showBoostResultAlertWith(count: count) {
                    if let newUserAward = newUserAward {
                        self.showNewUserAwardAlertWith(info: newUserAward) {
                            self._boostLoginInMinePage = false
                        }
                    }
                }
            } else if let newUserAward = newUserAward {
                self.showNewUserAwardAlertWith(info: newUserAward) {
                    self._boostLoginInMinePage = false
                }
            }
            
        }
    }
    
    /// 显示助力结果弹窗
    private func showBoostResultAlertWith(count: Int,
                                          completion: @escaping () -> ()) {
        guard let window = UIApplication.wh_currentWindow else {
            return
        }
        let alert = WHRewardAlertView(count: count,
                                      text: String(format: WHLocalizedString("%d美豆已到账"), count),
                                      showStarBg: true,
                                      closeBlock: completion,
                                      confirmBlock: completion)
        alert.show(in: window)
        NotificationCenter.default.post(name: .receiveAwardNotification, object: nil)
    }
    
    /// 显示新用户弹窗
    private func showNewUserAwardAlertWith(info: WHNewUserAwardTipInfo,
                                           completion: @escaping () -> ()) {
        guard let window = UIApplication.wh_currentWindow else {
            return
        }
        let tipView = WHAwardTipView(title: info.tipTitle,
                                     desc: info.tipDesc,
                                     completion: completion)
        tipView.show(in: window)
        NotificationCenter.default.post(name: .receiveAwardNotification, object: nil)
    }
    
}


@objcMembers
class WHNewUserAward: NSObject, YYModel, NSCoding {
    
    /// 新用户奖励提示
    var awardTip: WHNewUserAwardTipInfo?
    
    required override init() {
        super.init()
    }
    
    func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return [
            "awardTip": WHNewUserAwardTipInfo.classForCoder(),
        ]
    }
    
    class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "awardTip" : "tip",
        ]
    }
}

@objcMembers
class WHNewUserAwardTipInfo: NSObject, YYModel, NSCoding {
    
    var isShow: Bool = false
    var tipTitle: String = ""
    var tipDesc: String = ""
    
    required override init() {
        super.init()
    }
    
    func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "isShow"      : "show_new_user_award_tip",
            "tipTitle"  : "new_user_award_tip_title",
            "tipDesc": "new_user_award_tip_desc"
        ]
    }
}

extension Notification.Name {
    
    public static let receiveAwardNotification = Notification.Name(rawValue: "WHUserReceiveAwardNotification")
    
}
