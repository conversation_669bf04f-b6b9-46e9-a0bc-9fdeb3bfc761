//
//  WHAlertTaskManager.swift
//  WHAlertTaskManager
//

import Foundation

@objc
public enum WHAlertTaskPriorityType: WHAlertTaskPriority {
    case lowPriority = 30000
    case defaultPriority = 20000
    case highPriority = 10000
}

public typealias WHAlertTaskPriority = Int
public typealias WHAlertExecuteClosure = () -> Void

/// 弹窗任务协议
/**
 *priority  弹窗任务优先级，优先级数量越小，优先级越高，此设计便于扩展
 *execute:弹窗任务执行从弹窗管理器弹出，会执行此方法。弹窗任务实现协议，显示弹窗
 *show     弹窗任务显示，便于弹窗任务显示的调用
 *removeTask弹窗任务移除，所有弹窗任务都要实现此方法，否则弹窗管理器会堵塞
 */
@objc
public protocol WHAlertTaskProtocol: NSObjectProtocol {
    var priority: WHAlertTaskPriority { get set }
    func execute(_ completion: @escaping WHAlertExecuteClosure)
    func show()
    func removeTask()
    @objc optional func taskID() -> String
}

private var kMTAlertTaskIDKey: String = "kMTAlertTaskIDKey"

public extension WHAlertTaskProtocol {
    
    public func removeTask() {
        WHAlertTaskManager.removeTask(self)
    }
    
    public func taskID() -> String {
        if let string = objc_getAssociatedObject(self, &kMTAlertTaskIDKey) as? String {
            return string
        } else {
            let string = "\(NSStringFromClass(Self.self))_\(priority)_\(arc4random()%10000 + 1)"
            objc_setAssociatedObject(self, &kMTAlertTaskIDKey, string, objc_AssociationPolicy.OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            return string
        }
    }
}

/// 弹窗管理器协议
/**
 *showTask:添加并且推出弹窗任务
 *removeTask:移除弹窗任务
 *hasHigherPriority检测是否有比某个优先级更高的弹窗任务
 */
protocol WHAlertTaskManagerProtocol {
    func showTask(_ task: WHAlertTaskProtocol)
    func removeTask(_ task: WHAlertTaskProtocol)
    func hasHigherPriority(_ priority: WHAlertTaskPriority) -> Bool
    func removeAllTask()
}

@objc
public class WHAlertTaskManager: NSObject, WHAlertTaskManagerProtocol {
    /// 弹窗管理器是否为空
    @objc
    public var isEmpty: Bool {
        return self.alertTaskQueue.isEmpty
    }
    
    /// 弹窗任务队列
    private var alertTaskQueue: [WHAlertTaskProtocol] = []
    
    /// 当前正在执行的弹窗任务
    public weak var currentAlertTask: WHAlertTaskProtocol?
    
    fileprivate var lock: DispatchSemaphore = DispatchSemaphore(value: 1)
    
    static let `default` = WHAlertTaskManager()
    
    override private init() {
        super.init()
    }
    
    @objc
    public static func shared() -> WHAlertTaskManager {
        return WHAlertTaskManager.default
    }
    
    /// showTask:添加并且推出弹窗任务
    @objc
    public func showTask(_ task: WHAlertTaskProtocol) {
        lock.wait()
        var index = 0
        for t in alertTaskQueue {
            if t.priority > task.priority {
                break
            }
            index += 1
        }
        alertTaskQueue.insert(task, at: index)
        lock.signal()
        self.tryExecuteHighestPriorityTask()
    }
    
    /// hasHigherPriority检测是否有比某个优先级更高的弹窗任务
    @objc
    public func hasHigherPriority(_ priority: WHAlertTaskPriority) -> Bool {
        lock.wait()
        var satisfy = false
        if let currentAlertTask = self.currentAlertTask {
            if currentAlertTask.priority < priority {
                satisfy = true
            }
        }
        if satisfy == false {
            satisfy = self.alertTaskQueue.contains(where: { $0.priority < priority })
        }
        lock.signal()
        return satisfy
    }
    
    /// removeTask:移除弹窗任务
    @objc
    public func removeTask(_ task: WHAlertTaskProtocol) {
        lock.wait()
        alertTaskQueue.forEach { item in
        }
        if alertTaskQueue.count > 0, alertTaskQueue.contains(where: { $0.taskID() == task.taskID() }) {
            alertTaskQueue.removeFirst(where: { $0.taskID() == task.taskID() } )
        }
        if currentAlertTask != nil, currentAlertTask?.taskID() == task.taskID() {
            currentAlertTask = nil
            self.tryExecuteHighestPriorityTask()
        }
        lock.signal()
    }
    
    @objc
    public func removeAllTask() {
        lock.wait()
        self.alertTaskQueue.removeAll()
        lock.signal()
    }
    
    public func noExistAlertTask() -> Bool {
        return self.currentAlertTask == nil && self.isEmpty == true
    }
    
    /// 尝试推出弹窗任务并且执行，此处是循环调用
    private func tryExecuteHighestPriorityTask() {
        DispatchQueue.main.async {
            self.lock.wait()
            self.alertTaskQueue.forEach { item in
            }
            if self.canExecuteHighestPriorityTask() {
                let task = self.alertTaskQueue.removeFirst()
                self.currentAlertTask = task
//                防止execute中调用removetask引起死锁
                DispatchQueue.main.async {
                    task.execute { [weak self] in
                        self?.removeTask(task)
                    }
                }
            }
            self.lock.signal()
        }
    }
    
    /// 当前弹窗队列是空或者界面正在显示弹窗任务，则不能执行更高优先级的弹窗任务
    private func canExecuteHighestPriorityTask() -> Bool {
        return self.currentAlertTask == nil && self.isEmpty == false
    }
}

extension WHAlertTaskManager {
    
    @objc public static func showTask(_ task: WHAlertTaskProtocol) {
        WHAlertTaskManager.shared().showTask(task)
    }
    
    /// removeTask:移除弹窗任务
    @objc static public func removeTask(_ task: WHAlertTaskProtocol) {
        WHAlertTaskManager.shared().removeTask(task)
    }
}

public extension RangeReplaceableCollection {
    @discardableResult
    mutating func removeFirst(where predicate: (Element) throws -> Bool) rethrows -> Element? {
        guard let index = try firstIndex(where: predicate) else { return nil }
        return remove(at: index)
    }
}
