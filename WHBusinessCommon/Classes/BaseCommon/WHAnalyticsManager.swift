//
//  WHAnalyticsManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/16.
//

import Foundation
import MTAnalytics


public protocol WHAnalyticsEventId {
    func analyticsEventId() -> String
}

public struct WHAnalyticsKey: Hashable {
    
    private let rawValue: String
    
    public init(rawValue: String) {
        self.rawValue = rawValue
    }
    
    func analyticsKey() -> String {
        return self.rawValue
    }
}

@objcMembers
public class WHAnalyticsManager: NSObject {
    
    ///曝光事件
    public static func pageExpo(pageName:String,otherParams:[String:Any] = [:]) {
        var params:[String:Any] = ["page_name":pageName]
        if otherParams.count > 0 {
            for (key, value) in otherParams {
                params[key] = value
            }
        }
        otherTrackEvent("whee_pages_expo", params: params)
    }
    
    /// 事件埋点
    /// - Parameters:
    ///   - eventId: 事件ID
    ///   - params: 计数参数
    ///   - type: 事件类型
    ///   - source: 事件来源
    ///   - realtime: 是否实时上报
    public static func trackEvent(_ eventId: WHAnalyticsEventId,
                                  params: [WHAnalyticsKey: Any] = [:],
                                  type: String = "1",
                                  source: String = "0",
                                  realtime: Bool = true)  {
        trackEvent(eventId.analyticsEventId(),
                   params: transferParams(params),
                   type: type,
                   source: source,
                   realtime: realtime)
    }
        
    /// 用于网页,OC等其他功能打点
    /// - Parameters:
    ///   - eventId: 事件ID
    ///   - params: 计数参数
    ///   - type: 事件类型
    ///   - source: 事件来源
    ///   - realtime: 是否实时上报
    public static func otherTrackEvent(_ eventId: String,
                                       params: [String: Any]?,
                                       type: String = "1",
                                       source: String = "0",
                                       realtime: Bool = true) {
        trackEvent(eventId,
                   params: params,
                   type: type,
                   source: source,
                   realtime: realtime)
    }

    
    /// - Parameters:
    ///   - eventId: 事件ID
    ///   - params: 计数参数
    ///   - type: 事件类型
    ///   - source: 事件来源
    ///   - realtime: 是否实时上报
    private static func trackEvent(_ eventId: String,
                                   params: [String: Any]? = [:],
                                   type: String,
                                   source: String,
                                   realtime: Bool = true) {
        // 配置UID
//        MTAnalytics.setUserID(WHUserDefaultManager.uid ?? "")
        
        var attributes: [String: Any] = [
            MTAnalyticsRealTimeReportParamKey: realtime,
        ]
        
        attributes.merge(configCommonParamsWith(params)) { (current, new) in new }
        
        MTAnalytics.event(eventId,
                          attributes: attributes,
                          type: MTAnalyticsEventType(type),
                          source: MTAnalyticsEventSource(source))
    }
    
    /// 转换上报参数格式
    private static func transferParams(_ params: [WHAnalyticsKey: Any]?) -> [String: Any] {
        var attributes: [String: Any] = [:]
        if let aParams = params {
            for (key, value) in aParams {
                attributes[key.analyticsKey()] = value
            }
        }
        return attributes
    }

    
    /// 配置通用参数
    private static func configCommonParamsWith(_ params: [String: Any]?) -> [String: Any] {
        // 通用参数
        var attributes: [String: Any] = [:]
        if let aParams = params {
            attributes.merge(aParams) { (current, new) in new }
        }
        return attributes
    }
    
}
//MARK:处理通用上报
extension WHAnalyticsManager {
    public static func checkSchemaIsNeedAnalytics(schema:String?) {
        if let schema = schema,
            let URL = URL(string: schema),
           let model = WHRouterSchemeModel.modelWithSchemeUrl(schemeUrl: URL),
           let whee_external = model.queryDic?["whee_external"] as? String { //有whee_external，需要上报埋点
            var params = stringValueDic(whee_external) ?? [:]
            if params.count > 0 {
                params["gid"] = MTAnalyticsGID.sharedInstance()?.gid ?? ""
                params["os_type"] = "ios"
                WHAnalyticsManager.otherTrackEvent("share_visit_expo", params: params)
            }
        }
    }
    
    static func stringValueDic(_ str: String) -> [String : Any]? {
        if let data = str.data(using: String.Encoding.utf8), let dict = try? JSONSerialization.jsonObject(with: data, options: JSONSerialization.ReadingOptions.mutableContainers) as? [String : Any] {
            return dict
        }
        return nil
    }
}

