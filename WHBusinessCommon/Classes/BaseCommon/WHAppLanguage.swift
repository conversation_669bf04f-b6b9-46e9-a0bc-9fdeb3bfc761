//
//  WHAppLanguage.swift
//  MeituWhee
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/9/16.
//

import Foundation

enum whLanguageType: String, CaseIterable{
      case Chinese = "zh-Hans"
      case English = "en"
      var code: String {
          return rawValue
      }
  }

public class WHAppLanguage: NSObject {
    
    static let shared = WHAppLanguage()
    
    /// 当前系统的语言
    public func wh_currentSystemLanguage() -> String {
        let languages = UserDefaults.standard.object(forKey: "AppleLanguages") as? [String]
        let currentLanguage = languages?.first
        return currentLanguage ?? whLanguageType.English.code
    }
    
    /// 设置当前语言
    public func wh_setAppLanguage(language: String) {
        if language.count > 0 {
            UserDefaults.standard.set(language, forKey: "app_language_setting")
            UserDefaults.standard.synchronize()
        } else {
            UserDefaults.standard.removeObject(forKey: "app_language_setting")
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 当前app内语言
    public func wh_appLanguage() -> String {
        var currentLanguage = ""
        if let language = UserDefaults.standard.string(forKey: "app_language_setting") {
            currentLanguage = language
        } else {
            let language =  wh_currentSystemLanguage().lowercased()
            if language.hasPrefix("zh-hans") {
                currentLanguage = whLanguageType.Chinese.code
            } else if language.hasPrefix("zh-hant") || language.hasPrefix("zh-hk") {
                currentLanguage = whLanguageType.Chinese.code
            } else {
                currentLanguage = whLanguageType.English.code
            }
        }
        
        if whLanguageType.allCases.map({ $0.code }).contains(currentLanguage) {
            return currentLanguage
        }
        return whLanguageType.Chinese.code
    }
    
    /// 当前是否为中文简体
    public func wh_isLanguageZH() -> Bool {
        if wh_appLanguage().lowercased().hasPrefix("zh-hans") {
            return true
        }
        return false
    }
    
    func wh_localizationBundle() -> Bundle {
        if let path = Bundle.main.path(forResource:wh_appLanguage(), ofType: "lproj"),
           FileManager.default.fileExists(atPath: path),
           let bundle = Bundle(path: path) {
            return bundle
        } else {
            return Bundle.main
        }
    }
}

public let WHAppShareLanguage = WHAppLanguage.shared

// 国际化全局函数
public func WHLocalizedString(_ key: String, comment: String? = "") -> String {
    return NSLocalizedString(key, tableName: nil, bundle:WHAppLanguage.shared.wh_localizationBundle(), value: "", comment: comment ?? "")
}

// OC方法
@objcMembers final public class WHLocalizedConvertor: NSObject {
    public static func localizedString(_ key: String) -> String {
        return NSLocalizedString(key, tableName: nil, bundle:WHAppLanguage.shared.wh_localizationBundle(), value: "", comment: "")
    }
}
