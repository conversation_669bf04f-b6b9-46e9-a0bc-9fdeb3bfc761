//
//  WHCommonAlbumSelect.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/2/22.
//

import Foundation
import PhotosUI
import MobileCoreServices
import MTResourceUpload
import SDWebImage
import MTPhotoLibrary
import WHBaseLibrary
import Photos

//选图流程：
//1.检测权限
//2.相册选图（单选）
//3.图片本地检测（不超过30M，格式支持：jpg、png、bmp。其它格式会转为jpg。gif图直接会提示不能选）
//4.图片上传
//5.图片安全检测

public enum WHAlbumSelectType: Int {
    case albumSelect   = 0 // 仅选图
    case picLocalCheck = 1 // 到本地图片检测步骤
    case picUpload   = 2 // 到图片上传步骤
    case picSafeCheck = 3 // 到图片安全检测步骤
}

public enum WHAlbumPhotoLibraryType: Int {
    case none   = 0 //
    case inpainding = 1 // AI改图的选图
    case aiVideo   = 2 // AI视频
    case aiClear   = 3 // AI消除
    case txt2Img   = 4 // 文生图
    case img2Img   = 5 // 图生图
    case editor    = 6 // 改图编辑器
    case introduce = 7 // 功能介绍
    case aiUpScaler = 8 // AI超清
    case aiExpand   = 9 // AI扩图
    case aiLive     = 10 // 转Live
    case oneSentence = 11 // 一句话修图
    case smartCutout = 12 // 智能抠图
    case sop = 13 // sop
    
    func monitorTaskCategory() -> String {
        switch self {
        case .none:
            return ""
        case .inpainding:
            return "inpaint"
        case .aiVideo:
            return "ai_video"
        case .aiClear:
            return "ai_clear"
        case .txt2Img:
            return "txt2img"
        case .img2Img:
            return "img2img"
        case .editor:
            return "project"
        case .introduce:
            return "introduce"
        case .aiUpScaler:
            return "ai_ultra_hd"
        case .aiExpand:
            return "extend"
        case .aiLive:
            return "image_to_live"
        case .oneSentence:
            return "text_image_editing"
        case .smartCutout:
            return "ai_cutout"
        case .sop:
            return "ai_formula"
        default:
            return ""
        }
    }
    func monitorMtaSource() -> String {
        switch self {
        case .none:
            return ""
        case .inpainding:
            return "local_modification"
        case .aiVideo:
            return "ai_video"
        case .aiClear:
            return "ai_clear"
        case .txt2Img:
            return "text_to_image"
        case .img2Img:
            return "image_to_image"
        case .editor:
            return "project"
        case .introduce:
            return "introduce"
        case .aiUpScaler:
            return "ai_ultra_hd"
        case .aiExpand:
            return "image_extension"
        case .aiLive:
            return "image_to_live"
        case .oneSentence:
            return "text_edit_image"
        case .smartCutout:
            return "ai_cutout"
        case .sop:
            return "sop"
        default:
            return ""
        }
    }
}

public class WHCommonSelectAssetModel:NSObject {
    public var image:UIImage?
    public var mtAsset:MTPhotoAsset?
    public var imageUrl:String?
    public var imageSuffix:String?
    public var selectType:WHAlbumSelectType = .albumSelect
    public var isCompress:Bool = false
    public var mtResource:MTResource?
}

public protocol WHCommonAlbumSelectDelegate: NSObjectProtocol {
    func wh_albumSelectImage(assetModels:[WHCommonSelectAssetModel])
    func wh_albumSelectCancel()
    func wh_albumSelectFail()
}
///选图级别
public enum WHAlbumSelectLevel: Int { //为解决H5场景下选图超限的问题，增加选图级别，直接通过级别控制分辨率、比例、最短边、大小
    case normal   = 0 // 默认处理
    case levelA = 1 // 长边限制3840，比例3:1-1:3之间，短边不小于14，大小不超过10M
    case levelB   = 2 // 长边限制3840，比例5:2-2:5之间，短边不小于300，大小不超过10M
    //本地定义的能力从1001开始
    case smartCutout = 1001 // 长边限制3000，大小不超过20M
    case aiLive = 1002 // 长边限制3200，比例5:2-2:5之间，短边不小于300，大小不超过10M
    case oneSentence = 1003
    case txt2Img   = 1004 // 文生图
    case img2Img   = 1005 // 图生图
    case inpainding = 1006
    case aiVideo = 1007
    
    func limitLenght() -> CGFloat { //最长边限制（一级）如果算法侧不展示尺寸只限制大小的话，一级这里传-1，如果超出大小限制后，通过二级进行压缩
        switch self {
        case .levelA:
            return 2048
        case .levelB:
            return 2048
        case .smartCutout:
            return 3000
        case .aiLive:
            return 3200
        case .oneSentence:
            return 4096
        case .inpainding:
            return 3000
        default:
            return -1
        }
    }
    func secondCompressLimitLenght() -> CGFloat { //(二级)二次压缩时的最长边（部分类型可能会有二次压缩逻辑，第一次压缩后还不满足大小限制时，尝试二次压缩）
        switch self {
        case .oneSentence:
            return 2048
        default:
            return -1
        }
    }
    func limitSize() -> CGFloat { //大小限制
        switch self {
        case .levelA:
            return 10
        case .levelB:
            return 10
        case .smartCutout:
            return 20
        case .aiLive:
            return 10
        case .oneSentence:
            return 5
        default:
            return 30
        }
    }
    func secondLimitSize() -> CGFloat { //二次大小限制。这里未配置的类型会默认使用limitSize内的（目前仅一句话修图有特殊逻辑，二次压缩后，不再判断大小限制，直接放行）
        switch self {
        case .oneSentence:
            return 100000
        default:
            return limitSize()
        }
    }
    func limitRatio() -> CGFloat { //宽高比例限制，
        switch self {
        case .levelA:
            return 3.0/1.0
        case .levelB:
            return 5.0/2.0
        case .aiLive:
            return 5.0/2.0
        case .oneSentence:
            return 3.0/1.0
        case .txt2Img:
            return 3.0/1.0
        case .img2Img:
            return 3.0/1.0
        case .aiVideo:
            return 3.0/1.0
        default:
            return -1
        }
    }
    func limitMinLenght() -> CGFloat { //限制的最短边长度
        switch self {
        case .levelA:
            return 14.0
        case .levelB:
            return 300.0
        case .aiLive:
            return 300.0
        default:
            return -1
        }
    }
}

public class WHCommonAlbumSelect : NSObject, UINavigationControllerDelegate {
    
    public var selectType:WHAlbumSelectType = .picSafeCheck //默认走完全流程
    public var photoLibraryType:WHAlbumPhotoLibraryType = .none
    var isAutoColseSelect:Bool = false //选完图片后是否自动关闭相册
    public weak var delegate:WHCommonAlbumSelectDelegate?
    public var selectLevel:WHAlbumSelectLevel = .normal
    public var limitLenght:CGFloat = -1
    
    private var maskView:UIView?
    private var imageUrlString = ""
    private var oriImage:UIImage?
    private var mtAsset:MTPhotoAsset?
    private var imageSuffix:String = "jpg"
    private var reSetSize:CGSize = CGSizeZero
    private weak var pickerVC:UIViewController?
    private var isResize:Bool = false //默认没有进行尺寸压缩
    private var albumStyle:WHPhotosAlbumStyle = .onlyPhotos
    private var albumDefaultSelect:WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos
    private var uploadSpecificationUrl:String = "" //上传规范的地址，如果有就会在展示相册时显示
    
    ///selectType:选图要到哪一步，如：仅选图、本地校验、上传到云、安审
    ///photoLibraryType：来源，主要用于上报
    ///isAutoColseSelect：选图后，是否自动关闭相册
    ///limitLenght：对图片最长边的限制，如果超过内部会压缩（优先级会大于selectLevel中的限制，只影响一级压缩）
    ///albumStyle：相册样式：仅图片/全类型选择
    ///albumDefaultSelect：仅在全类型时生效，默认选中哪个tab
    public func selectPhotoWithParmas(delegate:WHCommonAlbumSelectDelegate?,
                                      selectType:WHAlbumSelectType = .picSafeCheck,
                                      photoLibraryType:WHAlbumPhotoLibraryType = .none,
                                      isAutoColseSelect:Bool = false,
                                      limitLenght:CGFloat = -1,
                                      albumStyle:WHPhotosAlbumStyle = .onlyPhotos,
                                      albumDefaultSelect:WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos,
                                      selectLevel:WHAlbumSelectLevel = .normal,
                                      uploadSpecificationUrl:String = "") {
        self.selectType = selectType
        self.photoLibraryType = photoLibraryType
        self.isAutoColseSelect = isAutoColseSelect
        self.limitLenght = limitLenght
        self.delegate = delegate
        self.albumStyle = albumStyle
        self.albumDefaultSelect = albumDefaultSelect
        self.selectLevel = selectLevel
        self.uploadSpecificationUrl = uploadSpecificationUrl
        if limitLenght == -1,selectLevel != .normal { //未传入limitLenght，并且selectLevel不是normal，则设置下限制的最长边
            self.limitLenght = selectLevel.limitLenght()
        }
        self.startAlumbTask()
    }
    
    public func startAlumbTask() {
        //初始化数据
        self.imageUrlString = ""
        self.oriImage = nil
        self.mtAsset = nil
        self.removeMaskView()
        self.pickerVC = nil
        self.isResize = false
        //检测登录
        if WHAccountShareManager.isLogin() {
            self.formalTask()
        } else {
            WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self?.formalTask()
                }
            } failure: {
            }
        }
        
    }
    func formalTask() {
        //检测权限--用户是否操作过
        let photoStatus: PHAuthorizationStatus
        if #available(iOS 14, *) {
           photoStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
           photoStatus = PHPhotoLibrary.authorizationStatus()
        }
        
        switch photoStatus {
           case .notDetermined:
               if #available(iOS 14, *) {
                   PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] newStatus in
                       guard let self = self else { return }
                       DispatchQueue.main.async {
                           if self.photoOpenStatus() {
                               self.openPhotoAlubm()
                           } else {
                               self.showAlbumAuthorizationAlert()
                           }
                       }
                   }
               } else {
                   PHPhotoLibrary.requestAuthorization { [weak self] newStatus in
                       guard let self = self else { return }
                       DispatchQueue.main.async {
                           if self.photoOpenStatus() {
                               self.openPhotoAlubm()
                           } else {
                               self.showAlbumAuthorizationAlert()
                           }
                       }
                   }
               }
               
           case .authorized, .limited:
               DispatchQueue.main.async {
                   self.openPhotoAlubm()
               }
           case .denied, .restricted:
               DispatchQueue.main.async {
                   self.showAlbumAuthorizationAlert()
               }
           @unknown default:
               DispatchQueue.main.async {
                   self.showAlbumAuthorizationAlert()
               }
           }

    }
    
    func showAlbumAuthorizationAlert() {
        let alertController = UIAlertController(title: WHLocalizedString("开启照片权限"), message: WHLocalizedString("你还没有开启照片权限，开启之后WHEE才能访问你的照片"), preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: WHLocalizedString("取消"), style: .cancel, handler: nil)
        let settingsAction = UIAlertAction(title: WHLocalizedString("去设置"), style: .default, handler: { (action) in
            self.openSettings()
        })
        alertController.addAction(cancelAction)
        alertController.addAction(settingsAction)
        WHRouter.topViewController?.present(alertController, animated: true, completion: nil)
    }
    
    func openPhotoAlubm() {
        // 配置相册参数并打开
        WHPhotoAlbumSharedManager.loadPhotosAndShowVCWithDelegate(delegate: self,
                                                                  isLookAllType: (self.albumStyle == .allStyle ? true : false),
                                                                  defaultSelect: self.albumDefaultSelect,
                                                                  uploadSpecificationUrl: self.uploadSpecificationUrl)
    }
    
    func loadFullResolutionImageWithAsset(_ asset: MTPhotoAsset?,
                                          completion: @escaping (UIImage?) -> ()) {
        asset?.fullResolutionImage(withMaxLength: NSIntegerMax,
                                   requestSync: false,
                                   careDetail: false) { image, _ in
            completion(image)
        }
    }

    //本地图片校验（可能会对图片进行压缩转格式等处理）
    //返回流程是否继续
    public func localCheckPicLimitAndGoForwardIfNeeded(mtAsset : MTPhotoAsset?, image: UIImage?) -> Bool {
        if !WHNetwork.isReachable() {
            showToast(text: WHLocalizedString("网络好像出问题了，请重试。"))
            self.delegate?.wh_albumSelectFail()
            return false
        }
        guard let image = image else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到图片内容，请重试或更换图片"))
            self.delegate?.wh_albumSelectFail()
            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
            return false
        }
        //赋值，检测图片是否合规
        self.mtAsset = mtAsset
        let imgFormat = mtAsset?.mediaFormat()
        var uploadImg = image
        var imgData: Data? //图片Data
        
        if mtAsset == nil || mtAsset?.assetFileURL() == nil{
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到图片内容，请重试或更换图片"))
            self.delegate?.wh_albumSelectFail()
            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
            return false
        }
        
        if imgFormat == "gif" { //不支持gif
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片格式不支持，请更换图片后再试"))
            self.delegate?.wh_albumSelectFail()
            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
            return false
        }
        
        if selectLevel != .normal,isExceedLimitRatio(mtAsset: mtAsset, limitRatio: selectLevel.limitRatio()) {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片比例较长，请更换图片后再试"))
            return false
        }
        
        if selectLevel != .normal,isExceedLimitLittleLenght(mtAsset: mtAsset, littleLenght: selectLevel.limitMinLenght()) {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片短边小于\(Int(selectLevel.limitMinLenght()))像素，请更换图片后再试"))
            return false
        }
                
        //先进行一级压缩，压缩后再判断大小
        if self.limitLenght > 0 { //需要修改图片尺寸
            let size = self.reSizeToLimitLenght(width: uploadImg.size.width ?? 0, height: uploadImg.size.height ?? 0,limitLenght: self.limitLenght)
            uploadImg = uploadImg.sd_resizedImage(with: size, scaleMode: .aspectFit) ?? uploadImg
            if self.isResize == true { //说明尺寸被调整了
                self.reSetSize = size
                if imgFormat == "png",
                   photoLibraryType != .oneSentence {
                    //如果是png类型的，要保持png，其它的转为jpg
                    // 一句话修图使用jpg格式
                    imgData = uploadImg.pngData()
                } else {
                    imgData = uploadImg.jpegData(compressionQuality: 0.99)
                }
            }
        }
        //一级校验文件大小
        let limitSize: CGFloat = self.selectLevel.limitSize()
        var isBeyondLimitSize:Bool = false //经过一级压缩后，是否还超过一级限制的大小
        
        if let resizeData = imgData { //上一步被压缩的图，已经有了Data，通过Data校验大小
            isBeyondLimitSize = isBeyondLimitImageDataSize(imgData: resizeData, size: limitSize)
        } else {
            isBeyondLimitSize = isBeyondLimitImageSize(mtAsset: mtAsset, size: limitSize)
        }
        if isBeyondLimitSize == true { //目前的文件大小超过了限制
            //判断是否要进行二级压缩
            if self.selectLevel.secondCompressLimitLenght() > 0 { //二级压缩
                let size = self.reSizeToLimitLenght(width: uploadImg.size.width ?? 0, height: uploadImg.size.height ?? 0,limitLenght: self.selectLevel.secondCompressLimitLenght())
                uploadImg = uploadImg.sd_resizedImage(with: size, scaleMode: .aspectFit) ?? uploadImg
                //uploadImg交由二级文件大小判断
                if self.isResize == true { //说明尺寸被调整了
                    self.reSetSize = size
                    if imgFormat == "png",
                       photoLibraryType != .oneSentence {
                        //如果是png类型的，要保持png，其它的转为jpg
                        // 一句话修图使用jpg格式
                        imgData = uploadImg.pngData()
                    } else {
                        imgData = uploadImg.jpegData(compressionQuality: 0.99)
                    }
                }
            } else {
                UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过\(Int(limitSize))M，请更换图片后再试"))
                self.delegate?.wh_albumSelectFail()
                WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
                return false
            }
        }
        
        //二级校验文件大小
        let secondLimitSize = self.selectLevel.secondLimitSize()
        var isBeyondSecondLimitSize:Bool = false //经过二级压缩后，是否还超过二级限制的大小
        if let secondResizeData = imgData { //上一步被压缩的图，已经有了Data，通过Data校验大小
            isBeyondSecondLimitSize = isBeyondLimitImageDataSize(imgData: secondResizeData, size: secondLimitSize)
        } else {
            isBeyondSecondLimitSize = isBeyondLimitImageSize(mtAsset: mtAsset, size: secondLimitSize)
        }
        if isBeyondSecondLimitSize == true {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过\(Int(limitSize))M，请更换图片后再试"))
            self.delegate?.wh_albumSelectFail()
            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
            return false
        }
        
        //准备要上传的Resource
        var uploadResource = MTResource()
        var imageSuffix = self.getSuffixNameWithImageType(imgFormat: imgFormat ?? "")
        if let imgData = imgData { //已经被压缩过的图，Data数据也已有，直接组装
            uploadResource = MTResource(resource: NSData(data: imgData), uploadType: .resourcePhoto)
        } else { //未经过压缩
            uploadResource = MTResource(resource: mtAsset?.asset ?? PHAsset(), uploadType: .resourcePhoto)
        }
        uploadResource.suffix = imageSuffix
        self.imageSuffix = imageSuffix //记录图片格式后缀
        self.oriImage = uploadImg
        
        if self.selectType == .picLocalCheck { //本地图片校验已结束，可以阶段返回结果
            let isRsize = self.isResize
            let assetModels = packageReturnAssetModels(image: uploadImg, mtAsset: mtAsset, imageUrl: nil, imageSuffix: imageSuffix, selectType: .picLocalCheck, isCompress: isRsize, mtResource: uploadResource)
            self.delegate?.wh_albumSelectImage(assetModels: assetModels)
            self.closePickerVC()
            return false
        }
        //准备上传
        WHResourceUploaderManager.manager.addTask(with: uploadResource) { resourceKey, percent in
            
        } completion: { [weak self] resourceKey, encryptResult, error in
            let urlStr = encryptResult?["url"] as? String
            DispatchQueue.main.async {
                
                if error != nil || urlStr == nil {
                    UIViewController.wh_top().showToast(title: error?.localizedDescription ?? "上传失败")
                    self?.removeMaskView()//移除蒙层
                    self?.delegate?.wh_albumSelectFail()
                } else {
                    WHAnalyticsManager.otherTrackEvent("upload_image_success", params: ["function": self?.photoLibraryExpo()])

                    if self?.selectType == .picUpload { //图片上传已结束，可以阶段返回结果
                        let isRsize = self?.isResize ?? false
                        let assetModels = self?.packageReturnAssetModels(image: self?.oriImage, mtAsset: mtAsset, imageUrl: urlStr, imageSuffix: imageSuffix, selectType: .picUpload, isCompress: isRsize, mtResource: nil)
                        self?.delegate?.wh_albumSelectImage(assetModels: assetModels ?? [])
                        self?.closePickerVC()
                        self?.removeMaskView()//移除蒙层
                        return;
                    }
                    self?.checkPicIsSafe(urlStr: urlStr as! String)
                }
            }
        }
        return true
    }

    //上传图片
//    public func uploadImgWithData(mtAsset : MTPhotoAsset?, image: UIImage?) -> Bool {
//        if !WHNetwork.isReachable() {
//            showToast(text: WHLocalizedString("网络好像出问题了，请重试。"))
//            self.delegate?.wh_albumSelectFail()
//            return false
//        }
//        
//        self.mtAsset = mtAsset
//        //检测图片是否合规
//        let imgFormat = mtAsset?.mediaFormat()
//        var uploadImg = image
//        
//        if mtAsset == nil || mtAsset?.assetFileURL() == nil{
//            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到图片内容，请重试或更换图片"))
//            self.delegate?.wh_albumSelectFail()
//            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
//            return false
//        }
//        
//        if imgFormat == "gif" { //不支持gif
//            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片格式不支持，请更换图片后再试"))
//            self.delegate?.wh_albumSelectFail()
//            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
//            return false
//        }
//        
//        if selectLevel != .normal,isExceedLimitRatio(mtAsset: mtAsset, limitRatio: selectLevel.limitRatio()) {
//            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片比例较长，请更换图片后再试"))
//            return false
//        }
//        
//        if selectLevel != .normal,isExceedLimitLittleLenght(mtAsset: mtAsset, littleLenght: selectLevel.limitMinLenght()) {
//            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片短边小于\(Int(selectLevel.limitMinLenght()))像素，请更换图片后再试"))
//            return false
//        }
//        
//        var compressionData: Data?
//        if photoLibraryType == .aiLive, let mtAsset = mtAsset {
//            if !WHAILiveUploadManager.shared.checkImageIsPermissible(mtAsset) {
//                return false
//            }else{
//                WHAnalyticsManager.otherTrackEvent("whee_pages_click", params: ["click_type":"pic_upload_click","page_name":"image_to_live"])
//            }
//        } else {
//            var imgData: Data?
//            if self.limitLenght > 0 { //需要修改图片尺寸
//                // 长边限制
//                let size = self.reSizeToLimitLenght(width: uploadImg?.size.width ?? 0, height: uploadImg?.size.height ?? 0,limitLenght: self.limitLenght)
//                uploadImg = uploadImg?.sd_resizedImage(with: size, scaleMode: .aspectFit)
//                if self.isResize == true { //说明尺寸被调整了
//                    self.reSetSize = size
//                    if imgFormat == "png",
//                       photoLibraryType != .oneSentence {
//                        //如果是png类型的，要保持png，其它的转为jpg
//                        // 一句话修图使用jpg格式
//                        imgData = uploadImg?.pngData()
//                    } else {
//                        imgData = uploadImg?.jpegData(compressionQuality: 0.99)
//                    }
//                }
//            }
//            //检测大小
//            var limitSize: CGFloat = self.selectLevel.limitSize()
//            if photoLibraryType == .oneSentence {
//                limitSize = 5
//            }
//            if let imgData = imgData {
//                if Double(imgData.count) / 1024 / 1024 >= limitSize {
//                    if photoLibraryType == .oneSentence,
//                       limitLenght > 2048 {
//                        let size = self.reSizeToLimitLenght(width: uploadImg?.size.width ?? 0, height: uploadImg?.size.height ?? 0,limitLenght: 2048)
//                        uploadImg = uploadImg?.sd_resizedImage(with: size, scaleMode: .aspectFit)
//                    } else {
//                        UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过\(Int(limitSize))M，请更换图片后再试"))
//                        self.delegate?.wh_albumSelectFail()
//                        WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
//                        return false
//                    }
//                }
//            } else if self.isBeyondLimitImageSize(mtAsset: mtAsset, size: limitSize) == true {
//                if photoLibraryType == .oneSentence {
//                    let data = uploadImg?.jpegData(compressionQuality: 0.99)
//                    if let data = data,
//                       Double(data.count) / 1024 / 1024 >= limitSize {
//                        if limitLenght > 2048 {
//                            let size = self.reSizeToLimitLenght(width: uploadImg?.size.width ?? 0, height: uploadImg?.size.height ?? 0,limitLenght: 2048)
//                            uploadImg = uploadImg?.sd_resizedImage(with: size, scaleMode: .aspectFit)
//                        } else {
//                            UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过\(Int(limitSize))M，请更换图片后再试"))
//                            self.delegate?.wh_albumSelectFail()
//                            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
//                            return false
//                        }
//                    } else {
//                        compressionData = data
//                    }
//                } else {
//                    UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过\(Int(limitSize))M，请更换图片后再试"))
//                    self.delegate?.wh_albumSelectFail()
//                    WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
//                    return false
//                }
//            }
//            //图生图检测宽高比是否超过3:1
//            if (photoLibraryType == .img2Img || photoLibraryType == .txt2Img || photoLibraryType == .oneSentence),
//               isExceed3TimesLimit(mtAsset: mtAsset) == true {
//                UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片比例较长，请更换图片后再试"))
//                return false
//            }
//        }
//        
//       
//        var uploadResource = MTResource()
//        var imageSuffix = self.getSuffixNameWithImageType(imgFormat: imgFormat ?? "")
//        if photoLibraryType == .inpainding, let image = uploadImg {
//            let max = max(image.size.width, image.size.height)
//            if max > 3000 {  // 图片太大时擦除会卡
//                uploadImg = mtAsset?.fetchImage(with: .imageFullScreenImage)
//                imageSuffix = ".jpg"
//            }
//        }
//        if photoLibraryType == .aiVideo, let image = uploadImg {
//            let max = max(image.size.width, image.size.height)
//            let min = min(image.size.width, image.size.height)
//            if max / min > 3 / 1 {
//                // AI视频使用的是 WHInpaindingManager 逻辑，这里不用处理
//                showToast(text: WHLocalizedString("图片过长"))
//                return false
//            }
//        }
//        if photoLibraryType == .editor { // 编辑器里，png还用png，其它的都转成jpg
//            if imageSuffix == ".png" {
//                let uploadData = uploadImg?.pngData()
//                uploadResource = MTResource(resource: NSData(data: uploadData ?? Data()), uploadType: .resourcePhoto)
//            } else {
//                imageSuffix = ".jpg"
//                let jpgData = uploadImg?.jpegData(compressionQuality: 0.99)
//                uploadImg = UIImage(data: jpgData ?? Data())
//                uploadResource = MTResource(resource: NSData(data: jpgData ?? Data()), uploadType: .resourcePhoto)
//            }
//        } else if photoLibraryType == .aiLive, let image = uploadImg {
//            if WHAILiveUploadManager.shared.beyondLengthBoundary(image){
//                UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过10M，请更换图片后再试"))
//                return false
//            }
//            //大于10M 压缩
//            if WHAILiveUploadManager.shared.beyondLimitImageSize(mtAsset: mtAsset) {
//                let size = self.reSizeToLimitLenght(width: image.size.width, height: image.size.height,limitLenght: 3840)
//                uploadImg = image.sd_resizedImage(with: size, scaleMode: .aspectFit)
//                self.reSetSize = size
//                if let newImg = uploadImg,
//                   WHAILiveUploadManager.shared.beyondLimitSize(for: newImg, imageSuffix: imageSuffix) {
//                    UIViewController.wh_top().showToast(title: WHLocalizedString("当前图片超过10M，请更换图片后再试"))
//                    return false
//                }
//            }
//            if imageSuffix == ".png" {
//                let uploadData = uploadImg?.pngData()
//                uploadResource = MTResource(resource: NSData(data: uploadData ?? Data()), uploadType: .resourcePhoto)
//            } else {
//                imageSuffix = ".jpg"
//                let jpgData = uploadImg?.jpegData(compressionQuality: 0.99)
//                uploadImg = UIImage(data: jpgData ?? Data())
//                uploadResource = MTResource(resource: NSData(data: jpgData ?? Data()), uploadType: .resourcePhoto)
//            }
//        }
//        else {
//            if (imageSuffix == ".jpg" || self.isResize == true),
//               let img = uploadImg  { //其它格式的图片需要转成jpg,重新调整尺寸的图片需要重新转化下
//                if imageSuffix == ".png",
//                   photoLibraryType != .oneSentence { //png类型的继续保持png
//                    let uploadData = img.pngData()
//                    uploadResource = MTResource(resource: NSData(data: uploadData ?? Data()), uploadType: .resourcePhoto)
//                } else {
//                    imageSuffix = ".jpg"
//                    let jpgData = img.jpegData(compressionQuality: 0.99)
//                    uploadImg = UIImage(data: jpgData ?? Data())
//                    uploadResource = MTResource(resource: NSData(data: jpgData ?? Data()), uploadType: .resourcePhoto)
//                }
//            } else {
//                if let compressionData = compressionData {
//                    uploadResource = MTResource(resource: NSData(data: compressionData),
//                                                uploadType: .resourcePhoto)
//                } else {
//                    uploadResource = MTResource(resource: mtAsset?.asset ?? PHAsset(),
//                                                uploadType: .resourcePhoto)
//                }
//                if photoLibraryType == .oneSentence {
//                    imageSuffix = ".jpg"
//                }
//            }
//        }
//        self.imageSuffix = imageSuffix //记录图片格式后缀
//        if self.selectType == .picLocalCheck { //本地图片校验已结束，可以阶段返回结果
//            let isRsize = self.isResize
//            self.delegate?.wh_albumSelectImage(image: uploadImg, mtAsset: mtAsset, imageUrl: nil, imageSuffix: imageSuffix,selectType: .picLocalCheck,isCompress: isRsize,mtResource: uploadResource)
//            self.closePickerVC()
//            return false
//        }
//        
//        self.oriImage = uploadImg
//        //开始上传
//        self.showMaskView() //添加蒙层，防止用户其他点击
//        uploadResource.suffix = imageSuffix
//        WHResourceUploaderManager.manager.addTask(with: uploadResource) { resourceKey, percent in
//            
//        } completion: { [weak self] resourceKey, encryptResult, error in
//            let urlStr = encryptResult?["url"] as? String
//            DispatchQueue.main.async {
//                
//                if error != nil || urlStr == nil {
//                    UIViewController.wh_top().showToast(title: error?.localizedDescription ?? "上传失败")
//                    self?.removeMaskView()//移除蒙层
//                    self?.delegate?.wh_albumSelectFail()
//                } else {
//                    WHAnalyticsManager.otherTrackEvent("upload_image_success", params: ["function": self?.photoLibraryExpo()])
//
//                    if self?.selectType == .picUpload { //图片上传已结束，可以阶段返回结果
//                        let isRsize = self?.isResize ?? false
//                        self?.delegate?.wh_albumSelectImage(image: self?.oriImage, mtAsset: mtAsset, imageUrl: urlStr, imageSuffix: imageSuffix,selectType: .picUpload, isCompress: isRsize,mtResource: nil)
//                        self?.closePickerVC()
//                        self?.removeMaskView()//移除蒙层
//                        return;
//                    }
//                    self?.checkPicIsSafe(urlStr: urlStr as! String)
//                }
//            }
//        }
//        return true
//    }
    
    //展示图片不符合要求
    func showUnPicAlert() {
        let alertView = WHCommonAlertView(title: WHLocalizedString("图片不符合要求"), desStr: WHLocalizedString("要求格式: jpg、jpeg、bmp、png \n大小: 30M以内"), alertViewType: .sureStyle)
        alertView.show(in: UIApplication.wh_currentWindow)
        self.delegate?.wh_albumSelectFail()
        WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"picture_not_qualifited", "source": photoLibraryType.monitorMtaSource()])
    }
    
    func showToast(text: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            UIViewController.wh_top().showToast(title: text)
        }
    }
    
    //鉴黄
    func checkPicIsSafe(urlStr:String) {
        if urlStr.isEmpty {
            self.removeMaskView()//移除蒙层
            WHRouter.topViewController?.showToast(title: "上传失败,图片链接无效")
            self.delegate?.wh_albumSelectFail()
            return
        }
        self.imageUrlString = urlStr
        var params:[String:Any] = [:]
        params["url"] = urlStr
        params["task_category"] = self.photoLibraryType.monitorTaskCategory()
        WHSharedRequest.POST("/image/monitor.json",params: params) { response in
            DispatchQueue.main.async {
                self.removeMaskView()//移除蒙层
                if response.error != nil {
                    self.delegate?.wh_albumSelectFail()
                    WHRouter.topViewController?.showToast(title: "网络连接失败")
                } else {
                    let result = response.data() as? [String: Any]
                    self.openMiniAppWithSafeResult(result: result ?? [:])
                }
            }
        }
    }
    
    func openMiniAppWithSafeResult(result:[String: Any]) {
        
//        let message = result["message"] ?? "安全检测失败"
        if let isSafe = result["result"] as? Bool,isSafe == true {
            if self.oriImage == nil {
                WHRouter.topViewController?.showToast(title: "未知错误，请重试")
                self.delegate?.wh_albumSelectFail()
                return
            }
            let isRsize = self.isResize
            let assetModels = self.packageReturnAssetModels(image: self.oriImage, mtAsset: self.mtAsset, imageUrl: self.imageUrlString, imageSuffix: self.imageSuffix, selectType: .picSafeCheck, isCompress: isRsize, mtResource: nil)
            self.delegate?.wh_albumSelectImage(assetModels: assetModels)
            self.closePickerVC()
        } else {
            WHRouter.topViewController?.showToast(title: WHLocalizedString("图片不适宜AI创作，请重新上传"))
        }
    }
    
    private func photoOpenStatus() -> Bool {
        var photoAuthorityOpenStatus = false
        if #available(iOS 14.0, *) {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized || PHPhotoLibrary.authorizationStatus() == .limited
        } else {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized
        }
        return photoAuthorityOpenStatus
    }

    private func openSettings() {
        DispatchQueue.main.async {
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        }
    }
    //展示遮罩view
    func showMaskView(title:String = "图片安全检测中...") {
        if ((self.maskView?.superview) != nil) {
            return
        }
        // 功能介绍页有自己的loading
        if self.photoLibraryType == .introduce {
            return
        }
        self.maskView = UIView(frame: CGRectMake(0, 0, UIScreen.main.bounds.size.width, UIScreen.main.bounds.size.height))
        UIApplication.wh_currentWindow?.addSubview(self.maskView ?? UIView())
        UIApplication.wh_currentWindow?.showLoading(title: title)
    }
    
    //重置数据
    
    //移除遮罩view
    func removeMaskView() {
        if self.maskView?.superview == nil {
            return
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.maskView?.removeFromSuperview()
            self.maskView = nil
        }
        UIApplication.wh_currentWindow?.hiddenToast()
        //重置isReszie
        self.isResize = false
    }
    //检测图片格式，返回应使用的后缀名
    func getSuffixNameWithImageType(imgFormat: String) -> String {
        var suffix = ".jpg"
        switch imgFormat {
        case "jpeg":
            suffix = ".jpeg"
        case "png":
            suffix = ".png"
//        case "bmp":
//            suffix = ".bmp"
        default:
            suffix = ".jpg"
        }
        return suffix
    }
    //检测图片文件大小
    func isBeyondLimitImageSize(mtAsset:MTPhotoAsset?, size: CGFloat) -> Bool {
        let length = mtAsset?.fileSize() ?? 0
        let MB = Double(length) / 1024.0 / 1024.0
        return MB >= size
    }
    //检测ImageData文件大小
    func isBeyondLimitImageDataSize(imgData:Data,size:CGFloat) -> Bool {
        let MB = Double(imgData.count) / 1024 / 1024
        return MB >= size
    }
    //关闭相册并移除当前pickVC
    func closePickerVC() {
        if self.isAutoColseSelect == true {
            self.pickerVC?.dismiss(animated: true)
            self.pickerVC = nil
        }
    }
    //检测图片宽高比是否超过3:1
    func isExceed3TimesLimit(mtAsset: MTPhotoAsset?) -> Bool {
        let width = mtAsset?.dimensions.width ?? 1
        let height = mtAsset?.dimensions.height ?? 1
        if (CGFloat(width) / CGFloat(height) > 3.0) || (CGFloat(height) / CGFloat(width) > 3.0) {
            return true
        }
        return false
    }
    //检测图片宽高比是否超限
    func isExceedLimitRatio(mtAsset: MTPhotoAsset?,limitRatio:CGFloat) -> Bool {
        if limitRatio == -1 { //不限制
            return false
        }
        let width = mtAsset?.dimensions.width ?? 1
        let height = mtAsset?.dimensions.height ?? 1
        if (CGFloat(width) / CGFloat(height) > limitRatio) || (CGFloat(height) / CGFloat(width) > limitRatio) {
            return true
        }
        return false
    }
    //检测图片最短边是否超限
    func isExceedLimitLittleLenght(mtAsset: MTPhotoAsset?,littleLenght:CGFloat) -> Bool {
        if littleLenght == -1 { //不限制
            return false
        }
        let width = mtAsset?.dimensions.width ?? 1
        let height = mtAsset?.dimensions.height ?? 1
        let minLenght = min(width, height)
        if minLenght < littleLenght {
            return true
        }
        return false
    }
    
    func photoLibraryExpo() -> String {
        switch self.photoLibraryType {
        case .inpainding:
            return "local_modification"
        case .aiVideo:
            return "ai_to_video"
        case .aiClear:
            return "ai_clear"
        case .txt2Img:
            return "text_to_image"
        case .img2Img:
            return "image_to_image"
        case .editor:
            return "ai_modification"
        case .introduce:
            return "introduce"
        case .aiUpScaler:
            return "ai_ultra_hd"
        case .none:
            return ""
        case .aiExpand:
            return "image_extension"
        case .aiLive:
            return "image_to_live"
        case .oneSentence:
            return "text_edit_image"
        case .smartCutout:
            return "smart_cutout"
        case .sop:
            return "sop"
        }
    }
    
    //转换最长边为limitLenght
    func reSizeToLimitLenght(width:CGFloat,height:CGFloat,limitLenght:CGFloat = 1080) -> CGSize {
        var width = width
        var height = height
        let max = max(width, height)
        if max > limitLenght {
            if width > height {
                height = floor(limitLenght * height / width)
                width = limitLenght
            } else {
                width = floor(width * limitLenght / height)
                height = limitLenght
            }
            self.isResize = true //当前只要被转换过一次，就被认为有压缩过
        }
        return CGSizeMake(width, height);
    }
    //组装返回外界的数据
    private func packageReturnAssetModels(image:UIImage?, mtAsset:MTPhotoAsset?, imageUrl:String?, imageSuffix:String?, selectType:WHAlbumSelectType,isCompress:Bool,mtResource:MTResource?) -> [WHCommonSelectAssetModel] {
        let assetModel = WHCommonSelectAssetModel()
        assetModel.image = image
        assetModel.mtAsset = mtAsset
        assetModel.imageUrl = imageUrl
        assetModel.imageSuffix = imageSuffix
        assetModel.selectType = selectType
        assetModel.isCompress = isCompress
        assetModel.mtResource = mtResource
        var assetModels:[WHCommonSelectAssetModel] = []
        assetModels.append(assetModel)
        return assetModels
    }

}

extension WHCommonAlbumSelect:WHPhotosViewControllerDelegate {
    public func selectPhotoWithMtasset(mtAssets: [MTPhotoAsset], vc: UIViewController) {
        let mtAsset = mtAssets.object(at: 0)
        if mtAsset == nil { //用户取消选图
            self.delegate?.wh_albumSelectCancel()
            return;
        }
        //视频、live图只到选图流程，资源返回给各功能
        if mtAsset?.resourceSelectType == .video || mtAsset?.resourceSelectType == .livePhoto {
            let assetModels = packageReturnAssetModels(image: nil, mtAsset: mtAsset, imageUrl: nil, imageSuffix: nil, selectType: .albumSelect, isCompress: false, mtResource: nil)
            self.delegate?.wh_albumSelectImage(assetModels: assetModels)
            if self.isAutoColseSelect == true {
                vc.dismiss(animated: true)
            }
            return;
        }
        
        if self.selectType == .albumSelect { //如果业务仅选图，则直接返回图片
            var uploadImg = mtAsset?.fetchImage(with: .imagefullResolutionImage)
            let isRsize = self.isResize
            let assetModels = packageReturnAssetModels(image: uploadImg, mtAsset: mtAsset, imageUrl: nil, imageSuffix: nil, selectType: .albumSelect, isCompress: isRsize, mtResource: nil)
            self.delegate?.wh_albumSelectImage(assetModels: assetModels)
            if self.isAutoColseSelect == true {
                vc.dismiss(animated: true)
            }
            return;
        }
        self.pickerVC = vc
        if let asset = mtAsset {
            var titleString = WHLocalizedString("图片安全检测中...")
            if self.selectType.rawValue <= 1 {
                titleString = ""
            } else if self.selectType == .picUpload {
                titleString = WHLocalizedString("图片上传中...")
            }
            showMaskView(title: titleString)
            loadFullResolutionImageWithAsset(asset) { [weak self] image in
                if self?.localCheckPicLimitAndGoForwardIfNeeded(mtAsset: asset, image: image) == false {
                    self?.removeMaskView()//移除蒙层
                }
            }
        }
    }
}

