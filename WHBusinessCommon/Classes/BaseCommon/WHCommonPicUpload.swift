//
//  WHCommonPicUpload.swift
//  MeituWhee
//
//  Created by 王耀 on 2025/7/8.
//

import Foundation
import MTResourceUpload
import WHBaseLibrary

//let kWHVideoCacheName = "WH_Image_Cache"   //whee视频缓存文件夹名

public class WHCommonWeakWrapper<T: AnyObject> {
    weak var value: T?
    init(_ value: T?) {
        self.value = value
    }
}

public enum WHCommonUploadStatus {
    case unStart
    case uploading
    case success
    case fail
}

public enum WHCommonUploadFailStage {
    case uploadFail          //上传阶段失败
    case safeCheckFail       //安审阶段失败
}

public class WHCommonUploadModel {
    public var uploadStatus:WHCommonUploadStatus = .unStart
    public var delegateWrapper:WHCommonWeakWrapper<AnyObject>?
    public var resourceUrl:String = ""
    public var progress:CGFloat = 0.0
    public var photoLibraryType:WHAlbumPhotoLibraryType = .none
    public var isNeedSafeCheck:Bool = true
    public var safeCheckFailToast:String = ""
    public var failStage:WHCommonUploadFailStage = .uploadFail
}

// MARK: - 上传回调协议
public protocol WHCommonPicUploadDelegate: AnyObject {
    func wh_uploadSucceed(taskID:String,urlString:String)
    func wh_uploadFail(taskID:String,error: Error?,stage:WHCommonUploadFailStage)
    func wh_uploadProgress(taskID:String,progress:CGFloat)
}

public let WHCommonPicUploadManager = WHCommonPicUpload.shared
public class WHCommonPicUpload : NSObject {
    
    static let shared = WHCommonPicUpload()
    
    private var listeners:[String:WHCommonUploadModel] = [:]
    
    //上传图片资源
    public func addUploadPic(uploadImg:UIImage,
                      mtResource:MTResource?,
                      suffix:String,
                      taskID:String,
                      isNeedSafeCheck:Bool,
                      photoLibraryType:WHAlbumPhotoLibraryType,
                      listener:WHCommonPicUploadDelegate?) {
        //主动触发一次无效清理
        cleanUpReleasedListeners()
        let task_Id = taskID.count > 0 ? taskID : UUID().uuidString
        if let listener = listener { //存储持有listener
            let model = WHCommonUploadModel()
            let taskWrapper = WHCommonWeakWrapper(listener as AnyObject)
            model.delegateWrapper = taskWrapper
            model.isNeedSafeCheck = isNeedSafeCheck
            model.photoLibraryType = photoLibraryType
            model.uploadStatus = .uploading
            self.listeners[task_Id] = model
        }
        //组装MTResource
        var uploadResource = MTResource()
        if let mtResource = mtResource { //如果外部有组装好的resource,则直接使用
            uploadResource = mtResource
            uploadResource.key = task_Id
            //容错suffix丢失的问题
            if uploadResource.suffix == nil || uploadResource.suffix?.isEmpty == true{
                if suffix.count > 0 {
                    uploadResource.suffix = suffix
                } else {
                    let isAlaph = WHSandboxFileManager.imageHasAlpha(uploadImg)
                    uploadResource.suffix = isAlaph ? ".png" : ".jpg"
                }
            }
        } else {
            uploadResource.key = task_Id
            uploadResource.suffix = suffix
            uploadResource.uploadType = .resourcePhoto
            if suffix == ".png" || suffix == "png" {
                let uploadData = uploadImg.pngData()
                uploadResource.resource = NSData(data: uploadData ?? Data())
            } else {
                let jpgData = uploadImg.jpegData(compressionQuality: 0.99)
                uploadResource.resource = NSData(data: jpgData ?? Data())
                if suffix == ".bmp" || suffix == "bmp" {
                    uploadResource.suffix = ".jpg"
                }
            }
        }
        //开始上传
        WHResourceUploaderManager.manager.addTask(with: uploadResource) {[weak self] resourceKey, percent in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.notifUploadProgress(progress: CGFloat(percent), task_Id: resourceKey)
            }
        } completion: {[weak self] resourceKey, encryptResult, error in
            guard let self = self else { return }
            let urlStr = encryptResult?["url"] as? String
            DispatchQueue.main.async {
                if error != nil || urlStr == nil { //上传失败
                    self.notifUploadFail(error: error, task_Id: resourceKey,stage: .uploadFail)
                    WHPrint("上传失败: \(String(describing: error))")
                } else {
                    self.notifUploadSuccess(urlString: urlStr ?? "", task_Id: resourceKey)
                    WHAnalyticsManager.otherTrackEvent("upload_image_success", params: ["function": photoLibraryType.monitorMtaSource(),"media_type":"image"])
                    WHPrint("上传成功: \(urlStr ?? "")")
                }
            }
        }
        
    }
    
    //鉴黄
    func checkPicIsSafe(task_Id:String,taskModel:WHCommonUploadModel) {
        taskModel.failStage = .safeCheckFail
        if taskModel.resourceUrl.isEmpty {
            let nsError = NSError(domain: "com.whee.upload",
                                  code: 11001,
                                  userInfo: [NSLocalizedDescriptionKey: "上传失败,图片链接无效"])
            notifUploadFail(error: nsError, task_Id: task_Id, stage: .safeCheckFail)
            return
        }
        var params:[String:Any] = [:]
        params["url"] = taskModel.resourceUrl
        params["task_category"] = taskModel.photoLibraryType.monitorTaskCategory()
        WHSharedRequest.POST("/image/monitor.json",params: params) {[weak self] response in
            guard let self = self else { self?.notifUploadFail(error: nil, task_Id: task_Id, stage: .safeCheckFail); return; }
            DispatchQueue.main.async {
                if response.error != nil {
                    let nsError = NSError(domain: "com.whee.upload",
                                          code: 11000,
                                          userInfo: [NSLocalizedDescriptionKey: "网络连接失败，请重试"])
                    self.notifUploadFail(error: nsError, task_Id: task_Id, stage: .safeCheckFail)
                } else {
                    let result = response.data() as? [String: Any]
                    if let isSafe = result?["result"] as? Bool,isSafe == true {
                        self.notifUploadSafeCheckSuccess(urlString: taskModel.resourceUrl, task_Id: task_Id)
                    } else {
                        let nsError = NSError(domain: "com.whee.upload",
                                              code: 11002,
                                              userInfo: [NSLocalizedDescriptionKey: "图片不适宜AI创作，请重新上传"])
                        self.notifUploadFail(error: nsError, task_Id: task_Id, stage: .safeCheckFail)
                    }
                }
            }
        }
    }
    
    //查询任务状态
    public func checkTaskStatus(taskID:String) -> WHCommonUploadModel? {
        let model = self.listeners[taskID]
        return model
    }
    //通知上传成功
    private func notifUploadSuccess(urlString:String,task_Id:String) {
        if let listener = self.listeners[task_Id] {
            listener.resourceUrl = urlString
            if listener.isNeedSafeCheck == true { //如果需要继续安审，则进行安审流程
                checkPicIsSafe(task_Id: task_Id, taskModel: listener)
            } else if let vc = listener.delegateWrapper?.value as? WHCommonPicUploadDelegate {
                listener.uploadStatus = .success
                vc.wh_uploadSucceed(taskID: task_Id, urlString: urlString)
            }
            
        }
    }
    //上传和安审都成功了
    private func notifUploadSafeCheckSuccess(urlString:String,task_Id:String) {
        if let listener = self.listeners[task_Id] {
            listener.uploadStatus = .success
            listener.resourceUrl = urlString
            if let vc = listener.delegateWrapper?.value as? WHCommonPicUploadDelegate {
                vc.wh_uploadSucceed(taskID: task_Id, urlString: urlString)
            }
        }
    }
    //通知上传失败
    private func notifUploadFail(error:Error?,task_Id:String,stage:WHCommonUploadFailStage) {
        if let listener = self.listeners[task_Id] {
            listener.uploadStatus = .fail
            if let error = error {
                listener.safeCheckFailToast = String(describing: error)
            }
            if let vc = listener.delegateWrapper?.value as? WHCommonPicUploadDelegate {
                vc.wh_uploadFail(taskID: task_Id, error: error,stage: stage)
            }
        }
    }
    //通知上传进度
    private func notifUploadProgress(progress:CGFloat,task_Id:String) {
        if let listener = self.listeners[task_Id] {
            listener.uploadStatus = .uploading
            listener.progress = progress
            if let vc = listener.delegateWrapper?.value as? WHCommonPicUploadDelegate {
                vc.wh_uploadProgress(taskID: task_Id, progress: progress)
            }
        }
    }
    // 清理无效的引用
    func cleanUpReleasedListeners() {
        listeners = listeners.filter { (_, model) in
            return model.delegateWrapper?.value != nil
        }
    }
}

