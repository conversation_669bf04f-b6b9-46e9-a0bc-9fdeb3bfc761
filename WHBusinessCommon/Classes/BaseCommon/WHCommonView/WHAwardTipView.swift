//
//  WHAwardTipView.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/11/14.
//

import Foundation
import WHBaseLibrary
import YYText

public class WHAwardTipView: WHCommonBaseAlertView {
        
    private lazy var bgImageView: UIImageView = {
        let make = UIImageView()
        make.contentMode = .scaleAspectFill
        make.image = UIImage(cm_named: "bg_mine_awardTips")
        return make
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor(wh_hexString: "#141414")
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        return label
    }()
    
    private lazy var desLabel: YYLabel = {
        let label = YYLabel()
        label.textAlignment = .left
        label.numberOfLines = 0
        label.textColor = UIColor(wh_hexString: "#7A7E85")
        label.font = UIFont.systemFont(ofSize: 13)
        return label
    }()
    
    private lazy var sureBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.backgroundColor = UIColor(wh_hexString: "#141414")
        btn.titleLabel?.font = .systemFont(ofSize: 15, weight: .medium)
        btn.titleLabel?.textAlignment = .center
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.setTitle(WHLocalizedString("知道了"), for: .normal)
        btn.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        btn.layer.cornerRadius = 20
        return btn
    }()
    
    private let completion: (() -> ())?
    
    public init(title: String?,
                desc: String?,
                completion: (() -> ())? = nil) {
        self.completion = completion
        super.init(frame: CGRect.zero)
        titleLabel.text = title
        desLabel.text = desc
        setupUI()
        config()
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        containerView.addSubview(bgImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(desLabel)
        containerView.addSubview(sureBtn)
        
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(52)
        }

        bgImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(160)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(108)
            make.centerX.equalToSuperview()
            make.height.equalTo(25)
        }

        desLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(30)
        }

        sureBtn.snp.makeConstraints { make in
            make.top.equalTo(desLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(40)
            make.bottom.equalToSuperview().offset(-24)
        }
    }
    
    func config() {
        let textWidth = WH_SCREEN_WIDTH - 52 * 2 - 24 * 2
        var height: CGFloat = 1
        height = desLabel.sizeThatFits(CGSize(width: textWidth, height: CGFloat(MAXFLOAT))).height
        desLabel.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["popup_name":"login_for_beauty_coin", "source": "mine_page"])
    }
    
    @objc func sureBtnAction() {
        dismiss {
            self.completion?()
        }
    }
}
