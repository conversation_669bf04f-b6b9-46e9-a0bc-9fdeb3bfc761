//
//  WHBoostAlertView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/3/13.
//

import Foundation

public class WHBoostAlertView: WHCommonBaseAlertView {
    
    private lazy var bgImgView: UIImageView = {
        let view = UIImageView(image: UIImage(cm_named: "wh_boost_alert_bg"))
        view.contentMode = .scaleAspectFill
        return view
    }()
    
    private lazy var meidouIcon: UIImageView = {
        return UIImageView(image: UIImage(cm_named: "wh_common_alert_meidou"))
    }()
    
    private lazy var meidouCountLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 44, weight: .semibold)
        label.textColor = .black
        return label
    }()
    
    private lazy var textLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 18, weight: .medium)
        label.textColor = .black
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var boostBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.titleLabel?.font = .pingFangSCFont(ofSize: 15, weight: .medium)
        btn.setBackgroundImage(UIImage(cm_named: "wh_boost_alert_button"), for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.setTitle(WHLocalizedString("助力"), for: .normal)
        btn.addTarget(self, action: #selector(boostAction), for: .touchUpInside)
        return btn
    }()
    
    private lazy var closeBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_boost_alert_close"), for: .normal)
        btn.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return btn
    }()
    
    private let count: Int
    private let text: String
    private let closeBlock: (() -> ())?
    private let boostBlock: (() -> ())?
    
    public init(count: Int,
                text: String,
                closeBlock: (() -> ())? = nil,
                boostBlock: (() -> ())? = nil) {
        self.count = count
        self.text = text
        self.closeBlock = closeBlock
        self.boostBlock = boostBlock
        super.init(frame: .zero)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc
    private func closeAction() {
        dismiss {
            self.closeBlock?()
        }
    }
    
    @objc
    private func boostAction() {
        dismiss {
            self.boostBlock?()
        }
    }
    
    private func setupSubviews() {
        meidouCountLabel.text = String(count)
        
        var paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = 0.93
        let attText = NSMutableAttributedString(string: text, attributes: [.paragraphStyle: paragraphStyle])
        let range = NSString(string: text).range(of: String(count))
        if range.location != NSNotFound {
            attText.setAttributes([.foregroundColor: UIColor(rgb: 0xAC133E)], range: range)
        }
        textLabel.attributedText = attText
        textLabel.textAlignment = .center

        containerView.backgroundColor = .clear
        
        containerView.addSubview(bgImgView)
        containerView.addSubview(meidouIcon)
        containerView.addSubview(meidouCountLabel)
        containerView.addSubview(textLabel)
        containerView.addSubview(boostBtn)
        containerView.addSubview(closeBtn)
        
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
//            make.left.right.equalToSuperview().inset(52)
        }
        bgImgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.size.equalTo(CGSize(width: 272, height: 328))
//            make.height.equalTo(bgImgView.snp.width).multipliedBy(656.0/544.0)
        }
        closeBtn.snp.makeConstraints { make in
            make.top.equalTo(bgImgView.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        meidouIcon.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(133)
            make.width.height.equalTo(50)
            make.right.equalTo(meidouCountLabel.snp.left).offset(-8)
        }
        meidouCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(meidouIcon)
            make.centerX.equalToSuperview().offset(29)
        }
        textLabel.snp.makeConstraints { make in
            make.top.equalTo(meidouIcon.snp.bottom).offset(10)
            make.left.right.equalToSuperview().inset(24)
            make.bottom.equalTo(boostBtn.snp.top).offset(-15)
        }
        boostBtn.snp.makeConstraints { make in
            make.bottom.equalTo(bgImgView).offset(-19)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 202, height: 49))
        }
    }
    
}
