//
//  WHCommonAlertView.swift
//  WHBusinessCommon
//
//  Created by zhanglifei on 2023/10/6.
//

import Foundation
import WHBaseLibrary

public enum WHAlertViewStyle: Int {
    case none  = 0     // 没有按钮
    case cancelStyle   // 只有取消按钮
    case sureStyle     // 只有确认按钮
    case allStyle      // 确认取消都有
    case contentView   //底部自动义View
    case onlyImage     //只显示图片
}

public struct WHAlertViewConfig {
    public var title: String = "标题"
    public var desString: String = ""
    public var surebtnStr: String = WHLocalizedString("知道了")
    public var cancelbtnStr: String = WHLocalizedString("取消")
    public var alertViewType: WHAlertViewStyle = .allStyle
    // 可拓展字段
    public var isShowClose: Bool = false // 是否显示关闭按钮
    public var duration: Int = 0  // 自动关闭时长
    
    public var picUrl: String = ""
    // 弹窗透传
    public var alertId: String = ""
    public var params: String = ""
    public var confirm: String?
    public var scheme: String?
    public var contentView: UIView?
    
    
    public init(title: String = "标题", desString: String = "") {
        self.title = title
        self.desString = desString
    }
}

open class WHCommonAlertView: WHCommonBaseAlertView {
    
    public var alertSureHandler: (() -> Void)?
    public var alertCancleHandler: (() -> Void)?
    
    public var config: WHAlertViewConfig = WHAlertViewConfig()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = UIColor(wh_hexString: "#121212")
        label.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        label.numberOfLines = 0
        label.text = ""
        return label
    }()
    
    private lazy var desTextView: UITextView = {
        let view = UITextView()
        view.textAlignment = .left
        view.backgroundColor = .clear
        view.textColor = UIColor(wh_hexString: "#7A7E85")
        view.font = UIFont.systemFont(ofSize: 13)
        view.textContainerInset = UIEdgeInsets(top: 1, left: 0, bottom: 0, right: 0)
        view.isEditable = false
        view.isScrollEnabled = false
        return view
    }()
    
    private lazy var sureBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.backgroundColor = UIColor(wh_hexString: "#121212")
        btn.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        btn.titleLabel?.textAlignment = .center
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.setTitle(WHLocalizedString("知道了"), for: .normal)
        btn.addTarget(self, action: #selector(sureBtnAction), for: .touchUpInside)
        btn.layer.cornerRadius = 20
        btn.layer.cornerCurve = .continuous
        return btn
    }()
    
    private lazy var cancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.backgroundColor = UIColor(wh_hexString: "#F7F7F8")
        btn.titleLabel?.font = .systemFont(ofSize: 15, weight: .medium)
        btn.titleLabel?.textAlignment = .center
        btn.setTitleColor(UIColor(wh_hexString: "#161617"), for: .normal)
        btn.setTitle(WHLocalizedString("取消"), for: .normal)
        btn.addTarget(self, action: #selector(cancelBtnAction), for: .touchUpInside)
        btn.layer.cornerRadius = 20
        btn.layer.cornerCurve = .continuous
        return btn
    }()
    
    private lazy var closeBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "icon_commonAlert_close"), for: .normal)
        btn.addTarget(self, action: #selector(closeBtnClick), for: .touchUpInside)
        btn.isHidden = true
        return btn
    }()
    
    private lazy var closeBottomBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "icon_commonAlert_bgclose"), for: .normal)
        btn.addTarget(self, action: #selector(closeBtnClick), for: .touchUpInside)
        btn.isHidden = true
        return btn
    }()
    
    private lazy var topBgView: UIImageView = {
        let imgView = UIImageView()
        imgView.image = UIImage(cm_named: "wh_common_alert_top_gradient_bg_image")
        return imgView
    }()
    
    private lazy var advertiseView: UIImageView = {
        let imgView = UIImageView()
        imgView.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(jumpRoute))
        imgView.addGestureRecognizer(tapGesture)
        return imgView
    }()
    
    public init(title: String, desStr: String, alertViewType: WHAlertViewStyle = .allStyle,sureButtonText:String = WHLocalizedString("知道了")) {
        super.init(frame: CGRect.zero)
        
        self.config.title = title
        self.config.desString = desStr
        self.config.alertViewType = alertViewType
        setupUI()
        alertViewConfig()
        sureBtn.setTitle(sureButtonText, for: .normal)
    }
    
    public init(config: WHAlertViewConfig) {
        super.init(frame: CGRect.zero)
        self.config = config
        
        setupUI()
        alertViewConfig()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        print("WHCommonAlertView deinit")
    }
    
    private func setupUI() {
        containerView.addSubview(topBgView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(desTextView)
        containerView.addSubview(sureBtn)
        containerView.addSubview(cancelBtn)
        containerView.addSubview(advertiseView)
        containerView.addSubview(closeBtn)
        addSubview(closeBottomBtn)
      
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(52)
        }
        
        topBgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(57.0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(config.isShowClose ? 44 : 24)
        }
        
        advertiseView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(374)
        }
        advertiseView.isHidden = true
        closeBottomBtn.snp.makeConstraints { make in
            make.centerX.equalTo(advertiseView)
            make.top.equalTo(containerView.snp.bottom).offset(8)
            make.width.height.equalTo(28)
        }
        closeBottomBtn.isHidden = true
        
        closeBtn.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.width.height.equalTo(28)
        }
        
        desTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(20)
        }
        
        let btnWidth = (WH_SCREEN_WIDTH - 52 * 2 - 24 * 2 - 16) / 2
        sureBtn.snp.makeConstraints { make in
            make.top.equalTo(desTextView.snp.bottom).offset(16)
            make.right.equalToSuperview().offset(-24)
            make.width.equalTo(btnWidth)
            make.height.equalTo(40)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        cancelBtn.snp.makeConstraints { make in
            make.top.equalTo(sureBtn.snp.top)
            make.left.equalToSuperview().offset(24)
            make.width.equalTo(btnWidth)
            make.height.equalTo(40)
            make.bottom.equalToSuperview().inset(24)
        }
       
    }
    
    open func alertViewConfig(){
        titleLabel.text = self.config.title
        desTextView.text = self.config.desString
        sureBtn.setTitle(self.config.surebtnStr, for: .normal)
        cancelBtn.setTitle(self.config.cancelbtnStr, for: .normal)
        closeBtn.isHidden = !self.config.isShowClose
        
        var height = self.config.desString.getStringHeight(with: WH_SCREEN_WIDTH - 110 - 48, font: UIFont.systemFont(ofSize: 13)) + 5
        desTextView.textAlignment = height > 22 ? .left : .center
        if height > WH_SCREEN_HEIGHT * 0.55 - 140 {
            height = WH_SCREEN_HEIGHT * 0.55 - 140
            desTextView.isScrollEnabled = true
        }
        
        if desTextView.text.count <= 0 {
            desTextView.snp.updateConstraints { make in
                make.height.equalTo(0)
            }
            sureBtn.snp.updateConstraints { make in
                make.top.equalTo(desTextView.snp.bottom).offset(0)
            }
        } else {
            desTextView.snp.updateConstraints { make in
                make.height.equalTo(height)
            }
        }

        switch self.config.alertViewType {
        case .none:
            sureBtn.isHidden = true
            cancelBtn.isHidden = true
            sureBtn.snp.updateConstraints { make in
                make.top.equalTo(desTextView.snp.bottom)
                make.height.equalTo(0)
            }
            cancelBtn.snp.updateConstraints { make in
                make.height.equalTo(0)
            }
        case .cancelStyle:
            sureBtn.isHidden = true
            cancelBtn.isHidden = false
            cancelBtn.snp.remakeConstraints { make in
                make.top.equalTo(desTextView.snp.bottom).offset(16)
                make.width.equalTo(170)
                make.height.equalTo(40)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-24)
            }
        case .sureStyle:
            sureBtn.isHidden = false
            cancelBtn.isHidden = true
            sureBtn.snp.remakeConstraints { make in
                make.top.equalTo(desTextView.snp.bottom).offset(16)
                make.width.equalTo(170)
                make.height.equalTo(40)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-24)
            }
        case .allStyle:
            sureBtn.isHidden = false
            cancelBtn.isHidden = false
        case .contentView: 
            sureBtn.isHidden = false
            cancelBtn.isHidden = false
            if let content = config.contentView {
                containerView.addSubview(content)
                content.snp.makeConstraints { make in
                    make.left.equalToSuperview().offset(24)
                    make.top.equalTo(desTextView.snp.bottom).offset(16)
                    make.right.equalToSuperview().offset(-24)
                }
                let btnWidth = (WH_SCREEN_WIDTH - 52 * 2 - 24 * 2 - 16) / 2
                sureBtn.snp.remakeConstraints { make in
                    make.top.equalTo(content.snp.bottom).offset(16)
                    make.right.equalToSuperview().offset(-24)
                    make.width.equalTo(btnWidth)
                    make.height.equalTo(40)
                    make.bottom.equalToSuperview().offset(-24)
                }
            }
        case .onlyImage:
            sureBtn.isHidden = true
            cancelBtn.isHidden = true
            titleLabel.isHidden = true
            desTextView.isHidden = true
            closeBtn.isHidden = true
            closeBottomBtn.isHidden = false
            advertiseView.isHidden = false
            advertiseView.sd_setImage(with: URL.init(string: config.picUrl))
            containerView.layer.cornerRadius = 16
            containerView.layer.masksToBounds = true
            containerView.backgroundColor = .clear
            containerView.snp.remakeConstraints() { make in
                make.center.equalToSuperview()
                make.width.equalTo(280)
                make.height.equalTo(374)
            }
        }
    }
    
    @objc open func sureBtnAction() {
        alertSureHandler?()
        dismiss()
    }
    
    @objc open func cancelBtnAction() {
        alertCancleHandler?()
        dismiss()
    }
    
    @objc open func closeBtnClick() {
        dismiss()
    }
                                                
    @objc func jumpRoute() {
        if let sch = config.scheme {
            WHRouter.route(with: sch)
            dismiss()
        }
        WHAnalyticsManager.otherTrackEvent("guide_popup_click", params: ["click_type":"try_now"])
    }
}

// MARK: - 基础弹框

open class WHCommonBaseAlertView: UIView {
    
    /// 是否需要弹簧效果
    private var needSpring: Bool = true
    ///是否移动
    private var needTransform: Bool = true
    private var duration: CGFloat = 0.5
    
    public lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 32.0
        view.layer.masksToBounds = true
        view.layer.cornerCurve = .continuous
        return view
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        backgroundColor = UIColor.black.withAlphaComponent(0.7)
        addSubview(containerView)
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func show(in view: UIView? = nil, needSpring: Bool = true,needTransform: Bool = true, duration: CGFloat = 0.5) {
        self.needSpring = needSpring
        self.duration = duration
        self.needTransform = needTransform
        self.show(in: view)
    }
    
//    @objc public func dismiss(needSpring: Bool = true) {
//        self.dismiss()
//    }
    
    open func show(in view: UIView? = nil) {
        var superview: UIView! = view
        if superview == nil {
            var vc = WHCurrentTopViewController()
            vc = vc?.navigationController ?? vc
            if let vv = vc?.view {
                superview = vv
            } else {
                superview = (UIApplication.shared.keyWindow ?? UIView())
            }
        }
        // 防止重复弹出
        if superview.subviews.contains(where: { view in
            return view is Self
        }) {
            return
        }
        superview.addSubview(self)
        self.snp.remakeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        layoutIfNeeded()
        
        alpha = 0
        if self.needTransform {
            containerView.transform = CGAffineTransform(scaleX: 0, y: 0)
        }
        
        let springValue: CGFloat = self.needSpring ? 0.7 : 1
        UIView.animate(withDuration: self.duration, delay: 0, usingSpringWithDamping: springValue, initialSpringVelocity: 0.5, options: .curveEaseOut, animations: {
            self.alpha = 1
            if self.needTransform {
                self.containerView.transform = .identity
            }
            self.layoutIfNeeded()
        }, completion: nil)
    }
    
    public func dismiss(completion: (() -> ())? = nil) {
        let springValue: CGFloat = self.needSpring ? 0.7 : 1
        UIView.animate(withDuration: self.duration, delay: 0, usingSpringWithDamping: springValue, initialSpringVelocity: 0.5, options: .curveEaseOut, animations: {
            self.alpha = 0
            if self.needTransform {
                self.containerView.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            }
            self.layoutIfNeeded()
        }) { (_) in
            self.removeFromSuperview()
            completion?()
        }
    }
    
}
