//
//  WHCommonBannerView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/9/23.
//

import Foundation
import SDWebImage

@objc public protocol WHCommonBannerViewDelegate {
    @objc optional func bannerView(_ bannerView: WHCommonBannerView, didScrollAt index:Int)
    @objc optional func bannerView(_ bannerView: WHCommonBannerView, didSelectedAt index:Int)
    ///首帧有结果（可能成功，可能失败）
    @objc optional func bannerViewFirstPageIsLoad(isSuccess: Bool)
}

public typealias WHCommonBannerPlayVideoBlock = (_ videoId: String,
                                                 _ url: String,
                                                 _ superView: UIView,
                                                 _ captureObject: UICollectionViewCell,
                                                 _ playingCallback: ((UICollectionViewCell, String?) -> ())?) -> ()

public class WHCommonBannerView: UIView {
    
    /// 是否全屏样式
    public var fullScreen: Bool = false
    
    public weak var delegate: WHCommonBannerViewDelegate?
    
    var needAnimation = false
    
    /// 是否允许自动滚动
    public var needAutoScroll = true
    
    public var dataArr:[[String:String]]? {
        didSet {
            self.cycleDotsView.pageCount = dataArr?.count ?? 0
            self.cycleDotsView.isHidden = self.cycleDotsView.pageCount > 1 ? false : true
            collectionView.reloadData {
                self.startAnimation()
            }
        }
    }
    private(set) lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.scrollDirection = .horizontal
        let colletionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        colletionView.backgroundColor = .clear
        colletionView.delegate = self
        colletionView.dataSource = self
        colletionView.decelerationRate = .fast
        colletionView.register(WHBannerCell.self, forCellWithReuseIdentifier: "WHBannerCell")
        colletionView.showsHorizontalScrollIndicator = false
        colletionView.bounces = false
        colletionView.isPagingEnabled = true
        return colletionView
    }()
    
    private(set) lazy var cycleDotsView: WHCommonCycleDotsView = {
        let dotsView = WHCommonCycleDotsView()
        dotsView.fixedWith = 36.0
        dotsView.associateScrollView = collectionView
        return dotsView
    }()
    
    public var currentIndex: Int = 0
    
    public var playVideoBlock: WHCommonBannerPlayVideoBlock?
    
    var displayCell: WHBannerCell?

    var timer: Timer?
    
    // MARK: - life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        layoutUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        invalidateTimer()
    }
    public override func layoutSubviews() {
        super.layoutSubviews()
        _ = NotificationCenter.default.addObserver(forName: UIApplication.didEnterBackgroundNotification,
                                                   object: nil,
                                                   queue: nil) { _ in
            self.invalidateTimer()
        }
        _ = NotificationCenter.default.addObserver(forName: UIApplication.willEnterForegroundNotification,
                                                   object: nil,
                                                   queue: nil) { _ in
            self.setupTimer()
        }
        if (Int(collectionView.contentOffset.x) == 0) {
            guard let dataArr = dataArr else { return }
            // 一个的时候不需要滚动
            if dataArr.count > 1 {
                currentIndex =  100 - 100 % dataArr.count
                let index = IndexPath(item:currentIndex, section: 0)
                collectionView.scrollToItem(at: index, at: .centeredHorizontally, animated: false)
            }
        }
    }
    // MARK: - setup ui
    func setupUI() {
        addSubview(collectionView)
        addSubview(cycleDotsView)
    }
    
    func layoutUI() {
        collectionView.snp.makeConstraints { (make) in
            make.edges.equalTo(self)
        }
        cycleDotsView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(36.0)
            make.height.equalTo(5.0)
            make.bottom.equalToSuperview().offset(-44.0)
        }
    }
    // MARK: - Public
    public func startAnimation() {
        guard let dataArr = dataArr else { return }
        needAnimation = true
        setupTimer()
        if dataArr.count > 0 {
            delegate?.bannerView?(self, didScrollAt: currentIndex % dataArr.count)
        }
    }
    
    func stopAnimation() {
        invalidateTimer()
    }
    // MARK: - prive method
    
    func setupTimer() {
        invalidateTimer()
        // 两个以上才轮播
        if (dataArr?.count ?? 0 <= 1) { return }
        let timeInterval: TimeInterval = 5
        if self.timer == nil {
            let timer = Timer(timeInterval: timeInterval, repeats: true) { [weak self] (timer) in
                guard let self = self, let dataArr = self.dataArr, dataArr.count >= 1 else { return }
                if self.getControllerfromview(view: self) != UIViewController.wh_top() {
                    return
                }
                if self.currentIndex == 999 { self.currentIndex = 0 }
                self.collectionView.setContentOffset(CGPoint(x: (self.currentIndex + 1) * Int(self.width), y: 0), duration: 0.6, timingFunction: .quadInOut)
            }
            RunLoop.main.add(timer, forMode: .common)
            self.timer = timer
        }
        self.timer?.fireDate = Date(timeIntervalSinceNow: timeInterval)
    }
    
    func invalidateTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    func getControllerfromview(view:UIView)->UIViewController?{
        var nextResponder: UIResponder? = view
        repeat {
            nextResponder = nextResponder?.next
            if let viewController = nextResponder as? UIViewController {
                return viewController
            }
        } while nextResponder != nil
        return nil
    }
}
// MARK: - UICollectionViewDelegate, UICollectionViewDelegateFlowLayout, UICollectionViewDataSource
extension WHCommonBannerView: UICollectionViewDelegate, UICollectionViewDelegateFlowLayout, UICollectionViewDataSource {
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if let dataArr = self.dataArr,
           dataArr.count > 0 {
            if (dataArr.count == 1) {
                return 1
            } else {
                return 1000
            }
        } else {
            return 0
        }
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "WHBannerCell", for: indexPath) as! WHBannerCell
        cell.fullScreen = self.fullScreen
//        cell.testLabel.text = cell.description
        if let dataArr = self.dataArr,
           dataArr.count > 0 {
            cell.imgView.isHidden = false
            let dict = dataArr[indexPath.row % dataArr.count]
            if let defaultModeBanner = dict["filename"] { //仅在审核时会触发
                cell.imgView.image = UIImage(named: defaultModeBanner)
                self.delegate?.bannerViewFirstPageIsLoad?(isSuccess: true)
            } else {
                if let video = dict["video"],
                   video.count > 0 {
                    cell.imgView.sd_setImage(with: URL(string: dict["videoCover"] ?? ""))
                    // 播放视频
                } else {
                    cell.imgView.sd_setImage(with: URL(string: dict["url"] ?? "")) { image, error, cacheType, imageUrl in
                        if indexPath.row == 0 {
                            let isSuccess = (error == nil) ? true : false
                            self.delegate?.bannerViewFirstPageIsLoad?(isSuccess: isSuccess) //如果第一张图已经有结果，则当前banner不应该隐藏
                        }
                    }
                }
            }
            cell.sheme = dict["scheme"] ?? ""
        }
        return cell
    }
        
    public func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        guard let cell = cell as? WHBannerCell else { return }
        
        displayCell?.imgView.isHidden = false
        displayCell = cell
        
        if let dataArr = self.dataArr,
           dataArr.count > 0 {
            cell.imgView.isHidden = false
            let dict = dataArr[indexPath.row % dataArr.count]
            if let defaultModeBanner = dict["filename"] {
            } else {
                if let video = dict["video"],
                   video.count > 0 {
                    cell.imgView.sd_setImage(with: URL(string: dict["videoCover"] ?? ""))
                    playVideoBlock?(video, video, cell.videoView, cell) { [weak self] object, id in
                        guard let self = self else { return }
                        if indexPath.row == 0 {
                            self.delegate?.bannerViewFirstPageIsLoad?(isSuccess: true) //如果第一张图已经有结果，则当前banner不应该隐藏
                        }
                        if let cell = object as? WHBannerCell {
                            cell.imgView.isHidden = true
                        }
                    }
                }
            }
        }
    }
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: collectionView.width, height: self.height)
    }
    
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let dataArr = self.dataArr,
           dataArr.count > 0 {
            delegate?.bannerView?(self, didSelectedAt: indexPath.row % dataArr.count)
        }
    }
}

extension WHCommonBannerView: UIScrollViewDelegate {
    public func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        if needAnimation {
            invalidateTimer()
        }
    }
    
    public func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if needAutoScroll {
            setupTimer()
        }
    }
    
    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if let dataArr = self.dataArr,
           dataArr.count > 0 {
            cycleDotsView.scrollViewScrollOffsetX(x: scrollView.contentOffset.x)
            let index = lround(collectionView.contentOffset.x / self.width)
            if (currentIndex != index) {
                currentIndex = index
                delegate?.bannerView?(self, didScrollAt: currentIndex % dataArr.count)
            }
        }
    }
}

class WHBannerCell: UICollectionViewCell {
    
    var sheme: String = ""
    /// 是否全屏样式
    var fullScreen: Bool = false {
        didSet {
            if oldValue != fullScreen {
//                imgView.layer.cornerRadius = 0
                imgView.snp.updateConstraints { make in
                    make.left.equalTo(self).offset(fullScreen ? 0 : 20)
                    make.right.equalTo(self).offset(fullScreen ? 0 : -20)
                }
                bgImgView.snp.updateConstraints { make in
                    make.left.equalTo(self).offset(fullScreen ? 0 : 20)
                    make.right.equalTo(self).offset(fullScreen ? 0 : -20)
                }
            }
        }
    }
    
    lazy var bgImgView: UIImageView = {
        let bgImgView = UIImageView()
        bgImgView.contentMode = .scaleAspectFill
//        bgImgView.layer.cornerRadius = 12
//        bgImgView.layer.masksToBounds = true
        return bgImgView
    }()
    
    lazy var videoView: UIView = {
        let view = UIView()
        return view
    }()
    
    lazy var imgView: UIImageView = {
        let img = UIImageView()
        img.contentMode = .scaleAspectFill
        img.clipsToBounds = true
//        img.layer.borderWidth = 1
//        img.layer.borderColor = UIColor.white.cgColor
//        img.layer.cornerRadius = 12
//        img.layer.masksToBounds = true
        return img
    }()
    
//    lazy var testLabel: UILabel = {
//        let label = UILabel()
//        label.font = .pingFangSCFont(ofSize: 20, weight: .bold)
//        label.textColor = .red
//        label.textAlignment = .center
//        label.numberOfLines = 0
//        return label
//    }()
    
    // MARK: - life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .clear
        setupUI()
        layoutUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - setup ui
    func setupUI() {
        self.contentView.addSubview(bgImgView)
        self.contentView.addSubview(videoView)
        self.contentView.addSubview(imgView)
//        self.contentView.addSubview(testLabel)
    }
    
    func layoutUI() {
        bgImgView.image = UIImage.init(wh_named: "wh_home_banner_bg_normal")
        bgImgView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(self).offset(fullScreen ? 0 : 20)
            make.right.equalTo(self).offset(fullScreen ? 0 : -20)
        }
        imgView.snp.makeConstraints { (make) in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(self).offset(fullScreen ? 0 : 20)
            make.right.equalTo(self).offset(fullScreen ? 0 : -20)
        }
        videoView.snp.makeConstraints { make in
            make.edges.equalTo(imgView)
        }
//        testLabel.snp.makeConstraints { make in
//            make.edges.equalTo(imgView)
//        }
    }
    
}

public extension UICollectionView {
    func reloadData(_ completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0, animations: {
            self.reloadData()
        }, completion: { _ in
            completion()
        })
    }
}
