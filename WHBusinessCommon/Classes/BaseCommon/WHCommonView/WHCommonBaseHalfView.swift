//
//  WHCommonBaseHalfView.swift
//  WHBusinessCommon
//
//  Created by z<PERSON>lifei on 2023/12/26.
//  半窗

import Foundation
import WHBaseLibrary

open class WHCommonBaseHalfView: UIView, UIGestureRecognizerDelegate {
    
    /// 处理滑动事件
    private var panY: CGFloat = 0
    /// 是否支持滑动手势
    public var isPan: Bool = false {
        didSet {
            if isPan {
                self.addGestureRecognizer(panGestureRecognizer)
            } else {
                self.removeGestureRecognizer(panGestureRecognizer)
            }
        }
    }
    
    private(set) lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.cornerRadius = 12
        return view
    }()
    
    public lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(wh_hexString: "#141414")
        view.layer.cornerCurve = .continuous
        view.layer.cornerRadius = 24
        return view
    }()
    
    private lazy var panGestureRecognizer: UIPanGestureRecognizer = {
        let pan = UIPanGestureRecognizer(target: self, action: #selector(handlePan(from:)))
        self.addGestureRecognizer(pan)
        return pan
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        addSubview(bgView)
        self.bgView.frame = CGRect(x: 0, y: WH_SCREEN_HEIGHT, width: WH_SCREEN_WIDTH, height: WH_SCREEN_HEIGHT)
        bgView.addSubview(containerView)
        
        addGestureRecognizer()
    }
    
    func addGestureRecognizer() {
        let ges = UITapGestureRecognizer(target: self, action: #selector(tapAction(_:)))
        ges.delegate = self
        self.addGestureRecognizer(ges)
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func show(in view: UIView? = nil) {
        var superview: UIView! = view
        if superview == nil {
            var vc = WHCurrentTopViewController()
            vc = vc?.navigationController ?? vc
            if let vv = vc?.view {
                superview = vv
            } else {
                superview = (UIApplication.shared.keyWindow ?? UIView())
            }
        }
        // 防止重复加载
        if superview.subviews.contains(where: { view in
            return view is Self
        }) {
            return
        }
        superview.addSubview(self)
        self.backgroundColor = UIColor.black.withAlphaComponent(0)
        self.frame = CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: WH_SCREEN_HEIGHT)

        UIView.animate(withDuration: 0.25) {
            self.backgroundColor = UIColor.black.withAlphaComponent(0.7)
            self.bgView.frame = CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: WH_SCREEN_HEIGHT)
        }
    }
    
    open func dismiss() {
        UIView.animate(withDuration: 0.25) {
            self.backgroundColor = UIColor.black.withAlphaComponent(0)
            self.containerView.frame = CGRect(x: 0, y: WH_SCREEN_HEIGHT, width: WH_SCREEN_WIDTH, height: self.containerView.height)
        } completion: { success in
            self.removeFromSuperview()
        }
    }
    
    // MARK: - event
    @objc func tapAction(_ tap: UITapGestureRecognizer) {
        let touchPoint = tap.location(in: self)
        if touchPoint.y < WH_SCREEN_HEIGHT - containerView.height {
            dismiss()
        }
    }
    
    @objc func handlePan(from recognizer: UIPanGestureRecognizer) {
        let height = containerView.height
        let containerViewY = WH_SCREEN_HEIGHT - containerView.height
        if recognizer.location(in: self).y < containerViewY {return}
        if recognizer.state == .began {
            panY = recognizer.location(in: self).y
        } else if recognizer.state == .ended {
            if recognizer.location(in: self).y - panY > 50 {
                dismiss()
            } else if recognizer.location(in: self).y - panY > 0 {
                UIView.animate(withDuration: 0.25) {
                    self.containerView.frame = CGRect(x: 0, y: containerViewY, width: WH_SCREEN_WIDTH, height: height)
                }
            } else {
                
            }
        } else {
            let diff = recognizer.location(in: self).y - panY
            if diff >= 0 {
                self.containerView.frame = CGRect(x: 0, y: containerViewY + diff, width: WH_SCREEN_WIDTH, height: height)
            } else {
                self.containerView.frame = CGRect(x: 0, y: containerViewY, width: WH_SCREEN_WIDTH, height: height)
            }
        }
    }
    
    open func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view?.superview is UITableViewCell {
            return false
        }
        return true
    }
}
