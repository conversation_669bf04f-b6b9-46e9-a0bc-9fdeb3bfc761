//
//  WHCommonCycleDotsView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/1/8.
//

import Foundation
import UIKit

public class WHCommonCycleDotsView: UIView {
    
    public var pageCount: Int {
        didSet {
            if oldValue != pageCount {
                layoutUI()
            }
        }
    }
    public var fixedWith: CGFloat = 0
    let spacing: CGFloat
    var dotWidth: CGFloat = 18
    public weak var associateScrollView: UIScrollView?

    lazy var contentView : UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(white: 1, alpha: 0.3)
        view.layer.cornerRadius = 2.5
        view.layer.masksToBounds = true
        return view
    }()
    
    lazy var cursorView : UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.frame = CGRect(x: 0, y: 0, width: dotWidth, height: 5)
        view.layer.cornerRadius = 2.5
        view.layer.masksToBounds = true
        return view
    }()
        
    // MARK: - life cycle
    public init(pageCount: Int = 0, spacing: CGFloat = 0) {
        self.pageCount = pageCount
        self.spacing = spacing
        super.init(frame: .zero)
        setupUI()
        layoutUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - setup ui
    func setupUI() {
        
        layer.cornerRadius = 2.5
        layer.masksToBounds = true
        
        addSubview(contentView)
        contentView.addSubview(cursorView)
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func layoutUI() {
        var width = dotWidth * CGFloat(pageCount) + spacing * CGFloat(pageCount - 1)
        if fixedWith > 0 {
            width = fixedWith
            dotWidth = fixedWith / 2.0
        }
        
        contentView.snp.makeConstraints { (make) in
            make.edges.equalTo(self)
            make.width.equalTo(width)
            make.height.equalTo(4)
        }
        cursorView.width = dotWidth
    }
    
    // MARK: - events

    // MARK: - Public Method
    public func scrollViewScrollOffsetX(x: CGFloat) {
        guard let associateScrollView = associateScrollView, self.pageCount > 1 else { return }
        
        let scrollViewWidth = associateScrollView.frame.width
        
        let w = scrollViewWidth * CGFloat(self.pageCount)
        let x = x.truncatingRemainder(dividingBy: w)
        if (x + scrollViewWidth) > w { //说明是最后一个页面要回到第一个，这个是否运动效果是从右往左移动
            let progress = x.truncatingRemainder(dividingBy: scrollViewWidth) / scrollViewWidth
            if progress >= 0 && progress <= 1 {
                if self.fixedWith > 0 {
                    let vx = self.fixedWith - self.dotWidth - progress * (self.fixedWith - self.dotWidth)
                    self.cursorView.frame = CGRectMake(vx, cursorView.frame.origin.y, cursorView.frame.size.width, cursorView.frame.size.height)
                } else {
                    let vx = self.frame.width - cursorView.frame.size.width - progress * (self.frame.width - cursorView.frame.size.width)
                    self.cursorView.frame = CGRectMake(vx, cursorView.frame.origin.y, cursorView.frame.size.width, cursorView.frame.size.height)
                }
            }
        } else {
            let progress = x / w
            if progress >= 0 && progress <= 1 {
                if self.fixedWith > 0 {
                    let vx = progress * CGFloat(self.pageCount) * (self.fixedWith / CGFloat(2 * (self.pageCount - 1)))
                    self.cursorView.frame = CGRectMake(vx, cursorView.frame.origin.y, cursorView.frame.size.width, cursorView.frame.size.height)
                } else {
                    let vx = progress * self.frame.width
                    self.cursorView.frame = CGRectMake(vx, cursorView.frame.origin.y, cursorView.frame.size.width, cursorView.frame.size.height)
                }
            }
        }
    }
    
    public func selectedCursorAt(index: Int) {
        UIView.animate(withDuration: 0.25) {
            if self.fixedWith > 0 {
                let vx = CGFloat(index) * ((self.fixedWith/CGFloat(self.pageCount * 2)) + self.spacing)
                self.cursorView.frame = CGRectMake(vx, self.cursorView.frame.origin.y, self.cursorView.frame.size.width, self.cursorView.frame.size.height)
            } else {
                let vx = CGFloat(index) * (self.dotWidth + self.spacing)
                self.cursorView.frame = CGRectMake(vx, self.cursorView.frame.origin.y, self.cursorView.frame.size.width, self.cursorView.frame.size.height)
            }
        }
    }
    
    // MARK: - Private Method
    
    
}
