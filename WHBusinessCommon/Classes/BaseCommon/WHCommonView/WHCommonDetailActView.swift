//
//  WHCommonDetailActView.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2024/10/17.
//

import Foundation
import WHBaseLibrary

public class WHCommonDetailActView:UIView {
    
    public var toastString: String = ""
    public var actSource: String = ""
    
    public func settingImageView(imageUrl:String) {
        self.bgImgView.sd_setImage(with: URL(string: imageUrl))
    }
    
    // MARK: - Life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        WHPrint("释放了")
    }
    
    private func setupViews() {
        addSubview(bgImgView)
        bgImgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(69.0)
        }
        let tap = UITapGestureRecognizer(target: self, action: #selector(clickAction))
        bgImgView.addGestureRecognizer(tap)
        
        addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(bgImgView.snp.bottom).offset(4.0)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSizeMake(20.0, 20.0))
        }
    }
    
    private lazy var bgImgView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.clipsToBounds = false
        v.isUserInteractionEnabled = true
        return v
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(cm_named: "wh_common_paste_alert_view_close_button_normal"), for: .normal)
        button.addTarget(self, action: #selector(closeView), for: .touchUpInside)
        return button
    }()
    
    @objc func clickAction() {
        WHAnalyticsManager.otherTrackEvent("floating_icon_click", params: ["activity_source":self.actSource])
        if self.toastString.count > 0 {
            UIViewController.wh_top().showToast(title: toastString)
        }
    }
    @objc func closeView() {
        self.removeFromSuperview()
    }
}
