//
//  WHCommonPasteAlertView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/1/27.
//

import Foundation
import WHBaseLibrary

class WHCommonPasteAlertView: UIView {
    
    private var alertModel:WHPasteCommandModel?
    
    public static func showInView(rootView:UIView,model:WHPasteCommandModel?) {
        let alertView = WHCommonPasteAlertView(frame: CGRectMake(0, 0, WH_SCREEN_WIDTH, WH_SCREEN_HEIGHT))
        rootView.addSubview(alertView)
        alertView.alertModel = model
        alertView.updateViews()
//        UIView.animate(withDuration: 0.35) {
//            alertView.backgroundColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0.8)
//            alertView.bgImgView.alpha = 1.0
//            alertView.lightMaskImgView.alpha = 1.0
//            alertView.closeButton.alpha = 1.0
//        } completion: { finish in
//            WHAnalyticsManager.otherTrackEvent("whee_sharing_loading_popup_exp", params: [:])
//        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            alertView.animateShow()
        }
        
        
    }
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        WHPrint("释放了")
    }
    // MARK: - Private methods
    
    func updateViews() {
        self.avatorImgView.sd_setImage(with: URL(string: self.alertModel?.actUser?.avatar ?? ""),placeholderImage: UIImage(cm_named: "wh_common_paste_alert_view_user_avator_image"))
        self.titleLabel.text = self.alertModel?.actUser?.screenName ?? ""
        self.messageLabel.text = self.alertModel?.text ?? ""
        self.activityImgView.sd_setImage(with: URL(string: self.alertModel?.pic ?? ""))
    }
    
    func animateShow() {
        lightMaskImgView.snp.updateConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(415.0, 550.0))
        }
        bgImgView.snp.updateConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(267.0, 430.0))
        }
        avatorImgView.snp.updateConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(-20.0)
            make.size.equalTo(CGSizeMake(66.0, 66.0))
        }
        titleLabel.snp.updateConstraints { make in
            make.top.equalTo(avatorImgView.snp.bottom).offset(16.0)
            make.centerX.equalToSuperview()
            make.width.equalTo(110.0)
            make.height.equalTo(21.0)
        }
        messageLabel.snp.updateConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(0.0)
            make.centerX.equalToSuperview()
            make.width.equalTo(200.0)
            make.height.equalTo(42.0)
        }
        
        activityImgView.snp.updateConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(49.0)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSizeMake(200.0, 200.0))
        }
        
        clickButton.snp.updateConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(bgImgView.snp.bottom).offset(8.0)
            make.size.equalTo(CGSizeMake(247.0, 93.0))
        }
        UIView.animate(withDuration: 0.35) {
            self.backgroundColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0.8)
            self.closeButton.alpha = 1.0
            self.layoutIfNeeded()
        } completion: { finish in
            self.titleLabel.alpha = 1.0
            self.messageLabel.alpha = 1.0
            WHAnalyticsManager.otherTrackEvent("whee_sharing_loading_popup_exp", params: [:])
        }
    }
    
    private func setupViews() {
        addSubview(lightMaskImgView)
        lightMaskImgView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(1, 1))
        }
        
        addSubview(bgImgView)
        bgImgView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(1, 1))
        }
        
        bgImgView.addSubview(avatorImgView)
        bgImgView.addSubview(titleLabel)
        titleLabel.alpha = 0.0
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(avatorImgView.snp.bottom).offset(16.0)
            make.centerX.equalToSuperview()
            make.width.equalTo(0.0)
            make.height.equalTo(0.0)
        }
        bgImgView.addSubview(messageLabel)
        messageLabel.alpha = 0.0
        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(0.0)
            make.centerX.equalToSuperview()
            make.width.equalTo(0.0)
            make.height.equalTo(0.0)
        }
        bgImgView.addSubview(activityImgView)
        bgImgView.addSubview(clickButton)
        addSubview(closeButton)
        closeButton.alpha = 0.0
        closeButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(bgImgView.snp.bottom).offset(24.0)
            make.size.equalTo(CGSizeMake(40.0, 40.0))
        }
    }
    
    @objc func clickButtonAction() {
        WHAnalyticsManager.otherTrackEvent("whee_sharing_loading_popup_click", params: [:])
        self.closeAlertView(isAnimate: false)
        WHRouter.route(with: self.alertModel?.url ?? "")
    }
    @objc func closeView() {
        self.closeAlertView(isAnimate: true)
    }
    func closeAlertView(isAnimate:Bool = true) {
        if isAnimate == true {
            UIView.animate(withDuration: 0.35) {
                self.backgroundColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0)
                self.bgImgView.alpha = 0.0
                self.lightMaskImgView.alpha = 0.0
                self.closeButton.alpha = 0.0
            } completion: { finish in
                self.removeFromSuperview()
            }
        } else {
            self.removeFromSuperview()
        }
    }
    ///Lazy
    private lazy var lightMaskImgView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(cm_named: "wh_common_paste_alert_view_light_mask_view_image")
        v.clipsToBounds = false
        v.isUserInteractionEnabled = true
        return v
    }()
    private lazy var bgImgView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = UIImage(cm_named: "wh_common_paste_alert_view_bg_img_view_image")
        v.clipsToBounds = false
        v.isUserInteractionEnabled = true
        return v
    }()
    private lazy var avatorImgView: UIImageView = {
        let v = UIImageView()
        v.contentMode = .scaleAspectFill
        v.layer.cornerRadius = 33.0
        v.layer.masksToBounds = true
        return v
    }()
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = WHFigmaColor.init(wh_hexString: "#774130")
        label.font = UIFont.pingFangSCFont(ofSize: 15,weight: .medium)
        label.textAlignment = .center
        return label
    }()
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.textColor = WHFigmaColor.init(wh_hexString: "#774130")
        label.font = UIFont.pingFangSCFont(ofSize: 15,weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    private lazy var activityImgView: UIImageView = {
        let v = UIImageView()
        v.contentMode = .scaleAspectFill
        v.layer.cornerRadius = 16.0
        v.layer.masksToBounds = true
        return v
    }()
    private lazy var clickButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setBackgroundImage(UIImage(cm_named: "wh_common_paste_alert_view_click_button_normal"), for: .normal)
        button.addTarget(self, action: #selector(clickButtonAction), for: .touchUpInside)
        return button
    }()
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(cm_named: "wh_common_paste_alert_view_close_button_normal"), for: .normal)
        button.addTarget(self, action: #selector(closeView), for: .touchUpInside)
        return button
    }()
}
