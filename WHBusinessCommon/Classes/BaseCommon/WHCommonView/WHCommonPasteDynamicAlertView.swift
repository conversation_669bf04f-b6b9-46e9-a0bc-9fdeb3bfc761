//
//  WHCommonPasteDynamicAlertView.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2024/10/17.
//

import Foundation
import WHBaseLibrary

class WHCommonPasteDynamicAlertView: UIView {
    
    private var alertModel:WHPasteCommandModel?
    private var extraString:String = "" //扩展的字符创（code）
    
    public static func showInView(rootView:UIView,model:WHPasteCommandModel?,extraString:String = "") {
        let alertView = WHCommonPasteDynamicAlertView(frame: CGRectMake(0, 0, WH_SCREEN_WIDTH, WH_SCREEN_HEIGHT))
        rootView.addSubview(alertView)
        alertView.alertModel = model
        alertView.extraString = extraString
        alertView.updateViews()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            alertView.animateShow()
        }
    }
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        WHPrint("释放了")
    }
    // MARK: - Private methods
    
    func updateViews() {
        self.bgImgView.sd_setImage(with: URL(string: self.alertModel?.pics?.bgPicUrl ?? ""))
        self.clickButton.sd_setImage(with: URL(string: self.alertModel?.pics?.btnPicUrl ?? ""), for: .normal)
    }
    
    func animateShow() {
        let bgViewWidth = WH_SCREEN_WIDTH - (54.0 * 2)
        let bgViewHeight = (bgViewWidth * 450.0) / 267.0
        
        bgImgView.snp.updateConstraints { make in
            make.centerY.equalToSuperview().offset(-32.0)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSizeMake(bgViewWidth, bgViewHeight))
        }

        let btnWidth = bgViewWidth - 20.0
        let btnHeight = (btnWidth * 93.0) / 247.0
        clickButton.snp.updateConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(bgImgView.snp.bottom).offset(8.0)
            make.size.equalTo(CGSizeMake(btnWidth, btnHeight))
        }
        UIView.animate(withDuration: 0.35) {
            self.backgroundColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0.8)
            self.closeButton.alpha = 1.0
            self.layoutIfNeeded()
        } completion: { finish in
            WHAnalyticsManager.otherTrackEvent("whee_popup_expo", params: ["activity_source":self.alertModel?.actSource ?? ""])
        }
    }
    
    private func setupViews() {
        
        addSubview(bgImgView)
        bgImgView.snp.makeConstraints { make in
            make.centerY.equalToSuperview().offset(-32.0)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSizeMake(1, 1))
        }
        
        bgImgView.addSubview(clickButton)
        addSubview(closeButton)
        closeButton.alpha = 0.0
        closeButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(bgImgView.snp.bottom).offset(24.0)
            make.size.equalTo(CGSizeMake(40.0, 40.0))
        }
    }
    
    @objc func clickButtonAction() {
        WHAnalyticsManager.otherTrackEvent("whee_popup_click", params: ["click_type":"create_now","activity_source":self.alertModel?.actSource ?? ""])
        self.closeAlertView(isAnimate: false)
        let oriJumpLink = alertModel?.btnJumpLink ?? ""
        var jumpLink = oriJumpLink
        if self.extraString.count > 0 {
            let paramsStr = "code="+(self.extraString.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? "")
            jumpLink = oriJumpLink+"&"+paramsStr
        }
        WHRouter.route(with: jumpLink)
    }
    @objc func closeView() {
        self.closeAlertView(isAnimate: true)
    }
    func closeAlertView(isAnimate:Bool = true) {
        if isAnimate == true {
            UIView.animate(withDuration: 0.35) {
                self.backgroundColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0)
                self.bgImgView.alpha = 0.0
                self.closeButton.alpha = 0.0
            } completion: { finish in
                self.removeFromSuperview()
            }
        } else {
            self.removeFromSuperview()
        }
    }
    ///Lazy

    private lazy var bgImgView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.clipsToBounds = false
        v.isUserInteractionEnabled = true
        return v
    }()

    private lazy var clickButton: UIButton = {
        let button = UIButton(type: .custom)
        button.addTarget(self, action: #selector(clickButtonAction), for: .touchUpInside)
        return button
    }()
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(cm_named: "wh_common_paste_alert_view_close_button_normal"), for: .normal)
        button.addTarget(self, action: #selector(closeView), for: .touchUpInside)
        return button
    }()
}
