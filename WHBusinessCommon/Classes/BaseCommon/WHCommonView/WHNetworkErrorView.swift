//
//  WHNetworkErrorView.swift
//  ThirdLibCompare
//
//

import UIKit
import WHBaseLibrary
import WHBusinessCommon
//import YYText

struct MTCNetworkEmptyViewHeight {
    //无网络空视图去设置时的最小高度
    static let kMTNetworkEmptyViewGoSetMinHeight: CGFloat = 300
    //无网络空视图有图时的最小高度
    static let kMTNetworkEmptyViewImageMinHeight: CGFloat = 240
    //无网络空视图无图时的最小高度
    static let kMTNetworkEmptyViewTitleMinHeight: CGFloat  = 90
}

public enum MTCNetworkErrorViewOption {
    case showImage(Bool)
    case showOpenSetting(Bool)
    case darkModel(Bool)
    case titleText(String)
}

public class WHNetworkErrorView: UIView {
    // custom closure
    open var tapRetryAction: (() -> Void)?
    open var tapOpenSettingAction: (() -> Void)?
    open var networkAvailableAction:(() -> Void)? //网络监听回调
    
    // custom property
    private var showImage: Bool = true
    private var showOpenSetting: Bool = false
    private var darkMode: Bool = false
    private var titleText: String?
    
    public init(tapOpenSettingAction: (() -> ())?) {
        super.init(frame: CGRect.zero)
        self.tapOpenSettingAction = tapOpenSettingAction
        self.setup()
    }
    
    /// 外界可以指定frame初始化无网页面
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.setup(frame)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setup()
    }
    
    deinit {
        
    }
    
    public init(showImage: Bool = true, showOpenSetting: Bool = false, darkMode: Bool = false, customText: String? = nil) {
        self.showImage = showImage
        self.showOpenSetting = showOpenSetting
        self.darkMode = darkMode
        self.titleText = customText
        super.init(frame: CGRect.zero)
        self.setup()
    }
    
    public lazy var imageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(cm_named: "icon_feed_nonetwork"))
        imageView.contentMode = .scaleToFill
        return imageView
    }()
    
    private lazy var label: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(wh_hexString: "#F7F8FA")
        label.textAlignment = .center
        return label
    }()
    
    public lazy var button: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .white
        button.setTitle(WHLocalizedString("重试", comment: ""), for: .normal)
        button.setTitleColor(UIColor(wh_hexString: "#121212"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 15, weight: .medium)
        button.setImage(UIImage(cm_named: "icon_nonetwork_refresh"), for: .normal)
        button.layoutButton(style: .Left, imageTitleSpace: 8)
        button.addTarget(self, action: #selector(actionRetry(_ :)), for: UIControl.Event.touchUpInside)
        button.layer.masksToBounds = true
        button.layer.cornerRadius = 20
        return button
    }()
    
    private func setup(_ frame: CGRect? = nil) {
        isUserInteractionEnabled = true
        self.backgroundColor = .clear
        let vHeight = showOpenSetting ? MTCNetworkEmptyViewHeight.kMTNetworkEmptyViewGoSetMinHeight : (self.showImage ? MTCNetworkEmptyViewHeight.kMTNetworkEmptyViewImageMinHeight : MTCNetworkEmptyViewHeight.kMTNetworkEmptyViewTitleMinHeight)
        self.frame = frame ?? CGRect(x: 0, y: 0, width: WH_SCREEN_WIDTH, height: vHeight)
        
        label.text = WHLocalizedString("网络异常，请重试", comment: "")
        addSubview(label)
        
        if self.showImage {
            addSubview(imageView)
            imageView.snp.makeConstraints { (make) in
                make.top.centerX.equalTo(self)
                make.width.equalTo(120)
                make.height.equalTo(120)
            }
        }
        label.snp.makeConstraints { (make) in
            if showImage {
                make.top.equalTo(imageView.snp.bottom)
            } else {
                make.top.equalToSuperview()
            }
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(22)
        }
        
        addSubview(button)
        button.snp.makeConstraints { (make) in
            make.top.equalTo(label.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
        
        if darkMode {
            imageView.image = UIImage(cm_named: "icon_feed_nonetwork")
            label.textColor = UIColor(r: 160, g: 163, b: 166)
        }
        if showOpenSetting {
            setupOpenSetting()
        }
        observerNetwork()
    }
    
    private func setupOpenSetting() {
//        let font = UIFont.systemFont(ofSize: 14)
//        let leftText = WHLocalizedString("若您未开启网络权限，请点此开启。")
//        let rightText = WHLocalizedString("立即开启")
//        let tipText = NSMutableAttributedString.init(string: WHLocalizedString("\(leftText)\(rightText)", comment: ""))
//        let tipsRange = NSRange(location: 0, length: tipText.length)
//        tipText.addAttribute(NSAttributedString.Key.font, value: font, range: tipsRange)
//        tipText.addAttribute(NSAttributedString.Key.foregroundColor, value:UIColor(rgb: 0xBBBBBB, alpha: 1), range: tipsRange)
//        tipText.addAttribute(NSAttributedString.Key.foregroundColor, value:UIColor.red, range: NSRange(location: leftText.count, length: 4))
//        let tipsLabel = YYLabel()
//        let highlight: YYTextHighlight = YYTextHighlight()
//        highlight.tapAction = { [weak self] (_,text,range,_) in
//            guard let self = self else { return }
//            if let tapOpenSettingAction = self.tapOpenSettingAction {
//                tapOpenSettingAction()
//            } else {
//                
//            }
//        }
//        highlight.setColor(UIColor.red)
//        tipText.yy_setTextHighlight(highlight, range: (tipText.string as NSString).range(of: WHLocalizedString(rightText, comment: "")))
//        tipsLabel.numberOfLines = 0
//        tipsLabel.attributedText = tipText
//        tipsLabel.textAlignment = .center
//        addSubview(tipsLabel)
//        tipsLabel.snp.makeConstraints { (make) in
//            make.centerX.equalTo(self)
//            make.left.equalTo(self).offset(16)
//            make.right.equalTo(self).offset(-16)
//            make.top.greaterThanOrEqualTo(button.snp.bottom).offset(16)
//        }
    }
    //监听网络
    private func observerNetwork() {
        NotificationCenter.default.addObserver(self, selector: #selector(networkDidChange), name: .KMINetworkDidChange, object: nil)
    }
    
    @objc func networkDidChange() {
        if WHNetworkChangeMonitorDefault.currentNetwork != .notReachable && WHAppInfo.isAppNewInstalled {
            guard let networkClosureAction = self.networkAvailableAction else { return }
            networkClosureAction()
        }
    }
    
    @objc func actionRetry(_ sender: UIButton) {
        tapRetryAction?()
    }
    
    public static func openSetting() {
        let settingUrl = URL(string: UIApplication.openSettingsURLString)
        if let url =  settingUrl {
//            UIApplication.shared.canOpenURL(url)
            UIApplication.shared.open(url)
        }
    }
}

private extension WHNetworkErrorView {
    func setOptions(_ options: [MTCNetworkErrorViewOption]?) {
        if let options = options {
            for option in options {
                switch option {
                case let .showImage(value):
                    self.showImage = value
                case let .showOpenSetting(value):
                    self.showOpenSetting = value
                case let .darkModel(value):
                    self.darkMode = value
                case let .titleText(value):
                    self.titleText = value
                }
            }
        }
    }
}
