//
//  WHBoostAlertView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/3/9.
//

import Foundation
import Lottie

public class WHRewardAlertView: WHCommonBaseAlertView {
    
    public lazy var starBgView: LottieAnimationView = {
        let lottieView = LottieAnimationView()
        lottieView.animation = LottieAnimation.named("reward_star",
                                               bundle: Bundle.main,
                                               subdirectory: nil,
                                               animationCache: nil)
        return lottieView
    }()
    
    private lazy var bgImgView: UIImageView = {
        return UIImageView(image: UIImage(cm_named: "wh_common_alert_bg"))
    }()
    
    private lazy var meidouIcon: UIImageView = {
        return UIImageView(image: UIImage(cm_named: "wh_common_alert_meidou"))
    }()
    
    private lazy var meidouCountLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 44, weight: .medium)
        return label
    }()
    
    private lazy var closeBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_common_alert_close"), for: .normal)
        btn.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        return btn
    }()
    
    private lazy var textLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 18, weight: .medium)
        label.textColor = UIColor(wh_hexString: "#141414")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var confirmBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.titleLabel?.font = .pingFangSCFont(ofSize: 14, weight: .medium)
        btn.setTitleColor(.white, for: .normal)
        btn.addTarget(self, action: #selector(confirmAction), for: .touchUpInside)
        return btn
    }()
    
    private let count: Int
    private let text: String
    private let confirmBtnUrl: String?
    private let showStarBg: Bool
    private let closeBlock: (() -> ())?
    private let confirmBlock: (() -> ())?
    
    public init(count: Int,
                text: String,
                confirmBtnUrl: String? = nil,
                showStarBg: Bool = false,
                closeBlock: (() -> ())? = nil,
                confirmBlock: (() -> ())? = nil) {
        self.count = count
        self.text = text
        self.confirmBtnUrl = confirmBtnUrl
        self.showStarBg = showStarBg
        self.closeBlock = closeBlock
        self.confirmBlock = confirmBlock
        super.init(frame: .zero)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public override func show(in view: UIView? = nil) {
        super.show(in: view)
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
    }
    
    @objc
    private func closeAction() {
        dismiss {
            self.closeBlock?()
        }
    }
    
    @objc
    private func confirmAction() {
        dismiss {
            self.confirmBlock?()
        }
    }
    
    private func setupSubviews() {
        meidouCountLabel.text = String(count)
        textLabel.text = text
        
        if let confirmBtnUrl = confirmBtnUrl,
           let url = URL(string: confirmBtnUrl) {
            confirmBtn.sd_setImage(with: url, for: .normal)
        } else {
            confirmBtn.backgroundColor = UIColor(wh_hexString: "#17171A")
            confirmBtn.setTitle(WHLocalizedString("知道了"), for: .normal)
            confirmBtn.layer.cornerRadius = 20
            confirmBtn.layer.masksToBounds = true
        }
        
        if showStarBg {
            insertSubview(starBgView, belowSubview: containerView)
            starBgView.snp.makeConstraints { make in
                make.center.equalTo(containerView)
                make.size.equalTo(CGSize(width: 840, height: 1624))
            }
            starBgView.play()
        }
        
        containerView.addSubview(bgImgView)
        containerView.addSubview(meidouIcon)
        containerView.addSubview(meidouCountLabel)
        containerView.addSubview(closeBtn)
        containerView.addSubview(textLabel)
        containerView.addSubview(confirmBtn)
        
        containerView.layer.cornerRadius = 32
        containerView.layer.masksToBounds = true
        
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(52)
        }
        bgImgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        meidouIcon.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(30)
            make.width.height.equalTo(50)
            make.right.equalTo(meidouCountLabel.snp.left).offset(-8)
        }
        meidouCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(meidouIcon)
            make.centerX.equalToSuperview().offset(29)
        }
        closeBtn.snp.makeConstraints { make in
            make.top.right.equalToSuperview()
            make.width.height.equalTo(52)
        }
        textLabel.snp.makeConstraints { make in
            make.top.equalTo(meidouIcon.snp.bottom).offset(28)
            make.left.right.equalToSuperview().inset(24)
        }
        confirmBtn.snp.makeConstraints { make in
            make.top.equalTo(textLabel.snp.bottom).offset(16)
            make.bottom.equalToSuperview().offset(-24)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(40)
        }
    }
    
}
