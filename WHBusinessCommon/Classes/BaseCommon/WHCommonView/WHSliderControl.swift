//
//  WHSliderControl.swift
//  MeituWhee
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/12/15.
//

import UIKit
import WHBaseLibrary

public enum WHSliderEvent {
    case touchDown
    case touchUp
    case valueChanged
}

public enum WHSliderUIStyle {
    case normal
    case largerVideo  //相册大屏浏览样式
}

public var titleLabelNormalWidth: CGFloat = 72

@objc(WHSliderControl)
open class WHSliderControl: UIView {
    
    public typealias ValueChangedHandle = (Float, WHSliderEvent) -> Void
    public typealias CustomizedValueTextHandle = (Float) -> String
    
    open var title: String? {
        get {
            return titleLabel.text
        } set {
            titleLabel.text = newValue
        }
    }
    
    open var isValueInputable: Bool = false {
        didSet {
            updateView()
        }
    }
    
    /// 标题宽度是否自适应，默认为false，这时宽度固定为56，为true时根据文本内容自动调整标题宽度
    public var isTitleContentFit: Bool = false {
        didSet {
            titleLabel.snp.remakeConstraints { make in
                make.left.equalTo(16)
                make.centerY.equalToSuperview()
                if !isTitleContentFit {
                    make.width.equalTo(titleLabelNormalWidth)
                }
            }
            titleLabel.adjustsFontSizeToFitWidth = !isTitleContentFit
        }
    }
    
    public var valueChangedHandle : ValueChangedHandle?
    
    public lazy var customizedValueTextHandle : CustomizedValueTextHandle = { [unowned self] (value) in
        return "\(Int(((value + self.slider.customizedShowValueOffset) * self.slider.customizedShowValueRatio).rounded()))"
    }
    
    private var observation: NSKeyValueObservation?

    public private(set) lazy var titleLabel: WHOutlineLabel = {
        let view = WHOutlineLabel()
        view.textColor = .white
        view.textAlignment = .center
        view.adjustsFontSizeToFitWidth = true
        view.font = .systemFont(ofSize: 12, weight: .medium)
        return view
    }()
    
    public private(set) lazy var valueTextFiled: UITextField = {
        let view = UITextField()
        view.tintColor = .black
        view.adjustsFontSizeToFitWidth = true
        view.textColor = .white
        view.font = .systemFont(ofSize: 12, weight: .medium)
        view.delegate = self
        view.keyboardType = .numberPad
        view.layer.cornerRadius = 4
        view.returnKeyType = .done
        return view
    }()
    
    public private(set) lazy var slider: Slider = {
        let view = Slider()
        observation = view.observe(\.value) { [unowned self] (_, _) in
            self.updateValueLabel()
        }
        view.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
        view.addTarget(self, action: #selector(actionValueChanged), for: .valueChanged)
        view.addTarget(self, action: #selector(actionTouchDown), for: .touchDown)
        view.addTarget(self, action: #selector(actionTouchUp), for: .touchCancel)
        view.addTarget(self, action: #selector(actionTouchUp), for: .touchUpInside)
        view.addTarget(self, action: #selector(actionTouchUp), for: .touchUpOutside)
        return view
    }()
    
    public func setSliderUIStyle(style:WHSliderUIStyle) {
        if style == .largerVideo {
            slider.snp.remakeConstraints { (make) in
                make.left.right.equalToSuperview()
                make.centerY.equalToSuperview()
            }
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required public init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupSubviews()
    }
    
    open override var intrinsicContentSize: CGSize {
        return CGSize(width: UIView.noIntrinsicMetric, height: 40)
    }
    
    private func setupSubviews() {
        addSubview(titleLabel)
        addSubview(slider)
//        addSubview(valueTextFiled)
        
        titleLabel.snp.makeConstraints { (make) in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(titleLabelNormalWidth)
        }
        slider.snp.makeConstraints { (make) in
            make.left.equalTo(titleLabel.snp.right)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
        }
//        valueTextFiled.snp.makeConstraints { (make) in
//            make.centerY.equalToSuperview()
//            make.size.equalTo(CGSize(width: 36, height: 24))
//            make.right.equalTo(-12)
//        }
        updateView()
        updateValueLabel()
    }
    
    private func updateView() {
        if isValueInputable {
            valueTextFiled.isUserInteractionEnabled = true
            valueTextFiled.textAlignment = .center
            valueTextFiled.backgroundColor = .white
        } else {
            valueTextFiled.isUserInteractionEnabled = false
            valueTextFiled.textAlignment = .right
            valueTextFiled.backgroundColor = .clear
        }
    }
    
    @objc private func actionValueChanged() {
        updateValueLabel()
        valueChangedHandle?(slider.value, .valueChanged)
    }
    
    @objc private func actionTouchDown() {
        valueChangedHandle?(slider.value, .touchDown)
    }
    
    @objc private func actionTouchUp() {
        valueChangedHandle?(slider.value, .touchUp)
    }
    
    private func updateValueLabel() {
        valueTextFiled.text = customizedValueTextHandle(slider.value)
    }

}

extension WHSliderControl: UITextFieldDelegate {
    
    public func textFieldDidBeginEditing(_ textField: UITextField) {
        // 光标移到最后
//        DispatchQueue.main.async {
//            let count = textField.text?.count ?? 0
//            textField.hbp.selectedRange = NSRange(location: count, length: 0)
//        }
    }
    
    public func textFieldDidEndEditing(_ textField: UITextField, reason: UITextField.DidEndEditingReason) {
        if let text = textField.text,
           let number = Float(text)?.rounded() {
            var value = number
            value = min(value, slider.maximumValue)
            value = min(value, slider.maximumValue)
            slider.value = value
            valueChangedHandle?(slider.value, .touchUp)
        } else {
//            HUD.showNotice(NSLocalizedString("请输入有效数值", comment: "请填写路径"))
            updateValueLabel()
        }
    }
    
    public func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        return true
    }
    
}

public struct SliderStyle {
    /// 圆球描边颜色
    var thumbBorderColor: UIColor
    
    /// 圆球-100 滑竿描边颜色
    var trackBorderColor: UIColor
    
    /// 圆球颜色
    var thumbBackground: UIColor
    
    /// 0 - 圆球 滑竿颜色
    var minTrackColor: UIColor
    
    /// 圆球-100 滑竿颜色
    var trackColor: UIColor
    
    /// 0 - 圆球 滑竿描边颜色
    var minTrackBorderColor: UIColor = .clear
    
    public static var white: SliderStyle {
        return SliderStyle(thumbBorderColor: .white,
                           trackBorderColor: UIColor(red: 0, green: 0, blue: 0, alpha: 0.16),
                           thumbBackground: .white,
                           minTrackColor: .white,
                           trackColor: UIColor(wh_hexString: "#43464D"))
        
    }
    
    public static var blue: SliderStyle {
        return SliderStyle(thumbBorderColor: .clear,
                           trackBorderColor: .clear,
                           thumbBackground: .white,
                           minTrackColor: UIColor(wh_hexString: "#3549FF"),
                           trackColor: UIColor(wh_hexString: "#43464D"),
                           minTrackBorderColor: .clear)
    }
    
    public static var disable: SliderStyle {
        return SliderStyle(thumbBorderColor: .white,
                           trackBorderColor: UIColor(red: 0, green: 0, blue: 0, alpha: 0.16),
                           thumbBackground: UIColor.white,
                           minTrackColor: UIColor.white,
                           trackColor: UIColor(wh_hexString: "#43464D"))
    }
    // 视频蓝色
    public static var video: SliderStyle {
        var s = SliderStyle(thumbBorderColor: UIColor.clear,
                            trackBorderColor: UIColor(red: 0, green: 0, blue: 0, alpha: 0.16),
                            thumbBackground: UIColor.white,
                            minTrackColor: UIColor(wh_hexString: "#3549FF"),
                            trackColor: UIColor(wh_hexString: "#43464D"))
        s.minTrackBorderColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.16)
        return s
    }
    
    public static var rightBlue: SliderStyle {
        return SliderStyle(thumbBorderColor: .clear,
                           trackBorderColor: .clear,
                           thumbBackground: .white,
                           minTrackColor: UIColor(wh_hexString: "#43464D"),
                           trackColor: UIColor(wh_hexString: "#3549FF"),
                           minTrackBorderColor: .clear)
    }

}

@objc(WHSlider)
public class Slider: UISlider {
    
    public enum Style {
        case normal
        // 整数吸附
        case normalAbsorb
        case bothway
        case section
        case bothwayCustomBG
        case customBG
    }
    
    public enum TipStyle {
        case none
        case alwaysAppear
        case valueChangedAppear
    }
    
    /// 吸附类型
    public enum AbsorbStyle {
        /// 结束时吸附
        case endAbsorb
        /// 滑动和结束时吸附
        case continueAbsorb
    }
    
    public override var isEnabled: Bool {
        didSet {
            updateView()
        }
    }
    
    public typealias CustomizedTipTextHandle = (_ sliderValue: Float) -> String
    
    public var customizedTipTextHandle : CustomizedTipTextHandle?
    
    /// 显示的数值的倍数
    public var customizedShowValueRatio: Float = 1
    /// 显示的数值的Offset(乘以倍数前）
    public var customizedShowValueOffset: Float = 0

    public var thumbBorderColor: UIColor? {
        didSet {
            updateView()
        }
    }
    
    public var style: Style = .normal {
        didSet {
            updateView()
        }
    }
    
    public var colorStyle: SliderStyle = .white {
        didSet {
            updateView()
        }
    }
    

    public var tipStyle: TipStyle = .none {
        didSet {
            tipbgView.isHidden = tipStyle == .alwaysAppear ? false : true
        }
    }
    
    // 吸附阈值， 距defaultValue小于阈值，则吸附
    public var absorbThreshold: Float?
    public var absorbStyle: AbsorbStyle = .endAbsorb
    
    public var enableDefaultValue: Bool = false {
        didSet {
            if enableDefaultValue, dotView.superview == nil {
                insertSubview(dotView, aboveSubview: minimumTrackView)
            }
        }
    }
    
    public var defaultValue: Float = 0
    
    // Section slider
    public var sectionCount: Int = 4 {
        didSet {
            updateView()
        }
    }
    
    public var backgroundImage: UIImage? {
        didSet {
            updateView()
        }
    }
    
    public var thumbImageString: String = "icon_mark_slider" {
        didSet {
            let thumbImage = UIImage(named: thumbImageString)
            setThumbImage(thumbImage, for: .normal)
        }
    }
    
    public var thumbImageBigString: String = "icon_mark_slider_big"
    
    private var thumbSize = CGSize(width: 24, height: 24)
    private let thumbDivideSize: CGFloat = 3
    private let trackHeight: CGFloat = 1
    
    private lazy var tipLabel : UILabel = {
        /// 这里的宽高会被 tipbgView 初始化的时候替换掉，因为里面用到了懒加载的tipLabel，所以肯定这里先执行
        let label = UILabel(frame: CGRect(origin: CGPoint.zero, size: CGSize(width: 34, height: 16)))
        label.font = .systemFont(ofSize: 12)
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.5
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    public var tipLabelTextColor : UIColor {
        get {
            return tipLabel.textColor
        }
        set {
            tipLabel.textColor = newValue
        }
    }
    
    private var tipOffset = CGPoint.zero
    
    private lazy var tipbgView : UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "icon_seekbar_tip"))
        imageView.frame = CGRect(origin: CGPoint.zero, size: CGSize(width: 34, height: 25))
        imageView.contentMode = .scaleAspectFit
        imageView.addSubview(tipLabel)
        self.addSubview(imageView)
        /// 数设计图格子
        tipLabel.frame = CGRect(x: 0, y: 1, width: 34, height: 19)
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var minimumTrackView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = false
        view.layer.borderWidth = 0.5
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var trackView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = false
        view.layer.masksToBounds = true
        view.layer.mask = maskLayer
        view.layer.borderWidth = 0.5

        return view
    }()
    
    private lazy var thumbView: UILabel = {
        let view = UILabel()
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        view.isUserInteractionEnabled = false
        
        return view
    }()

    private lazy var maskLayer: CAShapeLayer = {
        let layer = CAShapeLayer()
        layer.backgroundColor = UIColor.white.cgColor
        layer.fillRule = .evenOdd
        
        return layer
    }()
    
    private lazy var dotView: UIView = {
        let dot = UIView()
        dot.bounds = CGRect(x: 0, y: 0, width: 3, height: 7)
        dot.layer.cornerRadius = 2
        dot.layer.masksToBounds = true
        return dot
    }()
    
    private lazy var backgroundView: UIImageView = {
        let v = UIImageView()
        v.clipsToBounds = true
        return v
    }()
    
    private var tapGesture: UITapGestureRecognizer?
    
    private var isFirstTimeLayout = true
    
    private var currentValue: Float = 0
    private var isDragging = false
    
    private var displayValue: Float {
        let v: Float = ((self.value + customizedShowValueOffset) * customizedShowValueRatio).rounded()
        return v
    }
    
    public override func layoutSubviews() {
        super.layoutSubviews()
        
        if isFirstTimeLayout {
            isFirstTimeLayout = false
        }
        
        internalValueChange(self)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupViews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        setupViews()
    }
    
    public convenience init(style: Style) {
        self.init(frame: .zero)
        self.style = style
        updateView()
    }
    
    public override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        return bounds.insetBy(dx: -10, dy: -10).contains(point)
    }
    
    public override func setValue(_ value: Float, animated: Bool) {
        super.setValue(value, animated: animated)
        
        internalValueChange(self)
    }
    
    public override func beginTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        isDragging = false
        thumbSize = CGSize(width: 27, height: 27)
        return super.beginTracking(touch, with: event)
    }
    
    public override func continueTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
        super.continueTracking(touch, with: event)
        isDragging = true
        switch absorbStyle {
        case .continueAbsorb:
            let newValue = absorb(value: self.value)
            setValue(newValue, animated: true)
            sendActions(for: .valueChanged)
        default:
            break
        }
        return true
    }
    
    public override func cancelTracking(with event: UIEvent?) {
        thumbSize = CGSize(width: 24, height: 24)
        super.cancelTracking(with: event)
        let newValue = absorb(value: self.value)
        setValue(newValue, animated: true)
        sendActions(for: .valueChanged)
    }
    
    public override func endTracking(_ touch: UITouch?, with event: UIEvent?) {
        thumbSize = CGSize(width: 24, height: 24)
        super.endTracking(touch, with: event)
        let newValue = absorb(value: self.value)
        setValue(newValue, animated: true)
        sendActions(for: .valueChanged)
    }
    
    func setupViews() {
        isExclusiveTouch = true
        
        addTarget(self, action: #selector(internalValueChange), for: .valueChanged)
        addTarget(self, action: #selector(touchDown), for: .touchDown)
        addTarget(self, action: #selector(touchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
        tapGesture = UITapGestureRecognizer(target: self, action: #selector(actionTap(sender:)))
        if let tap = tapGesture {
            addGestureRecognizer(tap)
        }
        addSubview(backgroundView)
        addSubview(trackView)
        addSubview(minimumTrackView)
        addSubview(thumbView)
        sendSubviewToBack(trackView)
        
        let thumbImage = UIImage(named: thumbImageString)
        let trackImage = UIImage.init(color: .clear, size:  CGSize(width: 3, height: 2))?.withTintColor(UIColor.clear, renderingMode: .alwaysOriginal)
        for state in [.normal, .highlighted, .selected] as [UIControl.State] {
            setThumbImage(thumbImage, for: state)
            setMinimumTrackImage(trackImage, for: state)
            setMaximumTrackImage(trackImage, for: state)
        }
        
        updateView()
    }
    
    private func updateMaskLayer(thumbRect: CGRect, trackRect: CGRect) {
        let thumbCenter = thumbRect.center
        let offsetWidth = thumbRect.size.width / 2 + thumbDivideSize
        let targetRect = CGRect(x: thumbCenter.x - offsetWidth - trackRect.origin.x, y: 0, width: offsetWidth * 2 - 4, height: trackRect.height)
        
        let overlayPath = UIBezierPath(rect: CGRect(origin: .zero, size: trackRect.size))
        let transparentPath = UIBezierPath(rect: targetRect)
        overlayPath.append(transparentPath)
        overlayPath.usesEvenOddFillRule = true
        
        maskLayer.path = overlayPath.cgPath
    }
    
    public func setTipBackgroundImage(image: UIImage?, labelOffset: CGPoint, tipOffset: CGPoint) {
        guard tipStyle != .none else { return }
        tipbgView.image = image
        
        tipLabel.center.x += labelOffset.x
        tipLabel.center.y += labelOffset.y
        
        self.tipOffset = tipOffset
    }
    
    public func forceHideTips() {
        tipStyle = .none
        tipbgView.isHidden = true
    }
    
    @objc private func internalValueChange(_ slider: UISlider) {
        var trackRect = self.trackRect(forBounds: bounds)
        var thumbRect = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: slider.value)
        if colorStyle.thumbBorderColor == .clear {
            thumbRect = CGRect(
                x: thumbRect.origin.x + thumbDivideSize * 0.5,
                y: thumbRect.origin.y + thumbDivideSize * 0.5,
                width: thumbRect.size.width - thumbDivideSize,
                height: thumbRect.size.height - thumbDivideSize)
        }
        let thumbCenter = thumbRect.center
        let thumbWidth = thumbRect.size.width / 2
        let offsetWidth = thumbWidth + thumbDivideSize
        trackRect.size.height = 3
        let customTrackFrame = CGRect(x: 0, y: trackRect.origin.y + 1, width: bounds.width, height: trackRect.height)
        trackView.frame = customTrackFrame
        trackView.layer.cornerRadius = trackView.bounds.height / 2.0
        thumbRect.origin.y = 0
        thumbView.frame = CGRect(x: thumbRect.origin.x, y: 1.5, width: thumbRect.width, height: thumbRect.height)
        thumbView.center = thumbCenter
        thumbView.layer.cornerRadius = thumbView.width / 2.0
        thumbView.isHidden = !enableDefaultValue
        
        backgroundView.frame = bounds
        
        if enableDefaultValue {
            switch style {
            case .normal, .normalAbsorb:
                let thumb = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: defaultValue)
                dotView.center = CGPoint(x: dotView.bounds.size.width / 2.0 + thumb.minX, y: (thumb.maxY - thumb.minY) / 2.0 + thumb.minY)
            default:
                var dotCenter = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: defaultValue).center
                dotCenter.y += 2
                dotView.center = dotCenter
            }
        }
        
        switch style {
        case .normal, .normalAbsorb:
            let slValue: CGFloat = CGFloat(slider.value - slider.minimumValue)
            let srValue: CGFloat = CGFloat(slider.maximumValue - slider.minimumValue)
            let sValue: CGFloat = slValue / srValue
            let tWidth: CGFloat = (customTrackFrame.width - thumbSize.width) * sValue
            var minWidth = max(tWidth + 1, 0)
            minWidth = tWidth == 0 ? 0 : minWidth
            minimumTrackView.frame = CGRect(x: customTrackFrame.origin.x, y: customTrackFrame.origin.y, width: minWidth, height: customTrackFrame.height)
        case .bothway, .bothwayCustomBG:
            var halfValue = (maximumValue + minimumValue) / 2
            var centerX = bounds.width / 2
            
            if enableDefaultValue {
                halfValue = defaultValue
                let slValue: CGFloat = CGFloat(defaultValue - slider.minimumValue)
                let srValue: CGFloat = CGFloat(slider.maximumValue - slider.minimumValue)
                let sValue: CGFloat = slValue / srValue
                let tWidth: CGFloat = customTrackFrame.width * sValue
                centerX = max(tWidth, 0)
            }

            let x: CGFloat = min(slider.value < halfValue ? thumbCenter.x + offsetWidth : centerX, centerX)
            let maxX: CGFloat = slider.value < halfValue ? centerX : thumbCenter.x - offsetWidth
            minimumTrackView.frame = CGRect(x: x, y: trackRect.origin.y + 1, width: max(maxX - x, 0), height: trackRect.height)
        default:
            break
        }
        minimumTrackView.layer.cornerRadius = minimumTrackView.bounds.height / 2.0
        
        updateMaskLayer(thumbRect: thumbRect, trackRect: customTrackFrame)
        
        currentValue = slider.value
        
        if tipStyle != .none {
            tipbgView.center.x = thumbCenter.x + tipOffset.x
            /// 气泡视图（b）的y轴中心点离滑竿（s）的y轴中心点距离 = b的高度的一半（12.5）+ b的底部到s的y轴中心点的距离（15）
            tipbgView.center.y = thumbCenter.y - 15 - 12.5 + tipOffset.y
            tipLabel.text = customizedTipTextHandle?(slider.value) ?? "\(Int(displayValue))"
        }
    }
    
    @objc private func touchDown() {
        if tipStyle == .valueChangedAppear {
            tipbgView.isHidden = false
        }
        tapGesture?.isEnabled = false
        let thumbImage = UIImage(named: thumbImageBigString)
        setThumbImage(thumbImage, for: .normal)
        setThumbImage(thumbImage, for: .highlighted)
        setThumbImage(thumbImage, for: .selected)
    }
    
    @objc private func touchUp() {
        if tipStyle == .valueChangedAppear {
            tipbgView.isHidden = true
        }
        tapGesture?.isEnabled = true
        let thumbImage = UIImage(named: thumbImageString)
        setThumbImage(thumbImage, for: .normal)
    }
    
    @objc private func actionTap(sender: UITapGestureRecognizer) {
        if isDragging {
            return
        }
        let p = sender.location(in: self)
        let v: Double = Double(maximumValue - minimumValue) * (Double(p.x) / Double(bounds.size.width)) + Double(minimumValue)
        let newValue = absorb(value: Float(v))
        
        setValue(newValue, animated: true)
        sendActions(for: .touchDown)
        sendActions(for: .valueChanged)
        sendActions(for: .touchUpInside)
    }
    
    private func updateView() {
        switch style {
        case .section:
            minimumTrackView.isHidden = true
            trackView.isHidden = true
            dotView.isHidden = true
            backgroundView.isHidden = false
            backgroundView.contentMode = .scaleAspectFit
        case .bothwayCustomBG:
            minimumTrackView.isHidden = true
            trackView.isHidden = true
            dotView.isHidden = false
            backgroundView.isHidden = false
            backgroundView.contentMode = .scaleAspectFit
        case .customBG:
            minimumTrackView.isHidden = true
            trackView.isHidden = true
            dotView.isHidden = true
            backgroundView.isHidden = false
            backgroundView.contentMode = .scaleToFill
        default:
            minimumTrackView.isHidden = false
            trackView.isHidden = false
            dotView.isHidden = false
            backgroundView.isHidden = true
        }
        
        if backgroundImage != nil {
            backgroundView.image = backgroundImage
        } else {
//            var bgImageName: String
//            switch UIConst.sizeCategory {
//            case .inch4:
//                bgImageName = "slider_segment_small"
//            case .inch4_7, .inch5_5_zoom:
//                bgImageName = "slider_segment_mid"
//            default:
//                bgImageName = "slider_segment_big"
//            }
//            bgImageName += "_\(sectionCount)"
//            backgroundView.image = UIImage(named: bgImageName)
        }
        
        let style: SliderStyle = isEnabled ? colorStyle : SliderStyle.disable
        
        thumbView.backgroundColor = style.thumbBackground
        trackView.backgroundColor = style.trackColor
        minimumTrackView.backgroundColor = style.minTrackColor
        minimumTrackView.layer.borderColor = style.minTrackBorderColor.cgColor
        dotView.backgroundColor = style.minTrackColor
        
        thumbView.layer.borderWidth = trackHeight
        
        thumbView.layer.borderColor = thumbBorderColor?.cgColor ?? style.thumbBorderColor.cgColor
        trackView.layer.borderColor = style.trackBorderColor.cgColor
        minimumTrackView.layer.borderColor = style.thumbBorderColor.cgColor
        dotView.layer.borderColor = style.thumbBorderColor.cgColor
    }
    
    func absorb(value: Float) -> Float {
        switch style {
        case .section:
            let trackRect = self.trackRect(forBounds: bounds)
            let thumbRect = self.thumbRect(forBounds: bounds, trackRect: trackRect, value: value)
            let sectionTrackWidth = trackRect.size.width / CGFloat(sectionCount - 1)
            let range = self.maximumValue - self.minimumValue
            let newSection = round(thumbRect.midX / sectionTrackWidth)
            let rate = newSection * sectionTrackWidth / trackRect.width
            let newValue = range * Float(rate) + self.minimumValue
            return newValue
        case .normalAbsorb:
            return displayValue
        default:
            if enableDefaultValue, let absorb = absorbThreshold, abs(value - defaultValue) < absorb {
                // 吸附到默认值
                return defaultValue
            } else {
                return value
            }
        }
    }
}

