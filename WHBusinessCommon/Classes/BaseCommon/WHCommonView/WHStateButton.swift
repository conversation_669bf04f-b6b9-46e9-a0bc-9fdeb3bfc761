//
//  WHStateButton.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/12.
//

import Foundation
import WHBaseLibrary
import SnapKit

public class WHStateButton: UIButton {
    
    public enum State {
        case none
        case normal(icon: UIImage?, title: String?, subTitle: String?)
        case meidou(meidou: Int, title: String?)
    }
    
    public enum BackgroundColor {
        case color(color: UIColor)
        case gradient(points: (start: CGPoint, end: CGPoint),
                      colors: [UIColor],
                      locations: [NSNumber])
    }
    
    @available(*, unavailable, message: "请使用 setBackgroundColor方法 来设置背景色")
    public override var backgroundColor: UIColor? {
        get { super.backgroundColor }
        set {}
    }
    
    private lazy var icon: UIImageView = {
        let view = UIImageView()
        return view
    }()
    
    private lazy var titleLbl: UILabel = {
        let label = UILabel()
        label.textColor = .black
        label.font = .pingFangSCFont(ofSize: 16, weight: .semibold)
        return label
    }()
    
    private lazy var subTitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .black
        return label
    }()

    private var aState: State = .none
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setState(_ state: State) {
        self.aState = state
        remakeContentConstraints()
    }
    
    public func setBackgroundColor(_ bgColor: BackgroundColor) {
        switch bgColor {
        case .color(let color):
            super.backgroundColor = color
        case .gradient(let points, let colors, let locations):
            wh_enableGradient(points: points, colors: colors, locations: locations)
        }
    }
    
    private func setupSubviews() {
        addSubview(icon)
        addSubview(titleLbl)
        addSubview(subTitleLabel)
        remakeContentConstraints()
    }
    
    private func remakeContentConstraints() {
        switch aState {
        case .none:
            icon.image = nil
            titleLbl.text = nil
            subTitleLabel.text = nil
        case .normal(let icon, let title, let subTitle):
            self.icon.image = icon
            titleLbl.text = title
            subTitleLabel.text = subTitle
            subTitleLabel.font = .pingFangSCFont(ofSize: 11)
            subTitleLabel.snp.remakeConstraints { make in
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().offset(13)
                make.height.equalTo(15)
            }
            self.icon.snp.remakeConstraints { make in
                make.centerY.equalTo(titleLbl)
                make.width.height.equalTo(20)
                make.right.equalTo(titleLbl.snp.left).offset(-8)
            }
            titleLbl.snp.remakeConstraints { make in
                make.height.equalTo(24)
                if subTitle == nil {
                    make.centerY.equalToSuperview()
                } else {
                    make.bottom.equalTo(subTitleLabel.snp.top).offset(-2)
                }
                if icon == nil {
                    make.centerX.equalToSuperview()
                } else {
                    make.centerX.equalToSuperview().offset(14)
                }
            }
        case .meidou(let meidou, let title):
            icon.image = UIImage(cm_named: "mine_meidou")
            titleLbl.text = title
            subTitleLabel.text = String(meidou)
            subTitleLabel.font = .pingFangSCFont(ofSize: 18, weight: .medium)
            let subTitleWidth: CGFloat = subTitleLabel.sizeThatFits(CGSize(width: CGFloat.greatestFiniteMagnitude, height: 25)).width
            icon.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalTo(subTitleLabel.snp.left)
                make.width.height.equalTo(24)
            }
            subTitleLabel.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalTo(titleLbl.snp.left).offset(-8)
            }
            titleLbl.snp.remakeConstraints { make in
                make.centerY.equalToSuperview()
                make.centerX.equalToSuperview().offset((24 + subTitleWidth + 8) / 2)
            }
        }
    }
        
}
