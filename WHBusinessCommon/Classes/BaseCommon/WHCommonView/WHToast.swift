//
//  WHToast.swift
//  WHBusinessCommon
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/10/5.
//

import Foundation
import WHBaseLibrary

// MARK: - Toast
extension UIView {
    static var closeBlock: (() -> Void)?
    public func hiddenToast() {
        WHProgressHUD.hide(for: self, animated: true)
    }
    
    /// 带✅的toast
    /// - Parameters:
    ///   - title: 展示文案
    ///   - completion: 完成回调
    public func showSucessToast(title: String,completion: (() -> Void)? = nil){
        showToast(title: title,image: UIImage(cm_named: "checkBlack"))
    }
    
    /// toast
    /// - Parameters:
    ///   - title: 展示文案
    ///   - image: 展示图片
    ///   - completion: 完成回调
    public func showToast(title: String,
                          image: UIImage? = nil,
                          completion: (() -> Void)? = nil) {
        WHProgressHUD.hide(for: self, animated: false)
        let hud = WHProgressHUD.showAdded(to: self, animated: true)
        hud.offset = CGPoint(x: 0, y: WH_NAVIGATION_BAR_HEIGHT + 24)
        hud.mode = WHProgressHUDMode.text
        hud.label.text = title
        hud.label.numberOfLines = 0
        hud.bezelView.style = .solidColor
        hud.bezelView.backgroundColor = UIColor(wh_hexString: "#3549FF")
        hud.frame = CGRectMake(hud.frame.origin.x, hud.frame.origin.y, hud.frame.size.width, 120.0)
        if let image = image {
            hud.customView = UIImageView(image: image)
            hud.mode = .customView
        }
        let delay = 3.0
        hud.hide(animated: true, afterDelay: delay)
        if let completion = completion {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: completion)
        }
    }
    
    /// 带进度条的loading
    /// - Parameters:
    ///   - title: 展示文案
    ///   - progress: 进度0-1
    ///   - showClose: 是否展示关闭按钮
    ///   - close: 关闭按钮回调
    public func showLoading(title: String,showClose:Bool = false,showMask:Bool = false,close: (() -> Void)? = nil) {
        var hud:WHProgressHUD = WHProgressHUD.forView(self) ?? WHProgressHUD.showAdded(to: self, animated: true)
        hud.mode = WHProgressHUDMode.annularDeterminate
        hud.offset = CGPoint(x: 0, y: WH_NAVIGATION_BAR_HEIGHT + 24)
        hud.label.text = title
        hud.bezelView.style = .solidColor
        hud.bezelView.backgroundColor = UIColor(wh_hexString: "#3549FF")
        if showMask{
            hud.showMask()
        }
        if showClose{
            hud.mode = WHProgressHUDMode.determinate
            hud.button.setImage(UIImage(cm_named: "crossBlack"), for: .normal)
            hud.button.addTarget(self, action: #selector(cancel), for: .touchUpInside)
            hud.button.setTitle(" ", for: .normal)
            Self.closeBlock = close
        }
        hud.progress = 0.5
    }
    
    @objc func cancel(){
        Self.closeBlock?()
        hiddenToast()
    }

}

extension UIViewController{
    public func hiddenToast() {
        WHProgressHUD.hide(for: self.view, animated: true)
    }
    
    /// toast
    /// - Parameters:
    ///   - title: 展示文案
    ///   - image: 展示图片
    ///   - completion: 完成回调
    public func showToast(title: String,
                          image: UIImage? = nil,
                          completion: (() -> Void)? = nil) {
        self.view.showToast(title: title, image: image, completion: completion)
    }
    
    public func showLoading(title: String,showClose:Bool = false,showMask:Bool = false,close: (() -> Void)? = nil){
        self.view.showLoading(title: title,showClose: showClose,showMask: showMask,close: close)
    }
}
