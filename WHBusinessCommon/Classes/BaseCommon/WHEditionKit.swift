//
//  WHEditionKit.swift
//  WHBusinessCommon
//
//  Created by zhanglifei on 2023/10/23.
//

import Foundation
import CoreTelephony
import WHBaseLibrary

private let kMTEditionRegionConfigKey = "area_type"
private let kMTEditionServiceCCCacheKey = "WHEdition_ServiceCCCacheKey"
private let defaultRegionType: WHEditionKit.WHRegionType = .mainland
private let kMTCanSwitchChinaMainlandKey = "kMTCanSwitchChinaMainlandKey"
private let kMTEditionRegionKey = "MTEdition_kMTEditionAreaKey"
public let kMTCountryCode = "MTEdition_Country"

//地区语言说明：https://api-mock.meitu-int.com/project/2406/interface/api/134138
@objc public class WHEditionKit: NSObject, CTTelephonyNetworkInfoDelegate {
    
    @objc private static let shared = WHEditionKit()
    
    @objc private var networkInfo: CTTelephonyNetworkInfo?
    
    @objc public var simCountryCode: String?
    
    private var isServiceCCUpdate:Bool = false //当前启动是否更新过service_cc
    
    @objc public enum WHRegionType: Int {
        case mainland = 1   // 大陆
        case abroad = 2     // 海外
        case EU = 3         // 欧盟
    }

    /// 本地时区
    @objc private var localCountry: String {
        return Locale.current.regionCode ?? ""
    }
    
    /// 当前所在国家
    @objc private(set) var country: String = UserDefaults.standard.string(forKey: kMTCountryCode) ?? "" {
        didSet {
            if country == oldValue { return } // 所在地区未改变
            UserDefaults.standard.set(country, forKey: kMTCountryCode)
            UserDefaults.standard.synchronize()
        }
    }
    
    /// 当前所在地区
    private var regionType: WHRegionType = WHEditionKit.WHRegionType(rawValue: UserDefaults.standard.integer(forKey: kMTEditionRegionKey)) ?? defaultRegionType {
        didSet {
            if regionType == oldValue { return } // 所在地区未改变
            UserDefaults.standard.set(regionType.rawValue, forKey: kMTEditionRegionKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    override init() {
        super.init()
        setupNetworkInfo()
        setup()
        NotificationCenter.default.addObserver(self, selector: #selector(whConfigUpdate), name: .WHConfigCenterDataDidChange, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(setupNetworkInfo), name: UIApplication.willEnterForegroundNotification, object: nil)
    }
    
    private func setup() {
        // country 优先判断: sim卡 > 缓存service_cc >本机时区
        if let country = simCountryCode, country.count > 0 {
            self.country = country
        } else if let service_cc = WHEditionKit.getCountryCodeWithNewCache(),service_cc.count > 0 {
            self.country = service_cc
        } else if localCountry.count > 0 {
            self.country = localCountry
        }
        
        // region 优先级：配置中心 > 本地缓存 > 基于country本地逻辑处理
        // 以配置中心region 为准
        regionType = WHEditionKit.getRegionType(from: country)
    }
    
    @objc func setupNetworkInfo() {
        networkInfo = CTTelephonyNetworkInfo()
        if #available(iOS 13.0, *) {
            networkInfo?.delegate = self
        }
        simCountryCode = getCountryCodeWithSIM()
        WHPrint("MTEditionKit setup sim country code : \(simCountryCode)")
    }
    @objc func whConfigUpdate() { //配置更新--更新service_cc(area_cc)
        if self.isServiceCCUpdate == true {
            return
        }
        self.isServiceCCUpdate = true
        if let service_cc:String = WHConfigCenterManager.config(for: "area_cc"),service_cc.count > 0 {
            var cache_service_cc = ""
            if let sim_cc = self.getCountryCodeWithSIM(),sim_cc.count > 0 {
                cache_service_cc = sim_cc
                self.country = sim_cc
            } else {
                cache_service_cc = service_cc
                self.country = service_cc
            }
            UserDefaults.standard.set(cache_service_cc, forKey: kMTEditionServiceCCCacheKey)
            UserDefaults.standard.synchronize()
            //更新region
            self.regionType = WHEditionKit.getRegionType(from: self.country)
        }
    }
    public func dataServiceIdentifierDidChange(_ identifier: String) {
        simCountryCode = getCountryCodeWithSIM()
        WHPrint("MTEditionKit dataServiceIdentifierDidChange sim country code : \(simCountryCode)")
    }
    
    @objc private func getCountryCodeWithSIM() -> String? {
        guard let networkInfo = networkInfo else { return nil }
        if #available(iOS 12.0, *) {
            let infos = networkInfo.serviceSubscriberCellularProviders
            if #available(iOS 13.0, *) {
                if let currentDataId = networkInfo.dataServiceIdentifier {
                    if let carrier = infos?[currentDataId],
                       let name = carrier.carrierName, name.count > 0,
                       let code = carrier.isoCountryCode, code.count > 0,
                       let mobileNetworkCode = carrier.mobileNetworkCode,
                       mobileNetworkCode.count > 0, mobileNetworkCode != "65535" {
                        return code.uppercased()
                    }
                }
            }
            if let carrierInfos = infos {
                for (_, carrier) in carrierInfos {
                    //                    由于非国行手机会有内置SIM信息，比如美版iPad会内置gigsky等运营商，导致读取sim卡错误
                    if carrier.carrierName == "iPad" {
                        continue
                    }
                    if carrier.carrierName?.count ?? 0 > 0,
                       let code = carrier.isoCountryCode, code.count > 0,
                       let mobileNetworkCode = carrier.mobileNetworkCode,
                       mobileNetworkCode.count > 0, mobileNetworkCode != "65535" {
                        return code.uppercased()
                    }
                }
                return nil
            } else {
                return nil
            }
        } else {
            let carrier = networkInfo.subscriberCellularProvider
            let countryCode = carrier?.isoCountryCode?.uppercased()
            return countryCode
        }
    }
}

// MARK: - 获取国家码 刷新区域
public extension WHEditionKit {
    
    @objc public static func getRegionTypeWithSim() -> WHRegionType {
        guard let country = getCountryCodeWithSIM() else {
            return defaultRegionType
        }
        
        return getRegionType(from: country)
    }
    
    private static func getRegionType(from country: String) -> WHRegionType {
        let region: WHRegionType
        switch country {
        case "":
            region = defaultRegionType
        case "CN","cn":
            region = .mainland
        case "HK", "MO", "TW":
            region = .abroad
        default:
            if euRegionCodes.contains(country) {
                region = .EU
            } else {
                region = .abroad
            }
        }
        return region
    }
    
    /// 获取国家码 - 根据sim
    @objc public static func getCountryCodeWithSIM() -> String? {
        return WHEditionKit.shared.simCountryCode
    }
    
    /// 获取国家码 - 本地设置时区
    @objc public static func getCountryCodeWithLocal() -> String? {
        return WHEditionKit.shared.localCountry
    }
    /// 获取国家码 - 最新缓存的地区
    @objc public static func getCountryCodeWithNewCache() -> String? {
        return UserDefaults.standard.string(forKey: kMTEditionServiceCCCacheKey)
    }
    /// 欧盟国家地区码
     static var euRegionCodes: [String] {
        /*
         欧盟成员国 （奥地利AT、比利时BE、保加利亚BG、塞浦路斯CY、克罗地亚HR、捷克共和国CZ、丹麦DK、爱沙尼亚EE、芬兰FI、法国FR、德国DE、希腊GR(或是EL）、匈牙利HU、爱尔兰IE、意大利IT、拉脱维亚LV、立陶宛LT、卢森堡LU、马耳他MT、波兰PL、葡萄牙PT、罗马尼亚RO、斯洛伐克SK、斯洛文尼亚SI、西班牙ES、瑞典SE、荷兰NL、英国GB(或是UK))
         */
        ["AT", "BE", "BG", "CY", "HR", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "EL", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "NL", "GB", "UK"]
    }
}


// MARK: - 提供给各模块的判断条件
public extension WHEditionKit {
    /// 是否大陆
    @objc public static var isChinaMainLand: Bool {
        return WHEditionKit.shared.regionType == .mainland
    }
    
    /// 是否海外
    @objc public static var isAbroad: Bool {
        return WHEditionKit.shared.regionType == .abroad
    }
    
    /// 是否欧盟
    @objc public static var isEU: Bool {
        return WHEditionKit.shared.regionType == .EU
    }
    
    /// 当前所在国家code
    @objc public static var countryCode: String {
        var country = WHEditionKit.shared.country
        if country == "" {
            #if !IS_APP_STORE
            country = "CN"
            #endif
        }
        return country
    }
    
    public static var isEUCountry: Bool {
        return euRegionCodes.contains(countryCode)
    }
    
    /// 当前所在区域 (国内 / 港澳台 / 海外) (根据countryCode推断出, 未覆盖语言逻辑)
    @objc public static var regionType: WHRegionType {
        return WHEditionKit.shared.regionType
    }
    
    @objc public static func setup() {
        _ = WHEditionKit.shared
    }
    
    @objc public static var canSwitchChinaMainland: Bool {
        set {
            UserDefaults.standard.set(newValue, forKey: kMTCanSwitchChinaMainlandKey)
        }
        get {
            return UserDefaults.standard.bool(forKey: kMTCanSwitchChinaMainlandKey)
        }
    }
    
}

