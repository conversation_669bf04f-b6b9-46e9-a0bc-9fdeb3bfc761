//
//  WHEnvConfigManager.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2023/9/7.
//

import Foundation

@objc public enum WHRequestEnvironment: Int {
    case pre = 1
    case beta
    case release
}


public let WHEnvConfigShareManager = WHEnvConfigManager.shared

@objc public class WHEnvConfigManager: NSObject {
         public var envNetBaseUrl: String = ""
@objc    public static let shared = WHEnvConfigManager()
@objc    public var environment: WHRequestEnvironment = .release {
        didSet {
            envNetBaseUrl = self.envBaseURLsArray[environment.rawValue-1]
        }
    }
    
    var envBaseURLsArray: [String] = ["", "", ""]
    public func regisEnvBaseURLs(_ urls:[String], defaultEnv: WHRequestEnvironment){
        self.envBaseURLsArray = urls
        self.environment = defaultEnv
    }
}
