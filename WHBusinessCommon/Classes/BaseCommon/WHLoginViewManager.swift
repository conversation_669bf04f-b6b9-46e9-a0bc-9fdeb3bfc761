//
//  WHLoginViewManager.swift
//  WHBusinessCommon
//
//  Created by xiaoqi on 2023/9/7.
//

import Foundation
@_exported import MTAccount
public enum WHAcountLoginType: Int{
    case fullScreen
    case half
}

public typealias WHLoginServiceSuccessBlock = (Bool, MTACAccountInfo) -> Void
public typealias WHLoginServiceFailBlock = () -> Void
public typealias WHLoginServiceNoParamsBlock = () -> Void
public typealias WHLoginServiceDismissBlock = () -> Void

public let WHLoginViewsharedManager = WHLoginViewManager.shared
public class WHLoginViewManager: NSObject {
    static let shared = WHLoginViewManager()
    weak var hostVC: UIViewController?
    var accountInfo: MTACAccountInfo?
    public var successBlock: WHLoginServiceSuccessBlock?
    public var failBlock: WHLoginServiceFailBlock?
    
    public func showAccountLoginWith(hostVC: UIViewController,loginType: WHAcountLoginType,success: @escaping WHLoginServiceSuccessBlock,
                                     failure: @escaping WHLoginServiceFailBlock){
        if loginType == .half {
            alterLoginWith(hostVC: hostVC, success: success, failure: failure)
        } else {
            loginFullScreenWith(hostVC: hostVC, success: success, failure: failure)
        }
    }
    
    ///半窗登陆
    func alterLoginWith(hostVC: UIViewController,
                                     success: @escaping WHLoginServiceSuccessBlock,
                                     failure: @escaping WHLoginServiceFailBlock){
        let manager = MTACLoginAlertManager.share()
        manager.delegate = WHAccountShareManager
        manager.loginVCDelegate = WHAccountShareManager
        manager.currentVC = hostVC
        
        self.hostVC = hostVC
        self.successBlock = success
        self.failBlock = failure
        WHAccountShareManager.delegate = self
        manager.show()
    }
    ///全屏登陆
    func loginFullScreenWith(hostVC: UIViewController,success: @escaping WHLoginServiceSuccessBlock,
                                              failure: @escaping WHLoginServiceFailBlock,hiddenClose: Bool = false){
        let loginVC = MTACNewLoginViewController(delegate: WHAccountShareManager)
        loginVC.modalPresentationStyle = .fullScreen
        MTACAccountManager.share().closeBtnHidden = hiddenClose
        self.hostVC = hostVC
        self.successBlock = success
        self.failBlock = failure
        WHAccountShareManager.delegate = self
        let loginNav = UINavigationController.init(rootViewController: loginVC)
        loginNav.modalPresentationStyle = .fullScreen
        hostVC.present(loginNav, animated: true, completion: nil)
    }
}

extension WHLoginViewManager: WHAccountLoginManagerDelegate {
    public func willFinishLogin(completeType: MTACCompleteType, account: MTACAccountInfo) {
        
    }
    
    public func didFinishLogin(completeType: MTACCompleteType, account: MTACAccountInfo) {
        if let block = successBlock {
            block(true, account)
            successBlock = nil
        }
        NotificationCenter.default.post(name: NSNotification.Name.WHACCountLoginSuccess, object: nil)
    }
    
    public func didCancelLogin() {
        
    }
    
    public func didFailedLogin() {
        if let block = failBlock {
            block()
        }
    }
    
    public func didLoginOut() {
        NotificationCenter.default.post(name: NSNotification.Name.WHACCountLoginOut, object: nil)
    }
    
    public func didModifyPersonalInfo() {
        NotificationCenter.default.post(name: NSNotification.Name.WHACCountDidModifyPersonalInfo, object: nil)
    }
    
}



extension Notification.Name {
    public static let WHACCountLoginOut = Notification.Name("WHACCountLoginOut")
    public static let WHACCountLoginSuccess = Notification.Name("WHACCountLoginSuccess")
    public static let WHACCountDidModifyPersonalInfo = Notification.Name("WHACCountDidModifyPersonalInfo")
}
