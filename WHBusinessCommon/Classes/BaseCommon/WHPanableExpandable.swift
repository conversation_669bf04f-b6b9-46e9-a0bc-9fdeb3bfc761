//
//  WHPanableExpandable.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/11.
//

import Foundation

public protocol WHExpandable: AnyObject {
    func panableExpand(expand: Bool, anime: Bool, completion: (() -> ())?)
    var isExpand: Bool { get }
}

extension WHExpandable where Self: WHPanable {
    public func panableExpand(expand: Bool, anime: Bool) {
        panableExpand(expand: expand, anime: anime, completion: nil)
    }
    
    public func panableExpand(expand: Bool, anime: Bool, completion: (() -> ())?) {
        if expand == isExpand || isPanAnimating { return }
        _innerPanableHeightChange(height: expand ? panViewHeightMax : panViewHeightMin, anime: anime, completion: completion)
    }
    
    public var isExpand: Bool {
        return panViewHeight.rounded() == panViewHeightMax.rounded()
    }
}

public protocol WHPanable: AnyObject {
    var ignoreHeight : CGFloat { get }
    var panView: UIView { get }
    var panViewHeight: CGFloat { get }
    var extraView: UIScrollView? { get }
    var panViewHeightMin: CGFloat { get }
    var panViewHeightMax: CGFloat { get }
    var isPanAnimating: Bool { get set }
    
    var adjustExtraViewWhenReachLimit: Bool { get }
    func shouldAdjustExtraView(_ scrollView: UIScrollView) -> Bool
    
    var panGestureAvaiable: Bool { get }
    
    var gestureRecognizeMinY: CGFloat { get }
    var gestureRecognizeMaxY: CGFloat { get }
    
    func actionForGestureState(_ state: UIGestureRecognizer.State)
    
    func addPanGes()
    var gestureDelegate: UIGestureRecognizerDelegate? { get }
    
    func panableHeightChange(height: CGFloat, anime: Bool, gesture: UIGestureRecognizer?, completion: (() -> ())?)
    
    func panableCompletion()
}

extension WHPanable {
    public var panGestureAvaiable: Bool {
        return true
    }
    
    public var ignoreHeight : CGFloat {
        return 0
    }
    
    public var gestureRecognizeMinY: CGFloat {
        return 0
    }
    
    public var gestureRecognizeMaxY: CGFloat {
        return panViewHeightMax
    }
    
    public var adjustExtraViewWhenReachLimit: Bool {
        return true
    }
    
    public var panViewHeight: CGFloat {
        if let sv = panView.superview {
            return sv.bounds.height - panView.frame.minY
        } else {
            return 0
        }
    }
    
    public func shouldAdjustExtraView(_ scrollView: UIScrollView) -> Bool {
        return true
    }
    
    public func actionForGestureState(_ state: UIGestureRecognizer.State) {
        // do nothing
    }
    
    public func addPanGes() {
        let panGes = PanDirectionGestureRecognizer.init(axis: .vertical)
        panView.addGestureRecognizer(panGes)
        panGes.addHandle {[weak self] (ges) in
            // 注意：解决循环引用问题
            guard let self = self else { return}
            self.handleGes(rec: ges)
        }
        panGes.delegate = gestureDelegate
    }

    public func handleGes(rec: UIGestureRecognizer) {
        guard let recognizer = rec as? PanDirectionGestureRecognizer else { return }
        switch recognizer.state {
        case .began:
            recognizer.direction = nil
            actionForGestureState(.began)
        case .changed:
            let coordinateY = recognizer.translation(in: panView).y
            if coordinateY > 0 {
                recognizer.direction = .down
            } else {
                recognizer.direction = .up
            }
            
            let lastHeight = panViewHeight - coordinateY
            if lastHeight >= panViewHeightMin && lastHeight <= panViewHeightMax {
                _innerPanableHeightChange(height: lastHeight, anime: false, gesture: recognizer, adjust: false)
                extraView?.gestureRecognizers?.forEach({ (g) in
                    if let pan = g as? UIPanGestureRecognizer,
                       pan.state == .changed {
                        pan.setTranslation(.zero, in: pan.view)
                    }
                })
                recognizer.setTranslation(.zero, in: panView)
            } else {
                if let gestures = extraView?.gestureRecognizers,
                   gestures.contains(where: { $0 is UIPanGestureRecognizer && $0.state == .changed }) {
                    // do nothing
                } else {
                    recognizer.setTranslation(.zero, in: panView)
                }
            }
            
            actionForGestureState(.changed)
        case .ended, .cancelled, .failed:
            var height: CGFloat = recognizer.direction == .up ? panViewHeightMax : panViewHeightMin
            
            // 加20个像素点的区间，不根据趋势方向进行滑动，避免上滑后掉落问题
            if panViewHeight >= panViewHeightMax - ignoreHeight {
                height = panViewHeightMax
            } else if panViewHeight <= panViewHeightMin + ignoreHeight {
                height = panViewHeightMin
            }
            _innerPanableHeightChange(height: height, anime: true, gesture: recognizer)
            actionForGestureState(recognizer.state)
        default:
            break
        }
    }
    
    fileprivate func adjustContent(for scrollView: UIScrollView, currentHeight: CGFloat, animate: Bool) {
        let panViewHeightDiff = self.panViewHeightMax - self.panViewHeightMin
        
        if abs(currentHeight - self.panViewHeightMax) <= 0.5 {
            if scrollView.contentInset.bottom >= panViewHeightDiff {
                if let s = scrollView as? UIScrollView,
                   let o = s.contentOffset as? CGPoint {
                    s.contentInset.bottom -= panViewHeightDiff
                    s.setContentOffset(o, animated: true)
                } else {
                    let offsetBefore = scrollView.contentOffset
                    scrollView.contentInset.bottom -= panViewHeightDiff
                    scrollView.contentOffset = offsetBefore
                }
            }
            if scrollView.contentOffset.y + scrollView.bounds.height - scrollView.contentSize.height > -0.5 {
                var targetY = scrollView.contentSize.height - scrollView.bounds.height
                if targetY < 0 {
                    targetY = 0
                }
                scrollView.setContentOffset(.init(x: scrollView.contentOffset.x,
                                                  y: targetY),
                                           animated: animate)
            }
        } else if abs(currentHeight - self.panViewHeightMin) <= 0.5 {
            if scrollView.contentInset.bottom < panViewHeightDiff {
                if let s = scrollView as? UIScrollView,
                   let o = s.contentOffset as? CGPoint {
                    s.contentInset.bottom += panViewHeightDiff
                    s.setContentOffset(o, animated: true)
                } else {
                    let offsetBefore = scrollView.contentOffset
                    scrollView.contentInset.bottom += panViewHeightDiff
                    scrollView.contentOffset = offsetBefore
                }
            }
        }
    }
    
    fileprivate func _innerPanableHeightChange(height: CGFloat,
                                               anime: Bool,
                                               gesture: UIGestureRecognizer? = nil,
                                               adjust: Bool = true,
                                               completion: (() -> ())? = nil) {
        panableHeightChange(height: height, anime: anime, gesture: gesture) { [weak self] in
            if adjust,
               let self = self,
               self.adjustExtraViewWhenReachLimit,
               let extraView = self.extraView,
               self.shouldAdjustExtraView(extraView) {
                self.adjustContent(for: extraView, currentHeight: height, animate: true)
            }
            completion?()
            self?.panableCompletion()
        }
    }
    
    public func willChangingToExtraView(_ scrollView: UIScrollView) {
        if adjustExtraViewWhenReachLimit,
           shouldAdjustExtraView(scrollView) {
            adjustContent(for: scrollView, currentHeight: panViewHeight, animate: false)
        }
    }
    
    public func panableHeightChange(height: CGFloat, anime: Bool, gesture: UIGestureRecognizer?, completion: (() -> ())?) {
        commonPanableHeightChange(height: height, anime: anime, gesture: gesture, completion: completion)
    }
    
    public func commonPanableHeightChange(height: CGFloat, anime: Bool, gesture: UIGestureRecognizer?, completion: (() -> ())?) {
        guard panView.superview != nil else { return }
        
        panView.snp.updateConstraints { (make) in
            make.bottom.equalToSuperview().offset(panViewHeightMax - height)
        }
        if anime {
            let fullDistance = panViewHeightMax - panViewHeightMin
            let currDistance = abs(panViewHeight - height)
            let duration: TimeInterval = 0.28 * TimeInterval(currDistance / fullDistance)
            isPanAnimating = true
            UIView.animate(withDuration: duration) {
                self.panView.superview?.layoutIfNeeded()
            } completion: { _ in
                self.isPanAnimating = false
                completion?()
            }
        } else {
            self.panView.superview?.layoutIfNeeded()
            completion?()
        }
    }
    
    public func commonGestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        guard let panGes = gestureRecognizer as? PanDirectionGestureRecognizer else { return true }
        if !panGestureAvaiable {
            panGes.currentTouchStartPoint = nil
            return false
        }
        guard let startPoint = panGes.currentTouchStartPoint else { return true }
        if startPoint.y < gestureRecognizeMinY {
            // 若在展示区域外则不响应拖动手势，如：在滑竿展示区域上下拖动
            return false
        }
        if startPoint.y > gestureRecognizeMaxY {
            // http://cf.meitu.com/confluence/pages/viewpage.action?pageId=185330997
            // 若在指定热区外也不响应拖动手势
            return false
        }
        // 有额外响应手势的视图的话
        if let extraView = extraView {
            // 已经展开条件响应
            if panViewHeight.rounded() == panViewHeightMax.rounded() {
                let extraViewFrame = extraView.convert(extraView.bounds, to: panView)
                if !extraViewFrame.contains(startPoint) {
                    return true
                } else {
                    if (extraView.contentOffset.y) <= 0 {
                        return true
                    }
                    return false
                }
            }
            // 还没展开随时响应
            return true
        }
        // 如果只有单层视图直接响应
        return true
    }
    
    public func panableCompletion() {
        
    }
}
