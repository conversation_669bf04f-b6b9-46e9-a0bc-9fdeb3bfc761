//
//  WHPasteCommandManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/1/25.
//

import Foundation
import WHBaseLibrary
import YYModel

public let WHPasteCommandShareManager = WHPasteCommandManager.shared

public class WHPasteCommandManager: NSObject {
    static let shared = WHPasteCommandManager()
    
    let prefix = "whee://"
    let allowPageList = ["WHHomeViewController":true,"WHInspirationViewController":true,"WHMineViewController":true]
    var lastCount: Int = 0
    
    var isColdCheck:Bool = false //一次冷启动只check一次
    
    public func startPasteEngine() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(whConfigUpdate),
                                               name: .WHConfigCenterDataDidChange,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(applicationWillEnterForeground),
                                               name: UIApplication.willEnterForegroundNotification,
                                               object: nil)
    }
    
    //配置更新
    @objc func whConfigUpdate() {
        if self.getWheejtb(),isColdCheck == false {
            self.isColdCheck = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.checkParseCode()
            }
        }
    }
    //热启动检测
    @objc private func applicationWillEnterForeground() {
        let topVC = WHRouter.topViewController?.className ?? ""
        if self.getWheejtb(),self.allowPageList[topVC] == true {
            self.checkParseCode()
        }
    }
    
    func getWheejtb() -> Bool {
        var isKK = false
        if let iosExamined:String = WHConfigCenterManager.config(for: "switch.whee_jtb.switch") {
            if iosExamined == "1" {
                isKK = true
            }
        }
        return isKK
    }
    
    func checkParseCode() {
        if self.getWheejtb() == false {
            return
        }
        if #available(iOS 16.0, *) {
            self.isPasteEligible {[weak self] eligible in
                guard let self = self else { return }
                if eligible {
                    self.checkWHEEParseCode()
                }
            }
        } else {
            self.checkWHEEParseCode()
        }
    }
    
    func checkWHEEParseCode() {
        guard let pasteCode = UIPasteboard.general.string, pasteCode.count > 0 else { return }
        let resultCode: String = pasteCode
        if resultCode.contains(prefix),resultCode.count > prefix.count {
            WHAnalyticsManager.otherTrackEvent("clipboard_recognize_expo", params: [:])
            WHPasteCommandRequest.requestCodeExplain(code: resultCode) {[weak self] codeModel, whResponse in
                guard let self = self else { return }
                if whResponse.isSuccess(), let model = codeModel {
                    UIPasteboard.general.string = ""
                    if model.IsEffect == true,let window = UIApplication.wh_currentWindow {
                        //展示弹窗
                        //判断展示弹窗类型
                        if model.activityType == 4 {
                            if model.isReward {
                                // 已领取奖励
                                if model.receivedText.count > 0 {
                                    window.showToast(title: model.receivedText)
                                }
                            } else {
                                // 显示助力弹窗
                                self.showBoostAlertWith(code: resultCode, model: model)
                            }
                        } else {
                            if model.isDynamicPic == true { //动态模板
                                WHCommonPasteDynamicAlertView.showInView(rootView: window, model: model,extraString: resultCode)
                                if model.isReward == true && model.text.count > 0 { //有给奖励，给toast提示
                                    window.showToast(title: model.text)
                                }
                            } else { //本地迎新固定模版
                                WHCommonPasteAlertView.showInView(rootView: window, model: model)
                            }
                        }
                    } else if model.text.count > 0 {
                        UIApplication.wh_currentWindow?.showToast(title: model.text)
                    }
                }
            }
        }
    }
    
    func isPasteEligible(_ completion: @escaping ((Bool) -> ())) {
        let board = UIPasteboard.general
        guard board.changeCount > lastCount else {
            completion(false)
            return
        }
        if #available(iOS 16.0, *) {
            let keypsths: Set<PartialKeyPath<UIPasteboard.DetectedValues>> = [
                \UIPasteboard.DetectedValues.probableWebURL
            ]
            board.detectPatterns(for: keypsths) {[weak self] (result) in
                guard let self = self else { return }
//                for k in keypsthsArray {
//                    print(k.hashValue)
//                }
//
                switch result {
                case .success(let rvalues):
//                    print(rvalues)
                    var eligible = false
                    for val in rvalues {
//                        print(val.hashValue)
                        eligible = true
                    }
                    
                    if eligible {
                        let systemPast = UIPasteboard.general
                        if systemPast.changeCount > self.lastCount, let string = systemPast.string, string.count > 0 {
                            eligible = true
                        } else {
                            self.lastCount = systemPast.changeCount
                            eligible = false
                        }
                    }
                    completion(eligible)
                case .failure(_):
                    completion(false)
                }
            }
        } else {
            completion(true)
        }
    }
    
    /// 显示助力弹窗
    private func showBoostAlertWith(code: String, model: WHPasteCommandModel) {
        guard let window = UIApplication.wh_currentWindow else {
            return
        }
        let alert = WHBoostAlertView(count: model.rewardAmount,
                                     text: model.text) { [weak self] in
            guard let self = self else { return }
            if WHAccountShareManager.isLogin() {
                WHAlertPrioritySharedManager.manageBoostAndNewUserAlert(with: code,
                                                                        doLogin: false,
                                                                        currentPageName: WHRouter.topViewController?.className)
            } else {
                let currentPageName = WHRouter.topViewController?.className
                WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) {[weak self] (sucess, info) in
                    guard let self = self else { return }
                    WHAlertPrioritySharedManager.manageBoostAndNewUserAlert(with: code,
                                                                            doLogin: true,
                                                                            currentPageName: currentPageName)
                } failure: {
                }
            }
        }
        alert.show(in: window)
    }
}


public class WHPasteCommandRequest: NSObject {
    public static func requestCodeExplain(code: String, completion: ((_ codeModel: WHPasteCommandModel?,_ whResponse: WHOriginalResponse) -> Void)?) {
        var params: [String : Any] = [:]
        params["code"] = code
        WHSharedRequest.GET("/code/explain.json",params: params) { response in
            if let result = response.data() as? [String: Any] {
                let model = WHPasteCommandModel.yy_model(with: result)
                completion?(model,response)
            }
        }
    }
}

@objcMembers public class WHPasteCommandModel: NSObject, YYModel, NSCoding {
    
    /// 口令是否有效
    public var IsEffect:Bool = false
    public var url:String = ""
    public var pic:String = ""
    /// 活动文案
    public var text:String = ""
    public var actUser:WHPasteCommandActUserModel?
    public var isDynamicPic:Bool = false //是否是动态图片类型（弹窗图片由服务端下发）
    /// 是否领取奖励
    public var isReward:Bool = false
    public var pics:WHPasteCommandAltPicsModel?
    public var btnJumpLink:String = ""
    public var actSource:String = "" //source
    /// 活动类型，1:分享口令，2:邀请拉新口令，3:站酷雪王活动，4:裂变拉新
    public var activityType: Int = 0
    /// 按钮图片
    public var btnPic: String = ""
    /// 奖励美豆数量
    public var rewardAmount: Int = 0
    public var receivedText: String = ""
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
        
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "IsEffect": "is_effect",
            "url":"url",
            "pic": "pic",
            "text": "text",
            "actUser": "act_user",
            "isDynamicPic": "is_dynamic_pic",
            "isReward": "is_reward",
            "pics": "pics",
            "btnJumpLink": "btn_jump_link",
            "actSource": "activity_source",
            "activityType": "activity_type",
            "btnPic": "btn_pic",
            "rewardAmount": "reward_amount",
            "receivedText": "received_text",
        ]
    }
    
}

@objcMembers public class WHPasteCommandActUserModel: NSObject, YYModel, NSCoding {
    
    public var userId:String = ""
    public var screenName:String = ""
    public var avatar:String = ""
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "userId": "id",
            "screenName":"screen_name",
            "avatar": "avatar",
        ]
    }
}
@objcMembers public class WHPasteCommandAltPicsModel: NSObject, YYModel, NSCoding {
    
    public var bgPicUrl:String = ""
    public var btnPicUrl:String = ""
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "bgPicUrl": "bg_pic_url",
            "btnPicUrl":"btn_pic_url",
        ]
    }
}

/// 邀请助力
public class WHInviteBoostRequest {
    
    public static func requestBoostWith(code: String,
                                        completion: @escaping (WHBoostResultModel?, String?) -> ()) {
        WHSharedRequest.POST("/fission_invite/assist.json", params: ["code": code]) { response in
            if response.isSuccess(),
               let result = response.data() as? [String: Any],
               let model = WHBoostResultModel.yy_model(with: result) {
                completion(model, nil)
            } else {
                completion(nil, response.message())
            }
        }
    }
    
}

@objcMembers
public class WHBoostResultModel: NSObject, YYModel, NSCoding {
    
    /// 是否助力成功
    public var result: Bool = false
    /// 奖励美豆数量
    public var reawardAmount: Int = 0
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "result": "result",
            "reawardAmount": "reaward_amount",
        ]
    }
    
}
