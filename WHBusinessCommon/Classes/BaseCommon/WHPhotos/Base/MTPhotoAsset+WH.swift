//
//  MTPhotoAsset+WH.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2025/6/25.
//

import Foundation
import MTPhotoLibrary

@objc public enum WHPhotosAssetSelectType: Int {
    case photo     //照片
    case video     //视频
    case livePhoto //实况图
    
    public func reportString() -> String {
        switch self {
        case .photo:
            return "image"
        case .video:
            return "video"
        case .livePhoto:
            return "live"
        default:
            return ""
        }
    }
}

public extension MTPhotoAsset {
    
    private static var resourceSelectTypeAssociatedKey: UInt8 = 0

    public var resourceSelectType: WHPhotosAssetSelectType? {
        get {
            if let value = objc_getAssociatedObject(self, &Self.resourceSelectTypeAssociatedKey) as? NSNumber {
                return WHPhotosAssetSelectType(rawValue: value.intValue)
            }
            return nil
        }
        set {
            if let newValue = newValue {
                objc_setAssociatedObject(self, &Self.resourceSelectTypeAssociatedKey, NSNumber(value: newValue.rawValue), .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            } else {
                objc_setAssociatedObject(self, &Self.resourceSelectTypeAssociatedKey, nil, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            }
        }
    }
}
