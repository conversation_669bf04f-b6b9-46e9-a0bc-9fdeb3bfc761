//
//  WHPhotoAlbumContentView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/28.
//

import Foundation
import WHBaseLibrary
import MTPhotoLibrary

public protocol WHPhotoAlbumContentViewDelegate: NSObjectProtocol {
    func selectPhotoWithMtasset(mtAsset:MTPhotoAsset)
    func selectCollectionViewCell(currentCell:UIView?)
}

public class WHPhotoAlbumContentView: UIView {
    var contentFetchType: WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos
    ///delegate
    weak var delegate: WHPhotoAlbumContentViewDelegate?
    /// 当前选择的相簿
    var selectedPhotoAlbum: MTPhotoAlbum?
    /// 图片管理类
    private var imgManager: MTImageManager = MTImageManager()
    ///滑竿view
    public let navButton = WHPhotoAlbumNavButton()
    
    private var isNavEnable: Bool {
        Int(selectedPhotoAlbum?.numberOfAssets ?? 0) >= 70
    }
    //滑块顶部的填充（默认是在导航栏之下开始的）
    public var navButtonTopInset:CGFloat = 0.0
    
    private var isHalfView:Bool = false //是否是半屏样式（例如：上介绍下相册）
    
    private let kItemSpace = 4.0
    
    private var isCellClickable = true //防cell多次被点
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .black
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    // MARK: - Public methods
    func show() {
        self.isHidden = false
        if self.collectionView.superview == nil {
            configUI()
        }
        self.collectionView.reloadData()
    }
    func hidden() {
        self.isHidden = true
    }
    
    // MARK: - Private methods
    private func configUI() {
        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4.0)
            make.left.right.bottom.equalToSuperview()
        }
        addSubview(navButton)
        navButton.alpha = 0
        navButton.frame = CGRectMake(WH_SCREEN_WIDTH - 44, 0, 40.0, 44.0)
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        navButton.addGestureRecognizer(panGesture)
    }
    
    private func scrollToBottom() {
        if let numberOfAssets = self.selectedPhotoAlbum?.numberOfAssets {
            self.collectionView.scrollToItem(at: IndexPath(item: Int(numberOfAssets) - 1, section: 0), at: .bottom, animated: false)
        }
    }
    
//    func notifDelegateSelectPhotoWithMtAsset(mtAsset: MTPhotoAsset?) {
//        if let mtAsset = mtAsset, WHICloudMan.status(for: mtAsset.asset) == .isCloud, mtAsset.assetFileURL() == nil {
//            //icloud图片，提示下载
//            WHICloudMan.showAlertInTop(phAsset: mtAsset.asset) {
//                NotificationCenter.default.post(name: .WHPhotoAlbumICloudStatusChange, object: nil)
//                MTCloudImageDownloadManager.default().addImageDownloadTask(for: mtAsset) { progress in
//                    
//                } resultHandler: { image, detailInfo in
//                    NotificationCenter.default.post(name: .WHPhotoAlbumICloudStatusChange, object: nil)
//                }
//            }
//            return
//        }
//        self.delegate?.selectPhotoWithMtasset(mtAsset: mtAsset, vc: self.yy_viewController ?? UIViewController.wh_top())
//    }
    
    open lazy var collectionView: UICollectionView = {
        let width = (WH_SCREEN_WIDTH - kItemSpace * 4) / 3
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 5
        layout.minimumInteritemSpacing = kItemSpace
        layout.itemSize = CGSize(width: width, height: width)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.showsVerticalScrollIndicator = false
        collectionView.register(WHPhotoCell.self, forCellWithReuseIdentifier: WHPhotoCell.cellID)
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 3.5, bottom: isHalfView ? 30 : 0, right: 3.5)
        return collectionView
    }()
    
    // 处理拖动手势
    @objc func handlePan(_ gestureRecognizer: UIPanGestureRecognizer) {
        
        if !isNavEnable { return }
        
        let translation = gestureRecognizer.translation(in: self)
        
        let newY = navButton.frame.origin.y + translation.y
        if newY >= navButtonTopInset && newY <= self.frame.size.height - navButton.bounds.height - navButtonBottomSpace {
            navButton.frame.origin.y = newY
        }
        
        gestureRecognizer.setTranslation(.zero, in: self)
        onNavDragging()
    }
}

extension WHPhotoAlbumContentView: UICollectionViewDataSource, UICollectionViewDelegate, UIScrollViewDelegate, WHPhotoCellDelegate {
    
    // MARK: - UICollectionViewDataSource
    
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return Int(selectedPhotoAlbum?.numberOfAssets ?? 0)
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if let cell = collectionView.dequeueReusableCell(withReuseIdentifier: WHPhotoCell.cellID, for: indexPath) as? WHPhotoCell {
            cell.imageManager = imgManager
            cell.contentFetchType = contentFetchType
            //反向取图
            let index = Int(selectedPhotoAlbum?.numberOfAssets ?? 0) - indexPath.item - 1
            cell.photoAsset = selectedPhotoAlbum?.asset(at: UInt(index < 0 ? 0 : index))
            cell.indexPath = indexPath
            cell.delegate = self
            return cell
        }
        return UICollectionViewCell()
    }
    
    // MARK: - UICollectionViewDelegate
    
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard isCellClickable else { return }
        isCellClickable = false
        
        var index = Int(selectedPhotoAlbum?.numberOfAssets ?? 0) - (indexPath.item ?? 0) - 1
        if index < 0 {
            index = 0
        }
        let cell = collectionView.cellForItem(at: indexPath)
        self.delegate?.selectCollectionViewCell(currentCell: cell)
        if let asset = selectedPhotoAlbum?.asset(at: UInt(index)) {
            self.delegate?.selectPhotoWithMtasset(mtAsset: asset)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.isCellClickable = true
        }
    }
    // MARK: - WHPhotoCellDelegate
    func browserButtonClick(indexPath: IndexPath?) {
        let largeVC = WHPhotosImageBrowserController()
        largeVC.selectedPhotoAlbum = selectedPhotoAlbum
        largeVC.imageManager = imgManager
        largeVC.contentFetchType = contentFetchType
        largeVC.currentIndexPath = indexPath
        largeVC.delegate = self
        largeVC.isIntrodunce = self.isHalfView
        self.yy_viewController?.navigationController?.pushViewController(largeVC, animated: true)
    }
    
    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if !isNavEnable { return }
        
        navButton.show()
        updateNavPosition()
    }
    
    private func onNavDragging() {
        var ratio = (navButton.frame.origin.y - navButtonTopInset) / (self.frame.size.height - navButtonTopInset - navButton.frame.size.height - navButtonBottomSpace)
        if ratio < 0 {
            ratio = 0
        } else if ratio > 1 {
            ratio = 1
        }
        
        let totalOffsetY = collectionView.contentSize.height - collectionView.frame.size.height
        if totalOffsetY >= 0 {
            collectionView.contentOffset.y = totalOffsetY * ratio
        }
    }
    
    private func updateNavPosition() {
        var ratio: CGFloat = collectionView.contentOffset.y / (collectionView.contentSize.height - collectionView.frame.size.height)
        ratio = ratio.rounded(toPlaces: 3)
        if ratio < 0 {
            ratio = 0
        } else if ratio > 1 {
            ratio = 1
        }
        
        navButton.frame.origin.y = (self.frame.size.height - navButtonTopInset - navButton.frame.size.height - navButtonBottomSpace) * ratio + navButtonTopInset
    }
}

extension WHPhotoAlbumContentView: WHPhotosImageBrowserControllerDelegate {
    
    public func selectPhotoWithMtasset(mtAsset: MTPhotoAsset) {
        self.delegate?.selectPhotoWithMtasset(mtAsset: mtAsset)
    }
}
