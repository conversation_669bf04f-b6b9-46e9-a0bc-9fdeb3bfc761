//
//  WHICloudMan.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/1/2.
//

import Foundation
import UIKit
import Photos
import CoreServices

@objc
enum ICMPHAssetStatus: Int {
    case unknown = 0
    case isLocal = 1
    case isCloud = 2
}

@objc(WHICloudMan)
final class WHICloudMan: NSObject {

    let `default` = WHICloudMan()
    
    // MARK: - Public
    
    /// 下载iCloud内容（暂不实现）
    ///
    /// - Parameters:
    ///   - phAsset: PHAsset实例
    ///   - progress: 进度回调
    ///   - completion: 结束回调
    @objc
    private func download(phAsset: PHAsset?,
                          progress: PHAssetVideoProgressHandler,
                          completion: () -> Void) {
        fatalError()
    }
    
    /// 判断当前PHAsset的存储状态
    ///
    /// - Parameter phAsset: PHAsset实例
    /// - Returns: 相应状态（未知、本地、iCloud）
    @objc(statusForPHAsset:)
    public static func status(for phAsset: PHAsset?) -> ICMPHAssetStatus {
        guard let phAsset = phAsset else {
            return .unknown
        }
        
        if phAsset.mediaType == .video {
            return videoStatus(for: phAsset)
        } else if phAsset.mediaType == .image {
            return imageStatus(for: phAsset)
        }
        
        return .unknown
    }
    
    /// 判断当前视频PHAsset的存储状态
    ///
    /// - Parameter phAsset: PHAsset实例
    /// - Returns: 相应状态（未知、本地、iCloud）
    @objc(videoStatusForPHAsset:)
    public static func videoStatus(for phAsset: PHAsset?) -> ICMPHAssetStatus {
        guard let phAsset = phAsset, phAsset.mediaType == .video else {
            return .unknown
        }
        
        let options = PHVideoRequestOptions()
        options.isNetworkAccessAllowed = false
        options.deliveryMode = .automatic
        options.version = .original // 解决requestAVAsset无法正常返回的问题
        
        let semphaore = DispatchSemaphore(value: 0)
        var status = ICMPHAssetStatus.unknown
        
        PHImageManager.default().requestAVAsset(
            forVideo: phAsset,
            options: options) { (asset, audioMix, info) in
                defer {
                    semphaore.signal()
                }
                
                guard let info = info else {
                    return
                }
                
                var isCloud = false
                if info.keys.contains(PHImageResultIsInCloudKey) {
                    isCloud = info[PHImageResultIsInCloudKey] as! Bool
                }
                
                if asset != nil {
                    status = .isLocal
                } else {
                    if isCloud {
                        status = .isCloud
                    }
                }
        }
        semphaore.wait()
        return status
    }
    
    /// 判断当前图片PHAsset的存储状态
    ///
    /// - Parameter phAsset: PHAsset实例
    /// - Returns: 相应状态（未知、本地、iCloud）
    @objc(imageStatusForPHAsset:)
    public static func imageStatus(for phAsset: PHAsset?) -> ICMPHAssetStatus {
        guard let phAsset = phAsset, phAsset.mediaType == .image else {
            return .unknown
        }
        
        var status = ICMPHAssetStatus.unknown
        
        let requestOption = PHImageRequestOptions()
        requestOption.version = .original
        requestOption.isSynchronous = true
        PHImageManager.default().requestImageData(for: phAsset, options: requestOption) { (data, uti, orientation, info) in
            guard let info = info else {
                return
            }
            
            var isCloud = false
            if info.keys.contains(PHImageResultIsInCloudKey) {
                isCloud = info[PHImageResultIsInCloudKey] as! Bool
            }
            
            if data != nil {
                status = .isLocal
            } else {
                if isCloud {
                    status = .isCloud
                }
            }
        }
        
        return status
    }
    
    /// 通过PHAsset实例获取视频AVAsset
    ///
    /// - Parameter phAsset: PHAsset实例
    /// - Returns: AVAsset
    @objc(fetchVideoAssetForPHAsset:)
    public static func fetchVideoAsset(for phAsset: PHAsset?) -> AVAsset? {
        guard let phAsset = phAsset else {
            return nil
        }
        
        let options = PHVideoRequestOptions()
        options.isNetworkAccessAllowed = false
        options.deliveryMode = .automatic
        
        if phAsset.isGreaterThan1080P {
            options.version = .original
        }
        
        let semphaore = DispatchSemaphore(value: 0)
        var result: AVAsset?
        
        PHImageManager.default().requestAVAsset(
            forVideo: phAsset,
            options: options) { (asset, audioMix, info) in
                defer {
                    semphaore.signal()
                }
                result = asset
        }
        semphaore.wait()
        return result
    }
    
    /// 在最顶层控制器展示iCloud下载二次确认弹窗
    ///
    /// - Parameters:
    ///   - phAsset: PHAsset实例
    ///   - confirmBlock: 确认按钮回调
    @objc(showAlertInTopWithPHAsset:confirmBlock:)
    public static func showAlertInTop(phAsset: PHAsset?,
                                      confirmBlock: (() -> ())?) {
        guard let asset = phAsset else { return }
        var msg = WHLocalizedString("由于你将该照片存储在iCloud上，需要下载到本地才可进行编辑哦，是否下载该照片？",
                                    comment: "")
        if asset.mediaType == .video {
            msg = WHLocalizedString("由于你将该视频存储在iCloud上，需要下载到本地才可进行编辑哦，是否下载该视频？",
                                    comment: "")
        }
        let alertVC = UIAlertController(title: nil, message: msg, preferredStyle: .alert)
        let cancel = UIAlertAction(title: NSLocalizedString("取消", comment: ""),
                                   style: .cancel,
                                   handler: nil)
        let confirm = UIAlertAction(title: NSLocalizedString("下载", comment: ""),
                                    style: .default) { (action) in
                                        confirmBlock?()
        }
        alertVC.addAction(cancel)
        alertVC.addAction(confirm)
        WHRouter.topViewController?.present(alertVC, animated: true)
    }
}

public extension PHAsset {
    var isGIF: Bool {
        if let resource = PHAssetResource.assetResources(for: self).first {
            let uti = resource.uniformTypeIdentifier as CFString
            return UTTypeConformsTo(uti, kUTTypeGIF)
        }
        
        return false
    }
    
    var isPhotoLive: Bool {
        return mediaSubtypes.contains(.photoLive)
    }
    
    var isGreaterThan1080P: Bool {
        guard mediaType == .video else {
            return false
        }
        
        // <= 1080P
        let lessThanAndEqual1080P = pixelWidth <= 1080 && pixelHeight <= 1920 || pixelWidth <= 1920 && pixelHeight <= 1080
        return !lessThanAndEqual1080P
        
        //        let isGreaterThan = ((pixelWidth > 1080 && pixelHeight > 1920) ||
        //            (pixelWidth > 1920 && pixelHeight > 1080))
        //        return isGreaterThan
    }
    
    var isSlowMotionVideo: Bool {
        return mediaSubtypes.contains(.videoHighFrameRate)
    }
}
