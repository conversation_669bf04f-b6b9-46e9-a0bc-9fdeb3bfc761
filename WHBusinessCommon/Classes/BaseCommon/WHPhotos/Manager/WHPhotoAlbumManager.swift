//
//  WHPhotoAlbumManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary
import WHBaseLibrary

public let WHPhotoAlbumSharedManager = WHPhotoAlbumManager.shared

public class WHPhotoAlbumManager {
    
    public static let shared = WHPhotoAlbumManager()
    ///拉取数据并展示相册  isLookAllType：相册类型，是否要显示全部类型数据。默认只展示图片类型
    public func loadPhotosAndShowVCWithDelegate(delegate:WHPhotosViewControllerDelegate?,
                                                isLookAllType:Bool = false,
                                                defaultSelect:WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos,
                                                uploadSpecificationUrl:String = "") {
        self.photoLibraryAuth {
            MTPhotoLibrary.shared().fetchPhotoAlbums(with:(isLookAllType ? .all : .photos)) { photoAlbums in
                let photosVC = WHPhotosViewController(ablumStyle: (isLookAllType ? .allStyle : .onlyPhotos),ablumFetchTyps: defaultSelect)
                photosVC.delegate = delegate
                photosVC.photoAlbums = photoAlbums
                photosVC.uploadSpecificationUrl = uploadSpecificationUrl
                let nav = WHBaseNavigationController(rootViewController: photosVC)
                nav.modalPresentationStyle = .fullScreen
                WHRouter.topViewController?.present(nav, animated: true)
            }
        }
    }
    
    /// 相册权限
    public func photoLibraryAuth(complete: @escaping () -> Void) {
        switch MTPhotoLibrary.authorizationStatus() {
        case .authorized, .limited:
            complete()
        case .denied:
            self.showAlbumAuthorizationAlert()
        default:
            MTPhotoLibrary.requestAuthorization { status in
                if #available(iOS 14, *) {
                    if status == .authorized || status == .limited {
                        complete()
                    }
                } else {
                    if status == .authorized {
                        complete()
                    }
                }
            }
        }
    }
    
    public func photoOpenStatus() -> Bool {
        var photoAuthorityOpenStatus = false
        if #available(iOS 14.0, *) {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized || PHPhotoLibrary.authorizationStatus() == .limited
        } else {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus() == .authorized
        }
        return photoAuthorityOpenStatus
    }
    
    public func isPhotoOpenLimitStatus() -> Bool {
        var photoAuthorityOpenStatus = false
        if #available(iOS 14.0, *) {
            photoAuthorityOpenStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite) == .limited
        }
        return photoAuthorityOpenStatus
    }
    
    public func showAlbumAuthorizationAlert() {
        let alertController = UIAlertController(title: WHLocalizedString("开启照片权限"), message: WHLocalizedString("你还没有开启照片权限，开启之后WHEE才能访问你的照片"), preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: WHLocalizedString("取消"), style: .cancel, handler: nil)
        let settingsAction = UIAlertAction(title: WHLocalizedString("去设置"), style: .default, handler: { (action) in
            self.openSettingsForPhotoLibrary()
        })
        alertController.addAction(cancelAction)
        alertController.addAction(settingsAction)
        WHRouter.topViewController?.present(alertController, animated: true, completion: nil)
    }
    
    public func openSettingsForPhotoLibrary() {
        if let appSettings = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(appSettings) {
                if #available(iOS 10.0, *) {
                    UIApplication.shared.open(appSettings, options: [:], completionHandler: nil)
                } else {
                    UIApplication.shared.openURL(appSettings)
                }
            }
        }
    }
    
}
