//
//  WHPhotoAlbumCell.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary

class WHPhotoAlbumCell: UITableViewCell {
    
    static let cellID = "WHPhotoAlbumCell"
    
//    var album: MTPhotoAlbum? {
//        didSet {
//            if let album = album {
//                imgView.image = album.posterImage()
//                titleLbl.text = album.title
//                countLbl.text = "\(album.numberOfAssets)"+"张"
//            }
//        }
//    }
    
    var album: WHPhotoAlbumListModel? {
        didSet {
            if let album = album {
                imgView.image = album.posterImage
                titleLbl.text = album.albumTitle
                countLbl.text = album.albumCountText
            }
        }
    }
    
    var selectedAlbum: Bool = false {
        didSet {
            let bgColor = selectedAlbum ? WHFigmaColor(wh_hexString: "#131313ff") : WHFigmaColor(wh_hexString: "#1c1c1cff")
            backgroundColor = bgColor
        }
    }
    
    private lazy var imgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFill
        imgView.layer.masksToBounds = true
        return imgView
    }()
    
    private lazy var containerView: UIView = {
        let containerView = UIView(frame: .zero)
        containerView.backgroundColor = .clear
        return containerView
    }()
    
    private lazy var titleLbl: UILabel = {
        let titleLbl = UILabel(frame: .zero)
        titleLbl.font = UIFont.pingFangSCFont(ofSize: 15, weight: .medium)
        titleLbl.textColor = UIColor.white
        titleLbl.numberOfLines = 2
        return titleLbl
    }()
    
    private lazy var countLbl: UILabel = {
        let countLbl = UILabel(frame: .zero)
        countLbl.font = UIFont.pingFangSCFont(ofSize: 13, weight: .regular)
        countLbl.textColor = WHFigmaColor(wh_hexString: "#757575")
        countLbl.numberOfLines = 1
        return countLbl
    }()
    
    // MARK: - Life cycle

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        self.selectionStyle = .none
        backgroundColor = UIColor.black
        contentView.addSubview(imgView)
        contentView.addSubview(containerView)
        containerView.addSubview(titleLbl)
        containerView.addSubview(countLbl)
        imgView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(12)
            make.width.height.equalTo(68)
        }
        containerView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(imgView.snp_trailing).offset(16)
            make.trailing.equalToSuperview().offset(-12)
        }
        titleLbl.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
        }
        countLbl.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(titleLbl.snp_bottom).offset(4)
        }
    }
}
