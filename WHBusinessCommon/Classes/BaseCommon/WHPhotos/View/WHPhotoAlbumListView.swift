//
//  WHPhotoAlbumListView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary

protocol WHPhotoAlbumListViewDelegate: NSObjectProtocol {
    func didSelectAlbum(_ albumListModel: WHPhotoAlbumListModel)
}

class WHPhotoAlbumListModel:NSObject {
    var posterImage:UIImage?  //相册缩略图
    var albumTitle:String = ""  //名称
    var albumCountText:String = ""  //拼装好的显示张数
    var albumID:String = ""   //相册唯一ID
}

open class WHPhotoAlbumListView: UIView {
    
    weak var delegate: WHPhotoAlbumListViewDelegate?
    /// 相册数组
    var albums: [MTPhotoAlbum] = [] {
        didSet {
            //这里认为传入的是当前最全的相册
            var models:[WHPhotoAlbumListModel] = []
            for item in albums {
                let model = WHPhotoAlbumListModel()
                model.posterImage = item.posterImage()
                model.albumTitle = item.title
                model.albumCountText = "\(item.numberOfAssets)"+"张"
                model.albumID = item.localIdentifier
                models.append(model)
            }
            albumList = models
            self.tableView.reloadData()
        }
    }
    
    ///相册list使用的相册数组
    var albumList:[WHPhotoAlbumListModel] = []
    
//    /// 当前选中的相册
//    var selectedAlbum: MTPhotoAlbum?
    /// 当前选中的相册ID
    var selectedAlbumID: String = ""
    
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = .clear
        tableView.dataSource = self
        tableView.delegate = self
        tableView.rowHeight = 92
        tableView.separatorStyle = .none
        tableView.register(WHPhotoAlbumCell.self, forCellReuseIdentifier: WHPhotoAlbumCell.cellID)
        return tableView
    }()
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        backgroundColor = UIColor.black
        addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

extension WHPhotoAlbumListView: UITableViewDataSource, UITableViewDelegate {
    
    // MARK: - UITableViewDataSource
    
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return albumList.count
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if let cell = tableView.dequeueReusableCell(withIdentifier: WHPhotoAlbumCell.cellID, for: indexPath) as? WHPhotoAlbumCell {
            let album = albumList[indexPath.row]
            cell.album = album
            cell.selectedAlbum = selectedAlbumID == album.albumID
            return cell
        }
        return UITableViewCell()
    }
    
    // MARK: - UITableViewDelegate
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let album = albumList[indexPath.item]
//        if album.localIdentifier == selectedAlbum?.localIdentifier { //如果是当前选中的相册则不再处理
//            return
//        }
        selectedAlbumID = album.albumID
        tableView.reloadData()
        delegate?.didSelectAlbum(album)
    }
}
