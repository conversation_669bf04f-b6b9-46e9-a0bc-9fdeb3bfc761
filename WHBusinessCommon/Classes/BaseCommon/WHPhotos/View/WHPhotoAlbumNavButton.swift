//
//  WHPhotoAlbumNavButton.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/28.
//

import Foundation

open class WHPhotoAlbumNavButton: UIView {
    
    private var hideWorkItem: DispatchWorkItem?
    
    private var isShow = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func show() {
        
        delayHide()
        
        if !isShow {
            self.isShow = true
            hideWorkItem?.cancel()
            UIView.animate(withDuration: 0.3) {
                self.alpha = 1
            }
        }
    }
    
    private func delayHide() {
        hideWorkItem?.cancel()
        let workItem = DispatchWorkItem { [weak self] in
            UIView.animate(withDuration: 0.3) {
                self?.alpha = 0
            }
            
            self?.isShow = false
        }
        self.hideWorkItem = workItem
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5, execute: workItem)
    }
    
    private func setupUI() {
        let imageView = UIImageView()
        imageView.image = UIImage(cm_named: "wh_album_vc_nav_scroll_button_normal")
        addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

}
