//
//  WHPhotoCell.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary
import WHBaseLibrary

protocol WHPhotoCellDelegate: NSObjectProtocol {
    func browserButtonClick(indexPath:IndexPath?)
}

class WHPhotoCell: UICollectionViewCell {
    
    static let cellID = "WHPhotoCell"
    
    weak var delegate: WHPhotoCellDelegate?
    var indexPath:IndexPath?
    
    var photoAsset: MTPhotoAsset? {
        didSet {
            updateImageView()
        }
    }
    
    weak var imageManager: MTImageManager?
    
    private var requestID: MTImageRequestID = 0
    ///当前相册的类型。图片类型跟历史样式一致，都是图。
    var contentFetchType: WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos
    
    private lazy var icloudLabel: UILabel = {
        let titleLbl = UILabel(frame: .zero)
        titleLbl.textColor = WHFigmaColor(wh_hexString: "#ffffffff")
        titleLbl.font = UIFont.pingFangSCFont(ofSize: 12.0, weight: .medium)
        titleLbl.textAlignment = .center
        titleLbl.numberOfLines = 0
        return titleLbl
    }()
    
    private lazy var imgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFill
        imgView.clipsToBounds = true
        imgView.layer.cornerRadius = 16
        imgView.layer.cornerCurve = .continuous
        return imgView
    }()
    
    private lazy var logoImgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFill
        imgView.image = UIImage(cm_named: "wh_album_vc_cell_look_logo_normal")
        imgView.clipsToBounds = true
        return imgView
    }()
    
    private lazy var browserButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.addTarget(self, action: #selector(browserButtonClickAction), for: .touchUpInside)
        return btn
    }()
    
    private lazy var icloudProgressHud: WHCircularProgress  =  {
        let progress = WHCircularProgress()
        progress.startAngle = -90
        progress.progressThickness = 0.4
        progress.trackThickness = 0.4
        progress.clockwise = true
        progress.gradientRotateSpeed = 2
        progress.roundedCorners = true
        progress.glowMode = .forward
        progress.glowAmount = 0.9
        progress.trackColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0.2)
        progress.set(colors: UIColor.white)
        progress.progress = 0
        progress.isHidden = true
        return progress
    }()
    
    private lazy var videoLenghtLabel: UILabel = {
        let label = UILabel(frame: .zero)
        label.textColor = WHFigmaColor(wh_hexString: "#FFFFFF")
        label.font = UIFont.pingFangSCFont(ofSize: 11.0)
        label.textAlignment = .center
        label.backgroundColor = UIColor(wh_hexString: "#00000080")
        label.layer.cornerRadius = 10.0
        label.layer.masksToBounds = true
        return label
    }()
    
    private lazy var livePicLogoImgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFill
        imgView.image = UIImage(cm_named: "wh_album_vc_cell_live_pic_logo_image")
        imgView.clipsToBounds = true
        return imgView
    }()
    
    // MARK: - Life cycles
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        backgroundColor = .clear
        
        contentView.addSubview(icloudLabel)
        icloudLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.addSubview(imgView)
        imgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        contentView.addSubview(logoImgView)
        logoImgView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(4)
            make.bottom.equalToSuperview().offset(-4)
            make.size.equalTo(CGSizeMake(24.0, 24.0))
        }
        contentView.addSubview(videoLenghtLabel)
        videoLenghtLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-6.0)
            make.bottom.equalToSuperview().offset(-6.0)
            make.height.equalTo(20.0)
            make.width.equalTo(44.0)
        }
        contentView.addSubview(livePicLogoImgView)
        livePicLogoImgView.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview().offset(-6.0)
            make.size.equalTo(CGSizeMake(20.0, 20.0))
        }
        contentView.addSubview(icloudProgressHud)
        icloudProgressHud.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(30, 30))
        }
        contentView.addSubview(browserButton)
        browserButton.snp.makeConstraints { make in
            make.left.bottom.equalToSuperview()
            make.size.equalTo(CGSizeMake(49.0, 49.0))
        }
    }
    
    @objc func browserButtonClickAction(){
        self.delegate?.browserButtonClick(indexPath: self.indexPath)
    }
        
    private func updateImageView() {
        if let photoAsset = photoAsset, let imageManager = imageManager {
            let screenScale = UIScreen.main.scale
            let thumbsize = CGSize(width: 80 * screenScale, height: 80 * screenScale)
            
            var sx = thumbsize.width / photoAsset.dimensions.width
            var sy = thumbsize.height / photoAsset.dimensions.height
            
            let isLargeImage: Bool = photoAsset.dimensions.width > 5000 || photoAsset.dimensions.height > 5000
            let isLongImage: Bool = photoAsset.dimensions.width / photoAsset.dimensions.height > 5.0 || photoAsset.dimensions.height / photoAsset.dimensions.width > 5.0
            
            if isLargeImage, isLongImage {
                sx = 80.0 / photoAsset.dimensions.width
                sy = 80.0 / photoAsset.dimensions.height
            }
            
            let s = max(sx, sy)
            let thumbnailSize = CGSizeApplyAffineTransform(photoAsset.dimensions, CGAffineTransformMakeScale(s, s))
            
            requestID = imageManager.requestThumbnailImage(for: photoAsset,
                                                           targetSize: thumbnailSize,
                                                           contentMode: .aspectFill,
                                                           resultHandler: {
                image, _ in
                
                if let image = image {
                    self.requestID = PHInvalidImageRequestID
                    self.imgView.image = photoAsset.thumbnail()
                }
            })
            
            let downloadManager = MTCloudImageDownloadManager.default()
            if let task = MTCloudImageDownloadManager.default().task(for: photoAsset) {
                self.icloudProgressHud.isHidden = false
                self.icloudProgressHud.progress = 0
                task.progressHandler = { [weak self] (progress) in
                    self?.icloudProgressHud.progress = Double(progress)
                }
//                if photoAsset.localIdentifier == downloadManager?.currentTask.asset.localIdentifier {
//                    self.icloudLabel.text = WHLocalizedString("下载中...")
//                } else {
//                    
//                    self.icloudLabel.text = WHLocalizedString("等待中")
//                }
            } else {
                self.icloudProgressHud.progress = 0
                self.icloudProgressHud.isHidden = true
            }
            //视频时间标签
            if ((contentFetchType == .WHPhotosAlbumFetchTypeAll) || (contentFetchType == .WHPhotosAlbumFetchTypeVideos)),photoAsset.mediaType == .video { //显示时间标签
                self.videoLenghtLabel.isHidden = false
                let timeString = formatTime(photoAsset.duration)
                self.videoLenghtLabel.text = timeString
                let width = 16.0 + timeString.getStringWidth(20, font: self.videoLenghtLabel.font)
                self.videoLenghtLabel.snp.updateConstraints { make in
                    make.width.equalTo(width)
                }
            } else {
                self.videoLenghtLabel.isHidden = true
            }
            //实况图片标签
            if ((contentFetchType == .WHPhotosAlbumFetchTypeAll) || (contentFetchType == .WHPhotosAlbumFetchTypeLivePhotos)),photoAsset.mediaSubtype == .photoLive {
                self.livePicLogoImgView.isHidden = false
            } else {
                self.livePicLogoImgView.isHidden = true
            }
            
//            photoAsset.fetchDetailInfo(withSync: true) {[weak self] detailInfo in
//                DispatchQueue.main.async {
//                    if let isCloud = detailInfo[MTPhotoAssetResultInCloudKey] as? Bool,isCloud == true {
//                        let downloadManager = MTCloudImageDownloadManager.default()
//                        if let task = MTCloudImageDownloadManager.default().task(for: photoAsset) {
//                            if photoAsset.localIdentifier == downloadManager?.currentTask.asset.localIdentifier {
//                                self?.icloudLabel.text = WHLocalizedString("下载中...")
//                            } else {
//                                self?.icloudLabel.text = WHLocalizedString("等待中")
//                            }
//                        } else {
//                            self?.icloudLabel.text = ""
//                        }
//                        WHPrint("aaaaaaaaaaaaaaaaaaaaaaaaaaa")
//                    } else {
//                        self?.icloudLabel.text = ""
//                    }
//                }
//            }
            //iCloud Label
//            let assetStatus = WHICloudMan.status(for: photoAsset.asset)
//            if assetStatus == .isCloud {
//                let downloadManager = MTCloudImageDownloadManager.default()
//                if let task = MTCloudImageDownloadManager.default().task(for: photoAsset) {
//                    if photoAsset.localIdentifier == downloadManager?.currentTask.asset.localIdentifier {
//                        self.icloudLabel.text = WHLocalizedString("下载中...")
//                    } else {
//                        self.icloudLabel.text = WHLocalizedString("等待中")
//                    }
//                } else {
//                    self.icloudLabel.text = ""
//                }
//            } else {
//                self.icloudLabel.text = ""
//            }
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.minute, .second]
        formatter.zeroFormattingBehavior = [.pad] // 补零，比如 01:09
        return formatter.string(from: time.rounded()) ?? "00:00"
    }
}

