//
//  WHPhotoFetchTypeSelectView.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2025/6/14.
//

import Foundation
import WHBaseLibrary

public protocol WHPhotoFetchTypeSelectViewDelegate: NSObjectProtocol {
    ///相册内容筛选类型发生改变
    func selectFetchPhotoChange(from:WHPhotosAlbumFetchType,to:WHPhotosAlbumFetchType)
}

public class WHPhotoFetchTypeSelectView: UIView {
    
    weak var delegate: WHPhotoFetchTypeSelectViewDelegate?
    
    var selectButton:UIButton?
    var lastSelectButton:UIButton?
    
    var lineView:UIView?
    
    var currentSelectType:WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypeAll
    
    var tagButtons:[UIButton] = [] //存的四个button
    
    //MARK: - Public Cycle
    ///设置选中的类型，默认无动画效果
    public func selectType(type:WHPhotosAlbumFetchType,isAnimate:Bool = false) {
        self.currentSelectType = type
        var viewTag = 10000
        switch type {
        case .WHPhotosAlbumFetchTypeAll:
            viewTag = 10000
        case .WHPhotosAlbumFetchTypePhotos:
            viewTag = 10001
        case .WHPhotosAlbumFetchTypeVideos:
            viewTag = 10002
        case .WHPhotosAlbumFetchTypeLivePhotos:
            viewTag = 10003
        default:
            viewTag = 10000
        }
        
        for button in tagButtons {
            if button.tag == viewTag {
                button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0, weight: .medium)
                button.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
                self.selectButton = button
            } else {
                button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0)
                button.setTitleColor(UIColor(wh_hexString: "#7A7E85"), for: .normal)
            }
        }
        
        if let toButton = self.viewWithTag(viewTag) {
            if isAnimate {
                UIView.animate(withDuration: 0.35) {
                    self.lineView?.snp.remakeConstraints({ make in
                        make.size.equalTo(CGSizeMake(32.0, 3.0))
                        make.bottom.equalToSuperview()
                        make.centerX.equalTo(self.selectButton?.snp.centerX ?? 0)
                    })
                    self.layoutIfNeeded()
                }
            } else {
                self.lineView?.snp.remakeConstraints({ make in
                    make.size.equalTo(CGSizeMake(32.0, 3.0))
                    make.bottom.equalToSuperview()
                    make.centerX.equalTo(self.selectButton?.snp.centerX ?? 0)
                })
            }
        }
        self.lineView?.isHidden = false
    }
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupViews() {
        self.clipsToBounds = true
        let contents:[WHPhotosAlbumFetchType] = [.WHPhotosAlbumFetchTypeAll,.WHPhotosAlbumFetchTypePhotos,.WHPhotosAlbumFetchTypeVideos,.WHPhotosAlbumFetchTypeLivePhotos]
        var i = 0
        var buttons:[UIButton] = []
        for content in contents {
            let button = UIButton()
            button.setTitle(content.typeText(), for: .normal)
            button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0)
            button.setTitleColor(UIColor(wh_hexString: "#7A7E85"), for: .normal)
            button.tag = 10000+i
            if i == 0 {
//                button.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0, weight: .medium)
//                button.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
                self.selectButton = button
                self.currentSelectType = .WHPhotosAlbumFetchTypeAll
            }
            button.addTarget(self, action:#selector(selectButtonClick), for: .touchUpInside)
            let buttonWidth = (WH_SCREEN_WIDTH - 24.0) / 4.0
            addSubview(button)
            button.snp.makeConstraints { make in
                make.left.equalTo(12.0 + (buttonWidth*CGFloat(i)))
                make.top.bottom.equalToSuperview()
                make.width.equalTo(buttonWidth)
            }
            buttons.append(button)
            i=i+1
        }
        self.tagButtons = buttons
        //线
        let lineView = UIView()
        lineView.backgroundColor = WHFigmaColor(wh_hexString: "#3549FF")
        addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.size.equalTo(CGSizeMake(32.0, 3.0))
            make.bottom.equalToSuperview()
            make.centerX.equalTo(self.selectButton?.snp.centerX ?? 0)
        }
        lineView.layer.cornerRadius = 1.5
        lineView.layer.masksToBounds = true
        self.lineView = lineView
        self.lineView?.isHidden = true
    }
    
    @objc func selectButtonClick(button:UIButton) {
        //选择未发生改变时，不进行更新
        if button.tag == self.selectButton?.tag {
            return;
        }
        tapicEngineOccurred(.medium) //震动
        self.lastSelectButton = self.selectButton
        self.selectButton = button
        
        UIView.animate(withDuration: 0.35) {
            self.lineView?.snp.remakeConstraints({ make in
                make.size.equalTo(CGSizeMake(32.0, 3.0))
                make.bottom.equalToSuperview()
                make.centerX.equalTo(self.selectButton?.snp.centerX ?? 0)
            })
            self.layoutIfNeeded()
            self.lastSelectButton?.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0)
            self.lastSelectButton?.setTitleColor(UIColor(wh_hexString: "#7A7E85"), for: .normal)
            self.selectButton?.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17.0, weight: .medium)
            self.selectButton?.setTitleColor(UIColor(wh_hexString: "#FFFFFF"), for: .normal)
        }
        if button.tag == 10000 { //全部
            self.delegate?.selectFetchPhotoChange(from: self.currentSelectType, to: .WHPhotosAlbumFetchTypeAll)
            self.currentSelectType = .WHPhotosAlbumFetchTypeAll
        } else if button.tag == 10001 { //图片
            self.delegate?.selectFetchPhotoChange(from: self.currentSelectType, to: .WHPhotosAlbumFetchTypePhotos)
            self.currentSelectType = .WHPhotosAlbumFetchTypePhotos
        } else if button.tag == 10002 { //视频
            self.delegate?.selectFetchPhotoChange(from: self.currentSelectType, to: .WHPhotosAlbumFetchTypeVideos)
            self.currentSelectType = .WHPhotosAlbumFetchTypeVideos
        } else if button.tag == 10003 { //live
            self.delegate?.selectFetchPhotoChange(from: self.currentSelectType, to: .WHPhotosAlbumFetchTypeLivePhotos)
            self.currentSelectType = .WHPhotosAlbumFetchTypeLivePhotos
        }
        
    }
}
