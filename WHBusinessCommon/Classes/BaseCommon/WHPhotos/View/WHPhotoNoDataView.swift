//
//  WHPhotoNoDataView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import WHBaseLibrary

protocol WHPhotoNoDataViewDelegate: NSObjectProtocol {
    func selectPhotos()
    func modifyAuth()
}

public enum WHPhotoNoDataViewType {
    case normal            //纯文本样式
    case authorization     //授权样式
}

class WHPhotoNoDataView: UIView {
    
    weak var delegate: WHPhotoNoDataViewDelegate?
    
    private lazy var titleLbl: UILabel = {
        let titleLbl = UILabel(frame: .zero)
        titleLbl.font = UIFont.pingFangSCFont(ofSize: 16, weight: .medium)
        titleLbl.text = "无照片"
        titleLbl.textColor = WHFigmaColor(wh_hexString: "#ffffffff")
        titleLbl.textAlignment = .center
        return titleLbl
    }()
    
    private lazy var descLbl: UILabel = {
        let descLbl = UILabel(frame: .zero)
        descLbl.font = UIFont.pingFangSCFont(ofSize: 14, weight: .regular)
        descLbl.text = "未获取照片访问权限，可通过系统相册选择照片或修改授权"
        descLbl.textColor = WHFigmaColor(wh_hexString: "#8e8e8eff")
        descLbl.textAlignment = .center
        descLbl.numberOfLines = 0
        return descLbl
    }()
    
    private lazy var selectPhotoBtn: UIButton = {
        let selectPhotoBtn = UIButton(type: .custom)
        selectPhotoBtn.setTitle("去系统相册选择", for: .normal)
        selectPhotoBtn.setTitleColor(WHFigmaColor(wh_hexString: "#ffffffff"), for: .normal)
        selectPhotoBtn.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 15, weight: .medium)
        selectPhotoBtn.layer.cornerRadius = 20
        selectPhotoBtn.layer.masksToBounds = true
        selectPhotoBtn.addTarget(self, action: #selector(handleSelectPhotoClick), for: .touchUpInside)
        return selectPhotoBtn
    }()
    
    private lazy var modifyAuthBtn: UIButton = {
        let modifyAuthBtn = UIButton(type: .custom)
        modifyAuthBtn.setTitle("修改授权", for: .normal)
        modifyAuthBtn.setTitleColor(WHFigmaColor(wh_hexString: "#ffffffff"), for: .normal)
        modifyAuthBtn.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 15, weight: .medium)
        modifyAuthBtn.layer.cornerRadius = 20
        modifyAuthBtn.layer.masksToBounds = true
        modifyAuthBtn.layer.borderColor = WHFigmaColor(wh_hexString: "#ffffff80").cgColor
        modifyAuthBtn.layer.borderWidth = 0.5
        modifyAuthBtn.addTarget(self, action: #selector(handleModifyAuthClick), for: .touchUpInside)
        return modifyAuthBtn
    }()
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Event response
    
    @objc private func handleSelectPhotoClick() {
        delegate?.selectPhotos()
    }
    
    @objc private func handleModifyAuthClick() {
        delegate?.modifyAuth()
    }
    // MARK: - Public methods
    
    public func showWithType(type:WHPhotoNoDataViewType = .normal) {
        if type == .authorization {
            titleLbl.text = "暂无内容"
            titleLbl.font = UIFont.pingFangSCFont(ofSize: 16, weight: .medium)
            descLbl.text = "未获取照片访问权限，可通过系统相册选择照片或修改授权"
            selectPhotoBtn.isHidden = WHPhotoAlbumSharedManager.isPhotoOpenLimitStatus() == true ? false : true
            modifyAuthBtn.isHidden = false
        } else {
            titleLbl.text = "暂无内容"
            titleLbl.font = UIFont.pingFangSCFont(ofSize: 19, weight: .medium)
            descLbl.text = "目前您的相册暂无内容，请在有内容后重试"
            selectPhotoBtn.isHidden = true
            modifyAuthBtn.isHidden = true
        }
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        addSubview(titleLbl)
        addSubview(descLbl)
        addSubview(selectPhotoBtn)
        addSubview(modifyAuthBtn)
        titleLbl.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(20.0)
        }
        descLbl.snp.makeConstraints { make in
            make.top.equalTo(titleLbl.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(40.0)
        }
        selectPhotoBtn.snp.makeConstraints { make in
            make.top.equalTo(descLbl.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(40)
            make.width.equalTo(200)
        }
        modifyAuthBtn.snp.makeConstraints { make in
            make.top.equalTo(selectPhotoBtn.snp.bottom).offset(12)
            make.centerX.equalTo(selectPhotoBtn)
            make.size.equalTo(selectPhotoBtn)
        }

        DispatchQueue.main.async {
            self.selectPhotoBtn.wh_addGradientBorder(WHFigmaGradientColor.strokeButtonMain, frame: CGRectMake(0, 0, 200, 40), cornerRadius: 20.0, width: 3.0)
        }
    }
    
}
