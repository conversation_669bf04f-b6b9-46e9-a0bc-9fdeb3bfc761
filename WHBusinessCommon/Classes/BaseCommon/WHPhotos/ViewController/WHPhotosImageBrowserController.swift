//
//  WHPhotosImageBrowserController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/27.
//

import Foundation
import UIKit
import MTPhotoLibrary
import WHBaseLibrary
import Lottie

public protocol WHPhotosImageBrowserControllerDelegate: NSObjectProtocol {
    func selectPhotoWithMtasset(mtAsset:MTPhotoAsset)
}

open class WHPhotosImageBrowserController: WHViewController {
    var contentFetchType: WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos
    ///delegate
    weak var delegate: WHPhotosImageBrowserControllerDelegate?
    /// 当前选择的相簿
    var selectedPhotoAlbum: MTPhotoAlbum?
    var currentIndexPath:IndexPath?
    weak var imageManager: MTImageManager?
    // 是否功能介绍页
    public var isIntrodunce:Bool = false
    
    public var bgImageView: UIImageView?
    
    private let kItemSpace = 10.0
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: (view.bounds.size.width + kItemSpace),height: (WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT - WH_SCREEN_BOTTOM_SPACE - 80.0))
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.isPagingEnabled = true
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(WHPhotoBrowserLargeCell.self, forCellWithReuseIdentifier: WHPhotoBrowserLargeCell.cellID)
        return collectionView
    }()
    
    private lazy var currentSelectView: UILabel = {
        let currentSelectView = UILabel()
        currentSelectView.textColor = WHFigmaColor.contentNavbarTitle
        currentSelectView.font = UIFont.pingFangSCFont(ofSize: 17.0, weight: .medium)
        currentSelectView.textAlignment = .center
        return currentSelectView
    }()
    
    private lazy var loadingView: LottieAnimationView = {
        let lottie = LottieAnimationView(name: "image_upload", bundle: WHCMBundle.main)
        lottie.isUserInteractionEnabled = true
        lottie.loopMode = .loop
        lottie.backgroundBehavior = .pauseAndRestore
        return lottie
    }()
    
    private lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        view.isHidden = true
        return view
    }()
    
//    private lazy var sureButtonBgView: UIImageView = {
//        let bgImageView = UIImageView()
//        bgImageView.layer.cornerRadius = 20.0
//        bgImageView.layer.cornerCurve = .continuous
//        bgImageView.layer.masksToBounds = true
//        return bgImageView
//    }()
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        addObservers()
        DispatchQueue.main.async {
            self.scrollToCurrentIndex()
            self.updateCurrentSelectViewText()
//            if let assets = self.selectedPhotoAlbum?.assets(),assets.count > 0 {
//                self.imageManager?.startCachingImages(forAssets: self.selectedPhotoAlbum?.assets(), targetSize: CGSizeMake(WH_SCREEN_WIDTH, self.collectionView.height), contentMode: .aspectFit)
//            }
        }
        
    }
    
    open override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    open override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if VideoPlayerManager.shared.isPlaying { //页面消失时，主动停止一下播放
            VideoPlayerManager.shared.stop()
        }
    }
    
    open override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        //页面消失的时候，判断当前topvc是否是自己。如果不是。则移除自己
        if let topVC = UIViewController.wh_top() as? UIViewController,topVC != self {
            guard var muArr = self.navigationController?.viewControllers else { return }
            var i = 0
            for vc in muArr {
                if let vc = vc as? UIViewController,vc.isKind(of: WHPhotosImageBrowserController.self) {
                    muArr.remove(at: i)
                    break
                }
                i = i+1
            }
            self.navigationController?.viewControllers = muArr
        }
        loadingView.stop()
        bgView.removeFromSuperview()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func configUI() {
        self.view.backgroundColor = .black
        showBackButton(isShow: true)
        
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT)
            make.left.equalToSuperview().offset(-kItemSpace / 2.0)
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 72.0)
            make.right.equalToSuperview().offset(kItemSpace / 2.0)
        }
        
        view.addSubview(currentSelectView)
        currentSelectView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT - 36)
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSizeMake(180.0, 24))
        }
        
        //如果是XR以下机型，使用静图  //新UI这里不用webp了，先注释，防止再加回来
//        if WHDeviceSupport.compare(.iPhoneXR, compareBlock: { current, target in
//            current <= target
//        }) {
//            sureButtonBgView.image = UIImage(cm_named: "whee_common_sure_button_bg_image")
//        } else {
//            if let backgroundPath = WHCMBundle.main.path(forResource: "wh_gc_button_brandGradient", ofType: "webp") {
//                let url = URL(fileURLWithPath: backgroundPath)
//                sureButtonBgView.sd_setImage(with: url)
//            }
//        }
        
//        if let bgImageView = self.bgImageView {
//        view.addSubview(sureButtonBgView)
//        sureButtonBgView.snp.makeConstraints { make in
//            make.left.equalToSuperview().offset(12)
//            make.right.equalToSuperview().offset(-12)
//            make.height.equalTo(56)
//            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 8)
//        }
//        }
       
        let sureButton = UIButton(type: .custom)
        sureButton.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17,weight: .semibold)
        sureButton.setTitleColor(WHFigmaColor.contentButtonOnMain, for: .normal)
        sureButton.setTitle(WHLocalizedString("确定"), for: .normal)
        sureButton.addTarget(self, action: #selector(sureButtonClickAction), for: .touchUpInside)
        sureButton.backgroundColor = UIColor(wh_hexString: "#222326")
        ///圆角
        sureButton.layer.cornerRadius = 24.0
        sureButton.layer.masksToBounds = true
        sureButton.layer.cornerCurve = .continuous
        ///border
//        sureButton.setBackgroundImage(UIImage(named: "wh_home_create_button_border_normal"), for: .normal)
//        sureButton.setBackgroundImage(UIImage(named: "wh_home_create_button_border_normal"), for: .highlighted)
        view.addSubview(sureButton)
        sureButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 8)
        }
        
        view.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(WH_NAVIGATION_BAR_HEIGHT)
            make.left.right.bottom.equalToSuperview()
        }
        
        bgView.addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-WH_NAVIGATION_BAR_HEIGHT)
            make.width.height.equalTo(64)
        }
    }
    
    private func addObservers() {
        NotificationCenter.default.addObserver(self, selector: #selector(icloudImageChange), name: .WHPhotoAlbumICloudStatusChange, object: nil)
    }
    
    @objc func icloudImageChange() {
        self.collectionView.reloadData()
    }
    
    @objc func sureButtonClickAction(btn:UIButton){
        if isIntrodunce {
            bgView.isHidden = false
            loadingView.play()
        }
        btn.isUserInteractionEnabled = false
        var index = Int(selectedPhotoAlbum?.numberOfAssets ?? 0) - (self.currentIndexPath?.item ?? 0) - 1
        if index < 0 {
            index = 0
        }
        if let asset = selectedPhotoAlbum?.asset(at: UInt(index)) {
            self.delegate?.selectPhotoWithMtasset(mtAsset: asset)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            btn.isUserInteractionEnabled = true
        }
    }
    private func scrollToCurrentIndex() {
        if let numberOfAssets = self.selectedPhotoAlbum?.numberOfAssets {
            if self.currentIndexPath == nil {
                self.currentIndexPath = IndexPath(item: 0, section: 0)
            }
            let item = self.currentIndexPath?.item ?? 0
            let contentOffsetX = (WH_SCREEN_WIDTH + kItemSpace) * Double(item)
            self.collectionView.setContentOffset(CGPointMake(contentOffsetX, 0), animated: false)
        }
    }
    
    func updateCurrentSelectViewText() {
        let count = Int(self.selectedPhotoAlbum?.numberOfAssets ?? 0)
        let index = Int(self.currentIndexPath?.item ?? 0) + 1
        self.currentSelectView.text = String(index)+"/"+String(count)
    }
    
    public func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let item = Int(scrollView.contentOffset.x / (WH_SCREEN_WIDTH + kItemSpace))
        let indexPath = IndexPath(item: item, section: 0)
        self.currentIndexPath = indexPath
        self.updateCurrentSelectViewText()
    }
    
}

extension WHPhotosImageBrowserController: UICollectionViewDataSource, UICollectionViewDelegate {
    
    // MARK: - UICollectionViewDataSource
    
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return Int(selectedPhotoAlbum?.numberOfAssets ?? 0)
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if let cell = collectionView.dequeueReusableCell(withReuseIdentifier: WHPhotoBrowserLargeCell.cellID, for: indexPath) as? WHPhotoBrowserLargeCell {
            cell.contentFetchType = contentFetchType
            //反向取图
            let index = Int(selectedPhotoAlbum?.numberOfAssets ?? 0) - indexPath.item - 1
            if selectedPhotoAlbum?.numberOfAssets ?? 0 > index {
                cell.photoAsset = selectedPhotoAlbum?.asset(at: UInt(index))
            }
            cell.imageManager = imageManager
            return cell
        }
        return UICollectionViewCell()
    }
    
    // MARK: - UICollectionViewDelegate
    
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let asset = selectedPhotoAlbum?.asset(at: UInt(indexPath.item)),
            let img = asset.fullResolutionImage(withMaxLength: 0)
        {
//            checkPhoto(img)
//                uploadImg(img)
        }
    }
    //cell 滑出屏幕
    public func collectionView(_ collectionView: UICollectionView, didEndDisplaying cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if let largerCell = cell as? WHPhotoBrowserLargeCell {
            largerCell.didDisappearFromScreen()
        }
    }
    
}

class WHPhotoBrowserLargeCell: UICollectionViewCell, UIScrollViewDelegate {
    
    static let cellID = "WHPhotoBrowserLargeCell"
    
    weak var imageManager: MTImageManager?
    private var requestID: MTImageRequestID = 0
    
    var contentFetchType: WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos
    
    var photoAsset: MTPhotoAsset? {
        didSet {
            updateImageView()
        }
    }
    
    private lazy var imgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFit
        imgView.clipsToBounds = true
        return imgView
    }()
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.zoomScale = 1.0
        scrollView.maximumZoomScale = 4.0
        scrollView.minimumZoomScale = 1.0
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var icloudLabel: UILabel = {
        let titleLbl = UILabel(frame: .zero)
        titleLbl.textColor = WHFigmaColor(wh_hexString: "#ffffffff")
        titleLbl.font = UIFont.pingFangSCFont(ofSize: 17.0, weight: .medium)
        titleLbl.textAlignment = .center
        titleLbl.numberOfLines = 0
        return titleLbl
    }()
    
    private lazy var icloudProgressHud: WHCircularProgress  =  {
        let progress = WHCircularProgress()
        progress.startAngle = -90
        progress.progressThickness = 0.4
        progress.trackThickness = 0.4
        progress.clockwise = true
        progress.gradientRotateSpeed = 2
        progress.roundedCorners = true
        progress.glowMode = .forward
        progress.glowAmount = 0.9
        progress.trackColor = WHFigmaColor(r: 0, g: 0, b: 0, a: 0.2)
        progress.set(colors: UIColor.white)
        progress.progress = 0
        progress.isHidden = true
        return progress
    }()
    
    public lazy var playIcon: UIButton = {
        let b = UIButton()
        let blurEffect = UIBlurEffect(style: .dark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame.size = CGSize(width: 80, height:80)
        blurView.isUserInteractionEnabled = false
        b.addSubview(blurView)
        b.setImage(UIImage(named: "icon_detail_play_small"),
                   for: UIControl.State.normal)
        b.bringSubviewToFront(b.imageView ?? UIView())
        b.layer.cornerRadius = 40
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(tapAction), for: .touchUpInside)
        return b
    }()
    
    private lazy var videoTapView: UIView = {
        let view = UIView()
        let tap = UITapGestureRecognizer(target: self, action: #selector(videoTapViewClick))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var sliderView: WHSliderControl = {
        let view = WHSliderControl()
        view.slider.tipStyle = .none
        view.slider.colorStyle = .disable
        view.slider.minimumValue = 0
        view.slider.maximumValue = 100
        view.slider.value = 0
        view.setSliderUIStyle(style: .largerVideo)
        view.valueChangedHandle = { [weak self] (value, event) in
            guard let self = self else {return}
            if event == .valueChanged {
                self.sliderViewChanged(value: value)
            } else if event == .touchUp {
                if let videoId = photoAsset?.localIdentifier, videoId == VideoPlayerManager.shared.currentId {
                    if VideoPlayerManager.shared.isPause {
                        VideoPlayerManager.shared.play()
                    }
                }
            }
        }
        return view
    }()
    
    private lazy var livePhotoView : PHLivePhotoView = {
        let view = PHLivePhotoView()
        view.contentMode = .scaleAspectFit
        return view
    }()
    
    private lazy var livePicLogoImgView: UIImageView = {
        let imgView = UIImageView(frame: .zero)
        imgView.contentMode = .scaleAspectFill
        imgView.image = UIImage(cm_named: "wh_album_vc_cell_live_pic_logo_image")
        imgView.clipsToBounds = true
        return imgView
    }()
    
    // MARK: - Life cycles
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        backgroundColor = .clear
        
        contentView.addSubview(icloudLabel)
        icloudLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.equalToSuperview().offset(70.0)
            make.right.equalToSuperview().offset(-70.0)
            make.height.equalTo(100.0)
        }
        
        contentView.addSubview(scrollView)
        scrollView.delegate = self
        scrollView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(5.0)
            make.right.equalToSuperview().offset(-5.0)
            make.bottom.equalToSuperview().offset(-14.0)
        }
        
        scrollView.addSubview(imgView)
        imgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(contentView)
            make.width.equalTo(WH_SCREEN_WIDTH)
        }
        
        contentView.addSubview(videoTapView)
        videoTapView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(5.0)
            make.right.equalToSuperview().offset(-5.0)
            make.bottom.equalToSuperview().offset(-14.0)
        }
        
        contentView.addSubview(playIcon)
        playIcon.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(80.0, 80.0))
        }
        
        contentView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.left.equalToSuperview().offset(10)
            make.right.equalToSuperview().offset(-10)
            make.height.equalTo(28.0)
        }
        
        contentView.addSubview(livePhotoView)
        livePhotoView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        livePhotoView.addSubview(livePicLogoImgView)
        livePicLogoImgView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20.0)
            make.bottom.equalToSuperview().offset(-10.0)
            make.size.equalTo(CGSizeMake(25.0, 25.0))
        }
        
        contentView.addSubview(icloudProgressHud)
        icloudProgressHud.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(CGSizeMake(50, 50))
        }
    }
    //点击暂定视频
    @objc func videoTapViewClick() {
        let videoID = photoAsset?.localIdentifier ?? ""
        if videoID == VideoPlayerManager.shared.currentId,VideoPlayerManager.shared.isPlaying {
            VideoPlayerManager.shared.pause()
        }
    }
    //播放按钮点击
    @objc func tapAction() {
        let videoID = photoAsset?.localIdentifier ?? ""
        if let asset = photoAsset?.asset {
            getCurrentVideoUrlWith(asset: asset) { [weak self] url in
                guard let self = self else { return }
                if let url = url {
                    self.playVideoWith(id: videoID, url: url.absoluteString)
                } else {
                    self.playVideoWith(id: videoID, url: self.photoAsset?.assetFileURL()?.absoluteString ?? "")
                }
            }
        } else {
            playVideoWith(id: videoID,
                          url: photoAsset?.assetFileURL()?.absoluteString ?? "")
        }
    }
    
    private func playVideoWith(id: String, url: String) {
        if let phAsset = photoAsset?.asset {
            WHSandboxFileManager.getOriginalVideoFromPhotoLibrary(asset: phAsset) { [weak self] localUrl in
                guard let self = self else { return }
                VideoPlayerManager.shared.set(mute: false, manual: false)
                VideoPlayerManager.shared.load(videoId: id,
                                               url: localUrl?.path,
                                               coverImage: nil,
                                               on: scrollView,
                                               autoLoop: false,
                                               externalPlayer: nil,
                                               preservedCallbacks: [])?
                    .playing(capture: self, block: { (self, currentId) in
                        guard id == currentId else { return }
                            self.playIcon.isHidden = true
                    })
                    .paused(capture: self, block: { (self, currentId) in
                        guard id == currentId else { return }
                        self.playIcon.isHidden = false
                    })
                    .onPrrogress(capture: self, block: { (self, currentId) in
                        guard id == currentId else { return }
        //                self.sliderView.slider.value = Float(VideoPlayerManager.shared.player.progress * 100)
                        self.sliderView.slider.setValue(Float(VideoPlayerManager.shared.player.progress * 100), animated: true)
                    })
                    .onReachEnd(capture: self, block: { (self, currentId) in
                        guard id == currentId else { return }
                        DispatchQueue.main.async{
                            VideoPlayerManager.shared.pause()
                            self.playIcon.isHidden = false
                        }
                    })
                    .done()
                
                VideoPlayerManager.shared.player.view.contentMode = .scaleAspectFit
                VideoPlayerManager.shared.play()
            }
        }
    }
    
    private func getCurrentVideoUrlWith(asset: PHAsset, completion: @escaping (URL?) -> Void) {
        let options = PHVideoRequestOptions()
        options.version = .current
        options.isNetworkAccessAllowed = true

        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
            guard let urlAsset = avAsset as? AVURLAsset else {
                WHPrint("获取 AVURLAsset 失败")
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }
            DispatchQueue.main.async {
                completion(urlAsset.url)
            }
        }
    }
    
    //视频滑动杆更新
    func sliderViewChanged(value: Float) {
        if let videoId = photoAsset?.localIdentifier, videoId == VideoPlayerManager.shared.currentId {
            if VideoPlayerManager.shared.isPlaying {
                VideoPlayerManager.shared.pause()
            }
            VideoPlayerManager.shared.fastSeek(toPercent: (CGFloat(value) / 100.0))
        }
    }
    
    private func updateImageView() {
//        if let photoAsset = photoAsset,let imageManager = imageManager {
//            requestID = imageManager.requestImage(for: photoAsset, targetSize: CGSizeMake(WH_SCREEN_WIDTH, contentView.size.height), contentMode: .aspectFit, resultHandler: { image, _ in
//                if let image = image {
//                    self.requestID = PHInvalidImageRequestID
//                    self.imgView.image = image
//                    self.imgView.image = photoAsset.fullResolutionImage()
//                }
//            })
//        }
        
        let assetStatus = WHICloudMan.status(for: photoAsset?.asset)
        if assetStatus == .isCloud {
            let downloadManager = MTCloudImageDownloadManager.default()
            if let task = MTCloudImageDownloadManager.default().task(for: photoAsset) {
                self.icloudLabel.text = ""
                self.icloudProgressHud.isHidden = false
                self.icloudProgressHud.progress = 0
                task.progressHandler = { [weak self] (progress) in
                    self?.icloudProgressHud.progress = Double(progress)
                }
            } else {
                self.icloudProgressHud.isHidden = true
                self.icloudLabel.text = WHLocalizedString("iCloud云相册的照片，需要您先下载到系统相册，再重试哦~")
            }
        } else {
            self.icloudProgressHud.isHidden = true
            self.icloudLabel.text = ""
        }
        if let photoAsset = photoAsset {
            self.icloudLabel.text = ""
            self.imgView.image = nil
            photoAsset.fullResolutionImage(withMaxLength: kMTPhotoAssetUnlimitLength, requestSync: false, careDetail: false) { [weak self] image, info in
                guard let self = self else { return }
                self.imgView.image = image
            }
            
            if ((self.contentFetchType == .WHPhotosAlbumFetchTypeAll) || (self.contentFetchType == .WHPhotosAlbumFetchTypeVideos)), photoAsset.mediaType == .video { //处理视频类型
                self.playIcon.isHidden = false
                self.sliderView.isHidden = false
                self.livePhotoView.isHidden = true
                self.videoTapView.isHidden = false
            } else if ((self.contentFetchType == .WHPhotosAlbumFetchTypeAll) || (self.contentFetchType == .WHPhotosAlbumFetchTypeLivePhotos)), photoAsset.mediaSubtype == .photoLive { //处理live类型
                self.playIcon.isHidden = true
                self.sliderView.isHidden = true
                self.livePhotoView.isHidden = false
                self.videoTapView.isHidden = true
                loadLivePhoto()
            } else { //当前版本，默认其它类型都是纯图
                self.playIcon.isHidden = true
                self.sliderView.isHidden = true
                self.livePhotoView.isHidden = true
                self.videoTapView.isHidden = true
            }
    
        } else {
            self.imgView.image = nil
            self.playIcon.isHidden = true
            self.sliderView.isHidden = true
        }
        self.sliderView.slider.value = 0
//        self.sliderView.isHidden = true
    }
    //加载live图
    func loadLivePhoto() {
        guard let mediaAsset = photoAsset else { return }
        let options = PHLivePhotoRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        
        PHImageManager.default().requestLivePhoto(for: mediaAsset.asset,
                                                  targetSize: self.livePhotoView.bounds.size,
                                                  contentMode: .aspectFit,
                                                  options: options) { (livePhoto, _) in
            if let livePhoto = livePhoto {
                self.livePhotoView.livePhoto = livePhoto
//                self.livePhotoView.startPlayback(with: .full)
            }
        }
    }
    //cell移除屏幕
    func didDisappearFromScreen() {
        // 停止视频播放、live等
        self.livePhotoView.stopPlayback()
        //视频
        if let videoId = photoAsset?.localIdentifier, videoId == VideoPlayerManager.shared.currentId {
            VideoPlayerManager.shared.stop()
            self.playIcon.isHidden = false
        }
        self.sliderView.slider.value = 0
        
    }
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        if (contentFetchType == .WHPhotosAlbumFetchTypePhotos) || ((contentFetchType == .WHPhotosAlbumFetchTypeAll)&&(photoAsset?.mediaType == .image)&&(photoAsset?.mediaSubtype != .photoLive)) {
            return imgView;
        }
        return nil
    }
}
