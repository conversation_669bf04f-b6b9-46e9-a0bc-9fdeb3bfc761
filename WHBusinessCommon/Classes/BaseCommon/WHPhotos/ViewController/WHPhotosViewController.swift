//
//  WHPhotosViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/12/26.
//

import Foundation
import MTPhotoLibrary
import WHBaseLibrary

protocol WHAlbumSelectViewDelegate: NSObjectProtocol {
    func openOrCloseAlbumList()
}

public extension NSNotification.Name {
    static let WHPhotoAlbumICloudStatusChange = Notification.Name("WHPhotoAlbumICloudStatusChange")
}

open class WHAlbumSelectView: UIView {
    
    var title: String? {
        didSet {
            titleLbl.text = title
            arrowImgView.isHidden = title == nil || title?.count == 0
            var width = 0.0
            let safeTitle = title ?? ""
            if !safeTitle.isEmpty, safeTitle.range(of: "\u{FFFD}") == nil {
                width = safeTitle.getStringWidth(24.0, font: UIFont.pingFangSCFont(ofSize: 17, weight: .medium)) ?? 0.0
            }
            var arrowImgRight = ((152.0 / 2.0) - (width / 2.0) - 12.0)
            arrowImgRight = arrowImgRight < 4.0 ? 4.0 : arrowImgRight
            arrowImgView.snp.updateConstraints { make in
                make.right.equalToSuperview().offset(-arrowImgRight)
            }
        }
    }
    
    weak var delegate: WHAlbumSelectViewDelegate?
    
    private var isUnfold = false
    
    private lazy var titleLbl: UILabel = {
        let titleLbl = UILabel(frame: .zero)
        titleLbl.textColor = WHFigmaColor(wh_hexString: "#ffffffff")
        titleLbl.font = UIFont.pingFangSCFont(ofSize: 17, weight: .medium)
        titleLbl.textAlignment = .center
        titleLbl.isUserInteractionEnabled = true
        return titleLbl
    }()
    
    private lazy var arrowImgView: UIImageView = {
        let arrowImgView = UIImageView(frame: .zero)
        arrowImgView.image = UIImage(cm_named: "wh_album_select_album_arrow_button_normal")
        return arrowImgView
    }()
    
    private lazy var selectBtn: UIButton = {
        let selectBtn = UIButton(type: .custom)
        selectBtn.addTarget(self, action: #selector(handleTapEvent), for: .touchUpInside)
        return selectBtn
    }()
    
    // MARK: - Life cycle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Event response
    
    @objc private func handleTapEvent() {
        openOrCloseList()
    }
    
    // MARK: - Private methods
    
    private func setupViews() {
        addSubview(titleLbl)
        addSubview(arrowImgView)
        addSubview(selectBtn)
        titleLbl.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalToSuperview().offset(-16.0)
        }
        arrowImgView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-4.0)
            make.width.height.equalTo(12.0)
        }
        arrowImgView.transform = CGAffineTransformMakeRotation(.pi * 2)
        selectBtn.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Internal methods
    
    func openOrCloseList() {
        UIView.animate(withDuration: 0.3) {
            if self.isUnfold {
                self.arrowImgView.transform = CGAffineTransformMakeRotation(-.pi * 2)
            } else {
                self.arrowImgView.transform = CGAffineTransformMakeRotation(.pi * 3)
            }
        }
        isUnfold = !isUnfold
        delegate?.openOrCloseAlbumList()
    }
}

public protocol WHPhotosViewControllerDelegate: NSObjectProtocol {
    func selectPhotoWithMtasset(mtAssets: [MTPhotoAsset], vc:UIViewController)
}

let navButtonBottomSpace = WH_SCREEN_BOTTOM_SPACE + 50.0

public enum WHPhotosAlbumStyle {
    case onlyPhotos     //仅照片
    case allStyle       //所有类型
}

public enum WHPhotosAlbumFetchType {
    case WHPhotosAlbumFetchTypePhotos     //照片
    case WHPhotosAlbumFetchTypeVideos     //视频
    case WHPhotosAlbumFetchTypeLivePhotos //实况图
    case WHPhotosAlbumFetchTypeAll        //all
    
    func typeText() -> String{
        switch self {
        case .WHPhotosAlbumFetchTypePhotos:
            return WHLocalizedString("图片")
        case .WHPhotosAlbumFetchTypeVideos:
            return WHLocalizedString("视频")
        case .WHPhotosAlbumFetchTypeLivePhotos:
            return WHLocalizedString("LIVE图")
        case .WHPhotosAlbumFetchTypeAll:
            return WHLocalizedString("全部")
        }
    }
    
    func analyticsText() -> String {
        switch self {
        case .WHPhotosAlbumFetchTypePhotos:
            return "album_pic"
        case .WHPhotosAlbumFetchTypeVideos:
            return "album_video"
        case .WHPhotosAlbumFetchTypeLivePhotos:
            return "album_live"
        case .WHPhotosAlbumFetchTypeAll:
            return "album_all"
        }
    }
    
    func mtPhotoLibraryType() -> MTPhotoAlbumsFetchOption {
        switch self {
        case .WHPhotosAlbumFetchTypePhotos:
            return .photos
        case .WHPhotosAlbumFetchTypeVideos:
            return .videos
        case .WHPhotosAlbumFetchTypeLivePhotos:
            return .livePhotos
        case .WHPhotosAlbumFetchTypeAll:
            return .all
        }
    }
}

open class WHPhotosViewController: WHViewController {
    var ablumStyle: WHPhotosAlbumStyle = .onlyPhotos //当前相册的类型。默认仅图片类型
    var ablumFetchTyps: WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos  //当前相册选中的资源获取类型
    ///delegate
    open weak var delegate: WHPhotosViewControllerDelegate?
    /// 相簿数组（以初始化传入的为准，全部就是全部的相册，图片就是全部图片的相册。在相册页切换类型，不会影响这里的相簿数组，防止变换）
    open var photoAlbums: [MTPhotoAlbum] = []
    /// 显示选图规范的H5地址，如果有就会展示H5半窗
    public var uploadSpecificationUrl:String = ""
    /// 最大选择数量
    var maxSelectCount: Int = 1
    /// 相册列表是否展开
    private var albumListUnfold = false
    
    // 是否功能介绍页
    public var isIntrodunce:Bool = false
    //当前选中的cell，给H5、编辑器类型使用的
    public weak var currentCell: UIView?
    
    private let kItemSpace = 4.0
    ///相册内容view，默认是4个
    public var ablumContents:[String:Any] = [:]
    ///当前展示的相册contentview
    public var currentFetchContentView : WHPhotoAlbumContentView?
    
    open lazy var albumSelectView: WHAlbumSelectView = {
        let albumSelectView = WHAlbumSelectView(frame: .zero)
        albumSelectView.delegate = self
        return albumSelectView
    }()
    
    open lazy var albumListView: WHPhotoAlbumListView = {
        let albumListView = WHPhotoAlbumListView(frame: .zero)
        albumListView.delegate = self
        albumListView.isHidden = true
        return albumListView
    }()
    
    private lazy var noPhotoView: WHPhotoNoDataView = {
        let noPhotoView = WHPhotoNoDataView(frame: .zero)
        noPhotoView.isHidden = true
        noPhotoView.delegate = self
        return noPhotoView
    }()
    
    public lazy var topSelectView: WHPhotoFetchTypeSelectView = {
        let view = WHPhotoFetchTypeSelectView(frame: .zero)
        view.delegate = self
        return view
    }()
    
    // 功能介绍遮挡项目的向上动画
    private lazy var albumTopBgView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(wh_hexString: "#17171A")
        view.isHidden = true
        return view
    }()
        
    // MARK: - Life cycle
    //默认是只选图片样式
    public init(ablumStyle:WHPhotosAlbumStyle = .onlyPhotos, ablumFetchTyps:WHPhotosAlbumFetchType = .WHPhotosAlbumFetchTypePhotos) {
        self.ablumStyle = ablumStyle
        self.ablumFetchTyps = ablumFetchTyps
        super.init(nibName: nil, bundle: nil)
    }
    
    required public init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        initData()
        addObservers()
        MTPhotoLibrary.shared().register(self)
    }
    
    open override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.currentFetchContentView?.collectionView.reloadData()
    }
    
    deinit {
        MTPhotoLibrary.shared().unregisterChangeObserver(self)
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Private methods
    
    private func configUI() {
        self.view.backgroundColor = .black
        showBackButton(isShow: true)
        myNavigationBar.backItemImage = UIImage(cm_named: "wh_album_vc_close_button_normal")
        view.addSubview(albumTopBgView)
        view.addSubview(albumSelectView)
        view.insertSubview(albumListView, belowSubview: myNavigationBar)
        view.insertSubview(topSelectView, belowSubview: albumListView)
        
        
        //添加不同类型的相册content，顺序是固定的：全部-图片-视频-live
        var contentViews:[String:Any] = [WHPhotosAlbumFetchType.WHPhotosAlbumFetchTypePhotos.analyticsText():WHPhotoAlbumPicContentView(),
                                     WHPhotosAlbumFetchType.WHPhotosAlbumFetchTypeVideos.analyticsText():WHPhotoAlbumVideoContentView(),
                                     WHPhotosAlbumFetchType.WHPhotosAlbumFetchTypeLivePhotos.analyticsText():WHPhotoAlbumLivePicContenView(),
                                     WHPhotosAlbumFetchType.WHPhotosAlbumFetchTypeAll.analyticsText():WHPhotoAlbumAllContentView()]
        self.ablumContents = contentViews
        //先将当前的content添加到视图上，其它的使用时再添加
        let currentContent = contentViews[self.ablumFetchTyps.analyticsText()] as? WHPhotoAlbumContentView
        if let current = currentContent {
            view.insertSubview(current, belowSubview: albumListView)
            self.currentFetchContentView = current
            current.delegate = self
        }
        //无数据页面
        view.insertSubview(noPhotoView, belowSubview: albumListView)
        albumSelectView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(24)
            make.width.equalTo(152)
            make.top.equalToSuperview().offset(isIntrodunce ? 12 : WH_NAVIGATION_BAR_HEIGHT - 36)
        }
        albumTopBgView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(48)
        }
        topSelectView.snp.makeConstraints { make in
            make.top.equalTo(isIntrodunce ? 48 : WH_NAVIGATION_BAR_HEIGHT)
            make.height.equalTo(ablumStyle == .onlyPhotos ? 0.0 : 40.0)
            make.left.right.equalToSuperview()
        }
        //先布局第一个content
        if let current = currentContent {
            current.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                make.top.equalTo(topSelectView.snp.bottom)
                make.bottom.equalToSuperview()
            }
        }
        
        let albumListViewHeight: CGFloat = WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT
        albumListView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(albumListViewHeight)
            make.top.equalTo(myNavigationBar.snp_bottom).offset(-albumListViewHeight)
        }
        noPhotoView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.equalToSuperview().offset(50.0)
            make.right.equalToSuperview().offset(-50.0)
            make.height.equalTo(200.0)
        }
//        DispatchQueue.main.async {
//            self.scrollToBottom()
//        }
        albumTopBgView.isHidden = !isIntrodunce
        
        //选图规范
        if uploadSpecificationUrl.count > 0 {
            let halfWeb = WHCommonHalfPlayWebViewController.webControllerWithUrlString(urlString: uploadSpecificationUrl)
            halfWeb.preBackgroundColor = WHFigmaColor.backgroundActionBarActionBar
            halfWeb.showOnViewController(controller: UIViewController.wh_top(), height: WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT)
        }
    }
    //只第一次初始化或收到相册变动通知时触发
    private func initData() {
        let selectedPhotoAlbum = photoAlbums.first
        //初始化列表数据
        DispatchQueue.main.async {
            self.albumSelectView.title = selectedPhotoAlbum?.title
        }
        albumListView.albums = photoAlbums
        albumListView.selectedAlbumID = selectedPhotoAlbum?.localIdentifier ?? ""
        
        //如果是全部类型样式的相册，但默认不是选中全部Tab，则需要重新fetch下数据
        if ablumStyle == .allStyle, ablumFetchTyps != .WHPhotosAlbumFetchTypeAll {
            WHPhotoAlbumSharedManager.photoLibraryAuth {
                MTPhotoLibrary.shared().fetchPhotoAlbums(with: self.ablumFetchTyps.mtPhotoLibraryType()) { photoAlbums in
                    self.photoAlbums = photoAlbums
                    self.initContentData()
                }
            }
        } else {
            initContentData()
        }
    }
    
    private func initContentData() {
        //初始化Content数据
        if let currentContent = currentFetchContentView {
            self.topSelectView.selectType(type: currentContent.contentFetchType, isAnimate: false)
            let selectedPhotoAlbum = photoAlbums.first
            currentContent.selectedPhotoAlbum = selectedPhotoAlbum
            currentContent.show()
        }
        showNoPhotoView()
    }
    
    private func addObservers() {
        NotificationCenter.default.addObserver(self, selector: #selector(icloudImageChange), name: .WHPhotoAlbumICloudStatusChange, object: nil)
    }
    
    private func showNoPhotoView() {
        let isNeedShow = !(self.currentFetchContentView?.selectedPhotoAlbum?.numberOfAssets ?? 0 > 0)
        noPhotoView.isHidden = !isNeedShow
        if isNeedShow == true {
            var showType:WHPhotoNoDataViewType = .normal
            if self.photoAlbums.count > 0 {
                let firstAlbum = self.photoAlbums[0]
                if firstAlbum.localIdentifier == self.currentFetchContentView?.selectedPhotoAlbum?.localIdentifier,
                   self.currentFetchContentView?.contentFetchType == .WHPhotosAlbumFetchTypeAll {
                    showType = .authorization
                }
            } else if self.currentFetchContentView?.contentFetchType == .WHPhotosAlbumFetchTypeAll {
                showType = .authorization
            }
            noPhotoView.showWithType(type: showType)
        }
    }
        
    @objc func icloudImageChange() {
        self.currentFetchContentView?.collectionView.reloadData()
    }
    
    func notifDelegateSelectPhotoWithMtAsset(mtAsset: MTPhotoAsset?) {
        if let mtAsset = mtAsset, WHICloudMan.status(for: mtAsset.asset) == .isCloud, mtAsset.assetFileURL() == nil {
            //icloud图片，提示下载
            WHICloudMan.showAlertInTop(phAsset: mtAsset.asset) {
                NotificationCenter.default.post(name: .WHPhotoAlbumICloudStatusChange, object: nil)
                MTCloudImageDownloadManager.default().addImageDownloadTask(for: mtAsset) { progress in
                    
                } resultHandler: { image, detailInfo in
                    NotificationCenter.default.post(name: .WHPhotoAlbumICloudStatusChange, object: nil)
                }
            }
            return
        }
        //处理asset类型
        let fetchType = self.currentFetchContentView?.contentFetchType ?? .WHPhotosAlbumFetchTypePhotos
        if (fetchType == .WHPhotosAlbumFetchTypeAll || fetchType == .WHPhotosAlbumFetchTypeVideos),mtAsset?.mediaType == .video {
            mtAsset?.resourceSelectType = .video   //视频类型
        } else if (fetchType == .WHPhotosAlbumFetchTypeAll || fetchType == .WHPhotosAlbumFetchTypeLivePhotos),mtAsset?.mediaSubtype == .photoLive {
            mtAsset?.resourceSelectType = .livePhoto   //live图
        } else {
            mtAsset?.resourceSelectType = .photo
        }
        var mtAssets:[MTPhotoAsset] = []
        if let mtAsset = mtAsset {
            mtAssets.append(mtAsset)
        }
        self.delegate?.selectPhotoWithMtasset(mtAssets: mtAssets, vc: self)
    }
    
    open override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        if name == WH_NAVIGATION_BACK_EVENT_NAME {
            self.notifDelegateSelectPhotoWithMtAsset(mtAsset: nil)
            self.dismiss(animated: true)
        }
    }
}
// MARK: - WHPhotoFetchTypeSelectViewDelegate
extension WHPhotosViewController: WHPhotoFetchTypeSelectViewDelegate {
    //相册筛选类型发生改变
    public func selectFetchPhotoChange(from:WHPhotosAlbumFetchType,to:WHPhotosAlbumFetchType) {
        //toContent显示
        if let currentContent = self.ablumContents[to.analyticsText()] as? WHPhotoAlbumContentView {
            //toContent判断是否要Fetch数据
            WHPhotoAlbumSharedManager.photoLibraryAuth {
                MTPhotoLibrary.shared().fetchPhotoAlbums(with: to.mtPhotoLibraryType()) { photoAlbums in
                    //更新当前相簿
                    self.photoAlbums = photoAlbums
                    //查找当前相簿对应的数据
                    var isFind = false
                    for item in photoAlbums {
                        if item.localIdentifier == self.albumListView.selectedAlbumID {
                            currentContent.selectedPhotoAlbum = item
                            self.showContenView(contentView: currentContent)
                            isFind = true
                            break;
                        }
                    }
                    if isFind == false {//显示无数据页面
                        self.currentFetchContentView = nil
                    }
                    self.showNoPhotoView()
                }
            }
        }
        //lastContent隐藏
        if let lastContent = self.ablumContents[from.analyticsText()] as? WHPhotoAlbumContentView {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
                lastContent.hidden()
            }
        }
    }
    //显示contentView
    func showContenView(contentView:WHPhotoAlbumContentView?) {
        guard let contentView = contentView else { return }
        if contentView.superview == nil { //尚未添加的，先进行添加
            contentView.delegate = self
            view.insertSubview(contentView, belowSubview: albumListView)
            contentView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                make.top.equalTo(topSelectView.snp.bottom)
                make.bottom.equalToSuperview()
            }
        }
        //替换当前的currentContent
        self.currentFetchContentView = contentView
        contentView.show()
    }
}

// MARK: - MTPhotoLibraryChangeObserver

extension WHPhotosViewController: MTPhotoLibraryChangeObserver {
    
    public func photoLibraryDidChange(_ changeInstance: PHChange?) {
        guard MTPhotoLibrary.isAuthorized() else { return }
        
        if #available(iOS 14, *) {
            if MTPhotoLibrary.authorizationStatus() == .limited {
                MTPhotoLibrary.shared().fetchPhotoAlbums(with: self.ablumFetchTyps.mtPhotoLibraryType()) { photoAlbums in
                    self.photoAlbums = photoAlbums
                    self.initData()
                }
            }
        }
    }
    
}

// MARK: - WHPhotoNoDataViewDelegate

extension WHPhotosViewController: WHPhotoNoDataViewDelegate {
    
    func selectPhotos() {
        WHPrint("选择照片")
        if #available(iOS 14, *) {
            PHPhotoLibrary.shared().presentLimitedLibraryPicker(from: self)
        }
    }
    
    func modifyAuth() {
        WHPrint("修改权限")
        if let appSettings = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(appSettings) {
                if #available(iOS 10.0, *) {
                    UIApplication.shared.open(appSettings, options: [:], completionHandler: nil)
                } else {
                    UIApplication.shared.openURL(appSettings)
                }
            }
        }
    }
    
}

// MARK: - WHAlbumSelectViewDelegate

extension WHPhotosViewController: WHAlbumSelectViewDelegate {
    
    func openOrCloseAlbumList() {
        albumListUnfold = !albumListUnfold
        
        var top: CGFloat = 0
        var isHidden = false
        if albumListUnfold { // 展开
            top = isIntrodunce ? -WH_STATUS_BAR_HEIGHT : 0
            albumListView.isHidden = false
        } else { // 关闭
            top = -(WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT)
            isHidden = true
        }
        albumListView.snp.updateConstraints { make in
            make.top.equalTo(myNavigationBar.snp_bottom).offset(top)
        }
        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        } completion: { finished in
            if !self.albumListUnfold {
                self.albumListView.isHidden = true
            }
        }
    }
    
}

// MARK: - WHPhotoAlbumListViewDelegate

extension WHPhotosViewController: WHPhotoAlbumListViewDelegate {
    
    func didSelectAlbum(_ albumListModel: WHPhotoAlbumListModel) {
        guard albumListModel.albumID.count > 0 else { return }
        //取出选中的相册
        var selectAlbum:MTPhotoAlbum? = nil
        for item in self.photoAlbums {
            if item.localIdentifier == albumListModel.albumID {
                selectAlbum = item
                break;
            }
        }
        //如果当前没有currentContent显示，则添加一下
        if self.currentFetchContentView == nil,let currentContent = self.ablumContents[self.topSelectView.currentSelectType.analyticsText()] as? WHPhotoAlbumContentView {
            self.showContenView(contentView: currentContent)
        }
        self.currentFetchContentView?.selectedPhotoAlbum = selectAlbum
        self.currentFetchContentView?.collectionView.reloadData()
        // 刷新title
        self.albumSelectView.title = albumListModel.albumTitle
        self.albumSelectView.openOrCloseList()
        // 滚动到底部
//        self.scrollToBottom()
        
        showNoPhotoView()
    }
    
}
 
extension WHPhotosViewController: WHPhotosImageBrowserControllerDelegate, WHPhotoAlbumContentViewDelegate {
    
    public func selectPhotoWithMtasset(mtAsset: MTPhotoAsset) {
        self.notifDelegateSelectPhotoWithMtAsset(mtAsset: mtAsset)
    }
    
    public func selectCollectionViewCell(currentCell:UIView?) {
        self.currentCell = currentCell
    }
}

extension CGFloat {
    func rounded(toPlaces places: Int) -> CGFloat {
        let multiplier = pow(10, CGFloat(places))
        return (self * multiplier).rounded() / multiplier
    }
}
