//
//  WHPlayIntroduceLink.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/5/29.
//

import Foundation

public var WHPlayIntroducePath = "https://h5.whee.com/appTips/"


public enum WHPlayIntroduceLinkType: NSInteger {
    ///编辑器改图玩法介绍
    case editorInpaint
    ///编辑器扩图玩法介绍
    case editorExpand
    ///编辑器AI合成玩法介绍
    case editorAIGenerate
    ///编辑器AI合成controlnet玩法介绍
    case editorAIGenerateControlnet
    ///SOP上传规范
    case sopUploadSpecification
    
    public func typelink(temId:String = "",source:String = "") -> String{
        if  WHEnvConfigShareManager.environment == .pre {
            WHPlayIntroducePath = "http://pre-h5.whee.com/appTips/"
        } else {
            WHPlayIntroducePath = "https://h5.whee.com/appTips/"
        }
        switch self {
        case .editorInpaint:
            return WHPlayIntroducePath+"inpaint"
        case .editorExpand:
            return WHPlayIntroducePath+"extend"
        case .editorAIGenerate:
            return WHPlayIntroducePath+"edAIGenerate"
        case .editorAIGenerateControlnet:
            return WHPlayIntroducePath+"edAIControlnet"
        case .sopUploadSpecification:
            return WHPlayIntroducePath+"uploadTips?id=\(temId)&source=\(source)"
        }
    }
}
