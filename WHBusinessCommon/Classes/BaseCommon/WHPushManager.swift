//
//  WHPushManager.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/11/8.
//

import Foundation
import MTPushNotification
import MTGID

public let WHPushShareManager = WHPushManager.shared

public class WHPushManager: NSObject, UNUserNotificationCenterDelegate {
    static let shared = WHPushManager()
    public func registerPushSDK() {
        MTPushManager.share().register(withAppid: WHSharedRequest.sigAppId(), isTest: WHEnvConfigShareManager.environment == .pre ? true : false)
        MTPushManager.share().isUploadEveryLaunch = true
        MTPushExtension.setApplicationGroupIdentifier("group.com.meitu.whee")
        self.loginStatusChange()
        self.addNotificationObersers()
    }
    
    public func apns_setupWith(application:UIApplication?, launchOptions:[UIApplication.LaunchOptionsKey: Any]?) {
        let center = UNUserNotificationCenter.current()
        center.delegate = self
        center.requestAuthorization(options: [.alert, .sound]) { granted, error in
            DispatchQueue.main.async {
                if !granted {
                    self.uploadPushInfoWith(deviceToken: nil)
                } else {
                    application?.registerForRemoteNotifications()
                }
            }
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.checkIsNeedShowOpenAPNSAlert()
        }
    }
    //注册失败上报
    public func apns_application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        self.uploadPushInfoWith(deviceToken: nil)
    }
    //注册设备
    public func apns_application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        self.uploadPushInfoWith(deviceToken: deviceToken)
    }
    
    func uploadPushInfoWith(deviceToken: Data?) {
        let userId:String? = MTACInfoManager.userId()
        let uploadInfo = MTPushUploadInfo(deviceToken: deviceToken, uid: userId, gnum: MTAnalyticsGID.sharedInstance()?.gid, lang: WHAppShareLanguage.wh_appLanguage(), country: nil)
        MTPushManager.share().upload(with: uploadInfo)
    }
    
    // MARK -- UNUserNotificationCenterDelegate
    public func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        if #available(iOS 14, *) {
            completionHandler([.banner,.sound])
        } else {
            completionHandler([.alert,.sound])
        }
    }
    
    public func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        if response.actionIdentifier == UNNotificationDefaultActionIdentifier {
            let userInfo = response.notification.request.content.userInfo
            MTPushManager.share().handleRemoteNotification(userInfo)
            if UIApplication.shared.applicationState != .active {
                MTPushManager.share().statisticsPushMessageClicked(userInfo)
            }
            let pushMessage = MTPushManager.share().pushNotificationMessage
            let uriStr = pushMessage?.uri
            WHRouter.route(with: uriStr ?? "")
//            UIApplication.shared.applicationIconBadgeNumber = -1
        }
        completionHandler()
    }
    
    private func checkIsNeedShowOpenAPNSAlert() {
        UNUserNotificationCenter.current().getNotificationSettings { [weak self] settings in
            guard let self = self else { return }
            DispatchQueue.main.async {
                if settings.authorizationStatus != .authorized {
                    let currentTime = NSInteger(Date().timeIntervalSince1970)
                    if let dic = WHUserDefaults.getAPNSAuthorityAlertCache() as? [String:Any],let time = dic["time"] {
                        let num = Int((dic["num"] as? String) ?? "0") ?? 0
                        let time = NSInteger((dic["time"] as? String) ?? "0") ?? 0
                        if num < 3 {
                            let distance = currentTime - time
                            if distance > 604800 {
                                let newNum = num+1
                                let newDic = ["num":String(newNum),"time":String(currentTime)]
                                WHUserDefaults.setAPNSAuthorityAlertCache(newDic)
                                self.showAuthorityAlert()
                            }
                        }
                    } else {
                        let dic = ["num":"0","time":String(currentTime)]
                        WHUserDefaults.setAPNSAuthorityAlertCache(dic)
                    }
                    
                }
            }
        }
    }
    
    private func showAuthorityAlert() {
        let alertView = WHCommonAlertView(title: WHLocalizedString("开启通知权限"), desStr: WHLocalizedString("开启通知权限，及时了解你的任务生成情况～"), alertViewType: .allStyle, sureButtonText: WHLocalizedString("去开启"))
        alertView.show(in: WHRouter.topViewController?.view)
        alertView.alertSureHandler = {[weak self]() in
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        }
    }
    
    func addNotificationObersers() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginSuccess,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginOut,
                                               object: nil)
        NotificationCenter.default.addObserver(forName: NSNotification.Name.MTGIDDidChanged, object: nil, queue: OperationQueue.main) { note in
            self.uploadPushInfoWith(deviceToken: nil)
        }
    }
    
    @objc func loginStatusChange(){
        if WHAccountShareManager.isLogin() {
            let userId:String? = MTACInfoManager.userId()
            let bindInfo = MTPushBindInfo(uid: userId, gnum: MTAnalyticsGID.sharedInstance()?.gid)
            MTPushManager.share().bind(with: bindInfo)
        } else {
            MTPushManager.share().unbind()
        }
    }
    
}
