//
//  WHServerReport.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/5/29.
//

import Foundation
import WHBaseLibrary
import Alamofire

public enum WHServerTaskCategoryType: Int {
    case none   = 0 //
    case inpainding = 1 // AI改图的选图
    case aiVideo   = 2 // AI视频
    case aiClear   = 3 // AI消除
    case txt2Img   = 4 // 文生图
    case img2Img   = 5 // 图生图
    case editor    = 6 // 改图编辑器
    case introduce = 7 // 功能介绍
    case aiUpScaler = 8 // AI超清
    case aiExpand   = 9 // AI扩图
    case aiLive     = 10 // 转Live
    case oneSentence = 11 // 一句话修图
    case videoHud    = 12 //视频超清
    case smartCutout = 13 //智能抠图
    
    public func serverTaskCategory() -> String {
        switch self {
        case .none:
            return ""
        case .inpainding:
            return "inpaint"
        case .aiVideo:
            return "ai_video"
        case .aiClear:
            return "ai_eraser"
        case .txt2Img:
            return "txt2img"
        case .img2Img:
            return "img2img"
        case .editor:
            return "project"
        case .introduce:
            return "introduce"
        case .aiUpScaler:
            return "magicsr"
        case .aiExpand:
            return "extend"
        case .aiLive:
            return "image_to_live"
        case .oneSentence:
            return "text_image_editing"
        case .videoHud:
            return "ai_video_magicsr"
        case .smartCutout:
            return "cutout"
        default:
            return ""
        }
    }
}
public class WHServerReport: NSObject {
    
    public static func reportSaveSuccess(msgID:String,taskCategory:String,download:String) {
        let params = ["msg_id":msgID,
                      "task_category":taskCategory,
                      "download":download]
        WHSharedRequest.POST("/task/download.json", params: params) { response in
        }
    }
    
    ///获取美豆余额
    public static func requestBalanceAmount(completion: @escaping (WHBeansBalanceModel?) -> ()) {
        WHSharedRequest.GET("/sub/query_balance.json",
                            params: nil) { (response: WHOriginalResponse) in
            if response.isSuccess(),
               let data = response.data() as? [String: Any],
               let model = WHBeansBalanceModel.yy_model(with: data) {
                completion(model)
            } else {
                completion(nil)
            }
        }
    }
    
}
