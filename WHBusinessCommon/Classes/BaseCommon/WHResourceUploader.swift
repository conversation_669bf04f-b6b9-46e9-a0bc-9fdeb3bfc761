//
//  WHResourceUploader.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2023/11/16.
//

import Foundation
import MTResourceUpload
import MTAPM

let kWHResourceUploadAppKey = "aigc-platform"

public let WHResourceUploaderManager = WHResourceUploader.shared

public class WHResourceUploader {
    public static let shared = WHResourceUploader()

    public var manager: MTResourceUploadManager = MTResourceUploadManager.init(appKey: kWHResourceUploadAppKey, accessToken: WHAccountShareManager.getAccessToken())
        
    public init() {
        setupUploadManager()
        addObserver()
    }
    
    private func setupUploadManager() {
        manager.isTest = WHEnvConfigShareManager.environment == .pre ? true : false
        manager.setUseAPMCollectInfo(true)
    }
    
    private func addObserver() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginSuccess,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(loginStatusChange),
                                               name: NSNotification.Name.WHACCountLoginOut,
                                               object: nil)
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(resourceUploadAPM),
                                               name: Notification.Name("MTAPMUploadTaskNotification"),
                                               object: nil)
    }
    
    @objc private func loginStatusChange() {
        manager = MTResourceUploadManager.init(appKey: kWHResourceUploadAppKey, accessToken: WHAccountShareManager.getAccessToken())
        setupUploadManager()
    }
    
    @objc private func resourceUploadAPM(_ notify: Notification) {
        MTAPM.sharedInstance().uploadLogTypeString("upload_file_sdk", dictionary: notify.userInfo, filePaths: nil, completion: nil)
    }
    
}
