//
//  WHRouterConstants.swift
//  WHBusinessCommon
//
//  Created by <PERSON>iaoqi on 2023/9/23.
//

import Foundation

public let WHRouteScheme = "wheeai"
/// 设置页
public let WHRouteSetting = "wheeai://app/setting"
///网页
public let WHWebViewVC = "wheeai://web"
///小程序
//public let WHMiniApp = "wheeai://miniapp"

public let WHCustomerURL:String = {
    switch WHEnvConfigShareManager.environment{
    case .pre:
        return "https://pre-feedback.meitu.com"
    case .beta:
        return "https://beta-feedback.meitu.com"
    case .release:
        return "https://feedback.meitu.com"
        
    }
}()
