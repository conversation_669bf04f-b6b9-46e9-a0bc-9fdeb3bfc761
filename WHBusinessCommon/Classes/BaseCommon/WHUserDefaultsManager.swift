//
//  WHUserDefaultsManager.swift
//  WHBusinessCommon
//
//  Created by zhanglifei on 2023/11/10.
//  app内UserDefaults

import Foundation
import WHBaseLibrary

let kUserDidAgreePrivacyKey = "kUserDidAgreePrivacyKey"   // 隐私协议
let kVersionUpgradeKey = "kVersionUpgradeKey"   // 版本更新
let kMarkSmearTipsKey = "kMarkSmearTipsKey"     // 局部修改涂抹提示
let kMarkRectTipsKey = "kMarkRectTipsKey"       // 局部修改框选提示
let kVideoPlayUnWifiTipsKey = "kVideoPlayUnWifiTipsKey"       // 局部修改框选提示

let kEditorMarkSmearTipsKey = "kEditorMarkSmearTipsKey"     // 编辑器局部修改涂抹提示
let kEditorMarkRectTipsKey = "kEditorMarkRectTipsKey"       // 编辑器局部修改框选提示
let kEditorAutoMeidouKey = "kEditorAutoMeidouKey"       // 编辑器自动消耗美豆弹窗key
let kEditorFirstGuideKey = "kEditorFirstGuideKey"       // 编辑器一级新手引导
let kEditorHistoryGuideKey = "kEditorHistoryGuideKey"       // 编辑器历史新手引导
let kEditorLayerGuideKey = "kEditorLayerGuideKey"       // 编辑器图层新手引导

let kEditorInpaintPlayIntroduceKey = "kEditorInpaintPlayIntroduceKey"    // 编辑器改图玩法介绍
let kEditorExpandPlayIntroduceKey = "kEditorExpandPlayIntroduceKey"    // 编辑器扩图玩法介绍
let kEditorAIGeneratePlayIntroduceKey = "kEditorAIGeneratePlayIntroduceKey"    // 编辑器图AI合成玩法介绍

let kLiveGuideKey = "kWHAILiveGuideKey" // AILive新手引导toast

public class WHUserDefaultsManager: NSObject {
    
    /// 同意隐私协议
    public static func setAgreeUserPrivacy() {
        UserDefaults.standard.setValue(true, forKey: kUserDidAgreePrivacyKey)
        UserDefaults.standard.synchronize()
    }
    /// 撤回同意隐私协议
    public static func deleteAgreeUserPrivacy() {
        UserDefaults.standard.setValue(false, forKey: kUserDidAgreePrivacyKey)
        UserDefaults.standard.synchronize()
    }
    /// 是否同意隐私协议
    public static func isAgreeUserPrivacy() -> Bool {
        return UserDefaults.standard.bool(forKey: kUserDidAgreePrivacyKey)
    }
    
    /// 记录版本更新点击
    public static func setVersionUpgrade(_ version: String) {
        UserDefaults.standard.setValue(version, forKey: kVersionUpgradeKey)
        UserDefaults.standard.synchronize()
    }
    /// 是否弹版本更新
    public static func isShowVersionUpgrade(_ version: String) -> Bool {
        if let currentVersion = UserDefaults.standard.string(forKey: kVersionUpgradeKey) {
            if currentVersion != version, WHAppInfo.compareVersion(version: version) {
                return true
            } else {
                return false
            }
        }
        return WHAppInfo.compareVersion(version: version)
    }
    
    /// 局部调整涂抹提示
    public static func setMarkSmearTips(isSmear: Bool) {
        if isSmear {
            UserDefaults.standard.setValue(true, forKey: kMarkSmearTipsKey)
        } else {
            UserDefaults.standard.setValue(true, forKey: kMarkRectTipsKey)
        }
        UserDefaults.standard.synchronize()
    }
    /// 局部调整涂抹提示
    public static func isMarkSmearTips(isSmear: Bool) -> Bool {
        if isSmear {
            return UserDefaults.standard.bool(forKey: kMarkSmearTipsKey)
        } else {
            return UserDefaults.standard.bool(forKey: kMarkRectTipsKey)
        }
    }
    /// 视频非wifi下的提示
    public static func showUnWIFITips() -> Bool {
        let show = UserDefaults.standard.bool(forKey: kVideoPlayUnWifiTipsKey)
        if !show {
            UserDefaults.standard.set(true, forKey: kVideoPlayUnWifiTipsKey)
        }
        return !show
    }
    /// 编辑器局部调整涂抹提示
    public static func setEditorMarkSmearTips(isSmear: Bool) {
        if isSmear {
            UserDefaults.standard.setValue(true, forKey: kEditorMarkSmearTipsKey)
        } else {
            UserDefaults.standard.setValue(true, forKey: kEditorMarkRectTipsKey)
        }
        UserDefaults.standard.synchronize()
    }
    /// 局部调整涂抹提示
    public static func isEditorMarkSmearTips(isSmear: Bool) -> Bool {
        if isSmear {
            return UserDefaults.standard.bool(forKey: kEditorMarkSmearTipsKey)
        } else {
            return UserDefaults.standard.bool(forKey: kEditorMarkRectTipsKey)
        }
    }
    /// 获取是否需要弹自动美豆消耗弹窗
    public static func getEditorAutoMeidouIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorAutoMeidouKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorAutoMeidouKey)
        return true;
    }
    /// 获取是否需要弹一级新手引导
    public static func getEditorFirstGuideIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorFirstGuideKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorFirstGuideKey)
        return true;
    }
    /// 获取是否需要弹历史新手引导
    public static func getEditorHistoryGuideIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorHistoryGuideKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorHistoryGuideKey)
        return true;
    }
    /// 获取是否需要弹图层新手引导
    public static func getEditorLayerGuideIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorLayerGuideKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorLayerGuideKey)
        return true;
    }
    /// 获取是否需要弹编辑器改图玩法介绍
    public static func getEditorInpaintPlayIntroduceIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorInpaintPlayIntroduceKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorInpaintPlayIntroduceKey)
        return true;
    }
    /// 获取是否需要弹编辑器扩图玩法介绍
    public static func getEditorExpandPlayIntroduceIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorExpandPlayIntroduceKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorExpandPlayIntroduceKey)
        return true;
    }
    /// 获取是否需要弹编辑器AI合成玩法介绍
    public static func getEditorAIGeneratePlayIntroduceIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kEditorAIGeneratePlayIntroduceKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kEditorAIGeneratePlayIntroduceKey)
        return true;
    }
    
    /// 获取是否需要弹LIVE新手引导
    public static func getLiveGuideIsNeedAlert() -> Bool {
        let isAlerted = UserDefaults.standard.bool(forKey: kLiveGuideKey)
        if isAlerted == true {
            return false;
        }
        UserDefaults.standard.setValue(true, forKey: kLiveGuideKey)
        return true;
    }
}
