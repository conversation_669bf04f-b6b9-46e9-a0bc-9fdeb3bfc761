//
//  WHUserPrivacyManager.swift
//  MeituWhee
//
//  Created by zhanglifei on 2023/10/7.
//

import Foundation

public class WHUserPrivacyManager: NSObject {
    
    static var hasHandledPrivacyAlert = false
    
    public typealias AgreementBlock = (() -> Void)
    
    static var agreementHandlers: [AgreementBlock] = []
    
    static var lock = DispatchSemaphore(value: 1)
    
    /// 用户同意隐私协议
    public static func agreeUserPrivacy() {
        WHUserDefaultsManager.setAgreeUserPrivacy()
        
        self.lock.wait()
        var array = WHUserPrivacyManager.agreementHandlers
        for handler in array {
            handler()
        }
        WHUserPrivacyManager.agreementHandlers.removeAll()
        self.lock.signal()
    }
    
    /// 是否同意隐私协议
    public static func isAgreedUserPrivacy() -> Bool {
        return WHUserDefaultsManager.isAgreeUserPrivacy()
    }
    
    ///用户撤回隐私协议
    public static func deleteUserPrivacy() {
        WHUserDefaultsManager.deleteAgreeUserPrivacy()
    }
    
    /// 增加app启动同意初始化回调
    /// - Parameters:
    ///   - block: 触发事件
    @objc public static func addAgreementHandler(_ block: @escaping AgreementBlock) {
        self.lock.wait()
        if !WHUserPrivacyManager.isAgreedUserPrivacy() {
            WHUserPrivacyManager.agreementHandlers.append(block)
        } else {
            block()
        }
        self.lock.signal()
    }
}
