//
//  WHViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON>iaoqi on 2023/10/5.
//

import Foundation
import WHBaseLibrary

open class WHViewController: WHBaseViewController,WHRouteProtocol{
    open func loadRouteParams(_ params: [String : Any]) {
        
    }
    
    open override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
}

public extension Notification.Name {
    /// 显示美豆半窗
    static let showSubMeidouWindow = Notification.Name("WHShowSubMeidouWindow")
    /// 显示会员半窗
    static let showSubscribeWindow = Notification.Name("WHShowSubscribeWindow")
    
    static let VipStatusDidChanged = Notification.Name("VipStatusDidChanged")
    
}

public struct WHSubMeidouWindowNotificationParams {
    public let viewController: UIViewController
    public let params: [String: Any]
    public let selectMeidou: Bool
    public init(viewController: UIViewController, params: [String : Any], selectMeidou: Bool) {
        self.viewController = viewController
        self.params = params
        self.selectMeidou = selectMeidou
    }
}

public struct WHSubscribeWindowNotificationParams {
    public let viewController: UIViewController
    public let params: [String: Any]
    public init(viewController: UIViewController, params: [String : Any]) {
        self.viewController = viewController
        self.params = params
    }
}
