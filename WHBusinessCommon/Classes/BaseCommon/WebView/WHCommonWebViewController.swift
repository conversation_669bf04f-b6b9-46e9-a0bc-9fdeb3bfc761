//
//  WHCommonWebViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2024/5/29.
//

import Foundation
import WHBaseLibrary
import WHBusinessCommon
import MTWebKit

public class WHCommonWebViewController: WHViewController {
    public var webView: MTWWebView?
    public var urlString: String?
    
    public var showCustomHUD: Bool = true

    /// 网络加载时间默认60秒
    public var timeoutInterval: TimeInterval = 60.0
    /// 是否被进程杀死
    private var webContentProcessDidTerminate: Bool = false
    /// 显示原生导航栏
    private var showNativeNavBar: Bool = false
    /// 是否是全屏样式
    private var isFullScreen: Bool = false
    /// 是否隐藏原生返回按钮（仅在全屏浏览器下支持）
    private var isHiddenClose: Bool = false
    /// 是否是黑色背景
    private var BlackBg: Bool = false
    
    private var fullScreenCloseBtn:UIButton? //全屏下的关闭按钮
    
    public override func loadRouteParams(_ params: [String : Any]) {
        if let oriSchema = params["ori_schema"] as? String,oriSchema.count > 0,let URL = URL(string: oriSchema) {
            let schemeModel = WHRouterSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
            let queryDic = schemeModel?.queryDic
            if let url = queryDic?["url"] as? String {
                urlString = url
            } else if let webUrl = params["url"] as? String, webUrl.count > 0 {
                urlString = webUrl
            }
            if let fullScreen = queryDic?["fullscreen"] as? String {
                isFullScreen = fullScreen == "1"
            }
            if let hiddenClose = queryDic?["hiddenClose"] as? String {
                isHiddenClose = hiddenClose == "1"
            }
            if let isDark = queryDic?["isDark"] as? String {
                BlackBg = isDark == "1"
            }
            if let show = queryDic?["showNativeNavBar"] as? String {
                showNativeNavBar = show == "1"
            } else if let show = queryDic?["showNativeNavBar"] as? Bool {
                showNativeNavBar = show
            }
        } else if let url = params["url"] as? String, url.count > 0 {
            urlString = url
        }
    }
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        initWebView()
        registerWebVeiwProtocol()
        loadRequest()
        showBackButton(isShow: true)
    }
    
    
    // MARK: - Private Methods
    public func initWebView() {
        var options: [MTWebViewConfigurationOptionsKey : Any] = [
            .customUserAgentUseAppLanguage: WHAppShareLanguage.wh_appLanguage(),
            .scalesPageToFit: true,
        ]
        let config = MTWebViewConfiguration(options: options)
        // 设置 YES 为走内部通用协议实现 reload 之后重新注入协议功能.  默认为 NO
        config.internalProcessTermination = true
        // 保存最大的历史 js 调用次数, 配合
        config.maxHistoryJSCallCount = 20
        
        
        let aView = MTWWebView(frame: .zero,
                               configuration: config)
        view.addSubview(aView)
        webView = aView
        // 设置 webViewDelegate, 包含基础功能, 旧通用协议
        webView?.webViewDelegate = self
        // 设置 webViewJSGeneralDelegate, 包含新通用协议
//        webView?.webViewJSGeneralDelegate = self
        webView?.addObserver(self,
                             forKeyPath: "title",
                             options: [.new, .old],
                             context: nil)
        webView?.webViewConfiguration?.protocolJumpPageHandler = { [weak self] (link, showShareButton, isLocal, dictData) in
            self?.openJumpLink(urlString: link ?? "")
        }
        if self.BlackBg == true {
            webView?.setWebViewOpaque(false, backgroundColor: UIColor.black)
        }
        webView?.snp.makeConstraints { make in
            make.top.equalTo((isFullScreen ? 0 : myNavigationBar.snp.bottom))
            make.left.right.bottom.equalToSuperview()
        }
        if isFullScreen {
            self.myNavigationBar.isHidden = true
            let backButton = UIButton(type: .custom)
            backButton.isHidden = isHiddenClose
            backButton.setImage(UIImage(named: "wh_common_web_back_button_normal"), for: .normal)
            backButton.addTarget(self, action: #selector(backButtonClickAction), for: .touchUpInside)
            view.addSubview(backButton)
            backButton.snp.makeConstraints { make in
                make.left.equalTo(6.0)
                make.top.equalTo(WH_NAVIGATION_BAR_HEIGHT - 48.0)
                make.size.equalTo(CGSizeMake(48.0, 48.0))
            }
            self.fullScreenCloseBtn = backButton
        }
    }
    
    private func setupSubviewOrder() {
//        view.bringSubviewToFront(networkErrorRetryView)
        view.bringSubviewToFront(myNavigationBar)
    }
    
    
    private func showNetworkErrorRetryView(_ show: Bool) {
//        networkErrorRetryView.isHidden = !show
        if showNativeNavBar == false {
            panBackEnabled = show
        }
    }
    
    private func loadRequest() {
        if let urlString = urlString,
            let url = URL(string: urlString) {
            webView?.load(URLRequest(url: url,
                                     cachePolicy: .useProtocolCachePolicy,
                                     timeoutInterval: timeoutInterval))
        }
    }
    
    private func resetConfigration() {
    }
    
    private func updateNetworkErrorViewVisibility() {
        webView?.mt_evaluateJavaScript("document.body.innerHTML") { [weak self] (result: Any?, error: Error?) in
            guard let self = self else {
                return
            }
            if let result = result as? String {
                self.showNetworkErrorRetryView(result.isEmpty)
            }
        }
    }
    
    public func openJumpLink(urlString:String) {
        guard let url = URL(string: urlString) else {
            return
        }
        if urlString.hasPrefix("http://") || urlString.hasPrefix("https://") {
            let webEncoding = urlString.addingPercentEncoding(withAllowedCharacters: CharacterSet.alphanumerics) ?? ""
            let urlScheme = "wheeai://web?url="+webEncoding
            WHRouter.route(with: urlScheme)
            return;
        }
        let scheme = url.scheme
        if scheme == WHRouteScheme {
            WHRouter.route(with: urlString)
            return;
        }
        UIApplication.shared.open(url, options: [:]) { success in
        }
    }
    
    public override func wh_handleEventWith(_ name: String, params: [String : Any]? = nil) {
        if ((self.webView?.canGoBack) == true) {
            self.webView?.goBack()
        } else {
            if let navigationController = self.navigationController,
               navigationController.viewControllers.count > 1 {
                navigationController.popViewController(animated: true)
            } else if self.presentingViewController != nil {
                self.dismiss(animated: true) {
                }
            }
        }
    }
    @objc func backButtonClickAction() {
        self.wh_handleEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
    }
    deinit {
        webView?.removeObserver(self,
                                forKeyPath: "title")
    }
}

// MARK: - MTWebViewDelegate
extension WHCommonWebViewController: MTWebViewDelegate {
    
    public func mt_webViewDidStartLoad(_ webView: MTWebViewProtocol) {
        if showCustomHUD {
//            showLoading(with: "加载中...")
        }
        if self.isFullScreen == true { //如果是全屏浏览器，重置下关闭按钮状态
            self.fullScreenCloseBtn?.isHidden = self.isHiddenClose
        }
    }
    
    public func mt_webViewDidFinishLoad(_ webView: MTWebViewProtocol) {
        if showCustomHUD {
//            hideHUD()
        }
        if self.isFullScreen == true { //如果是全屏浏览器，且不隐藏关闭按钮。这里再显示下按钮
            self.fullScreenCloseBtn?.isHidden = self.isHiddenClose
        }
        updateNetworkErrorViewVisibility()
    }
    
    public func mt_webView(_ webView: MTWebViewProtocol, didFailLoadWithError error: Error) {
        if showCustomHUD {
//            hideHUD()
        }
        if self.isFullScreen == true { //如果是全屏浏览器，页面加载失败时。显示下关闭按钮
            self.fullScreenCloseBtn?.isHidden = false
        }
        updateNetworkErrorViewVisibility()
    }
    
    public func mt_webViewWebContentProcessDidTerminate(_ webView: MTWebViewProtocol) {
        webContentProcessDidTerminate = true
        resetConfigration()
        webView.reload()
    }
    
    // MARK: - KVO
    public override func observeValue(forKeyPath keyPath: String?,
                                      of object: Any?,
                                      change: [NSKeyValueChangeKey : Any]?,
                                      context: UnsafeMutableRawPointer?) {
        if keyPath == "title",
            let text = change?[.newKey] as? String {
            myNavigationBar.title = text
        } else {
            super.observeValue(forKeyPath: keyPath,
                               of: object,
                               change: change,
                               context: context)
        }
    }
    
}

// MARK: - MTWebViewJSAccountDelegate
extension WHCommonWebViewController: MTWebViewJSAccountDelegate{
    
}
// MARK: - MTWebViewJSGeneralDelegate
extension WHCommonWebViewController: MTWebViewJSGeneralDelegate {
    
    public func webViewJSGeneralGetAppInfo(_ webView: MTWWebView,
                                           params: MTWebViewJSGeneralParams,
                                           callbackHandler: @escaping MTProtocolJSGeneralCallbackHandler) {
        guard let appInfo = params.eventData?[.appInfo] as? MTWebViewAppInfo else {
            callbackHandler(nil, nil)
            return
        }
        appInfo.gid = MTAnalyticsGID.sharedInstance().gid
        appInfo.clientId = MTACAccountManager.share().clientId
        appInfo.countryCode = (Locale.current as NSLocale).object(forKey: .countryCode) as? String ?? ""
        appInfo.language = MTACLanguageManager.share().currentLanguage
        appInfo.dataProtected = true
        appInfo.channel = "App Store"
        callbackHandler(appInfo, nil)
    }
    /// 获取当前分享的渠道数组，返回渠道数组
    public func webViewJSShareGetChannels(_ webView: MTWWebView) -> [MTWebViewJSShareChannel] {
        return [.weixin, .weixinMoments, .QQ, .qzone, .weibo]
    }
    
    ///directToShare 直接分享链接或图片等信息
    public func webViewJSShareDirect(_ webView: MTWWebView, params: MTWebViewJSGeneralParams, callbackHandler: @escaping MTProtocolJSShareCallbackHandler) {
        guard let shareInfo = params.eventData?[MTWebViewJSGeneralParamsKey.shareInfo] as? MTWebViewJSShareInfo else { return }
//        WHShareViewManager.showWebCommonShareSheet(with: shareInfo) { channel, isReach in
//            callbackHandler(channel, isReach)
//        } completion: { resp in
//        }
    }
    
    ///showShareBottomSheet  显示当前页面的底部分享弹框，用于分享链接（卡片）、图片等
    public func webViewJSShareBottomSheet(_ webView: MTWWebView, params: MTWebViewJSGeneralParams, callbackHandler: @escaping MTProtocolJSShareCallbackHandler) {
        guard let shareInfo = params.eventData?[MTWebViewJSGeneralParamsKey.shareInfo] as? MTWebViewJSShareInfo else { return }
//        WHShareViewManager.showWebCommonShareSheet(with: shareInfo) { channel, isReach in
//            callbackHandler(channel, isReach)
//        } completion: { resp in
//        }
    }
    
//    public func
    
}

// MARK: - MTWebViewJSNetworkDelegate
extension WHCommonWebViewController: MTWebViewJSNetworkDelegate {
    
}

// MARK: - H5协议
extension WHCommonWebViewController {
    
    /// 注册协议
    public func registerWebVeiwProtocol() {
        // 关闭webView
        webView?.mt_registerProtocol(kMTWebViewJsCommandCloseWebview,
                                    handler: closeWebViewHandler(),
                                    mapping: nil)
    }
    
    /// 关闭webView
    public func closeWebViewHandler() -> MTWVJBHandler {
        return { [weak self] (command: MTWKJSCommand, callback: MTWVJBResponseCallback?) in
            guard let self = self else {
                return
            }
            if let presentingViewController = self.presentingViewController {
                if self.navigationController == nil || self.navigationController?.viewControllers.last == self {
                    self.dismiss(animated: true)
                } else {
                    if var viewControllers = self.navigationController?.viewControllers {
                        if let index = viewControllers.firstIndex(of: self) {
                            viewControllers.remove(at: index)
                        }
                        self.navigationController?.setViewControllers(viewControllers,
                                                                      animated: true)
                    }
                }
            } else {
                if self.navigationController?.viewControllers.last == self {
                    self.navigationController?.popViewController(animated: true)
                } else {
                    if var viewControllers = self.navigationController?.viewControllers {
                        if let index = viewControllers.firstIndex(of: self) {
                            viewControllers.remove(at: index)
                        }
                        self.navigationController?.setViewControllers(viewControllers,
                                                                      animated: true)
                    }
                }
            }
        }
    }
    
}

public class WHCommonHalfPlayWebViewController:WHCommonWebViewController {
    
    public var preBackgroundColor: UIColor = UIColor.white
    
    public weak var controller: UIViewController?
    public weak var contentView: UIView?
    public var initHeight: CGFloat = 0
    public var swipeGesture: UISwipeGestureRecognizer?
    public var lastTranslation: CGPoint = CGPointZero
    public var maskTopView: UIView?
    
    public override func viewDidLoad() {
        super.viewDidLoad()
        self.view.backgroundColor = self.preBackgroundColor
        
        let lineContainerView = UIView()
        self.view.addSubview(lineContainerView)
        lineContainerView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(24.0)
        }
        let lineView = UIView()
        lineView.backgroundColor = UIColor(wh_hexString: "#292A2B")
        lineView.layer.cornerRadius = 2.5
        lineView.layer.masksToBounds = true
        lineContainerView.addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(8.0)
            make.size.equalTo(CGSizeMake(32.0, 5.0))
        }
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan))
        lineContainerView.addGestureRecognizer(panGesture)

        self.webView?.snp.remakeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(lineContainerView.snp.bottom)
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 80)
        }
        self.webView?.setWebViewOpaque(false, backgroundColor: WHFigmaColor.backgroundActionBarActionBar)
        
        let topView = UIView()
        topView.backgroundColor = self.preBackgroundColor
        self.view.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WH_SCREEN_BOTTOM_SPACE - 80)
        }
        self.maskTopView = topView
        
        let  selectButton = UIButton(type: .custom)
        selectButton.layer.cornerRadius = 28
        selectButton.setTitle("知道了", for: .normal)
        selectButton.setTitleColor(WHFigmaColor.contentButtonOnMain, for: .normal)
        selectButton.titleLabel?.font = UIFont.pingFangSCFont(ofSize: 17,weight: .medium)
        selectButton.addTarget(self, action: #selector(konwButtonClick), for: .touchUpInside)
        selectButton.layer.cornerRadius = 28
        selectButton.clipsToBounds = true
        selectButton.backgroundColor = WHFigmaColor.backgroundButtonCancel
        self.view.addSubview(selectButton)
        selectButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(self.webView?.snp.bottom ?? 0).offset(16)
            make.height.equalTo(56)
        }
    }
    
    @objc func konwButtonClick(){
        self.closeHalfWeb(gestureRecognizer: nil)
    }
    
    public override func mt_webViewDidStartLoad(_ webView: MTWebViewProtocol) {
        super.mt_webViewDidStartLoad(webView)
        UIView.animate(withDuration: 0.35) {
            self.maskTopView?.alpha = 0.0
        } completion: { finish in
            self.maskTopView?.removeFromSuperview()
        }
    }
    
    public override func mt_webView(_ webView: MTWebViewProtocol, didFailLoadWithError error: Error) {
        super.mt_webView(webView, didFailLoadWithError: error)
        UIView.animate(withDuration: 0.35) {
            self.maskTopView?.alpha = 0.0
        } completion: { finish in
            self.maskTopView?.removeFromSuperview()
        }
    }
    
    public static func webControllerWithUrlString(urlString:String?) -> WHCommonHalfPlayWebViewController {
        let halfWeb = WHCommonHalfPlayWebViewController()
        halfWeb.myNavigationBar.isHidden = true
        halfWeb.urlString = urlString
        return halfWeb
    }
    
    public func showOnViewController(controller:UIViewController,height:CGFloat) {
                
        self.initHeight = height
        self.controller = controller
        let superBounds = controller.view.bounds
        
        let contentView = UIView(frame: superBounds)
        
        let closeTap = UITapGestureRecognizer(target: self, action: #selector(closeHalfWeb))
        contentView.addGestureRecognizer(closeTap)
        controller.view.addSubview(contentView)
        self.contentView = contentView //contentView交给controller强持有,self弱引用
        
        let initFrame = CGRectMake(0, superBounds.size.height, superBounds.size.width, height)
        self.controller?.addChild(self)
        self.view.frame = initFrame
        self.contentView?.addSubview(self.view)
        
        let finialFrame = CGRectMake(0, superBounds.size.height - height, superBounds.size.width, height)
        //模拟底部弹出
        UIView.animate(withDuration: 0.2) {
            contentView.backgroundColor = UIColor.init(white: 0, alpha: 0.7)
            self.view.frame = finialFrame
        } completion: { finished in
        }
        
        self.view.clipCorner(corners: [.topLeft,.topRight], radius: 24.0)
        
        //手势
        if let scrollView = self.webView?.scrollView {
            scrollView.panGestureRecognizer.addTarget(self, action: #selector(handleScrollViewPan))
            self.swipeGesture = UISwipeGestureRecognizer(target: self, action: #selector(handleSwipe))
            self.swipeGesture?.direction = .down
            scrollView.addGestureRecognizer(self.swipeGesture!)
        }
        

    }
    
    @objc public func closeHalfWeb(gestureRecognizer:UITapGestureRecognizer?) {
        let location = gestureRecognizer?.location(in: self.contentView)
        let minHalfWebY = (self.controller?.view.frame.size.height ?? 0) - self.initHeight
        if (location?.y ?? 0) > minHalfWebY {
            return
        }
        let finialFrame = CGRectMake(0, (self.controller?.view.frame.size.height ?? 0), (self.controller?.view.frame.size.width ?? 0), self.view.frame.size.height)
        UIView.animate(withDuration: 0.2) {
            self.view.frame = finialFrame
            self.contentView?.alpha = 0.0
        } completion: { finished in
            self.willMove(toParent: nil)
            self.removeFromParent()
            self.view.removeFromSuperview()
            self.contentView?.removeFromSuperview()
            self.didMove(toParent: nil)
        }

    }
    
    @objc public func handlePan(gesture:UIPanGestureRecognizer) {
        switch gesture.state {
        case .began:
            break
        case .changed:
            let minY = (self.contentView?.frame.size.height ?? 0) - self.initHeight
            let maxY = self.contentView?.frame.size.height ?? 0
            let currentTranslation = gesture.translation(in: gesture.view)
            var rect = self.view.frame
            rect.origin.y += currentTranslation.y
            if rect.origin.y < minY {
                rect.origin.y = minY
            } else if rect.origin.y > maxY {
                rect.origin.y = maxY
            }
            self.view.frame = rect
            gesture.setTranslation(CGPoint.zero, in: gesture.view)
            break
        case .ended,.cancelled:
            self.decideCloseOrRecovery()
        break
        default:
            break
        }
    }
    
    @objc public func handleScrollViewPan(panGesture:UIPanGestureRecognizer) {
        switch panGesture.state {
        case .began:
            break
        case .changed:
            if let scrollView = panGesture.view as? UIScrollView {
                let hasScrollToTop = scrollView.contentOffset.y <= 1
                let translation = panGesture.translation(in: panGesture.view)
                let minY = (self.contentView?.frame.size.height ?? 0) - self.initHeight
                let currentTranslation = CGPointMake(translation.x, translation.y - self.lastTranslation.y)
                
                if hasScrollToTop {
                    let maxY = self.contentView?.frame.size.height ?? 0
                    self.lastTranslation = translation
                    var rect = self.view.frame
                    rect.origin.y += currentTranslation.y
                    if rect.origin.y < minY {
                        rect.origin.y = minY
                    } else if rect.origin.y > maxY {
                        rect.origin.y = maxY
                    }
                    self.view.frame = rect
                    scrollView.contentOffset = CGPoint.zero
                } else {
                    let isUp = translation.y < self.lastTranslation.y
                    let viewHasMoved = self.view.frame.origin.y > minY
                    if isUp && viewHasMoved {
                        scrollView.contentOffset = CGPoint.zero
                        var rect = self.view.frame
                        rect.origin.y += currentTranslation.y
                        //因这里已经判断了向上滑动了，不再检测maxY了
                        if rect.origin.y < minY {
                            rect.origin.y = minY
                        }
                        self.view.frame = rect
                    }
                    self.lastTranslation = translation
                }
            }
            break
        case .ended, .cancelled:
            self.lastTranslation = CGPoint.zero
            self.decideCloseOrRecovery()
            break
        default:
            break
        }

    }
    
    @objc func handleSwipe(swipe:UISwipeGestureRecognizer) {
        if self.swipeGesture?.state == .ended {
            self.closeHalfWeb(gestureRecognizer: nil)
        }
    }
    
    func decideCloseOrRecovery() {
        var rect = self.view.frame
        let minY = (self.contentView?.frame.size.height ?? 0) - self.initHeight
        // CGFloat maxY = self.contentView.frame.size.height
        if rect.origin.y == minY {
            return //已经滑到顶了，does nothing.
        }
        let currentY = rect.origin.y
        if currentY < (minY + self.initHeight / 2.0) {
            //滑动小于一半，还原
            rect.origin.y = minY
            UIView.animate(withDuration: 0.2) {
                self.view.frame = rect
            }
        } else {
            //大于一半了，关闭
            self.closeHalfWeb(gestureRecognizer: nil)
        }
    }
}
