//
//  WHRouterSopResponder.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import WHBaseLibrary

class WHRouterSopResponder: WHRouterBaseResponder {
//    override func setConfig(with config: [String: Any]) {
//        configDict = config
//    }
    
    override func respond(with URL: URL,
                 viewController: UIViewController? = nil,
                 params: [String: Any]? = nil,
                 application: UIApplication? = nil) {
        mergeRouteParams(with: URL, params: params)
        prepareRoute(with: viewController,
                          application: application)
    
        let schemeModel = WHRouterSchemeModel.modelWithSchemeUrl(schemeUrl: URL)
        if let schemeModel = schemeModel {
            if WHAccountShareManager.isLogin() {
                WHSopFlowManager.shared.startSop(routerModel: schemeModel)
            } else {
                // 未登录，显示登录界面
                WHLoginViewsharedManager.showAccountLoginWith(hostVC: WHRouter.topViewController ?? WHViewController(), loginType: .half) { (success, info) in
                    if success {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                            WHSopFlowManager.shared.startSop(routerModel: schemeModel)
                        }
                    }
                } failure: {
                    // 登录失败处理
                }
            }
        } else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("参数异常，请重试"))
        }
    }
}
