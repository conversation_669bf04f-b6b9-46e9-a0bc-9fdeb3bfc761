//
//  WHSopFlowManager.swift
//  WHBusinessCommon
//
//  Created by 王耀 on 2025/9/10.
//

import WHBaseLibrary
import MTPhotoLibrary

//流程如下：
//1.全屏中转介绍页---可能无
//2.半窗图片要求介绍页---可能无
//3.相册选图
//4.图片上传、安审
//5.任务创建
//6.结果轮询/结果feed等待

public class WHSopFlowManager : NSObject {
    public static let shared = WHSopFlowManager()
    
    var source:String = ""   //来源
    var sopGroupId:String = ""  //sop组ID,用于中转介绍页
    var sopId:String = ""  //sopId
    var showUploadTips:Bool = false //是否展示图片要求页
    var imageSelectLevel:WHAlbumSelectLevel = .normal //选图级别
    
    private var currentUseSopId:String = "" //当前使用的sopID,这里无值时，使用sopId（这里考虑套娃出现的manager复用问题）
    private var currentUploadTaskId:String = "" //当前上传图片的任务ID
    private var currentImageSize:CGSize = CGSizeZero //当前选中图片的size
    private var currentMTCCFunction:String = "" //当前sop的mtcc上报functionName
    
    private var photoSelecter:WHCommonAlbumSelect? //相册选择器
    
    //开始SOP流程
    public func startSop(routerModel:WHRouterSchemeModel) {
        source = routerModel.queryDic?["source"] as? String ?? ""
        sopGroupId = routerModel.queryDic?["sop_group_id"] as? String ?? ""
        sopId = routerModel.queryDic?["sop_id"] as? String ?? ""
        let uploadTips = routerModel.queryDic?["show_uploadTips"] as? String ?? ""
        showUploadTips = uploadTips == "1" ? true : false
        let imageLevel = Int(routerModel.queryDic?["imagelevel"] as? String ?? "0") ?? 0
        imageSelectLevel = WHAlbumSelectLevel(rawValue: imageLevel) ?? .normal
        
        if sopGroupId.count <= 0,sopId.count <= 0 { //组ID，sopID至少要有一个
            UIViewController.wh_top().showToast(title: WHLocalizedString("参数错误"))
            return;
        }
        //重置状态
        currentUseSopId = ""
        currentUploadTaskId = ""
        currentImageSize = CGSizeZero
        currentMTCCFunction = ""
        //开始流程
        showFullScreenIntroduceViewIfNeeded()
    }
    //step1:中转过渡页
    func showFullScreenIntroduceViewIfNeeded() {
        if self.sopGroupId.count > 0 { //需要展示过渡介绍页
            let preVC = WHSOPPreViewController()
            preVC.id = sopGroupId
            preVC.estimatedCount = 3
            preVC.delegate = self
            UIViewController.wh_top().wh_show(preVC)
        } else {
            showAblumAndImageRequireIfNeeded(sopId: sopId, funcStandardId: sopId) //单一SOP
        }
    }
    //step2:相册&选图规范
    func showAblumAndImageRequireIfNeeded(sopId:String,funcStandardId:String) {
        let selecter = WHCommonAlbumSelect()
        self.photoSelecter = selecter
        let spUrl = getUploadSpecificationUrlIfNeeded(funcStandardId: funcStandardId)
        selecter.selectPhotoWithParmas(delegate: self,selectType: .picLocalCheck, photoLibraryType: .sop,albumStyle: .onlyPhotos,selectLevel: imageSelectLevel,uploadSpecificationUrl: spUrl)
    }
    //step3:创建
    func createSopTask(imageUrl:String) {
        let params = ["template_id":self.currentUseSopId.count > 0 ? Int(self.currentUseSopId) : Int(self.sopId),
                      "init_image":imageUrl,
                      "width":Int(self.currentImageSize.width),
                      "height":Int(self.currentImageSize.height)] as [String : Any]
        WHSopRequest.requestSopDo(mtccFunctionName: self.currentMTCCFunction,params: params) {[weak self] whResponse in
            guard let self = self else { return }
            
        }
    }
    
    //获取上传规范H5地址
    func getUploadSpecificationUrlIfNeeded(funcStandardId:String) -> String {
        let urlString = WHPlayIntroduceLinkType.sopUploadSpecification.typelink(temId: funcStandardId,source: source)
        
        //本地校验是否已经弹过了，弹过的就返回空地址
        return urlString
    }
    
}
//过渡介绍页的代理
extension WHSopFlowManager: WHSOPPreViewControllerDelegate {
    //用户点击了立即体验
    func sopPreViewController(_ viewController: WHSOPPreViewController, didSelectItem item: WHSOPPreItemModel,functionCode: String) {
        self.currentMTCCFunction = functionCode
        showAblumAndImageRequireIfNeeded(sopId: String(item.id), funcStandardId: String(item.funcStandardId))
    }
}

extension WHSopFlowManager: WHCommonAlbumSelectDelegate {
    public func wh_albumSelectImage(assetModels: [WHCommonSelectAssetModel]) {
        guard let assetModel = assetModels.object(at: 0),let uploadImage = assetModel.image else {
            UIViewController.wh_top().showToast(title: WHLocalizedString("当前识别不到内容，请重试或更换图片"))
            return
        }
        self.currentImageSize = uploadImage.size
        //上传图片
        let uploadTaskID = UUID().uuidString
        self.currentUploadTaskId = uploadTaskID
        WHCommonPicUploadManager.addUploadPic(uploadImg: uploadImage, mtResource: assetModel.mtResource, suffix: assetModel.imageSuffix ?? "", taskID: uploadTaskID, isNeedSafeCheck: true,photoLibraryType:.sop, listener: self)
    }
    
    public func wh_albumSelectCancel() {
        
    }
    
    public func wh_albumSelectFail() {
        
    }
}

extension WHSopFlowManager: WHCommonPicUploadDelegate {
    public func wh_uploadSucceed(taskID: String, urlString: String) {
        if taskID == self.currentUploadTaskId {
            createSopTask(imageUrl: urlString)
        }
    }
    
    public func wh_uploadFail(taskID: String, error: (any Error)?, stage: WHCommonUploadFailStage) {
        UIViewController.wh_top().showToast(title: error?.localizedDescription ?? WHLocalizedString("发生错误，请重试"))
    }
    
    public func wh_uploadProgress(taskID: String, progress: CGFloat) {
        
    }
}
