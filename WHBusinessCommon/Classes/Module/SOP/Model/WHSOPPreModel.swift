//
//  WHSOPPreModel.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/12.
//

import Foundation
import YYModel

@objcMembers
class WHSOPPreModel: NSObject, YYModel, NSCoding {
    
    var name: String?
    var desc: String?
    var index: Int = 0
    var templates: [WHSOPPreItemModel] = []
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return [
            "templates": WHSOPPreItemModel.classForCoder(),
        ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "name": "name",
            "desc": "desc",
            "index": "selected_index",
            "templates": "templates",
        ]
    }
}

@objcMembers
class WHSOPPreItemModel: NSObject, YYModel, NSCoding {
    
    var id: Int = 0
    var name: String?
    var preview: WHSOPPreItemPreviewModel?
    var coverUrl: String = ""
    var limitVip: Bool = false
    var funcStandardId: Int = 0 //上传规范ID，如果配置了跳转到规范详情
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelContainerPropertyGenericClass() -> [String : Any]? {
        return [
            "preview": WHSOPPreItemPreviewModel.classForCoder(),
        ]
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "id": "id",
            "name": "name",
            "preview": "preview",
            "coverUrl": "cover_url",
            "limitVip": "limit_vip",
            "funcStandardId": "func_standard_id",
        ]
    }
}

@objcMembers
class WHSOPPreItemPreviewModel: NSObject, YYModel, NSCoding {
    
    var type: String = ""
    var coverUrl: String = ""
    var url: String = ""
    var mainColor: String = ""
    
    public required override init() {
        super.init()
    }
    
    public func encode(with coder: NSCoder) {
        self.yy_modelEncode(with: coder)
    }
    
    public required convenience init?(coder: NSCoder) {
        self.init()
        self.yy_modelInit(with: coder)
    }
    
    public class func modelCustomPropertyMapper() -> [String : Any]? {
        return [
            "type": "type",
            "coverUrl": "cover_url",
            "url": "url",
            "mainColor": "main_color",
        ]
    }
}
