//
//  WHSopRequest.swift
//  MeituWhee
//
//  Created by 王耀 on 2025/6/27.
//

import WHBaseLibrary
import Alamofire

class WHSopRequest: NSObject {
    
    //创建视频超清
    public static func requestSopDo(mtccFunctionName:String = "whee.freeformula" ,params: [String: Any], completion: ((_ whResponse: WHOriginalResponse) -> Void)?) {
        let httpHeader = HTTPHeader(name: "x-mtcc-client", value: WHSharedRequest.requestHeadersMTCC(position_level1: "CreativeTools", function_name: mtccFunctionName))
        WHSharedRequest.POST("/ai_formula/do.json",params: params,headers: [httpHeader]) { response in
            completion?(response)
        }
    }
}
