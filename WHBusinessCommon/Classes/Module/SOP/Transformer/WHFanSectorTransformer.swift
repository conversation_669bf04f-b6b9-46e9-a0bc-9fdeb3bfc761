//
//  WHFanSectorTransformer.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation

class WHFanSectorTransformer: WHPagerViewBaseTransformer {
    
    let count: Int
    
    init(count: Int) {
        self.count = count
        super.init(type: .ferrisWheel)
    }
    
    override func applyTransform(to attributes: FSPagerViewLayoutAttributes) {
        super.applyTransform(to: attributes)
        guard let pagerView = self.pagerView,
              pagerView.scrollDirection == .horizontal else {
            return
        }
        let position = attributes.position
        var zIndex = 0
        var transform = CGAffineTransform.identity
        switch position {
        case -5 ... 5:
            let itemSpacing = attributes.bounds.width + proposedInteritemSpacing()
            let circle: CGFloat = .pi * 2.0
            let radius = itemSpacing * CGFloat(count) / circle
            let theta = circle / CGFloat(count)
            let rotation = position * theta
            transform = transform.translatedBy(x: -position * itemSpacing, y: radius)
            transform = transform.rotated(by: rotation)
            transform = transform.translatedBy(x: 0, y: -radius)
            zIndex = Int((4.0-abs(position)*10))
        default:
            break
        }
        attributes.transform = transform
        attributes.zIndex = zIndex
    }
    
    override func proposedInteritemSpacing() -> CGFloat {
        return pagerView?.interitemSpacing ?? 0
    }
    
}
