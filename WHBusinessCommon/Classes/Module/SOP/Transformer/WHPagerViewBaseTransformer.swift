//
//  WHPagerViewBaseTransformer.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation

class WHPagerViewBaseTransformer: FSPagerViewTransformer {
    
    override func applyTransform(to attributes: FSPagerViewLayoutAttributes) {
        guard let pagerView = self.pagerView else {
            return
        }
        if let cell = pagerView.cellForItem(at: attributes.indexPath.row) as? WHPagerViewBaseCell {
            cell.setOffsetRelativeCenter(attributes.position)
        } else {
            // 第一次执行时，需要在下一个runloop获取cell，当前runloop获取不到
            DispatchQueue.main.async {
                if let cell = pagerView.cellForItem(at: attributes.indexPath.row) as? WHPagerViewBaseCell {
                    cell.setOffsetRelativeCenter(attributes.position)
                }
            }
        }
    }
    
}
