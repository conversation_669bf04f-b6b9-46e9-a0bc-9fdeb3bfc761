//
//  WHSegmentedTransformer.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation

class WHSegmentedTransformer: WHPagerViewBaseTransformer {
    
    init() {
        super.init(type: .linear)
    }
    
    override func applyTransform(to attributes: FSPagerViewLayoutAttributes) {
        super.applyTransform(to: attributes)
    }
    
    override func proposedInteritemSpacing() -> CGFloat {
        return pagerView?.interitemSpacing ?? 0
    }
    
}
