//
//  WHPagerView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation

class WHPagerView: FSPagerView {
    
    func setScrollOffset(_ offset: CGFloat) {
        guard let layout = collectionViewLayout as? FSPagerViewLayout else {
            return
        }
        var targetOffset: CGPoint
        switch scrollDirection {
        case .horizontal:
            targetOffset = CGPoint(x: offset * layout.itemSpacing, y: 0)
        case .vertical:
            targetOffset = CGPoint(x: 0, y: offset * layout.itemSpacing)
        }
        collectionView.setContentOffset(targetOffset, animated: false)
    }
    
}
