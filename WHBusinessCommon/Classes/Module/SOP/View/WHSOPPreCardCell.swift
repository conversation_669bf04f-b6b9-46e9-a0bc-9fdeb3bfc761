//
//  WHSOPPreCardCell.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/11.
//

import Foundation

class WHSOPPreCardCell: WHPagerViewBaseCell {
    
    private let maxScale: CGFloat = 1.2
    
    private lazy var cardView: WHSOPPreCardView = {
        let view = WHSOPPreCardView()
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func setOffsetRelativeCenter(_ offset: CGFloat) {
        let scale = min(1 + (maxScale - 1) * abs(offset), maxScale)
        cardView.setContentTransform(CGAffineTransform(scaleX: scale, y: scale))
    }
    
    func configData(with model: WHSOPPreItemModel) {
        cardView.configData(with: model)
    }
    
    func didSelected() {
        cardView.didSelected()
    }
    
    func stopVideoIfNeeds() {
        cardView.stopVideoIfNeeds()
    }
    
    private func setupSubviews() {
        contentView.addSubview(cardView)
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
}
