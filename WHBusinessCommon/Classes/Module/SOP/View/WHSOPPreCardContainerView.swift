//
//  WHSOPPreCardContainerView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/13.
//

import Foundation

class WHSOPPreCardContainerView: UIView {
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 28, weight: .semibold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    lazy var descLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 13)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private(set) var model: WHSOPPreModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configData(with model: WHSOPPreModel) {
        self.model = model
        titleLabel.text = model.name
        descLabel.text = model.desc
    }
    
    func setupSubviews() {
    }
    
    func willAppear() {
    }
    
    func willDisappear() {
    }
    
}
