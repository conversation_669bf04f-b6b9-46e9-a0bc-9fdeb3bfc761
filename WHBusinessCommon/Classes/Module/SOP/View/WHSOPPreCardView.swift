//
//  WHSOPPreCardView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/11.
//

import Foundation
import WHBaseLibrary

class WHSOPPreCardView: UIView {
        
    private lazy var imgView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        return view
    }()
    
    private var model: WHSOPPreItemModel?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setContentTransform(_ transform: CGAffineTransform) {
        imgView.transform = transform
    }
    
    func configData(with model: WHSOPPreItemModel) {
        self.model = model
        if let cover = model.preview?.coverUrl,
           let url = URL(string: cover) {
            imgView.sd_setImage(with: url)
        } else {
            imgView.image = nil
        }
    }
    
    func didSelected() {
        guard let model = model else {
            return
        }
        if model.preview?.type == "video",
           let url = model.preview?.url,
           url.count > 0 {
            
            // 获取配置，低端机是否播视频
            let (isPlayLimit, device) = WHConfigCenterManager.introducePlayLimit()
            if isPlayLimit,
               WHDeviceSupport.compare(WHDeviceModel_iPhone(rawValue: device) ?? .iPhoneXR, compareBlock: { current, target in
                   current <= target
               }) {
                return
            }
            
            guard subviews.contains(VideoPlayerManager.shared.playerContainer) == false else {
                return
            }
            
            VideoPlayerManager.shared.load(videoId: String(model.id),
                                           url: url,
                                           coverImage: nil,
                                           on: self,
                                           autoLoop: true,
                                           externalPlayer: nil,
                                           preservedCallbacks: [])?
                .playing(capture: self, block: { object, currentId in
                    object.sendSubviewToBack(object.imgView)
                })
                .done()
            VideoPlayerManager.shared.player.view.contentMode = .scaleAspectFill
            VideoPlayerManager.shared.set(mute: true, manual: false)
            VideoPlayerManager.shared.play()
            bringSubviewToFront(imgView)
        }
    }
    
    func stopVideoIfNeeds() {
        guard subviews.contains(VideoPlayerManager.shared.playerContainer) else {
            return
        }
        VideoPlayerManager.shared.clearDiskCache {
        }
    }
    
    private func setupSubviews() {
        backgroundColor = UIColor(rgb: 0x24262B)
        layer.allowsEdgeAntialiasing = true
        layer.shouldRasterize = true
        layer.rasterizationScale = UIScreen.main.scale
        layer.cornerRadius = 24
        layer.borderColor = UIColor(rgb: 0xffffff, alpha: 0.1).cgColor
        layer.borderWidth = 1
        layer.masksToBounds = true
        
        addSubview(imgView)
        imgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
}
