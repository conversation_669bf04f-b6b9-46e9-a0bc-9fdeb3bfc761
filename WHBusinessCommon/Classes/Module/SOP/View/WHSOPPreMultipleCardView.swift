//
//  WHSOPPreMultipleCardView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/11.
//

import Foundation

protocol WHSOPPreMultipleCardViewDelegate: NSObjectProtocol {
    
    func multipleCardView(_ view: WHSOPPreMultipleCardView, didSelectItemAt index: Int)
    
}

class WHSOPPreMultipleCardView: WHSOPPreCardContainerView {
    
    weak var delegate: WHSOPPreMultipleCardViewDelegate?
    
    private lazy var cardView: WHPagerView = {
        let view = WHPagerView()
        view.dataSource = self
        view.delegate = self
        view.register(WHSOPPreCardCell.self, forCellWithReuseIdentifier: "WHSOPPreCardCell")
        view.itemSize = WHSOPPreViewLayoutConstants.cardSize
        view.interitemSpacing = WHSOPPreViewLayoutConstants.cardSpace
        view.transformer = WHFanSectorTransformer(count: 72)
        return view
    }()
    
    private lazy var segmentedView: WHSOPPreSegmentedView = {
        let view = WHSOPPreSegmentedView()
        view.delegate = self
        return view
    }()
        
    private var isScrollSyncing = false
    
    override func configData(with model: WHSOPPreModel) {
        super.configData(with: model)
        cardView.reloadData()
        segmentedView.configData(with: model.templates)
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                return
            }
            var index = model.index
            if index >= model.templates.count {
                index = 0
            }
            if index > 0 {
                self.cardView.scrollToItem(at: index, animated: false)
            }
            self.delegate?.multipleCardView(self, didSelectItemAt: index)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.playVideoIfNeeds(with: index)
            }
        }
    }
    
    override func setupSubviews() {
        addSubview(cardView)
        addSubview(segmentedView)
        addSubview(titleLabel)
        addSubview(descLabel)
        cardView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(WHSOPPreViewLayoutConstants.cardViewHeight)
        }
        segmentedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(titleLabel.snp.top).offset(-WHSOPPreViewLayoutConstants.segmentedBottomSpace)
            make.height.equalTo(WHSOPPreSegmentedView.Height)
        }
        titleLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(descLabel.snp.top).offset(-WHSOPPreViewLayoutConstants.titleBottomSpace)
            make.height.equalTo(WHSOPPreViewLayoutConstants.titleHeight)
        }
        descLabel.snp.makeConstraints { make in
            make.left.right.equalTo(titleLabel)
            make.bottom.equalToSuperview().inset(WHSOPPreViewLayoutConstants.descBottomSpace)
            make.height.equalTo(WHSOPPreViewLayoutConstants.descHeight)
        }
    }
    
    override func willAppear() {
        playVideoIfNeeds(with: cardView.currentIndex)
    }
    
    override func willDisappear() {
        if let cell = cardView.cellForItem(at: cardView.currentIndex) as? WHSOPPreCardCell {
            cell.stopVideoIfNeeds()
        }
    }
    
    private func linkScrollWith(view: WHPagerView?, offset: CGFloat) {
        guard isScrollSyncing == false else {
            return
        }
        isScrollSyncing = true
        if view == cardView {
            segmentedView.setScrollOffset(offset)
        } else {
            cardView.setScrollOffset(offset)
        }
        isScrollSyncing = false
    }
    
    private func playVideoIfNeeds(with index: Int) {
        if let cell = cardView.cellForItem(at: index) as? WHSOPPreCardCell {
            cell.didSelected()
        }
    }
    
}

// MARK: - FSPagerViewDataSource
extension WHSOPPreMultipleCardView: FSPagerViewDataSource {
    
    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return model?.templates.count ?? 0
    }
    
    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "WHSOPPreCardCell", at: index)
        if let cell = cell as? WHSOPPreCardCell,
           let datas = model?.templates {
            cell.configData(with: datas[index])
        }
        return cell
    }
    
}

// MARK: - FSPagerViewDelegate
extension WHSOPPreMultipleCardView: FSPagerViewDelegate {
    
    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        linkScrollWith(view: cardView, offset: cardView.scrollOffset)
    }
    
    func pagerViewDidEndDecelerating(_ pagerView: FSPagerView) {
        delegate?.multipleCardView(self, didSelectItemAt: pagerView.currentIndex)
        // 滑动卡片结束回调
        playVideoIfNeeds(with: pagerView.currentIndex)
    }
    
    func pagerViewDidEndScrollAnimation(_ pagerView: FSPagerView) {
        // 点击下方按钮，执行动画选中时回调
        playVideoIfNeeds(with: pagerView.currentIndex)
    }
    
}

// MARK: - WHSOPPreSegmentedViewDelegate
extension WHSOPPreMultipleCardView: WHSOPPreSegmentedViewDelegate {
    
    func segmentedView(_ view: WHSOPPreSegmentedView, didScrollWith offset: CGFloat) {
        linkScrollWith(view: nil, offset: offset)
    }
    
    func segmentedView(_ view: WHSOPPreSegmentedView, didSelectItemAt index: Int, byDrag drag: Bool) {
        if drag {
            // 滑动下方按钮结束回调
            // card选中是通过滑动联动实现，所以这里不需要执行选中逻辑
            playVideoIfNeeds(with: index)
        } else {
            cardView.selectItem(at: index, animated: true)
        }
        delegate?.multipleCardView(self, didSelectItemAt: index)
    }
    
}
