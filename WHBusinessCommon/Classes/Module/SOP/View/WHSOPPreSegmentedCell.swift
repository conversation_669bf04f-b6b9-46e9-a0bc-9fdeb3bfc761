//
//  WHSOPPreSegmentedCell.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation
import WHBaseLibrary
import SnapKit

class WHSOPPreSegmentedCell: WHPagerViewBaseCell {
    
    static let CellSize: CGSize = CGSize(width: 56, height: 90)
    
    private let minimumScale: CGFloat = 52 / 56
    
    private lazy var imgView: UIImageView = {
        let view = UIImageView()
        view.backgroundColor = UIColor(rgb: 0x24262B)
        view.contentMode = .scaleAspectFill
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .pingFangSCFont(ofSize: 10)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var vipIcon: UIImageView = {
        let view = UIImageView(image: UIImage(cm_named: "wh_sop_segmented_vip"))
        return view
    }()
    
    public var vipInsetConstraint: Constraint?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(imgView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(vipIcon)
        imgView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(6)
            make.left.right.equalToSuperview()
            make.height.equalTo(imgView.snp.width)
        }
        titleLabel.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(16)
        }
        vipIcon.snp.makeConstraints { make in
            vipInsetConstraint = make.top.right.equalTo(imgView).inset(0).constraint
            make.width.height.equalTo(16)
        }
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func setOffsetRelativeCenter(_ offset: CGFloat) {
        let scale = max(1 - (1 - minimumScale) * abs(offset), minimumScale)
        imgView.transform = CGAffineTransform(scaleX: scale, y: scale)
        imgView.layer.cornerRadius = 12 + min(4 * abs(offset), 4)
        vipInsetConstraint?.update(inset: min(2 * abs(offset), 2))
    }
    
    func configData(with model: WHSOPPreItemModel) {
        if let url = URL(string: model.coverUrl) {
            imgView.sd_setImage(with: url)
        } else {
            imgView.image = nil
        }
        titleLabel.text = model.name
        vipIcon.isHidden = !model.limitVip
    }
    
}
