//
//  WHSOPPreSegmentedView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/10.
//

import Foundation
import WHBaseLibrary

protocol WHSOPPreSegmentedViewDelegate: NSObjectProtocol {
    
    func segmentedView(_ view: WHSOPPreSegmentedView, didSelectItemAt index: Int, byDrag drag: Bool)
    func segmentedView(_ view: WHSOPPreSegmentedView, didScrollWith offset: CGFloat)
    
}

class WHSOPPreSegmentedView: UIView, FSPagerViewDataSource, FSPagerViewDelegate {
    
    weak var delegate: WHSOPPreSegmentedViewDelegate?
    
    static var Height: CGFloat {
        return WHSOPPreSegmentedCell.CellSize.height
    }
    
    private lazy var pagerView: WHPagerView = {
        let view = WHPagerView()
        view.dataSource = self
        view.delegate = self
        view.itemSize = WHSOPPreSegmentedCell.CellSize
        view.interitemSpacing = 24
        view.transformer = WHSegmentedTransformer()
        view.register(WHSOPPreSegmentedCell.self,
                      forCellWithReuseIdentifier: "WHSOPPreSegmentedCell")
        return view
    }()
    
    private lazy var selectedView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = false
        view.layer.cornerRadius = 18
        view.layer.borderColor = UIColor.white.cgColor
        view.layer.borderWidth = 2
        return view
    }()
    
    private var models: [WHSOPPreItemModel] = []
    private let feedback = UIImpactFeedbackGenerator(style: .medium)
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
        feedback.prepare()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setScrollOffset(_ offset: CGFloat) {
        pagerView.setScrollOffset(offset)
    }
    
    func configData(with models: [WHSOPPreItemModel]) {
        self.models = models
        pagerView.reloadData()
    }
    
    // MARK: - Private Methods
    private func setupSubviews() {
        let leftGradientView = UIView()
        leftGradientView.isUserInteractionEnabled = false
        leftGradientView.wh_enableGradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)),
                                           colors: [UIColor(rgb: 0x000000),
                                                    UIColor(rgb: 0x000000, alpha: 0)],
                                           locations: [0, 1])
        let rightGradientView = UIView()
        rightGradientView.isUserInteractionEnabled = false
        rightGradientView.wh_enableGradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)),
                                            colors: [UIColor(rgb: 0x000000, alpha: 0),
                                                     UIColor(rgb: 0x000000)],
                                            locations: [0, 1])
        addSubview(pagerView)
        addSubview(selectedView)
//        addSubview(leftGradientView)
//        addSubview(rightGradientView)
        pagerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        selectedView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(68)
        }
//        leftGradientView.snp.makeConstraints { make in
//            make.left.top.bottom.equalToSuperview()
//            make.width.equalTo(WH_SCREEN_WIDTH / 10)
//        }
//        rightGradientView.snp.makeConstraints { make in
//            make.right.top.bottom.equalToSuperview()
//            make.width.equalTo(WH_SCREEN_WIDTH / 10)
//        }
    }
    
    // MARK: - FSPagerViewDataSource
    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return models.count
    }
    
    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "WHSOPPreSegmentedCell", at: index)
        if let cell = cell as? WHSOPPreSegmentedCell {
            cell.configData(with: models[index])
        }
        return cell
    }
    
    // MARK: - FSPagerViewDelegate
    func pagerView(_ pagerView: FSPagerView, didSelectItemAt index: Int) {
        pagerView.scrollToItem(at: index, animated: true)
        delegate?.segmentedView(self, didSelectItemAt: index, byDrag: false)
        // 点击 cell 时震动
        feedback.impactOccurred()
    }
    
    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        delegate?.segmentedView(self, didScrollWith: pagerView.scrollOffset)
    }
    
    func pagerViewDidEndDecelerating(_ pagerView: FSPagerView) {
        // 滑动结束时震动
        feedback.impactOccurred()
        delegate?.segmentedView(self, didSelectItemAt: pagerView.currentIndex, byDrag: true)
    }
    
}
