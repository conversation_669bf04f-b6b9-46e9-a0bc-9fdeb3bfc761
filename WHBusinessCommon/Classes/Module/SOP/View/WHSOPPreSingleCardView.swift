//
//  WHSOPPreSingleCardView.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/11.
//

import Foundation

class WHSOPPreSingleCardView: WHSOPPreCardContainerView {
    
    private lazy var cardContentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var cardView: WHSOPPreCardView = {
        let view = WHSOPPreCardView()
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func configData(with model: WHSOPPreModel) {
        super.configData(with: model)
        if model.templates.count > 0 {
            cardView.configData(with: model.templates[0])
            cardView.didSelected()
        }
    }
    
    override func setupSubviews() {
        addSubview(cardContentView)
        addSubview(titleLabel)
        addSubview(descLabel)
        cardContentView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(titleLabel.snp.top).offset(-WHSOPPreViewLayoutConstants.cardViewBottomSpace1)
        }
        titleLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(descLabel.snp.top).offset(-WHSOPPreViewLayoutConstants.titleBottomSpace)
            make.height.equalTo(WHSOPPreViewLayoutConstants.titleHeight)
        }
        descLabel.snp.makeConstraints { make in
            make.left.right.equalTo(titleLabel)
            make.bottom.equalToSuperview().inset(WHSOPPreViewLayoutConstants.descBottomSpace1)
            make.height.equalTo(WHSOPPreViewLayoutConstants.descHeight)
        }
        
        cardContentView.addSubview(cardView)
        cardView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(WHSOPPreViewLayoutConstants.cardSize1)
        }
    }
    
    override func willAppear() {
        cardView.didSelected()
    }
    
    override func willDisappear() {
        cardView.stopVideoIfNeeds()
    }
    
}
