//
//  WHSOPDetailsViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/13.
//

import Foundation
import WHBaseLibrary

class WHSOPDetailsViewController: WHViewController {
    
    private lazy var backBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_navigation_bar_back_white"), for: .normal)
        btn.addTarget(self, action: #selector(backAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var shareBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_navigation_bar_share_white"), for: .normal)
        btn.addTarget(self, action: #selector(shareAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var slider: UISlider = {
        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.value = 0
        slider.minimumTrackTintColor = .white
        slider.maximumTrackTintColor = UIColor(rgb: 0x6A6A6A, alpha: 0.3)
        slider.setThumbImage(UIImage(cm_named: "wh_sop_video_slider_thumb"), for: .normal)
        slider.addTarget(self,
                         action: #selector(progressSliderValueChanged(_:event:)),
                         for: .valueChanged)
        return slider
    }()
    
    override func viewDidLoad() {
        navigationBarStyle = .none
        super.viewDidLoad()
    }
    
    override func setupSubviews() {
        super.setupSubviews()
        view.backgroundColor = .black
        view.addSubview(backBtn)
        view.addSubview(shareBtn)
        view.addSubview(slider)

        backBtn.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(WH_STATUS_BAR_HEIGHT + 2)
            make.left.equalToSuperview().inset(4)
            make.width.height.equalTo(40)
        }
        shareBtn.snp.makeConstraints { make in
            make.size.centerY.equalTo(backBtn)
            make.right.equalToSuperview().inset(4)
        }
        slider.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Events
    @objc
    private func backAction(_ sender: UIButton) {
        wh_handleEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
    }
    
    @objc
    private func shareAction(_ sender: UIButton) {
        
    }
    
    @objc
    private func progressSliderValueChanged(_ sender: UISlider,
                                            event: UIEvent) {
//        guard let touchEvent = event.allTouches?.first else {
//            return
//        }
//        let position = Int(Float(totalDuration) * sender.value)
//        switch touchEvent.phase {
//        case .began:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchDown)
//        case .moved:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchSlide)
//        case .ended:
//            compareTool?.player?.seek(toPosition: position, sliderEvent: .touchUp)
//        default:
//            break
//        }
//        progressDidChange()
    }
    
}
