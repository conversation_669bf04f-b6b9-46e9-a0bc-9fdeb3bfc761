//
//  WHSOPPreViewController.swift
//  WHBusinessCommon
//
//  Created by <PERSON> on 2025/9/9.
//

import Foundation
import WHBaseLibrary

protocol WHSOPPreViewControllerDelegate: NSObjectProtocol {
    
    func sopPreViewController(_ viewController: WHSOPPreViewController,
                              didSelectItem item: WHSOPPreItemModel,
                              functionCode: String)
    
}

class WHSOPPreViewController: WHViewController {
        
    // MARK: - Properties
    
    weak var delegate: WHSOPPreViewControllerDelegate?
    
    var id: String?
    
    var estimatedCount: Int = 0 {
        didSet {
            guard estimatedCount > 0 else {
                return
            }
            let aModel = WHSOPPreModel()
            aModel.index = estimatedCount / 2
            let itemModel = WHSOPPreItemModel()
            aModel.templates = Array(repeating: itemModel, count: estimatedCount)
            model = aModel
        }
    }
    
    private lazy var backBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_navigation_bar_back_white"), for: .normal)
        btn.addTarget(self, action: #selector(backAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var historyBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(cm_named: "wh_navigation_bar_history_white"), for: .normal)
        btn.addTarget(self, action: #selector(historyAction(_:)), for: .touchUpInside)
        return btn
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var gradientView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var multipleCardView: WHSOPPreMultipleCardView = {
        let view = WHSOPPreMultipleCardView()
        view.delegate = self
        return view
    }()
    
    private lazy var singleCardView: WHSOPPreSingleCardView = {
        let view = WHSOPPreSingleCardView()
        return view
    }()
    
    private lazy var doneBtn: WHStateButton = {
        let btn = WHStateButton()
        btn.layer.cornerRadius = 16
        btn.addTarget(self, action: #selector(doneAction), for: .touchUpInside)
        return btn
    }()
    
    private var model: WHSOPPreModel? {
        didSet {
            guard let model = model,
                  model.templates.count > 0 else {
                return
            }
            multipleCardView.isHidden = model.templates.count <= 1
            singleCardView.isHidden = model.templates.count > 1
            if model.templates.count > 1 {
                multipleCardView.configData(with: model)
            } else {
                singleCardView.configData(with: model)
                selectedItem(with: 0)
            }
        }
    }
    private var currentItem: WHSOPPreItemModel?
    private var priceDict: [Int: WHGcPriceModel] = [:]
    private var balance: WHBeansBalanceModel?
        
    // MARK: - Lifecycle
    override func loadRouteParams(_ params: [String : Any]) {
        if let id = params["id"] as? Int {
            self.id = String(id)
        } else if let id = params["id"] as? String {
            self.id = id
        }
        
        if let count = params["estimatedCount"] as? Int {
            estimatedCount = count
        } else if let countStr = params["estimatedCount"] as? String {
            estimatedCount = Int(countStr) ?? 0
        }
    }
    
    override func viewDidLoad() {
        navigationBarStyle = .none
        super.viewDidLoad()
        loadData()
        loadBalance()
        addObserver()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        multipleCardView.willAppear()
        singleCardView.willAppear()
        if viewIsLoaded {
            loadBalance()
            loadItemPrice()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        multipleCardView.willDisappear()
        singleCardView.willAppear()
    }
    
    // MARK: - Override
    override func setupSubviews() {
        super.setupSubviews()
        view.backgroundColor = .black
        view.addSubview(backBtn)
        view.addSubview(historyBtn)
        view.addSubview(contentView)
        contentView.addSubview(gradientView)
        contentView.addSubview(multipleCardView)
        contentView.addSubview(singleCardView)
        contentView.addSubview(doneBtn)
        backBtn.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(WH_STATUS_BAR_HEIGHT + 2)
            make.left.equalToSuperview().inset(4)
            make.width.height.equalTo(40)
        }
        historyBtn.snp.makeConstraints { make in
            make.size.centerY.equalTo(backBtn)
            make.right.equalToSuperview().inset(4)
        }
        contentView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(WH_NAVIGATION_BAR_HEIGHT)
            make.left.right.bottom.equalToSuperview()
        }
        gradientView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(WHSOPPreViewLayoutConstants.gradientHeight)
        }
        multipleCardView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(doneBtn.snp.top)
        }
        singleCardView.snp.makeConstraints { make in
            make.edges.equalTo(multipleCardView)
        }
        doneBtn.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(WHSOPPreViewLayoutConstants.descBottomSpace)
            make.height.equalTo(WHSOPPreViewLayoutConstants.doneBtnHeight)
        }
        
        refreshGradientView()
        refreshDoneBtn()
    }
    
    open override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    // MARK: - Events
    @objc
    private func backAction(_ sender: UIButton) {
        wh_handleEventWith(WH_NAVIGATION_BACK_EVENT_NAME)
    }
    
    @objc
    private func historyAction(_ sender: UIButton) {
//        let vc = WHMineWorksViewController()
//        vc.worksType = .myWorks
//        vc.worksTabsType = .aiMagicsr
//        self.navigationController?.pushViewController(vc, animated: true)
        let vc = WHSOPDetailsViewController()
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc
    private func doneAction() {
        guard let currentItem = currentItem else {
            return
        }
        
        if let price = priceDict[currentItem.id] {
            if price.freeNum <= 0 {
                if price.isVip {
                    if price.rightNum <= 0,
                       let availableAmount = Int(balance?.availableAmount ?? "0"),
                       let priceAmount = Int(price.amount ?? "0"),
                       availableAmount < priceAmount {
                        // 调起美豆半窗
                        let params = WHSubMeidouWindowNotificationParams(viewController: self, params: ["source_page": ""], selectMeidou: true)
                        NotificationCenter.default.post(name: .showSubMeidouWindow,
                                                        object: nil,
                                                        userInfo: ["params": params])
                        return
                    }
                } else {
                    // 调起会员半窗
                    let params = WHSubscribeWindowNotificationParams(viewController: self, params: ["source_page": ""])
                    NotificationCenter.default.post(name: .showSubscribeWindow,
                                                    object: nil,
                                                    userInfo: ["params": params])
                    return
                }
            }
        }
        
        delegate?.sopPreViewController(self,
                                       didSelectItem: currentItem,
                                       functionCode: priceDict[currentItem.id]?.functionCode ?? "whee.freeformula")
    }
    
    // MARK: - Private Methods
    private func addObserver() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(vipStatusDidChanged),
                                               name: .VipStatusDidChanged,
                                               object: nil)
    }
    
    @objc
    private func vipStatusDidChanged() {
        loadBalance()
        loadItemPrice()
    }
    
    private func selectedItem(with index: Int) {
        guard let templates = model?.templates,
              index < templates.count else {
            return
        }
        currentItem = templates[index]
        refreshGradientView()
        refreshDoneBtn()
    }
    
    private func refreshGradientView() {
        var colors = [UIColor.black, UIColor(rgb: 0xA4A4A4)]
        var locations: [NSNumber] = [0, 1]
        if let mainColor = currentItem?.preview?.mainColor,
           (mainColor.count == 7 || mainColor.count == 9) {
            colors.insert(UIColor(wh_hexString: mainColor), at: 1)
            locations.insert(0.5, at: 1)
        }
        gradientView.wh_enableGradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 0, y: 1)),
                                       colors: colors,
                                       locations: locations)
    }
    
    private func refreshDoneBtn() {
        if let currentItem = currentItem,
           let price = priceDict[currentItem.id] {
            if price.freeNum > 0 {
                // SOP有限免次数
                doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_star_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.freeNum)))
                doneBtn.setBackgroundColor(.color(color: .white))
            } else {
                if price.isVip {
                    if price.rightNum > 0 {
                        doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: String(format: WHLocalizedString("限免剩余 %d 次 "), price.rightNum)))
                        doneBtn.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
                    } else {
                        doneBtn.setState(.meidou(meidou: Int(price.amount ?? "0") ?? 0, title: WHLocalizedString("立即体验")))
                        doneBtn.setBackgroundColor(.color(color: .white))
                    }
                } else {
                    doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_vip_icon"), title: WHLocalizedString("立即体验"), subTitle: nil))
                    doneBtn.setBackgroundColor(.gradient(points: (CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 0)), colors: [UIColor(rgb: 0xF9E9BD), UIColor(rgb: 0xF7DDA6), UIColor(rgb: 0xF7D48F)], locations: [0, 0.5, 1]))
                }
            }
        } else {
            doneBtn.setState(.normal(icon: UIImage(cm_named: "wh_sop_pre_star_icon"), title: WHLocalizedString("立即体验"), subTitle: nil))
            doneBtn.setBackgroundColor(.color(color: .white))
        }
    }
    
    private func loadData() {
        guard let id = id else {
            return
        }
        contentView.isUserInteractionEnabled = false
        WHSharedRequest.GET("/feed_card/transfer_info.json",
                            params: ["id": id]) { [weak self] (response: WHOriginalResponse) in
            guard let self = self else {
                return
            }
            if response.isSuccess(),
               let data = response.data() as? [String: Any],
               let model = WHSOPPreModel.yy_model(with: data) {
                self.contentView.isUserInteractionEnabled = true
                self.model = model
                self.loadItemPrice()
            } else {
                self.showToast(title: response.message() ?? WHLocalizedString("网络异常，请重试"))
            }
        }
    }
    
    private func loadItemPrice() {
        guard let model = model else {
            return
        }
        let list = model.templates.filter { $0.limitVip }
        guard list.count > 0 else {
            return
        }
        let ids: [String: String] = [
            "template_ids": list.map({ String($0.id) }).joined(separator: ",")
        ]
        let params: [String: Any] = [
            "function_code": "ai_template",
            "function_body": ids.wh_toJsonString() ?? "",
        ]
        WHSharedRequest.POST("/sub/get_price_batch.json",
                             params: params) { [weak self] (response: WHOriginalResponse) in
            guard let self = self else {
                return
            }
            if response.isSuccess(),
               let data = response.data() as? [String: Any],
               let list = data["list"] as? [[String: Any]],
               list.count > 0 {
                for dict in list {
                    if let templateId = dict["template_id"] as? Int,
                       let priceData = dict["price_data"] as? [String: Any] {
                        self.priceDict[templateId] = WHGcPriceModel.yy_model(with: priceData)
                    }
                }
                self.refreshDoneBtn()
            }
        }
    }
    
    private func loadBalance() {
        WHServerReport.requestBalanceAmount { [weak self] model in
            self?.balance = model
        }
    }
    
}

// MARK: - WHSOPPreMultipleCardViewDelegate
extension WHSOPPreViewController: WHSOPPreMultipleCardViewDelegate {
    
    func multipleCardView(_ view: WHSOPPreMultipleCardView, didSelectItemAt index: Int) {
        selectedItem(with: index)
    }
    
}

struct WHSOPPreViewLayoutConstants {
    
    // MARK: - 公共常量
    static let doneBtnHeight: CGFloat = 56
    static let doneBtnBottomSpace: CGFloat = 42
    static let descHeight: CGFloat = 18
    static let titleHeight: CGFloat = 39
    static let titleBottomSpace: CGFloat = 4
    
    static var gradientHeight: CGFloat {
        return WHSOPPreSegmentedView.Height + segmentedBottomSpace + titleHeight + titleBottomSpace + descHeight + descBottomSpace + doneBtnHeight + doneBtnBottomSpace
    }
    
    // MARK: - 多卡片布局常量
    static let descBottomSpace: CGFloat = 28
    static let segmentedBottomSpace: CGFloat = 24
    static let cardViewBottomSpace: CGFloat = 20

    static var cardViewHeight: CGFloat {
        return WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT - cardViewBottomSpace - WHSOPPreSegmentedView.Height - segmentedBottomSpace - titleHeight - titleBottomSpace - descHeight - descBottomSpace - doneBtnHeight - doneBtnBottomSpace
    }
    
    static var cardSize: CGSize {
        let height = cardViewHeight - 28
        return CGSize(width: height * 3.0 / 4.0, height: height)
    }
    
    static var cardSpace: CGFloat {
        return (WH_SCREEN_WIDTH - cardSize.width) / 4
    }
    
    // MARK: - 单卡片布局常量
    static let descBottomSpace1: CGFloat = 40
    static let cardViewBottomSpace1: CGFloat = 20
    static let cardInset = UIEdgeInsets(top: 22, left: 16, bottom: 22, right: 16)

    static var cardViewHeight1: CGFloat {
        return WH_SCREEN_HEIGHT - WH_NAVIGATION_BAR_HEIGHT - cardViewBottomSpace1 - titleHeight - titleBottomSpace - descHeight - descBottomSpace1 - doneBtnHeight - doneBtnBottomSpace
    }
    
    static var cardSize1: CGSize {
        var width: CGFloat = WH_SCREEN_WIDTH - cardInset.left - cardInset.right
        var height: CGFloat = width * 4.0 / 3.0
        if height > cardViewHeight1 - cardInset.top - cardInset.bottom {
            height = cardViewHeight1 - cardInset.top - cardInset.bottom
            width = height * 3.0 / 4.0
        }
        return CGSize(width: width, height: height)
    }
    
}
